{"version": 3, "file": "static/js/593.594610ec.chunk.js", "mappings": "8PAEO,SAASA,EAAoBC,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,CACA,MACA,GADoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,OAAQ,gBAAiB,iBAAkB,kBAAmB,SAAU,iB,wBCHxH,MAAMC,EAAuB,CAClCC,QAAS,eACTC,YAAa,eACbC,UAAW,iBACXC,cAAe,iBACfC,MAAO,cAiBT,EAZ0BC,IAGpB,IAHqB,MACzBC,EAAK,WACLC,GACDF,EACC,MAAMG,EAP0BC,IACzBV,EAAqBU,IAAUA,EAMbC,CAA0BH,EAAWE,OACxDA,GAAQE,EAAAA,EAAAA,IAAQL,EAAO,WAAFM,OAAaJ,IAAoB,IAAUD,EAAWE,MAC3EI,GAAeF,EAAAA,EAAAA,IAAQL,EAAO,WAAFM,OAAaJ,EAAgB,YAC/D,MAAI,SAAUF,GAASO,EACd,QAAPD,OAAeC,EAAY,YAEtBC,EAAAA,EAAAA,IAAML,EAAO,GAAI,E,aClB1B,MAAMM,EAAY,CAAC,YAAa,QAAS,YAAa,SAAU,UAAW,oBAAqB,YAAa,UAAW,MA2BlHC,GAAWC,EAAAA,EAAAA,IAAOC,EAAAA,EAAY,CAClCC,KAAM,UACNvB,KAAM,OACNwB,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJf,GACEc,EACJ,MAAO,CAACC,EAAOC,KAAMD,EAAO,YAADV,QAAaY,EAAAA,EAAAA,GAAWjB,EAAWkB,aAAwC,WAAzBlB,EAAWmB,WAA0BJ,EAAOK,OAAO,GAPnHV,EASdZ,IAGG,IAHF,MACFC,EAAK,WACLC,GACDF,EACC,OAAOuB,EAAAA,EAAAA,GAAS,CAAC,EAA4B,SAAzBrB,EAAWkB,WAAwB,CACrDI,eAAgB,QACU,UAAzBtB,EAAWkB,WAAyB,CACrCI,eAAgB,OAChB,UAAW,CACTA,eAAgB,cAEQ,WAAzBtB,EAAWkB,YAA0BG,EAAAA,EAAAA,GAAS,CAC/CC,eAAgB,aACM,YAArBtB,EAAWE,OAAuB,CACnCqB,oBAAqBC,EAAkB,CACrCzB,QACAC,gBAED,CACD,UAAW,CACTuB,oBAAqB,aAEI,WAAzBvB,EAAWmB,WAA0B,CACvCM,SAAU,WACVC,wBAAyB,cACzBC,gBAAiB,cAGjBC,QAAS,EACTC,OAAQ,EACRC,OAAQ,EAERC,aAAc,EACdC,QAAS,EAETC,OAAQ,UACRC,WAAY,OACZC,cAAe,SACfC,cAAe,OAEfC,iBAAkB,OAElB,sBAAuB,CACrBC,YAAa,QAEf,CAAC,KAADjC,OAAMkC,EAAYC,eAAiB,CACjCZ,QAAS,SAEX,IA0HJ,EAxH0Ba,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAM7B,GAAQ8B,EAAAA,EAAAA,GAAgB,CAC5B9B,MAAO4B,EACP9B,KAAM,aAEF,UACFiC,EAAS,MACT3C,EAAQ,UAAS,UACjBiB,EAAY,IAAG,OACf2B,EAAM,QACNC,EAAO,kBACPC,EAAiB,UACjB9B,EAAY,SAAQ,QACpB+B,EAAU,UAAS,GACnBC,GACEpC,EACJqC,GAAQC,EAAAA,EAAAA,GAA8BtC,EAAON,IACzC,kBACJ6C,EACAP,OAAQQ,EACRP,QAASQ,EACTZ,IAAKa,IACHC,EAAAA,EAAAA,MACGjB,EAAckB,GAAmBjB,EAAAA,UAAe,GACjDkB,GAAaC,EAAAA,EAAAA,GAAWjB,EAAKa,GAmB7BxD,GAAaqB,EAAAA,EAAAA,GAAS,CAAC,EAAGP,EAAO,CACrCZ,QACAiB,YACAqB,eACAtB,YACA+B,YAEIY,EAzHkB7D,KACxB,MAAM,QACJ6D,EAAO,UACP1C,EAAS,aACTqB,EAAY,UACZtB,GACElB,EACE8D,EAAQ,CACZ9C,KAAM,CAAC,OAAQ,YAAFX,QAAcY,EAAAA,EAAAA,GAAWC,IAA4B,WAAdC,GAA0B,SAAUqB,GAAgB,iBAE1G,OAAOuB,EAAAA,EAAAA,GAAeD,EAAO1E,EAAqByE,EAAQ,EA+G1CG,CAAkBhE,GAClC,OAAoBiE,EAAAA,EAAAA,KAAKxD,GAAUY,EAAAA,EAAAA,GAAS,CAC1CnB,MAAOA,EACP2C,WAAWqB,EAAAA,EAAAA,GAAKL,EAAQ7C,KAAM6B,GAC9BgB,QAASb,EACT7B,UAAWA,EACX2B,OA/BiBqB,IACjBb,EAAkBa,IACgB,IAA9Bd,EAAkBe,SACpBV,GAAgB,GAEdZ,GACFA,EAAOqB,EACT,EAyBApB,QAvBkBoB,IAClBZ,EAAmBY,IACe,IAA9Bd,EAAkBe,SACpBV,GAAgB,GAEdX,GACFA,EAAQoB,EACV,EAiBAxB,IAAKgB,EACL3D,WAAYA,EACZiD,QAASA,EACTC,GAAI,IAAMmB,OAAOC,KAAK9E,GAAsB+E,SAASrE,GAEhD,GAFyD,CAAC,CAC7DA,aACYsE,MAAMC,QAAQvB,GAAMA,EAAK,CAACA,KACvCC,GACL,G,4IC6GA,QApQA,WACI,MAAOuB,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KACvCK,EAAeC,UACjBf,EAAMgB,iBACN,IACI,MAAMC,QAAiBC,EAAAA,EAAAA,GAAwBX,GAC3CU,EAASE,QACTR,GAAa,GAEbE,EAAcI,EAASG,aAE/B,CAAE,MAAO1F,GACL2F,QAAQ3F,MAAM,sBAAuBA,EACzC,GAOJ,OACIoE,EAAAA,EAAAA,KAACwB,EAAAA,EAAS,CAACC,SAAS,KAAKxC,GAAI,CAAEyC,UAAW,QAAQC,SAC5Cf,GAsJMZ,EAAAA,EAAAA,KAACwB,EAAAA,EAAS,CAACC,SAAS,KAAKxC,GAAI,CAAEyC,UAAW,SAAUC,UACxDC,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CACA5C,GAAI,CACAyC,UAAW,EACXI,QAAS,OACTC,cAAe,SACfC,WAAY,UACdL,SAAA,EAEF3B,EAAAA,EAAAA,KAACtD,EAAAA,EAAU,CAACsC,QAAQ,KAAKC,GAAI,CACzBgD,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,cAAe,wBACfC,UAAW,UAEbX,SAAC,sBAGHC,EAAAA,EAAAA,MAAClF,EAAAA,EAAU,CAACsC,QAAQ,QAAQ/C,MAAM,gBAAgBsG,MAAM,SAAStD,GAAI,CACjEgD,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,cAAe,wBACfC,UAAW,UACbX,SAAA,CAAC,mCAC0ClB,EAAM,gDAEnDT,EAAAA,EAAAA,KAACwC,EAAAA,EAAM,CACKC,WAAS,EACTzD,QAAQ,YACRC,GAAI,CACAyD,MAAO,iBACPC,OAAQ,eACR5E,QAAS,sBACT6E,IAAK,MACL9E,aAAc,OACd+E,WAAY,UACZnB,UAAW,GAEfoB,QAAS9B,EAAaW,SACjC,kBAGD3B,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAACkB,GAAI,EAAEpB,UACPC,EAAAA,EAAAA,MAAClF,EAAAA,EAAU,CAACsC,QAAQ,QAAQ/C,MAAM,gBAAgBsG,MAAM,SAAStD,GAAI,CAAEyC,UAAW,GAAIC,SAAA,EAClF3B,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC/D,GAAI,CACNyD,MAAO,iBACPC,OAAQ,eACRC,IAAK,OACLK,QAAS,MACT5F,eAAgB,OAChBpB,MAAO,UACPyF,UAAW,EACXO,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,QACZE,UAAW,UAEZY,KAAK,gBAAevB,SAAC,iBAAmB,OAAG3B,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC/D,GAAI,CACpDyD,MAAO,iBACPC,OAAQ,eACRC,IAAK,OACLK,QAAS,MACT5F,eAAgB,OAChBpB,MAAO,UACPyF,UAAW,EACXO,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,QACZE,UAAW,UAEZY,KAAK,kBAAiBvB,SAAC,8BAjOtCC,EAAAA,EAAAA,MAAA,QAAMuB,SAAUnC,EAAaW,SAAA,EACzB3B,EAAAA,EAAAA,KAACtD,EAAAA,EAAU,CACPsC,QAAQ,KACRoE,cAAY,EACZnE,GAAI,CACAgD,WAAY,YACZC,SAAU,OACVC,WAAY,IACZC,WAAY,UACZC,cAAe,QACfC,UAAW,SACXrG,MAAO,WACT0F,SACL,gBAIDC,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CACA5C,GAAI,CACAyC,UAAW,EACXI,QAAS,OACTC,cAAe,SACfC,WAAY,UACdL,SAAA,EAEF3B,EAAAA,EAAAA,KAACtD,EAAAA,EAAU,CAACsC,QAAQ,KAAKoE,cAAY,EAACnE,GAAI,CACtCgD,WAAY,UACZC,SAAU,OACVC,WAAY,MACZC,WAAY,OACZC,cAAe,wBACfC,UAAW,UACbX,SAAC,yBAGH3B,EAAAA,EAAAA,KAACtD,EAAAA,EAAU,CAACsC,QAAQ,QAAQ/C,MAAM,gBAAgBsG,MAAM,SAAStD,GAAI,CACjEgD,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,cAAe,wBACfC,UAAW,SACXrG,MAAO,WACT0F,SAAC,0FAGHC,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAC5C,GAAI,CAAEyD,MAAO,OAAQK,GAAI,GAAIpB,SAAA,EAC9B3B,EAAAA,EAAAA,KAACtD,EAAAA,EAAU,CAACsC,QAAQ,QAAQC,GAAI,CAC5BgD,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZnG,MAAO,UACPoH,IAAK,EACLf,UAAW,OACXgB,WAAY,QACd3B,SAAC,WAGH3B,EAAAA,EAAAA,KAACuD,EAAAA,EAAS,CACN1F,OAAO,SACP2F,UAAQ,EACRf,WAAS,EACTgB,GAAG,QACH9G,KAAK,QACL+G,aAAa,QACbC,MAAOlD,EACPmD,SA3EDC,IACvBnD,EAASmD,EAAEC,OAAOH,OAClB5C,EAAc,GAAG,EA0EOgD,WAAS,EACTnI,QAASkF,EACTkD,WAAYlD,EACZ7B,GAAI,CACAyD,MAAO,iBACPC,OAAQ,eACR5E,QAAS,mBACTD,aAAc,kBACdF,OAAQ,kBACRqF,QAAS,MACT,qCAAsC,CAClCnF,aAAc,eAK9BkC,EAAAA,EAAAA,KAACwC,EAAAA,EAAM,CACHyB,KAAK,SACLxB,WAAS,EACTzD,QAAQ,YACRC,GAAI,CACAyD,MAAO,iBACPC,OAAQ,eACR5E,QAAS,sBACT6E,IAAK,MACL9E,aAAc,OACdmF,QAAS,MACTJ,WAAY,WACdlB,SACL,cAID3B,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAACE,KAAQgB,+BAA+BlF,QAAQ,QAAQuD,MAAM,SAAStD,GAAI,CAC5EyC,UAAW,EACXzF,MAAO,UACPoB,eAAgB,OAChB4E,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZE,UAAW,UACbX,SAAC,mBAGH3B,EAAAA,EAAAA,KAAC6B,EAAAA,EAAG,CAACkB,GAAI,EAAEpB,UACPC,EAAAA,EAAAA,MAAClF,EAAAA,EAAU,CAACsC,QAAQ,QAAQ/C,MAAM,gBAAgBsG,MAAM,SAAStD,GAAI,CAAEyC,UAAW,GAAIC,SAAA,EAClF3B,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC/D,GAAI,CACNyD,MAAO,iBACPC,OAAQ,eACRC,IAAK,OACLK,QAAS,MACT5F,eAAgB,OAChBpB,MAAO,UACPyF,UAAW,EACXO,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZE,UAAW,UAEZY,KAAK,gBAAevB,SAAC,iBAAmB,OAAG3B,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC/D,GAAI,CACpDyD,MAAO,iBACPC,OAAQ,eACRC,IAAK,OACLK,QAAS,MACT5F,eAAgB,OAChBpB,MAAO,UACPyF,UAAW,EACXO,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZE,UAAW,UAEZY,KAAK,kBAAiBvB,SAAC,+BA0F1D,C", "sources": ["../node_modules/@mui/material/Link/linkClasses.js", "../node_modules/@mui/material/Link/getTextDecoration.js", "../node_modules/@mui/material/Link/Link.js", "components/login/Forgotpassword.tsx"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "import { getPath } from '@mui/system';\nimport { alpha } from '@mui/system/colorManipulator';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"onBlur\", \"onFocus\", \"TypographyClasses\", \"underline\", \"variant\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useForkRef from '../utils/useForkRef';\nimport Typography from '../Typography';\nimport linkClasses, { getLinkUtilityClass } from './linkClasses';\nimport getTextDecoration, { colorTransformations } from './getTextDecoration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    component,\n    focusVisible,\n    underline\n  } = ownerState;\n  const slots = {\n    root: ['root', `underline${capitalize(underline)}`, component === 'button' && 'button', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getLinkUtilityClass, classes);\n};\nconst LinkRoot = styled(Typography, {\n  name: 'MuiLink',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`underline${capitalize(ownerState.underline)}`], ownerState.component === 'button' && styles.button];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({}, ownerState.underline === 'none' && {\n    textDecoration: 'none'\n  }, ownerState.underline === 'hover' && {\n    textDecoration: 'none',\n    '&:hover': {\n      textDecoration: 'underline'\n    }\n  }, ownerState.underline === 'always' && _extends({\n    textDecoration: 'underline'\n  }, ownerState.color !== 'inherit' && {\n    textDecorationColor: getTextDecoration({\n      theme,\n      ownerState\n    })\n  }, {\n    '&:hover': {\n      textDecorationColor: 'inherit'\n    }\n  }), ownerState.component === 'button' && {\n    position: 'relative',\n    WebkitTapHighlightColor: 'transparent',\n    backgroundColor: 'transparent',\n    // Reset default value\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    border: 0,\n    margin: 0,\n    // Remove the margin in Safari\n    borderRadius: 0,\n    padding: 0,\n    // Remove the padding in Firefox\n    cursor: 'pointer',\n    userSelect: 'none',\n    verticalAlign: 'middle',\n    MozAppearance: 'none',\n    // Reset\n    WebkitAppearance: 'none',\n    // Reset\n    '&::-moz-focus-inner': {\n      borderStyle: 'none' // Remove Firefox dotted outline.\n    },\n    [`&.${linkClasses.focusVisible}`]: {\n      outline: 'auto'\n    }\n  });\n});\nconst Link = /*#__PURE__*/React.forwardRef(function Link(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiLink'\n  });\n  const {\n      className,\n      color = 'primary',\n      component = 'a',\n      onBlur,\n      onFocus,\n      TypographyClasses,\n      underline = 'always',\n      variant = 'inherit',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const handlerRef = useForkRef(ref, focusVisibleRef);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    focusVisible,\n    underline,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(LinkRoot, _extends({\n    color: color,\n    className: clsx(classes.root, className),\n    classes: TypographyClasses,\n    component: component,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    ref: handlerRef,\n    ownerState: ownerState,\n    variant: variant,\n    sx: [...(!Object.keys(colorTransformations).includes(color) ? [{\n      color\n    }] : []), ...(Array.isArray(sx) ? sx : [sx])]\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Link.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the link.\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Typography`](/material-ui/api/typography/) element.\n   */\n  TypographyClasses: PropTypes.object,\n  /**\n   * Controls when the link should have an underline.\n   * @default 'always'\n   */\n  underline: PropTypes.oneOf(['always', 'hover', 'none']),\n  /**\n   * Applies the theme typography styles.\n   * @default 'inherit'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string])\n} : void 0;\nexport default Link;", "import React, { useState } from 'react';\r\nimport { Con<PERSON><PERSON>, <PERSON><PERSON>ield, Button, Typography, Link, Box } from '@mui/material';\r\nimport { sendForgotPasswordEmail } from '../../services/ForgotPasswordService';\r\n\r\nfunction ForgotPassword() {\r\n    const [email, setEmail] = useState('');\r\n    const [submitted, setSubmitted] = useState(false);\r\n    const [Errorfiled, setErrorField] = useState(\" \");\r\n    const handleSubmit = async (event: React.FormEvent) => {\r\n        event.preventDefault();\r\n        try {\r\n            const response = await sendForgotPasswordEmail(email);  \r\n            if (response.Success) { \r\n                setSubmitted(true);\r\n            }else{\r\n                setErrorField(response.ErrorMessage);\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error sending email\", error);\r\n        }\r\n    };\r\n    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        setEmail(e.target.value);\r\n        setErrorField(''); \r\n    };\r\n\r\n    return (\r\n        <Container maxWidth=\"xs\" sx={{ marginTop: \"60px\"}}>\r\n            {!submitted ? (\r\n                <form onSubmit={handleSubmit}>\r\n                    <Typography\r\n                        variant=\"h5\"\r\n                        gutterBottom\r\n                        sx={{\r\n                            fontFamily: 'Syncopate',\r\n                            fontSize: '19px',\r\n                            fontWeight: 700,\r\n                            lineHeight: '19.78px',\r\n                            letterSpacing: '0.3px',\r\n                            textAlign: 'center',\r\n                            color: \"#5F9EA0\",\r\n                        }}\r\n                    >\r\n                        QuickAdopt\r\n                    </Typography>\r\n\r\n                    <Box\r\n                        sx={{\r\n                            marginTop: 8,\r\n                            display: 'flex',\r\n                            flexDirection: 'column',\r\n                            alignItems: 'center',\r\n                        }}\r\n                    >\r\n                        <Typography variant=\"h5\" gutterBottom sx={{\r\n                            fontFamily: \"Poppins\",\r\n                            fontSize: \"24px\",\r\n                            fontWeight: \"700\",\r\n                            lineHeight: \"36px\",\r\n                            letterSpacing: \"0.30000001192092896px\",\r\n                            textAlign: \"center\"\r\n                        }}>\r\n                            Reset your password\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{\r\n                            fontFamily: \"Poppins\",\r\n                            fontSize: \"14px\",\r\n                            fontWeight: 400,\r\n                            lineHeight: \"19px\",\r\n                            letterSpacing: \"0.30000001192092896px\",\r\n                            textAlign: \"center\",\r\n                            color: \"#222222\"\r\n                        }}>\r\n                            Enter your email address and we’ll send you instructions to reset your password\r\n                        </Typography>\r\n                        <Box sx={{ width: '100%', mt: 3 }}>\r\n                            <Typography variant=\"body2\" sx={{\r\n                                fontFamily: \"Poppins\",\r\n                                fontSize: \"16px\",\r\n                                fontWeight: 400,\r\n                                lineHeight: \"23px\",\r\n                                color: \"#444444\",\r\n                                mb: -3,\r\n                                textAlign: \"left\",\r\n                                marginLeft: \"12px\"\r\n                            }}>\r\n                                Email\r\n                            </Typography>\r\n                            <TextField\r\n                                margin=\"normal\"\r\n                                required\r\n                                fullWidth\r\n                                id=\"email\"\r\n                                name=\"email\"\r\n                                autoComplete=\"email\"\r\n                                value={email}\r\n                                onChange={handleEmailChange}\r\n                                autoFocus\r\n                                error={!!Errorfiled} \r\n                                helperText={Errorfiled} \r\n                                sx={{\r\n                                    width: \"Fill (325px)px\",\r\n                                    height: \"Hug (48px)px\",\r\n                                    padding: \"12px 0px 0px 0px\",\r\n                                    borderRadius: \"6px 0px 0px 0px\",\r\n                                    border: \"1px 0px 0px 0px\",\r\n                                    opacity: \"0px\",\r\n                                    '& .MuiOutlinedInput-notchedOutline': {\r\n                                        borderRadius: \"15px\",\r\n                                    },\r\n                                }}\r\n                            />\r\n                        </Box>\r\n                        <Button\r\n                            type=\"submit\"\r\n                            fullWidth\r\n                            variant=\"contained\"\r\n                            sx={{\r\n                                width: \"Fill (325px)px\",\r\n                                height: \"Hug (44px)px\",\r\n                                padding: \"10px 12px 10px 12px\",\r\n                                gap: \"4px\",\r\n                                borderRadius: \"15px\",\r\n                                opacity: \"0px\",\r\n                                background: \"#5F9EA0\"\r\n                            }}\r\n                        >\r\n                            Continue\r\n                        </Button>\r\n\r\n                        <Link href = {process.env.REACT_APP_IDS_API} variant=\"body2\" align=\"center\" sx={{\r\n                            marginTop: 4,\r\n                            color: '#6BB2A1',\r\n                            textDecoration: 'none', \r\n                            fontFamily: \"Poppins\",\r\n                            fontSize: \"16px\",\r\n                            fontWeight: 400,\r\n                            lineHeight: \"24px\",\r\n                            textAlign: \"center\"\r\n                        }}>\r\n                            Back to login\r\n                        </Link>\r\n                        <Box mt={5}>\r\n                            <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{ marginTop: 9 }} >\r\n                                <Link sx={{\r\n                                    width: \"Fill (325px)px\",\r\n                                    height: \"Hug (24px)px\",\r\n                                    gap: \"10px\",\r\n                                    opacity: \"0px\",\r\n                                    textDecoration: 'none',\r\n                                    color: '#6BB2A1',\r\n                                    marginTop: 9,\r\n                                    fontFamily: \"Poppins\",\r\n                                    fontSize: \"16px\",\r\n                                    fontWeight: 400,\r\n                                    lineHeight: \"24px\",\r\n                                    textAlign: \"center\"\r\n\r\n                                }} href=\"/terms-of-use\">Terms of use</Link> | <Link sx={{\r\n                                    width: \"Fill (325px)px\",\r\n                                    height: \"Hug (24px)px\",\r\n                                    gap: \"10px\",\r\n                                    opacity: \"0px\",\r\n                                    textDecoration: 'none',\r\n                                    color: '#6BB2A1',\r\n                                    marginTop: 9,\r\n                                    fontFamily: \"Poppins\",\r\n                                    fontSize: \"16px\",\r\n                                    fontWeight: 400,\r\n                                    lineHeight: \"24px\",\r\n                                    textAlign: \"center\"\r\n\r\n                                }} href=\"/privacy-policy\">Privacy Policy</Link>\r\n                            </Typography>\r\n                        </Box>\r\n                    </Box>\r\n                </form>\r\n            ) : (\r\n                    <Container maxWidth=\"xs\" sx={{ marginTop: \"135px\" }}>\r\n                <Box\r\n                    sx={{\r\n                        marginTop: 8,\r\n                        display: 'flex',\r\n                        flexDirection: 'column',\r\n                        alignItems: 'center',\r\n                    }}\r\n                >\r\n                    <Typography variant=\"h5\" sx={{\r\n                        fontFamily: \"Poppins\",\r\n                        fontSize: \"24px\",\r\n                        fontWeight: 700,\r\n                        lineHeight: \"36px\",\r\n                        letterSpacing: \"0.30000001192092896px\",\r\n                        textAlign: \"center\",\r\n\r\n                    }}>\r\n                        Check your email\r\n                    </Typography>\r\n                    <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{\r\n                        fontFamily: \"Poppins\",\r\n                        fontSize: \"14px\",\r\n                        fontWeight: 400,\r\n                        lineHeight: \"19px\",\r\n                        letterSpacing: \"0.30000001192092896px\",\r\n                        textAlign: \"center\"\r\n                    }}>\r\n                                Please check your email address {email} for instructions to reset your password.\r\n                    </Typography>\r\n                    <Button\r\n                                fullWidth\r\n                                variant=\"contained\"\r\n                                sx={{\r\n                                    width: \"Fill (325px)px\",\r\n                                    height: \"Hug (44px)px\",\r\n                                    padding: \"10px 12px 10px 12px\",\r\n                                    gap: \"4px\",\r\n                                    borderRadius: \"15px\",\r\n                                    background: \"#5F9EA0\",\r\n                                    marginTop: 3\r\n                                }}\r\n                                onClick={handleSubmit}\r\n                    >\r\n                        Resend Email\r\n                    </Button>\r\n                    <Box mt={5}>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\" align=\"center\" sx={{ marginTop: 9 }} >\r\n                            <Link sx={{\r\n                                width: \"Fill (325px)px\",\r\n                                height: \"Hug (24px)px\",\r\n                                gap: \"10px\",\r\n                                opacity: \"0px\",\r\n                                textDecoration: 'none',\r\n                                color: '#6BB2A1',\r\n                                marginTop: 9,\r\n                                fontFamily: \"Poppins\",\r\n                                fontSize: \"16px\",\r\n                                fontWeight: 400,\r\n                                lineHeight: \"273px\",\r\n                                textAlign: \"center\"\r\n\r\n                            }} href=\"/terms-of-use\">Terms of use</Link> | <Link sx={{\r\n                                width: \"Fill (325px)px\",\r\n                                height: \"Hug (24px)px\",\r\n                                gap: \"10px\",\r\n                                opacity: \"0px\",\r\n                                textDecoration: 'none',\r\n                                color: '#6BB2A1',\r\n                                marginTop: 9,\r\n                                fontFamily: \"Poppins\",\r\n                                fontSize: \"16px\",\r\n                                fontWeight: 400,\r\n                                lineHeight: \"273px\",\r\n                                textAlign: \"center\"\r\n\r\n                            }} href=\"/privacy-policy\">Privacy Policy</Link>\r\n                        </Typography>\r\n                    </Box>\r\n                </Box>\r\n                        </Container>\r\n            )}\r\n        </Container >\r\n    );\r\n}\r\n\r\nexport default ForgotPassword;\r\n"], "names": ["getLinkUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "error", "_ref", "theme", "ownerState", "transformedColor", "color", "transformDeprecatedColors", "<PERSON><PERSON><PERSON>", "concat", "channelColor", "alpha", "_excluded", "LinkRoot", "styled", "Typography", "name", "overridesResolver", "props", "styles", "root", "capitalize", "underline", "component", "button", "_extends", "textDecoration", "textDecorationColor", "getTextDecoration", "position", "WebkitTapHighlightColor", "backgroundColor", "outline", "border", "margin", "borderRadius", "padding", "cursor", "userSelect", "verticalAlign", "MozAppearance", "WebkitAppearance", "borderStyle", "linkClasses", "focusVisible", "React", "inProps", "ref", "useDefaultProps", "className", "onBlur", "onFocus", "TypographyClasses", "variant", "sx", "other", "_objectWithoutPropertiesLoose", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "focusVisibleRef", "useIsFocusVisible", "setFocusVisible", "handler<PERSON>ef", "useForkRef", "classes", "slots", "composeClasses", "useUtilityClasses", "_jsx", "clsx", "event", "current", "Object", "keys", "includes", "Array", "isArray", "email", "setEmail", "useState", "submitted", "setSubmitted", "Errorfiled", "setErrorField", "handleSubmit", "async", "preventDefault", "response", "sendForgotPasswordEmail", "Success", "ErrorMessage", "console", "Container", "max<PERSON><PERSON><PERSON>", "marginTop", "children", "_jsxs", "Box", "display", "flexDirection", "alignItems", "fontFamily", "fontSize", "fontWeight", "lineHeight", "letterSpacing", "textAlign", "align", "<PERSON><PERSON>", "fullWidth", "width", "height", "gap", "background", "onClick", "mt", "Link", "opacity", "href", "onSubmit", "gutterBottom", "mb", "marginLeft", "TextField", "required", "id", "autoComplete", "value", "onChange", "e", "target", "autoFocus", "helperText", "type", "process"], "sourceRoot": ""}