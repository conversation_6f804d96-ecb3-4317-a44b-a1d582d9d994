import React, { useState } from 'react';
import { Box, Typography, IconButton, Container, Tabs, Tab } from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  TrendingUp,
  TrendingDown,
  People,
  CheckCircle,
  Star,
  Schedule,
  FilterList,
  CalendarToday
} from '@mui/icons-material';
import Card from '../common/Card';
import ModernButton from '../common/ModernButton';
import ChartPlaceholder from './ChartPlaceholder';

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeValue: string;
  trend: 'up' | 'down';
  icon: React.ReactNode;
  color: string;
}

const DashboardWrapper = styled('div')({
  backgroundColor: '#f6f9ff',
  minHeight: '100vh',
});

const HeaderSection = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: 'var(--spacing-4)',
});

const StyledTabs = styled(Tabs)({
  marginBottom: 'var(--spacing-4)',
  '& .MuiTabs-indicator': {
    backgroundColor: 'var(--color-primary-600)',
  },
  '& .MuiTab-root': {
    fontSize: 'var(--font-size-sm)',
    fontWeight: 'var(--font-weight-medium)',
    textTransform: 'none',
    color: 'var(--color-gray-600)',
    '&.Mui-selected': {
      color: 'var(--color-primary-600)',
      fontWeight: 'var(--font-weight-semibold)',
    },
  },
});

const FilterSection = styled(Box)({
  display: 'flex',
  gap: 'var(--spacing-3)',
  alignItems: 'center',
});

const MetricsGrid = styled(Box)({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
  gap: 'var(--spacing-4)',
  marginBottom: 'var(--spacing-6)',
});

const MetricCardContainer = styled(Card)({
  padding: 'var(--spacing-5)',
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-4)',
});

const MetricIcon = styled(Box)<{ color: string }>(({ color }) => ({
  width: '48px',
  height: '48px',
  borderRadius: 'var(--radius-lg)',
  backgroundColor: `${color}15`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  
  '& svg': {
    color: color,
    fontSize: '24px',
  },
}));

const MetricContent = styled(Box)({
  flex: 1,
});

const MetricTitle = styled(Typography)({
  fontSize: 'var(--font-size-sm)',
  color: 'var(--color-gray-600)',
  marginBottom: 'var(--spacing-1)',
});

const MetricValue = styled(Typography)({
  fontSize: 'var(--font-size-2xl)',
  fontWeight: 'var(--font-weight-bold)',
  color: 'var(--color-gray-900)',
  marginBottom: 'var(--spacing-1)',
});

const MetricChange = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
});

const ChangeIndicator = styled(Box)<{ trend: 'up' | 'down' }>(({ trend }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
  fontSize: 'var(--font-size-xs)',
  fontWeight: 'var(--font-weight-medium)',
  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',
  
  '& svg': {
    fontSize: '16px',
  },
}));

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeValue,
  trend,
  icon,
  color,
}) => {
  return (
    <MetricCardContainer shadow="sm" hover>
      <MetricIcon color={color}>
        {icon}
      </MetricIcon>
      <MetricContent>
        <MetricTitle>{title}</MetricTitle>
        <MetricValue>{value}</MetricValue>
        <MetricChange>
          <ChangeIndicator trend={trend}>
            {trend === 'up' ? <TrendingUp /> : <TrendingDown />}
            {change}
          </ChangeIndicator>
          <Typography variant="caption" color="text.secondary">
            {changeValue}
          </Typography>
        </MetricChange>
      </MetricContent>
    </MetricCardContainer>
  );
};

const ModernDashboard: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    x: number;
    y: number;
    content: string;
    title: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    content: '',
    title: ''
  });

  const showTooltip = (event: React.MouseEvent, title: string, content: string) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;

    setTooltip({
      visible: true,
      x: rect.left + scrollX + rect.width / 2,
      y: rect.top + scrollY - 10,
      content,
      title
    });
  };

  const hideTooltip = () => {
    setTooltip(prev => ({ ...prev, visible: false }));
  };

  const overviewMetrics = [
    {
      title: 'Completion Rate',
      value: '87.3%',
      change: '****%',
      changeValue: '+2.8pp',
      trend: 'up' as const,
      icon: <CheckCircle />,
      color: 'var(--color-success-600)',
    },
    {
      title: 'User Satisfaction',
      value: '4.6',
      change: '+0.2',
      changeValue: 'out of 5.0',
      trend: 'up' as const,
      icon: <Star />,
      color: 'var(--color-warning-600)',
    },
    {
      title: 'Hours Saved',
      value: '2,847',
      change: '+18.7%',
      changeValue: '+447h',
      trend: 'up' as const,
      icon: <Schedule />,
      color: 'var(--color-primary-600)',
    },
  ];

  const analyticsMetrics = [
    {
      title: 'Active Users',
      value: '12,847',
      change: '+12.5%',
      changeValue: '+1,432',
      trend: 'up' as const,
      icon: <People />,
      color: 'var(--color-primary-600)',
    },
    {
      title: 'Completion Rate',
      value: '87.3%',
      change: '****%',
      changeValue: '+2.8pp',
      trend: 'up' as const,
      icon: <CheckCircle />,
      color: 'var(--color-success-600)',
    },
    {
      title: 'User Satisfaction',
      value: '4.6',
      change: '+0.2',
      changeValue: 'out of 5.0',
      trend: 'up' as const,
      icon: <Star />,
      color: 'var(--color-warning-600)',
    },
    {
      title: 'Hours Saved',
      value: '2,847',
      change: '+18.7%',
      changeValue: '+447h',
      trend: 'up' as const,
      icon: <Schedule />,
      color: 'var(--color-primary-600)',
    },
  ];

  const aiPerformanceMetrics = [
    {
      title: 'AI Response Time',
      value: '1.2s',
      change: '-15%',
      changeValue: 'faster',
      trend: 'up' as const,
      icon: <Schedule />,
      color: 'var(--color-success-600)',
    },
    {
      title: 'AI Accuracy',
      value: '94.8%',
      change: '+2.1%',
      changeValue: 'improved',
      trend: 'up' as const,
      icon: <CheckCircle />,
      color: 'var(--color-primary-600)',
    },
    {
      title: 'Model Confidence',
      value: '89.3%',
      change: '+1.8%',
      changeValue: 'higher',
      trend: 'up' as const,
      icon: <Star />,
      color: 'var(--color-warning-600)',
    },
    {
      title: 'Processing Load',
      value: '67%',
      change: '+5%',
      changeValue: 'capacity',
      trend: 'up' as const,
      icon: <People />,
      color: 'var(--color-error-500)',
    },
  ];

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0: // Overview
        return (
          <>
            {/* Header with Filters */}
            <HeaderSection>
              <Box>
                <Typography variant="h4" fontWeight="bold" color="text.primary">
                  Dashboard Overview
                </Typography>
              </Box>
              <FilterSection>
                <ModernButton
                  variant="outline"
                  startIcon={<FilterList />}
                  size="sm"
                >
                  Filter
                </ModernButton>
                <ModernButton
                  variant="outline"
                  startIcon={<CalendarToday />}
                  size="sm"
                >
                  Last 30 days
                </ModernButton>
              </FilterSection>
            </HeaderSection>

            {/* Overview Metrics Grid (4 cards like Analytics) */}
            <MetricsGrid>
              {analyticsMetrics.map((metric, index) => (
                <MetricCard key={index} {...metric} />
              ))}
            </MetricsGrid>

            {/* Overview Charts - Growth Trends and User Satisfaction */}
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
              {/* Growth Trends Chart */}
              <Card title="📈 Growth Trends" subtitle="User engagement over the last 6 months" padding="lg">
                <Box sx={{ height: '300px', position: 'relative', backgroundColor: 'white', borderRadius: 'var(--radius-md)' }}>
                  {/* Interactive Line Chart */}
                  <svg width="100%" height="280" viewBox="0 0 450 250" style={{ overflow: 'visible' }}>
                    {/* Grid lines */}
                    <defs>
                      <pattern id="interactiveGrid" width="75" height="50" patternUnits="userSpaceOnUse">
                        <path d="M 75 0 L 0 0 0 50" fill="none" stroke="#f1f5f9" strokeWidth="1"/>
                      </pattern>
                    </defs>
                    <rect width="100%" height="200" fill="url(#interactiveGrid)" />

                    {/* Y-axis */}
                    <line x1="50" y1="20" x2="50" y2="200" stroke="#e2e8f0" strokeWidth="1"/>
                    {/* X-axis */}
                    <line x1="50" y1="200" x2="400" y2="200" stroke="#e2e8f0" strokeWidth="1"/>

                    {/* Y-axis labels */}
                    <text x="40" y="25" fontSize="11" fill="#64748b" textAnchor="end">14000</text>
                    <text x="40" y="75" fontSize="11" fill="#64748b" textAnchor="end">10500</text>
                    <text x="40" y="125" fontSize="11" fill="#64748b" textAnchor="end">7000</text>
                    <text x="40" y="175" fontSize="11" fill="#64748b" textAnchor="end">3500</text>
                    <text x="40" y="205" fontSize="11" fill="#64748b" textAnchor="end">0</text>

                    {/* X-axis labels */}
                    <text x="80" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Jan</text>
                    <text x="140" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Feb</text>
                    <text x="200" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Mar</text>
                    <text x="260" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Apr</text>
                    <text x="320" y="220" fontSize="11" fill="#64748b" textAnchor="middle">May</text>
                    <text x="380" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Jun</text>

                    {/* Area fill with gradient */}
                    <defs>
                      <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.3"/>
                        <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.05"/>
                      </linearGradient>
                    </defs>
                    <path d="M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70 L 380 200 L 80 200 Z" fill="url(#areaGradient)"/>

                    {/* Main line */}
                    <path d="M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70" fill="none" stroke="#3b82f6" strokeWidth="3" strokeLinecap="round"/>

                    {/* Interactive data points */}
                    <g className="data-points">
                      <circle
                        cx="80" cy="160" r="8" fill="white" stroke="#3b82f6" strokeWidth="3"
                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Jan point');
                          showTooltip(e, 'Jan', '8,500 users');
                          e.currentTarget.setAttribute('r', '10');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('r', '8');
                        }}
                      />
                      <circle
                        cx="140" cy="150" r="8" fill="white" stroke="#3b82f6" strokeWidth="3"
                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Feb point');
                          showTooltip(e, 'Feb', '9,200 users');
                          e.currentTarget.setAttribute('r', '10');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('r', '8');
                        }}
                      />
                      <circle
                        cx="200" cy="130" r="8" fill="white" stroke="#3b82f6" strokeWidth="3"
                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Mar point');
                          showTooltip(e, 'Mar', '10,100 users');
                          e.currentTarget.setAttribute('r', '10');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('r', '8');
                        }}
                      />
                      <circle
                        cx="260" cy="110" r="8" fill="white" stroke="#3b82f6" strokeWidth="3"
                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Apr point');
                          showTooltip(e, 'Apr', '11,100 users');
                          e.currentTarget.setAttribute('r', '10');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('r', '8');
                        }}
                      />
                      <circle
                        cx="320" cy="90" r="8" fill="white" stroke="#3b82f6" strokeWidth="3"
                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering May point');
                          showTooltip(e, 'May', '12,300 users');
                          e.currentTarget.setAttribute('r', '10');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('r', '8');
                        }}
                      />
                      <circle
                        cx="380" cy="70" r="8" fill="white" stroke="#3b82f6" strokeWidth="3"
                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Jun point');
                          showTooltip(e, 'Jun', '13,200 users');
                          e.currentTarget.setAttribute('r', '10');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('r', '8');
                        }}
                      />
                    </g>
                  </svg>
                </Box>
              </Card>

              {/* User Satisfaction Donut Chart */}
              <Card title="😊 User Satisfaction" subtitle="Rating distribution this month" padding="lg">
                <Box sx={{ height: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', borderRadius: 'var(--radius-md)', position: 'relative' }}>
                  {/* Interactive Donut Chart */}
                  <svg width="220" height="220" viewBox="0 0 220 220">
                    {/* Donut segments with hover effects */}
                    <g>
                      {/* Excellent (5★) - 65% - Green */}
                      <circle
                        cx="110" cy="110" r="75" fill="none" stroke="#10b981" strokeWidth="30"
                        strokeDasharray="305 470" strokeDashoffset="0" transform="rotate(-90 110 110)"
                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Excellent segment');
                          showTooltip(e, 'Excellent (5★)', '65%');
                          e.currentTarget.setAttribute('stroke-width', '35');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('stroke-width', '30');
                        }}
                      />

                      {/* Good (4★) - 20% - Light Green */}
                      <circle
                        cx="110" cy="110" r="75" fill="none" stroke="#84cc16" strokeWidth="30"
                        strokeDasharray="94 470" strokeDashoffset="-305" transform="rotate(-90 110 110)"
                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Good segment');
                          showTooltip(e, 'Good (4★)', '20%');
                          e.currentTarget.setAttribute('stroke-width', '35');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('stroke-width', '30');
                        }}
                      />

                      {/* Average (3★) - 10% - Orange */}
                      <circle
                        cx="110" cy="110" r="75" fill="none" stroke="#f59e0b" strokeWidth="30"
                        strokeDasharray="47 470" strokeDashoffset="-399" transform="rotate(-90 110 110)"
                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Average segment');
                          showTooltip(e, 'Average (3★)', '10%');
                          e.currentTarget.setAttribute('stroke-width', '35');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('stroke-width', '30');
                        }}
                      />

                      {/* Poor (2★) - 3% - Orange-Red */}
                      <circle
                        cx="110" cy="110" r="75" fill="none" stroke="#f97316" strokeWidth="30"
                        strokeDasharray="14 470" strokeDashoffset="-446" transform="rotate(-90 110 110)"
                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Poor segment');
                          showTooltip(e, 'Poor (2★)', '3%');
                          e.currentTarget.setAttribute('stroke-width', '35');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('stroke-width', '30');
                        }}
                      />

                      {/* Very Poor (1★) - 2% - Red */}
                      <circle
                        cx="110" cy="110" r="75" fill="none" stroke="#ef4444" strokeWidth="30"
                        strokeDasharray="9 470" strokeDashoffset="-460" transform="rotate(-90 110 110)"
                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}
                        onMouseEnter={(e) => {
                          console.log('Hovering Very Poor segment');
                          showTooltip(e, 'Very Poor (1★)', '2%');
                          e.currentTarget.setAttribute('stroke-width', '35');
                        }}
                        onMouseLeave={(e) => {
                          hideTooltip();
                          e.currentTarget.setAttribute('stroke-width', '30');
                        }}
                      />
                    </g>

                    {/* Center circle for donut effect */}
                    <circle cx="110" cy="110" r="45" fill="white"/>
                  </svg>
                </Box>
              </Card>
            </Box>

            {/* Quick Actions and Recent Activity - Same layout as Analytics */}
            {/* <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: 'var(--spacing-6)', mb: 4 }}>
             
              <Card title="Quick Actions" subtitle="Take action based on your dashboard insights" padding="lg">
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>
                  <ModernButton variant="primary" fullWidth>
                    Create New Guide
                  </ModernButton>
                  <ModernButton variant="outline" fullWidth>
                    View Full Analytics
                  </ModernButton>
                  <ModernButton variant="outline" fullWidth>
                    AI Assistant Settings
                  </ModernButton>
                </Box>
              </Card>

              
              <Card title="Recent Activity" padding="lg">
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                  {[
                    { user: 'SC', action: 'New guide created', name: 'Sarah Chen', time: '2 min ago', type: 'create' },
                    { user: 'S', action: 'Guide completed 47 times', name: 'System', time: '15 min ago', type: 'completion' },
                    { user: 'MJ', action: 'AI response updated', name: 'Mike Johnson', time: '1 hour ago', type: 'update' },
                    { user: 'S', action: 'Low performance alert', name: 'System', time: '2 hours ago', type: 'alert' },
                  ].map((activity, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-3)' }}>
                      <Box sx={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        backgroundColor: 'var(--color-primary-100)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: 'var(--font-size-sm)',
                        fontWeight: 'var(--font-weight-semibold)',
                        color: 'var(--color-primary-700)'
                      }}>
                        {activity.user}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" fontWeight="medium">
                          {activity.action}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {activity.name} • {activity.time}
                        </Typography>
                      </Box>
                      <Box sx={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'
                      }} />
                    </Box>
                  ))}
                </Box>
              </Card>
            </Box> 
            */}

            {/* User Feedback & Satisfaction Section */}
            {/* User Satisfaction Ratings and Satisfaction Trend */}
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
              {/* User Satisfaction Ratings */}
              <Card title="⭐ User Satisfaction Ratings" padding="lg">
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                  {/* Excellent (5★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Excellent (5★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '65%',
                        height: '100%',
                        backgroundColor: '#10b981',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      1,247
                    </Typography>
                  </Box>

                  {/* Good (4★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Good (4★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '45%',
                        height: '100%',
                        backgroundColor: '#84cc16',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      892
                    </Typography>
                  </Box>

                  {/* Average (3★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Average (3★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '22%',
                        height: '100%',
                        backgroundColor: '#f59e0b',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      434
                    </Typography>
                  </Box>

                  {/* Poor (2★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Poor (2★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '8%',
                        height: '100%',
                        backgroundColor: '#f97316',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      123
                    </Typography>
                  </Box>

                  {/* Very Poor (1★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Very Poor (1★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '4%',
                        height: '100%',
                        backgroundColor: '#ef4444',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      57
                    </Typography>
                  </Box>

                  {/* Summary Cards */}
                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>
                    <Box sx={{
                      p: 2,
                      backgroundColor: '#f0fdf4',
                      borderRadius: 'var(--radius-md)',
                      textAlign: 'center'
                    }}>
                      <Typography variant="h5" fontWeight="bold" color="#16a34a">
                        77%
                      </Typography>
                      <Typography variant="caption" color="#16a34a">
                        Positive
                      </Typography>
                    </Box>
                    <Box sx={{
                      p: 2,
                      backgroundColor: '#fffbeb',
                      borderRadius: 'var(--radius-md)',
                      textAlign: 'center'
                    }}>
                      <Typography variant="h5" fontWeight="bold" color="#d97706">
                        16%
                      </Typography>
                      <Typography variant="caption" color="#d97706">
                        Neutral
                      </Typography>
                    </Box>
                    <Box sx={{
                      p: 2,
                      backgroundColor: '#fef2f2',
                      borderRadius: 'var(--radius-md)',
                      textAlign: 'center'
                    }}>
                      <Typography variant="h5" fontWeight="bold" color="#dc2626">
                        7%
                      </Typography>
                      <Typography variant="caption" color="#dc2626">
                        Negative
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Card>

              {/* Satisfaction Trend */}
              <Card title="📈 Satisfaction Trend" padding="lg">
                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8fafc', borderRadius: 'var(--radius-md)', position: 'relative' }}>
                  {/* Satisfaction Trend Line Chart */}
                  <svg width="100%" height="250" viewBox="0 0 400 200" style={{ overflow: 'visible' }}>
                    {/* Grid lines */}
                    <defs>
                      <pattern id="feedbackGrid" width="66.67" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 66.67 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" strokeWidth="0.5"/>
                      </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#feedbackGrid)" />

                    {/* Y-axis labels */}
                    <text x="10" y="20" fontSize="10" fill="#64748b">5</text>
                    <text x="10" y="60" fontSize="10" fill="#64748b">4.75</text>
                    <text x="10" y="100" fontSize="10" fill="#64748b">4.5</text>
                    <text x="10" y="140" fontSize="10" fill="#64748b">4.25</text>
                    <text x="10" y="180" fontSize="10" fill="#64748b">4</text>

                    {/* X-axis labels */}
                    <text x="50" y="215" fontSize="10" fill="#64748b">Jan</text>
                    <text x="110" y="215" fontSize="10" fill="#64748b">Feb</text>
                    <text x="170" y="215" fontSize="10" fill="#64748b">Mar</text>
                    <text x="230" y="215" fontSize="10" fill="#64748b">Apr</text>
                    <text x="290" y="215" fontSize="10" fill="#64748b">May</text>
                    <text x="350" y="215" fontSize="10" fill="#64748b">Jun</text>

                    {/* Line showing satisfaction trend from 4.2 to 4.7 */}
                    <path d="M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88" fill="none" stroke="#3b82f6" strokeWidth="3"/>

                    {/* Data points */}
                    <circle cx="50" cy="168" r="4" fill="#3b82f6"/>
                    <circle cx="110" cy="152" r="4" fill="#3b82f6"/>
                    <circle cx="170" cy="136" r="4" fill="#3b82f6"/>
                    <circle cx="230" cy="120" r="4" fill="#3b82f6"/>
                    <circle cx="290" cy="104" r="4" fill="#3b82f6"/>
                    <circle cx="350" cy="88" r="4" fill="#3b82f6"/>

                    {/* Value labels on data points */}
                    <text x="50" y="160" fontSize="9" fill="#3b82f6" textAnchor="middle">4.2</text>
                    <text x="110" y="144" fontSize="9" fill="#3b82f6" textAnchor="middle">4.3</text>
                    <text x="170" y="128" fontSize="9" fill="#3b82f6" textAnchor="middle">4.4</text>
                    <text x="230" y="112" fontSize="9" fill="#3b82f6" textAnchor="middle">4.5</text>
                    <text x="290" y="96" fontSize="9" fill="#3b82f6" textAnchor="middle">4.6</text>
                    <text x="350" y="80" fontSize="9" fill="#3b82f6" textAnchor="middle">4.7</text>
                  </svg>
                </Box>
              </Card>
            </Box>

            {/* Feedback Summary */}
            <Card title="📊 Feedback Summary" padding="lg">
              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 'var(--spacing-6)' }}>
                {/* Total Feedback */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f8fafc',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1 }}>
                    2,238
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Feedback
                  </Typography>
                </Box>

                {/* Positive Sentiment */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f0fdf4',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h3" fontWeight="bold" color="#16a34a" sx={{ mb: 1 }}>
                    85.8%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Positive Sentiment
                  </Typography>
                </Box>

                {/* Average Rating */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#eff6ff',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h3" fontWeight="bold" color="#2563eb" sx={{ mb: 1 }}>
                    4.6/5
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average Rating
                  </Typography>
                </Box>

                {/* Growth vs Last Month */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#fdf4ff',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h3" fontWeight="bold" color="#9333ea" sx={{ mb: 1 }}>
                    +12%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    vs Last Month
                  </Typography>
                </Box>
              </Box>
            </Card>

            {/* Recent Feedback */}
            <Card title="Recent Feedback" padding="lg">
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                {[
                  { user: 'JD', action: '5-star rating received', name: 'John Doe', time: '5 min ago', type: 'completion' },
                  { user: 'SM', action: 'Improvement suggestion submitted', name: 'Sarah Miller', time: '20 min ago', type: 'create' },
                  { user: 'RW', action: 'Bug report submitted', name: 'Robert Wilson', time: '45 min ago', type: 'alert' },
                  { user: 'LB', action: 'Feature request submitted', name: 'Lisa Brown', time: '1 hour ago', type: 'update' },
                ].map((activity, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-3)' }}>
                    <Box sx={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'var(--color-primary-100)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: 'var(--font-size-sm)',
                      fontWeight: 'var(--font-weight-semibold)',
                      color: 'var(--color-primary-700)'
                    }}>
                      {activity.user}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {activity.action}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {activity.name} • {activity.time}
                      </Typography>
                    </Box>
                    <Box sx={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'
                    }} />
                  </Box>
                ))}
              </Box>
            </Card>
          </>
        );

      case 1: // Analytics
        return (
          <>
            {/* Guide Performance Overview */}
            <Card title="Guide Performance Overview" subtitle="Click on any guide to see detailed funnel analysis" padding="lg">
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                {/* Product Onboarding */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Product Onboarding
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#e8f5e9',
                        color: '#2e7d32',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        excellent
                      </Box>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: 'var(--color-gray-100)',
                        color: 'var(--color-gray-700)',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px'
                      }}>
                        Onboarding
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />
                        1,400 views
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />
                        1,247 completed
                      </Box>
                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium' }}>
                        11% drop-off
                      </Box>
                      <Box>
                        Updated 2 days ago
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      89%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '89%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Feature Discovery */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Feature Discovery
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#e3f2fd',
                        color: '#1976d2',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        good
                      </Box>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: 'var(--color-gray-100)',
                        color: 'var(--color-gray-700)',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px'
                      }}>
                        Feature
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />
                        1,174 views
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />
                        892 completed
                      </Box>
                      <Box sx={{ color: 'var(--color-warning-600)', fontWeight: 'medium' }}>
                        24% drop-off
                      </Box>
                      <Box>
                        Updated 1 day ago
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      76%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '76%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Advanced Settings */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Advanced Settings
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#fff3e0',
                        color: '#f57c00',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        needs attention
                      </Box>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: 'var(--color-gray-100)',
                        color: 'var(--color-gray-700)',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px'
                      }}>
                        Configuration
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />
                        962 views
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />
                        634 completed
                      </Box>
                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium' }}>
                        32% drop-off
                      </Box>
                      <Box>
                        Updated 5 days ago
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      65%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '65%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Card>
          </>
        );

      case 2: // AI Performance
        return (
          <>
            
             {/* Bottom Metrics Cards */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 'var(--spacing-6)', mb: 4 }}>
              {/* Total Interactions Card */}
              <Box sx={{
                p: 4,
                backgroundColor: 'white',
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--color-gray-200)',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                position: 'relative'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="caption" color="#3b82f6" sx={{ fontWeight: 'medium' }}>
                    Total Interactions
                  </Typography>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: '#e3f2fd',
                    borderRadius: 'var(--radius-md)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Box sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: '#3b82f6',
                      borderRadius: 'var(--radius-sm)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '14px',
                      fontWeight: 'bold'
                    }}>
                      💬
                    </Box>
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1, fontSize: '2rem' }}>
                  2,847
                </Typography>
                <Typography variant="caption" color="#10b981" sx={{ fontWeight: 'medium' }}>
                  +12% from last month
                </Typography>
              </Box>

              {/* Success Rate Card */}
              <Box sx={{
                p: 4,
                backgroundColor: 'white',
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--color-gray-200)',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                position: 'relative'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="caption" color="#10b981" sx={{ fontWeight: 'medium' }}>
                    Success Rate
                  </Typography>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: '#e8f5e9',
                    borderRadius: 'var(--radius-md)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Box sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: '#10b981',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '14px',
                      fontWeight: 'bold'
                    }}>
                      ✓
                    </Box>
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1, fontSize: '2rem' }}>
                  91%
                </Typography>
                <Typography variant="caption" color="#10b981" sx={{ fontWeight: 'medium' }}>
                  +3% improvement
                </Typography>
              </Box>

              {/* Avg Response Time Card */}
              <Box sx={{
                p: 4,
                backgroundColor: 'white',
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--color-gray-200)',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                position: 'relative'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="caption" color="#8b5cf6" sx={{ fontWeight: 'medium' }}>
                    Avg Response Time
                  </Typography>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: '#f3e8ff',
                    borderRadius: 'var(--radius-md)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Box sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: '#8b5cf6',
                      borderRadius: 'var(--radius-sm)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '14px',
                      fontWeight: 'bold'
                    }}>
                      ⚡
                    </Box>
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1, fontSize: '2rem' }}>
                  1.9s
                </Typography>
                <Typography variant="caption" color="#10b981" sx={{ fontWeight: 'medium' }}>
                  -0.3s faster
                </Typography>
              </Box>
            </Box>

            {/* AI Task Performance Section */}
            <Box sx={{ mb: 4 }}>
              <Card title="AI Task Performance" padding="lg">
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                {/* Password Reset */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Password Reset
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#e8f5e9',
                        color: '#2e7d32',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        96%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        342 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 1.2s
                      </Typography>
                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        +2% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      96%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '96%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Account Setup */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Account Setup
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#e3f2fd',
                        color: '#1976d2',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        89%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        198 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 1.4s
                      </Typography>
                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        -5% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      89%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '89%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Feature Explanation */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Feature Explanation
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#e3f2fd',
                        color: '#1976d2',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        90%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        267 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 2.1s
                      </Typography>
                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        -1% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      90%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '90%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Troubleshooting */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Troubleshooting
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#fff3e0',
                        color: '#f57c00',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        88%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        156 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 3.1s
                      </Typography>
                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        -3% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      88%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '88%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Integration Help */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Integration Help
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#fff3e0',
                        color: '#f57c00',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        87%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        123 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 2.5s
                      </Typography>
                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        +1% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      87%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '87%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Card>
            </Box>

            {/* AI Insights & Recommendations */}
            <Card title="AI Insights & Recommendations" padding="lg">
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>
                {/* Optimize Workflow */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#fffbeb',
                  border: '1px solid #fbbf24',
                  borderRadius: 'var(--radius-md)',
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 2
                }}>
                  <Box sx={{
                    width: 6,
                    height: 6,
                    backgroundColor: '#f59e0b',
                    borderRadius: '50%',
                    mt: 1
                  }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="medium" sx={{ mb: 0.5 }}>
                      Optimize Workflow
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Consider optimizing your workflow to reduce response time by 15%
                    </Typography>
                  </Box>
                </Box>

                {/* Excluded Performance */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f0f9ff',
                  border: '1px solid #3b82f6',
                  borderRadius: 'var(--radius-md)',
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 2
                }}>
                  <Box sx={{
                    width: 6,
                    height: 6,
                    backgroundColor: '#3b82f6',
                    borderRadius: '50%',
                    mt: 1
                  }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="medium" sx={{ mb: 0.5 }}>
                      Excluded Performance
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Excluded tasks are performing well with 94% accuracy rate
                    </Typography>
                  </Box>
                </Box>

                {/* Personalized Suggestions */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f0f9ff',
                  border: '1px solid #3b82f6',
                  borderRadius: 'var(--radius-md)',
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 2
                }}>
                  <Box sx={{
                    width: 6,
                    height: 6,
                    backgroundColor: '#3b82f6',
                    borderRadius: '50%',
                    mt: 1
                  }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="medium" sx={{ mb: 0.5 }}>
                      Personalized Suggestions
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      AI suggests implementing advanced filtering for better user experience
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Card>
          </>
        );



      default:
        return null;
    }
  };

  return (
    <div className='qadpt-web'>
      <div className='qadpt-webcontent'>
        <DashboardWrapper>
          <Container maxWidth="xl" sx={{ py: 3 }}>
            {/* Navigation Tabs */}
            <StyledTabs value={selectedTab} onChange={handleTabChange}>
              <Tab label="Overview" />
              <Tab label="Analytics" />
              <Tab label="AI Performance" />
            </StyledTabs>

            {/* Render Tab Content */}
            {renderTabContent()}
          </Container>
        </DashboardWrapper>

        {/* Interactive Tooltip */}
        {tooltip.visible && (
          <Box
            sx={{
              position: 'fixed',
              left: tooltip.x,
              top: tooltip.y,
              transform: 'translate(-50%, -100%)',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              padding: '6px 10px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              zIndex: 10000,
              pointerEvents: 'none',
              fontSize: '11px',
              minWidth: '70px',
              textAlign: 'center',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
            }}
          >
            <Typography variant="body2" sx={{ fontSize: '10px', color: '#6b7280', mb: 0.2, lineHeight: 1.2 }}>
              {tooltip.title}
            </Typography>
            <Typography variant="body2" sx={{ fontSize: '11px', color: '#111827', fontWeight: '600', lineHeight: 1.2 }}>
              {tooltip.content}
            </Typography>
          </Box>
        )}
      </div>
    </div>
  );
};

export default ModernDashboard;
