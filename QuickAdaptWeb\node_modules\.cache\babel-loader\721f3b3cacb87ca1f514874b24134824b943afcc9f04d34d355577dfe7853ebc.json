{"ast": null, "code": "import _objectSpread from\"E:/Code/Qadpt/quickadapt/QuickAdaptWeb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Box,Typography,Container,Tabs,Tab}from'@mui/material';import{styled}from'@mui/material/styles';import{TrendingUp,TrendingDown,People,CheckCircle,Star,Schedule,FilterList,CalendarToday}from'@mui/icons-material';import Card from'../common/Card';import ModernButton from'../common/ModernButton';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const DashboardWrapper=styled('div')({backgroundColor:'#f6f9ff',minHeight:'100vh'});const HeaderSection=styled(Box)({display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'var(--spacing-4)'});const StyledTabs=styled(Tabs)({marginBottom:'var(--spacing-4)','& .MuiTabs-indicator':{backgroundColor:'var(--color-primary-600)'},'& .MuiTab-root':{fontSize:'var(--font-size-sm)',fontWeight:'var(--font-weight-medium)',textTransform:'none',color:'var(--color-gray-600)','&.Mui-selected':{color:'var(--color-primary-600)',fontWeight:'var(--font-weight-semibold)'}}});const FilterSection=styled(Box)({display:'flex',gap:'var(--spacing-3)',alignItems:'center'});const MetricsGrid=styled(Box)({display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(280px, 1fr))',gap:'var(--spacing-4)',marginBottom:'var(--spacing-6)'});const MetricCardContainer=styled(Card)({padding:'var(--spacing-5)',display:'flex',alignItems:'center',gap:'var(--spacing-4)'});const MetricIcon=styled(Box)(_ref=>{let{color}=_ref;return{width:'48px',height:'48px',borderRadius:'var(--radius-lg)',backgroundColor:\"\".concat(color,\"15\"),display:'flex',alignItems:'center',justifyContent:'center','& svg':{color:color,fontSize:'24px'}};});const MetricContent=styled(Box)({flex:1});const MetricTitle=styled(Typography)({fontSize:'var(--font-size-sm)',color:'var(--color-gray-600)',marginBottom:'var(--spacing-1)'});const MetricValue=styled(Typography)({fontSize:'var(--font-size-2xl)',fontWeight:'var(--font-weight-bold)',color:'var(--color-gray-900)',marginBottom:'var(--spacing-1)'});const MetricChange=styled(Box)({display:'flex',alignItems:'center',gap:'var(--spacing-1)'});const ChangeIndicator=styled(Box)(_ref2=>{let{trend}=_ref2;return{display:'flex',alignItems:'center',gap:'var(--spacing-1)',fontSize:'var(--font-size-xs)',fontWeight:'var(--font-weight-medium)',color:trend==='up'?'var(--color-success-600)':'var(--color-error-600)','& svg':{fontSize:'16px'}};});const MetricCard=_ref3=>{let{title,value,change,changeValue,trend,icon,color}=_ref3;return/*#__PURE__*/_jsxs(MetricCardContainer,{shadow:\"sm\",hover:true,children:[/*#__PURE__*/_jsx(MetricIcon,{color:color,children:icon}),/*#__PURE__*/_jsxs(MetricContent,{children:[/*#__PURE__*/_jsx(MetricTitle,{children:title}),/*#__PURE__*/_jsx(MetricValue,{children:value}),/*#__PURE__*/_jsxs(MetricChange,{children:[/*#__PURE__*/_jsxs(ChangeIndicator,{trend:trend,children:[trend==='up'?/*#__PURE__*/_jsx(TrendingUp,{}):/*#__PURE__*/_jsx(TrendingDown,{}),change]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:changeValue})]})]})]});};const ModernDashboard=()=>{const[selectedTab,setSelectedTab]=useState(0);const[tooltip,setTooltip]=useState({visible:false,x:0,y:0,content:'',title:''});const showTooltip=(event,title,content)=>{const rect=event.currentTarget.getBoundingClientRect();const scrollX=window.pageXOffset||document.documentElement.scrollLeft;const scrollY=window.pageYOffset||document.documentElement.scrollTop;setTooltip({visible:true,x:rect.left+scrollX+rect.width/2,y:rect.top+scrollY-10,content,title});};const hideTooltip=()=>{setTooltip(prev=>_objectSpread(_objectSpread({},prev),{},{visible:false}));};const overviewMetrics=[{title:'Completion Rate',value:'87.3%',change:'****%',changeValue:'+2.8pp',trend:'up',icon:/*#__PURE__*/_jsx(CheckCircle,{}),color:'var(--color-success-600)'},{title:'User Satisfaction',value:'4.6',change:'+0.2',changeValue:'out of 5.0',trend:'up',icon:/*#__PURE__*/_jsx(Star,{}),color:'var(--color-warning-600)'},{title:'Hours Saved',value:'2,847',change:'+18.7%',changeValue:'+447h',trend:'up',icon:/*#__PURE__*/_jsx(Schedule,{}),color:'var(--color-primary-600)'}];const analyticsMetrics=[{title:'Active Users',value:'12,847',change:'+12.5%',changeValue:'+1,432',trend:'up',icon:/*#__PURE__*/_jsx(People,{}),color:'var(--color-primary-600)'},{title:'Completion Rate',value:'87.3%',change:'****%',changeValue:'+2.8pp',trend:'up',icon:/*#__PURE__*/_jsx(CheckCircle,{}),color:'var(--color-success-600)'},{title:'User Satisfaction',value:'4.6',change:'+0.2',changeValue:'out of 5.0',trend:'up',icon:/*#__PURE__*/_jsx(Star,{}),color:'var(--color-warning-600)'},{title:'Hours Saved',value:'2,847',change:'+18.7%',changeValue:'+447h',trend:'up',icon:/*#__PURE__*/_jsx(Schedule,{}),color:'var(--color-primary-600)'}];const aiPerformanceMetrics=[{title:'AI Response Time',value:'1.2s',change:'-15%',changeValue:'faster',trend:'up',icon:/*#__PURE__*/_jsx(Schedule,{}),color:'var(--color-success-600)'},{title:'AI Accuracy',value:'94.8%',change:'+2.1%',changeValue:'improved',trend:'up',icon:/*#__PURE__*/_jsx(CheckCircle,{}),color:'var(--color-primary-600)'},{title:'Model Confidence',value:'89.3%',change:'+1.8%',changeValue:'higher',trend:'up',icon:/*#__PURE__*/_jsx(Star,{}),color:'var(--color-warning-600)'},{title:'Processing Load',value:'67%',change:'+5%',changeValue:'capacity',trend:'up',icon:/*#__PURE__*/_jsx(People,{}),color:'var(--color-error-500)'}];const handleTabChange=(event,newValue)=>{setSelectedTab(newValue);};const renderTabContent=()=>{switch(selectedTab){case 0:// Overview\nreturn/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(HeaderSection,{children:[/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",color:\"text.primary\",children:\"Dashboard Overview\"})}),/*#__PURE__*/_jsxs(FilterSection,{children:[/*#__PURE__*/_jsx(ModernButton,{variant:\"outline\",startIcon:/*#__PURE__*/_jsx(FilterList,{}),size:\"sm\",children:\"Filter\"}),/*#__PURE__*/_jsx(ModernButton,{variant:\"outline\",startIcon:/*#__PURE__*/_jsx(CalendarToday,{}),size:\"sm\",children:\"Last 30 days\"})]})]}),/*#__PURE__*/_jsx(MetricsGrid,{children:analyticsMetrics.map((metric,index)=>/*#__PURE__*/_jsx(MetricCard,_objectSpread({},metric),index))}),/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'var(--spacing-6)',mb:4},children:[/*#__PURE__*/_jsx(Card,{title:\"\\uD83D\\uDCC8 Growth Trends\",subtitle:\"User engagement over the last 6 months\",padding:\"lg\",children:/*#__PURE__*/_jsx(Box,{sx:{height:'300px',position:'relative',backgroundColor:'white',borderRadius:'var(--radius-md)'},children:/*#__PURE__*/_jsxs(\"svg\",{width:\"100%\",height:\"280\",viewBox:\"0 0 450 250\",style:{overflow:'visible'},children:[/*#__PURE__*/_jsx(\"defs\",{children:/*#__PURE__*/_jsx(\"pattern\",{id:\"interactiveGrid\",width:\"75\",height:\"50\",patternUnits:\"userSpaceOnUse\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M 75 0 L 0 0 0 50\",fill:\"none\",stroke:\"#f1f5f9\",strokeWidth:\"1\"})})}),/*#__PURE__*/_jsx(\"rect\",{width:\"100%\",height:\"200\",fill:\"url(#interactiveGrid)\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"50\",y1:\"20\",x2:\"50\",y2:\"200\",stroke:\"#e2e8f0\",strokeWidth:\"1\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"50\",y1:\"200\",x2:\"400\",y2:\"200\",stroke:\"#e2e8f0\",strokeWidth:\"1\"}),/*#__PURE__*/_jsx(\"text\",{x:\"40\",y:\"25\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"end\",children:\"14000\"}),/*#__PURE__*/_jsx(\"text\",{x:\"40\",y:\"75\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"end\",children:\"10500\"}),/*#__PURE__*/_jsx(\"text\",{x:\"40\",y:\"125\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"end\",children:\"7000\"}),/*#__PURE__*/_jsx(\"text\",{x:\"40\",y:\"175\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"end\",children:\"3500\"}),/*#__PURE__*/_jsx(\"text\",{x:\"40\",y:\"205\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"end\",children:\"0\"}),/*#__PURE__*/_jsx(\"text\",{x:\"80\",y:\"220\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"middle\",children:\"Jan\"}),/*#__PURE__*/_jsx(\"text\",{x:\"140\",y:\"220\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"middle\",children:\"Feb\"}),/*#__PURE__*/_jsx(\"text\",{x:\"200\",y:\"220\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"middle\",children:\"Mar\"}),/*#__PURE__*/_jsx(\"text\",{x:\"260\",y:\"220\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"middle\",children:\"Apr\"}),/*#__PURE__*/_jsx(\"text\",{x:\"320\",y:\"220\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"middle\",children:\"May\"}),/*#__PURE__*/_jsx(\"text\",{x:\"380\",y:\"220\",fontSize:\"11\",fill:\"#64748b\",textAnchor:\"middle\",children:\"Jun\"}),/*#__PURE__*/_jsx(\"defs\",{children:/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"areaGradient\",x1:\"0%\",y1:\"0%\",x2:\"0%\",y2:\"100%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"#3b82f6\",stopOpacity:\"0.3\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"#3b82f6\",stopOpacity:\"0.05\"})]})}),/*#__PURE__*/_jsx(\"path\",{d:\"M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70 L 380 200 L 80 200 Z\",fill:\"url(#areaGradient)\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70\",fill:\"none\",stroke:\"#3b82f6\",strokeWidth:\"3\",strokeLinecap:\"round\"}),/*#__PURE__*/_jsxs(\"g\",{className:\"data-points\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"80\",cy:\"160\",r:\"8\",fill:\"white\",stroke:\"#3b82f6\",strokeWidth:\"3\",style:{cursor:'pointer',transition:'r 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Jan point');showTooltip(e,'Jan','8,500 users');e.currentTarget.setAttribute('r','10');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('r','8');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"140\",cy:\"150\",r:\"8\",fill:\"white\",stroke:\"#3b82f6\",strokeWidth:\"3\",style:{cursor:'pointer',transition:'r 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Feb point');showTooltip(e,'Feb','9,200 users');e.currentTarget.setAttribute('r','10');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('r','8');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"200\",cy:\"130\",r:\"8\",fill:\"white\",stroke:\"#3b82f6\",strokeWidth:\"3\",style:{cursor:'pointer',transition:'r 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Mar point');showTooltip(e,'Mar','10,100 users');e.currentTarget.setAttribute('r','10');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('r','8');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"260\",cy:\"110\",r:\"8\",fill:\"white\",stroke:\"#3b82f6\",strokeWidth:\"3\",style:{cursor:'pointer',transition:'r 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Apr point');showTooltip(e,'Apr','11,100 users');e.currentTarget.setAttribute('r','10');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('r','8');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"320\",cy:\"90\",r:\"8\",fill:\"white\",stroke:\"#3b82f6\",strokeWidth:\"3\",style:{cursor:'pointer',transition:'r 0.2s ease'},onMouseEnter:e=>{console.log('Hovering May point');showTooltip(e,'May','12,300 users');e.currentTarget.setAttribute('r','10');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('r','8');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"380\",cy:\"70\",r:\"8\",fill:\"white\",stroke:\"#3b82f6\",strokeWidth:\"3\",style:{cursor:'pointer',transition:'r 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Jun point');showTooltip(e,'Jun','13,200 users');e.currentTarget.setAttribute('r','10');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('r','8');}})]})]})})}),/*#__PURE__*/_jsx(Card,{title:\"\\uD83D\\uDE0A User Satisfaction\",subtitle:\"Rating distribution this month\",padding:\"lg\",children:/*#__PURE__*/_jsx(Box,{sx:{height:'300px',display:'flex',justifyContent:'center',alignItems:'center',backgroundColor:'white',borderRadius:'var(--radius-md)',position:'relative'},children:/*#__PURE__*/_jsxs(\"svg\",{width:\"220\",height:\"220\",viewBox:\"0 0 220 220\",children:[/*#__PURE__*/_jsxs(\"g\",{children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"110\",cy:\"110\",r:\"75\",fill:\"none\",stroke:\"#10b981\",strokeWidth:\"30\",strokeDasharray:\"305 470\",strokeDashoffset:\"0\",transform:\"rotate(-90 110 110)\",style:{cursor:'pointer',transition:'stroke-width 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Excellent segment');showTooltip(e,'Excellent (5★)','65%');e.currentTarget.setAttribute('stroke-width','35');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('stroke-width','30');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"110\",cy:\"110\",r:\"75\",fill:\"none\",stroke:\"#84cc16\",strokeWidth:\"30\",strokeDasharray:\"94 470\",strokeDashoffset:\"-305\",transform:\"rotate(-90 110 110)\",style:{cursor:'pointer',transition:'stroke-width 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Good segment');showTooltip(e,'Good (4★)','20%');e.currentTarget.setAttribute('stroke-width','35');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('stroke-width','30');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"110\",cy:\"110\",r:\"75\",fill:\"none\",stroke:\"#f59e0b\",strokeWidth:\"30\",strokeDasharray:\"47 470\",strokeDashoffset:\"-399\",transform:\"rotate(-90 110 110)\",style:{cursor:'pointer',transition:'stroke-width 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Average segment');showTooltip(e,'Average (3★)','10%');e.currentTarget.setAttribute('stroke-width','35');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('stroke-width','30');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"110\",cy:\"110\",r:\"75\",fill:\"none\",stroke:\"#f97316\",strokeWidth:\"30\",strokeDasharray:\"14 470\",strokeDashoffset:\"-446\",transform:\"rotate(-90 110 110)\",style:{cursor:'pointer',transition:'stroke-width 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Poor segment');showTooltip(e,'Poor (2★)','3%');e.currentTarget.setAttribute('stroke-width','35');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('stroke-width','30');}}),/*#__PURE__*/_jsx(\"circle\",{cx:\"110\",cy:\"110\",r:\"75\",fill:\"none\",stroke:\"#ef4444\",strokeWidth:\"30\",strokeDasharray:\"9 470\",strokeDashoffset:\"-460\",transform:\"rotate(-90 110 110)\",style:{cursor:'pointer',transition:'stroke-width 0.2s ease'},onMouseEnter:e=>{console.log('Hovering Very Poor segment');showTooltip(e,'Very Poor (1★)','2%');e.currentTarget.setAttribute('stroke-width','35');},onMouseLeave:e=>{hideTooltip();e.currentTarget.setAttribute('stroke-width','30');}})]}),/*#__PURE__*/_jsx(\"circle\",{cx:\"110\",cy:\"110\",r:\"45\",fill:\"white\"})]})})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:'1fr 2fr',gap:'var(--spacing-6)',mb:4},children:[/*#__PURE__*/_jsx(Card,{title:\"Quick Actions\",subtitle:\"Take action based on your dashboard insights\",padding:\"lg\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:'var(--spacing-3)'},children:[/*#__PURE__*/_jsx(ModernButton,{variant:\"primary\",fullWidth:true,children:\"Create New Guide\"}),/*#__PURE__*/_jsx(ModernButton,{variant:\"outline\",fullWidth:true,children:\"View Full Analytics\"}),/*#__PURE__*/_jsx(ModernButton,{variant:\"outline\",fullWidth:true,children:\"AI Assistant Settings\"})]})}),/*#__PURE__*/_jsx(Card,{title:\"Recent Activity\",padding:\"lg\",children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexDirection:'column',gap:'var(--spacing-4)'},children:[{user:'SC',action:'New guide created',name:'Sarah Chen',time:'2 min ago',type:'create'},{user:'S',action:'Guide completed 47 times',name:'System',time:'15 min ago',type:'completion'},{user:'MJ',action:'AI response updated',name:'Mike Johnson',time:'1 hour ago',type:'update'},{user:'S',action:'Low performance alert',name:'System',time:'2 hours ago',type:'alert'}].map((activity,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:'var(--spacing-3)'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:'40px',height:'40px',borderRadius:'50%',backgroundColor:'var(--color-primary-100)',display:'flex',alignItems:'center',justifyContent:'center',fontSize:'var(--font-size-sm)',fontWeight:'var(--font-weight-semibold)',color:'var(--color-primary-700)'},children:activity.user}),/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:activity.action}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[activity.name,\" \\u2022 \",activity.time]})]}),/*#__PURE__*/_jsx(Box,{sx:{width:'8px',height:'8px',borderRadius:'50%',backgroundColor:activity.type==='alert'?'var(--color-error-500)':'var(--color-success-500)'}})]},index))})})]})]});case 1:// Analytics\nreturn/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(Card,{title:\"Guide Performance Overview\",subtitle:\"Click on any guide to see detailed funnel analysis\",padding:\"lg\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:'var(--spacing-4)'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:3,border:'1px solid var(--color-gray-200)',borderRadius:'var(--radius-md)','&:hover':{backgroundColor:'var(--color-gray-50)',cursor:'pointer'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"semibold\",children:\"Product Onboarding\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'#e8f5e9',color:'#2e7d32',borderRadius:'var(--radius-sm)',fontSize:'12px',fontWeight:'medium'},children:\"excellent\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'var(--color-gray-100)',color:'var(--color-gray-700)',borderRadius:'var(--radius-sm)',fontSize:'12px'},children:\"Onboarding\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:3,color:'var(--color-gray-600)',fontSize:'14px'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(Box,{sx:{width:4,height:4,borderRadius:'50%',backgroundColor:'var(--color-gray-400)'}}),\"1,400 views\"]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(CheckCircle,{sx:{fontSize:16,color:'var(--color-success-500)'}}),\"1,247 completed\"]}),/*#__PURE__*/_jsx(Box,{sx:{color:'var(--color-success-600)',fontWeight:'medium'},children:\"11% drop-off\"}),/*#__PURE__*/_jsx(Box,{children:\"Updated 2 days ago\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",children:\"89%\"}),/*#__PURE__*/_jsx(Box,{sx:{width:80,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden'},children:/*#__PURE__*/_jsx(Box,{sx:{width:'89%',height:'100%',backgroundColor:'var(--color-gray-800)',borderRadius:'var(--radius-full)'}})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:3,border:'1px solid var(--color-gray-200)',borderRadius:'var(--radius-md)','&:hover':{backgroundColor:'var(--color-gray-50)',cursor:'pointer'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"semibold\",children:\"Feature Discovery\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'#e3f2fd',color:'#1976d2',borderRadius:'var(--radius-sm)',fontSize:'12px',fontWeight:'medium'},children:\"good\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'var(--color-gray-100)',color:'var(--color-gray-700)',borderRadius:'var(--radius-sm)',fontSize:'12px'},children:\"Feature\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:3,color:'var(--color-gray-600)',fontSize:'14px'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(Box,{sx:{width:4,height:4,borderRadius:'50%',backgroundColor:'var(--color-gray-400)'}}),\"1,174 views\"]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(CheckCircle,{sx:{fontSize:16,color:'var(--color-success-500)'}}),\"892 completed\"]}),/*#__PURE__*/_jsx(Box,{sx:{color:'var(--color-warning-600)',fontWeight:'medium'},children:\"24% drop-off\"}),/*#__PURE__*/_jsx(Box,{children:\"Updated 1 day ago\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",children:\"76%\"}),/*#__PURE__*/_jsx(Box,{sx:{width:80,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden'},children:/*#__PURE__*/_jsx(Box,{sx:{width:'76%',height:'100%',backgroundColor:'var(--color-gray-800)',borderRadius:'var(--radius-full)'}})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:3,border:'1px solid var(--color-gray-200)',borderRadius:'var(--radius-md)','&:hover':{backgroundColor:'var(--color-gray-50)',cursor:'pointer'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"semibold\",children:\"Advanced Settings\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'#fff3e0',color:'#f57c00',borderRadius:'var(--radius-sm)',fontSize:'12px',fontWeight:'medium'},children:\"needs attention\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'var(--color-gray-100)',color:'var(--color-gray-700)',borderRadius:'var(--radius-sm)',fontSize:'12px'},children:\"Configuration\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:3,color:'var(--color-gray-600)',fontSize:'14px'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(Box,{sx:{width:4,height:4,borderRadius:'50%',backgroundColor:'var(--color-gray-400)'}}),\"962 views\"]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(CheckCircle,{sx:{fontSize:16,color:'var(--color-success-500)'}}),\"634 completed\"]}),/*#__PURE__*/_jsx(Box,{sx:{color:'var(--color-error-600)',fontWeight:'medium'},children:\"32% drop-off\"}),/*#__PURE__*/_jsx(Box,{children:\"Updated 5 days ago\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",children:\"65%\"}),/*#__PURE__*/_jsx(Box,{sx:{width:80,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden'},children:/*#__PURE__*/_jsx(Box,{sx:{width:'65%',height:'100%',backgroundColor:'var(--color-gray-800)',borderRadius:'var(--radius-full)'}})})]})]})]})})});case 2:// AI Performance\nreturn/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:'repeat(3, 1fr)',gap:'var(--spacing-6)',mb:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{p:4,backgroundColor:'white',borderRadius:'var(--radius-lg)',border:'1px solid var(--color-gray-200)',boxShadow:'0 1px 3px rgba(0, 0, 0, 0.1)',position:'relative'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'flex-start',mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#3b82f6\",sx:{fontWeight:'medium'},children:\"Total Interactions\"}),/*#__PURE__*/_jsx(Box,{sx:{width:40,height:40,backgroundColor:'#e3f2fd',borderRadius:'var(--radius-md)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Box,{sx:{width:24,height:24,backgroundColor:'#3b82f6',borderRadius:'var(--radius-sm)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'14px',fontWeight:'bold'},children:\"\\uD83D\\uDCAC\"})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:\"bold\",color:\"text.primary\",sx:{mb:1,fontSize:'2rem'},children:\"2,847\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#10b981\",sx:{fontWeight:'medium'},children:\"+12% from last month\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:4,backgroundColor:'white',borderRadius:'var(--radius-lg)',border:'1px solid var(--color-gray-200)',boxShadow:'0 1px 3px rgba(0, 0, 0, 0.1)',position:'relative'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'flex-start',mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#10b981\",sx:{fontWeight:'medium'},children:\"Success Rate\"}),/*#__PURE__*/_jsx(Box,{sx:{width:40,height:40,backgroundColor:'#e8f5e9',borderRadius:'var(--radius-md)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Box,{sx:{width:24,height:24,backgroundColor:'#10b981',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'14px',fontWeight:'bold'},children:\"\\u2713\"})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:\"bold\",color:\"text.primary\",sx:{mb:1,fontSize:'2rem'},children:\"91%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#10b981\",sx:{fontWeight:'medium'},children:\"+3% improvement\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:4,backgroundColor:'white',borderRadius:'var(--radius-lg)',border:'1px solid var(--color-gray-200)',boxShadow:'0 1px 3px rgba(0, 0, 0, 0.1)',position:'relative'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'flex-start',mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#8b5cf6\",sx:{fontWeight:'medium'},children:\"Avg Response Time\"}),/*#__PURE__*/_jsx(Box,{sx:{width:40,height:40,backgroundColor:'#f3e8ff',borderRadius:'var(--radius-md)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Box,{sx:{width:24,height:24,backgroundColor:'#8b5cf6',borderRadius:'var(--radius-sm)',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'14px',fontWeight:'bold'},children:\"\\u26A1\"})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:\"bold\",color:\"text.primary\",sx:{mb:1,fontSize:'2rem'},children:\"1.9s\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#10b981\",sx:{fontWeight:'medium'},children:\"-0.3s faster\"})]})]}),/*#__PURE__*/_jsx(Box,{sx:{mb:4},children:/*#__PURE__*/_jsx(Card,{title:\"AI Task Performance\",padding:\"lg\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:'var(--spacing-4)'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:3,border:'1px solid var(--color-gray-200)',borderRadius:'var(--radius-md)','&:hover':{backgroundColor:'var(--color-gray-50)',cursor:'pointer'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"semibold\",children:\"Password Reset\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'#e8f5e9',color:'#2e7d32',borderRadius:'var(--radius-sm)',fontSize:'12px',fontWeight:'medium'},children:\"96%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"342 interactions\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,color:'var(--color-gray-600)',fontSize:'14px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Avg time: 1.2s\"}),/*#__PURE__*/_jsx(Box,{sx:{color:'var(--color-success-600)',fontWeight:'medium',fontSize:'12px'},children:\"+2% trend\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",children:\"96%\"}),/*#__PURE__*/_jsx(Box,{sx:{width:80,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden'},children:/*#__PURE__*/_jsx(Box,{sx:{width:'96%',height:'100%',backgroundColor:'var(--color-gray-800)',borderRadius:'var(--radius-full)'}})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:3,border:'1px solid var(--color-gray-200)',borderRadius:'var(--radius-md)','&:hover':{backgroundColor:'var(--color-gray-50)',cursor:'pointer'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"semibold\",children:\"Account Setup\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'#e3f2fd',color:'#1976d2',borderRadius:'var(--radius-sm)',fontSize:'12px',fontWeight:'medium'},children:\"89%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"198 interactions\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,color:'var(--color-gray-600)',fontSize:'14px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Avg time: 1.4s\"}),/*#__PURE__*/_jsx(Box,{sx:{color:'var(--color-error-600)',fontWeight:'medium',fontSize:'12px'},children:\"-5% trend\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",children:\"89%\"}),/*#__PURE__*/_jsx(Box,{sx:{width:80,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden'},children:/*#__PURE__*/_jsx(Box,{sx:{width:'89%',height:'100%',backgroundColor:'var(--color-gray-800)',borderRadius:'var(--radius-full)'}})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:3,border:'1px solid var(--color-gray-200)',borderRadius:'var(--radius-md)','&:hover':{backgroundColor:'var(--color-gray-50)',cursor:'pointer'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"semibold\",children:\"Feature Explanation\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'#e3f2fd',color:'#1976d2',borderRadius:'var(--radius-sm)',fontSize:'12px',fontWeight:'medium'},children:\"90%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"267 interactions\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,color:'var(--color-gray-600)',fontSize:'14px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Avg time: 2.1s\"}),/*#__PURE__*/_jsx(Box,{sx:{color:'var(--color-error-600)',fontWeight:'medium',fontSize:'12px'},children:\"-1% trend\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",children:\"90%\"}),/*#__PURE__*/_jsx(Box,{sx:{width:80,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden'},children:/*#__PURE__*/_jsx(Box,{sx:{width:'90%',height:'100%',backgroundColor:'var(--color-gray-800)',borderRadius:'var(--radius-full)'}})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:3,border:'1px solid var(--color-gray-200)',borderRadius:'var(--radius-md)','&:hover':{backgroundColor:'var(--color-gray-50)',cursor:'pointer'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"semibold\",children:\"Troubleshooting\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'#fff3e0',color:'#f57c00',borderRadius:'var(--radius-sm)',fontSize:'12px',fontWeight:'medium'},children:\"88%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"156 interactions\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,color:'var(--color-gray-600)',fontSize:'14px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Avg time: 3.1s\"}),/*#__PURE__*/_jsx(Box,{sx:{color:'var(--color-error-600)',fontWeight:'medium',fontSize:'12px'},children:\"-3% trend\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",children:\"88%\"}),/*#__PURE__*/_jsx(Box,{sx:{width:80,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden'},children:/*#__PURE__*/_jsx(Box,{sx:{width:'88%',height:'100%',backgroundColor:'var(--color-gray-800)',borderRadius:'var(--radius-full)'}})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:3,border:'1px solid var(--color-gray-200)',borderRadius:'var(--radius-md)','&:hover':{backgroundColor:'var(--color-gray-50)',cursor:'pointer'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"semibold\",children:\"Integration Help\"}),/*#__PURE__*/_jsx(Box,{sx:{px:1.5,py:0.5,backgroundColor:'#fff3e0',color:'#f57c00',borderRadius:'var(--radius-sm)',fontSize:'12px',fontWeight:'medium'},children:\"87%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"123 interactions\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,color:'var(--color-gray-600)',fontSize:'14px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Avg time: 2.5s\"}),/*#__PURE__*/_jsx(Box,{sx:{color:'var(--color-success-600)',fontWeight:'medium',fontSize:'12px'},children:\"+1% trend\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",children:\"87%\"}),/*#__PURE__*/_jsx(Box,{sx:{width:80,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden'},children:/*#__PURE__*/_jsx(Box,{sx:{width:'87%',height:'100%',backgroundColor:'var(--color-gray-800)',borderRadius:'var(--radius-full)'}})})]})]})]})})}),/*#__PURE__*/_jsx(Card,{title:\"AI Insights & Recommendations\",padding:\"lg\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:'var(--spacing-3)'},children:[/*#__PURE__*/_jsxs(Box,{sx:{p:3,backgroundColor:'#fffbeb',border:'1px solid #fbbf24',borderRadius:'var(--radius-md)',display:'flex',alignItems:'flex-start',gap:2},children:[/*#__PURE__*/_jsx(Box,{sx:{width:6,height:6,backgroundColor:'#f59e0b',borderRadius:'50%',mt:1}}),/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",sx:{mb:0.5},children:\"Optimize Workflow\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Consider optimizing your workflow to reduce response time by 15%\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:3,backgroundColor:'#f0f9ff',border:'1px solid #3b82f6',borderRadius:'var(--radius-md)',display:'flex',alignItems:'flex-start',gap:2},children:[/*#__PURE__*/_jsx(Box,{sx:{width:6,height:6,backgroundColor:'#3b82f6',borderRadius:'50%',mt:1}}),/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",sx:{mb:0.5},children:\"Excluded Performance\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Excluded tasks are performing well with 94% accuracy rate\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:3,backgroundColor:'#f0f9ff',border:'1px solid #3b82f6',borderRadius:'var(--radius-md)',display:'flex',alignItems:'flex-start',gap:2},children:[/*#__PURE__*/_jsx(Box,{sx:{width:6,height:6,backgroundColor:'#3b82f6',borderRadius:'50%',mt:1}}),/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",sx:{mb:0.5},children:\"Personalized Suggestions\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"AI suggests implementing advanced filtering for better user experience\"})]})]})]})})]});case 3:// Feedback\nreturn/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'var(--spacing-6)',mb:4},children:[/*#__PURE__*/_jsx(Card,{title:\"\\u2B50 User Satisfaction Ratings\",padding:\"lg\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:'var(--spacing-4)'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,minWidth:120},children:[/*#__PURE__*/_jsx(Box,{sx:{width:8,height:8,backgroundColor:'#10b981',borderRadius:'50%'}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:\"Excellent (5\\u2605)\"})]}),/*#__PURE__*/_jsx(Box,{sx:{flex:1,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden',mr:2},children:/*#__PURE__*/_jsx(Box,{sx:{width:'65%',height:'100%',backgroundColor:'#10b981',borderRadius:'var(--radius-full)'}})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"bold\",sx:{minWidth:40,textAlign:'right'},children:\"1,247\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,minWidth:120},children:[/*#__PURE__*/_jsx(Box,{sx:{width:8,height:8,backgroundColor:'#84cc16',borderRadius:'50%'}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:\"Good (4\\u2605)\"})]}),/*#__PURE__*/_jsx(Box,{sx:{flex:1,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden',mr:2},children:/*#__PURE__*/_jsx(Box,{sx:{width:'45%',height:'100%',backgroundColor:'#84cc16',borderRadius:'var(--radius-full)'}})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"bold\",sx:{minWidth:40,textAlign:'right'},children:\"892\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,minWidth:120},children:[/*#__PURE__*/_jsx(Box,{sx:{width:8,height:8,backgroundColor:'#f59e0b',borderRadius:'50%'}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:\"Average (3\\u2605)\"})]}),/*#__PURE__*/_jsx(Box,{sx:{flex:1,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden',mr:2},children:/*#__PURE__*/_jsx(Box,{sx:{width:'22%',height:'100%',backgroundColor:'#f59e0b',borderRadius:'var(--radius-full)'}})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"bold\",sx:{minWidth:40,textAlign:'right'},children:\"434\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,minWidth:120},children:[/*#__PURE__*/_jsx(Box,{sx:{width:8,height:8,backgroundColor:'#f97316',borderRadius:'50%'}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:\"Poor (2\\u2605)\"})]}),/*#__PURE__*/_jsx(Box,{sx:{flex:1,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden',mr:2},children:/*#__PURE__*/_jsx(Box,{sx:{width:'8%',height:'100%',backgroundColor:'#f97316',borderRadius:'var(--radius-full)'}})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"bold\",sx:{minWidth:40,textAlign:'right'},children:\"123\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,minWidth:120},children:[/*#__PURE__*/_jsx(Box,{sx:{width:8,height:8,backgroundColor:'#ef4444',borderRadius:'50%'}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:\"Very Poor (1\\u2605)\"})]}),/*#__PURE__*/_jsx(Box,{sx:{flex:1,height:8,backgroundColor:'var(--color-gray-200)',borderRadius:'var(--radius-full)',overflow:'hidden',mr:2},children:/*#__PURE__*/_jsx(Box,{sx:{width:'4%',height:'100%',backgroundColor:'#ef4444',borderRadius:'var(--radius-full)'}})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"bold\",sx:{minWidth:40,textAlign:'right'},children:\"57\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:'repeat(3, 1fr)',gap:2,mt:3},children:[/*#__PURE__*/_jsxs(Box,{sx:{p:2,backgroundColor:'#f0fdf4',borderRadius:'var(--radius-md)',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",color:\"#16a34a\",children:\"77%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#16a34a\",children:\"Positive\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:2,backgroundColor:'#fffbeb',borderRadius:'var(--radius-md)',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",color:\"#d97706\",children:\"16%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#d97706\",children:\"Neutral\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:2,backgroundColor:'#fef2f2',borderRadius:'var(--radius-md)',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",color:\"#dc2626\",children:\"7%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"#dc2626\",children:\"Negative\"})]})]})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\uD83D\\uDCC8 Satisfaction Trend\",padding:\"lg\",children:/*#__PURE__*/_jsx(Box,{sx:{height:'300px',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',backgroundColor:'#f8fafc',borderRadius:'var(--radius-md)',position:'relative'},children:/*#__PURE__*/_jsxs(\"svg\",{width:\"100%\",height:\"250\",viewBox:\"0 0 400 200\",style:{overflow:'visible'},children:[/*#__PURE__*/_jsx(\"defs\",{children:/*#__PURE__*/_jsx(\"pattern\",{id:\"feedbackGrid\",width:\"66.67\",height:\"40\",patternUnits:\"userSpaceOnUse\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M 66.67 0 L 0 0 0 40\",fill:\"none\",stroke:\"#e2e8f0\",strokeWidth:\"0.5\"})})}),/*#__PURE__*/_jsx(\"rect\",{width:\"100%\",height:\"100%\",fill:\"url(#feedbackGrid)\"}),/*#__PURE__*/_jsx(\"text\",{x:\"10\",y:\"20\",fontSize:\"10\",fill:\"#64748b\",children:\"5\"}),/*#__PURE__*/_jsx(\"text\",{x:\"10\",y:\"60\",fontSize:\"10\",fill:\"#64748b\",children:\"4.75\"}),/*#__PURE__*/_jsx(\"text\",{x:\"10\",y:\"100\",fontSize:\"10\",fill:\"#64748b\",children:\"4.5\"}),/*#__PURE__*/_jsx(\"text\",{x:\"10\",y:\"140\",fontSize:\"10\",fill:\"#64748b\",children:\"4.25\"}),/*#__PURE__*/_jsx(\"text\",{x:\"10\",y:\"180\",fontSize:\"10\",fill:\"#64748b\",children:\"4\"}),/*#__PURE__*/_jsx(\"text\",{x:\"50\",y:\"215\",fontSize:\"10\",fill:\"#64748b\",children:\"Jan\"}),/*#__PURE__*/_jsx(\"text\",{x:\"110\",y:\"215\",fontSize:\"10\",fill:\"#64748b\",children:\"Feb\"}),/*#__PURE__*/_jsx(\"text\",{x:\"170\",y:\"215\",fontSize:\"10\",fill:\"#64748b\",children:\"Mar\"}),/*#__PURE__*/_jsx(\"text\",{x:\"230\",y:\"215\",fontSize:\"10\",fill:\"#64748b\",children:\"Apr\"}),/*#__PURE__*/_jsx(\"text\",{x:\"290\",y:\"215\",fontSize:\"10\",fill:\"#64748b\",children:\"May\"}),/*#__PURE__*/_jsx(\"text\",{x:\"350\",y:\"215\",fontSize:\"10\",fill:\"#64748b\",children:\"Jun\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88\",fill:\"none\",stroke:\"#3b82f6\",strokeWidth:\"3\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"50\",cy:\"168\",r:\"4\",fill:\"#3b82f6\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"110\",cy:\"152\",r:\"4\",fill:\"#3b82f6\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"170\",cy:\"136\",r:\"4\",fill:\"#3b82f6\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"230\",cy:\"120\",r:\"4\",fill:\"#3b82f6\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"290\",cy:\"104\",r:\"4\",fill:\"#3b82f6\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"350\",cy:\"88\",r:\"4\",fill:\"#3b82f6\"}),/*#__PURE__*/_jsx(\"text\",{x:\"50\",y:\"160\",fontSize:\"9\",fill:\"#3b82f6\",textAnchor:\"middle\",children:\"4.2\"}),/*#__PURE__*/_jsx(\"text\",{x:\"110\",y:\"144\",fontSize:\"9\",fill:\"#3b82f6\",textAnchor:\"middle\",children:\"4.3\"}),/*#__PURE__*/_jsx(\"text\",{x:\"170\",y:\"128\",fontSize:\"9\",fill:\"#3b82f6\",textAnchor:\"middle\",children:\"4.4\"}),/*#__PURE__*/_jsx(\"text\",{x:\"230\",y:\"112\",fontSize:\"9\",fill:\"#3b82f6\",textAnchor:\"middle\",children:\"4.5\"}),/*#__PURE__*/_jsx(\"text\",{x:\"290\",y:\"96\",fontSize:\"9\",fill:\"#3b82f6\",textAnchor:\"middle\",children:\"4.6\"}),/*#__PURE__*/_jsx(\"text\",{x:\"350\",y:\"80\",fontSize:\"9\",fill:\"#3b82f6\",textAnchor:\"middle\",children:\"4.7\"})]})})})]}),/*#__PURE__*/_jsx(Card,{title:\"\\uD83D\\uDCCA Feedback Summary\",padding:\"lg\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:'repeat(4, 1fr)',gap:'var(--spacing-6)'},children:[/*#__PURE__*/_jsxs(Box,{sx:{p:3,backgroundColor:'#f8fafc',borderRadius:'var(--radius-md)',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:\"bold\",color:\"text.primary\",sx:{mb:1},children:\"2,238\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Total Feedback\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:3,backgroundColor:'#f0fdf4',borderRadius:'var(--radius-md)',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:\"bold\",color:\"#16a34a\",sx:{mb:1},children:\"85.8%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Positive Sentiment\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:3,backgroundColor:'#eff6ff',borderRadius:'var(--radius-md)',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:\"bold\",color:\"#2563eb\",sx:{mb:1},children:\"4.6/5\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Average Rating\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:3,backgroundColor:'#fdf4ff',borderRadius:'var(--radius-md)',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:\"bold\",color:\"#9333ea\",sx:{mb:1},children:\"+12%\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"vs Last Month\"})]})]})}),/*#__PURE__*/_jsx(Card,{title:\"Recent Feedback\",padding:\"lg\",children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexDirection:'column',gap:'var(--spacing-4)'},children:[{user:'JD',action:'5-star rating received',name:'John Doe',time:'5 min ago',type:'completion'},{user:'SM',action:'Improvement suggestion submitted',name:'Sarah Miller',time:'20 min ago',type:'create'},{user:'RW',action:'Bug report submitted',name:'Robert Wilson',time:'45 min ago',type:'alert'},{user:'LB',action:'Feature request submitted',name:'Lisa Brown',time:'1 hour ago',type:'update'}].map((activity,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:'var(--spacing-3)'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:'40px',height:'40px',borderRadius:'50%',backgroundColor:'var(--color-primary-100)',display:'flex',alignItems:'center',justifyContent:'center',fontSize:'var(--font-size-sm)',fontWeight:'var(--font-weight-semibold)',color:'var(--color-primary-700)'},children:activity.user}),/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:activity.action}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[activity.name,\" \\u2022 \",activity.time]})]}),/*#__PURE__*/_jsx(Box,{sx:{width:'8px',height:'8px',borderRadius:'50%',backgroundColor:activity.type==='alert'?'var(--color-error-500)':'var(--color-success-500)'}})]},index))})})]});default:return null;}};return/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-web\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-webcontent\",children:[/*#__PURE__*/_jsx(DashboardWrapper,{children:/*#__PURE__*/_jsxs(Container,{maxWidth:\"xl\",sx:{py:3},children:[/*#__PURE__*/_jsxs(StyledTabs,{value:selectedTab,onChange:handleTabChange,children:[/*#__PURE__*/_jsx(Tab,{label:\"Overview\"}),/*#__PURE__*/_jsx(Tab,{label:\"Analytics\"}),/*#__PURE__*/_jsx(Tab,{label:\"AI Performance\"}),/*#__PURE__*/_jsx(Tab,{label:\"Feedback\"})]}),renderTabContent()]})}),tooltip.visible&&/*#__PURE__*/_jsxs(Box,{sx:{position:'fixed',left:tooltip.x,top:tooltip.y,transform:'translate(-50%, -100%)',backgroundColor:'white',border:'1px solid #d1d5db',borderRadius:'6px',padding:'6px 10px',boxShadow:'0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',zIndex:10000,pointerEvents:'none',fontSize:'11px',minWidth:'70px',textAlign:'center',fontFamily:'-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontSize:'10px',color:'#6b7280',mb:0.2,lineHeight:1.2},children:tooltip.title}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontSize:'11px',color:'#111827',fontWeight:'600',lineHeight:1.2},children:tooltip.content})]})]})});};export default ModernDashboard;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Container", "Tabs", "Tab", "styled", "TrendingUp", "TrendingDown", "People", "CheckCircle", "Star", "Schedule", "FilterList", "CalendarToday", "Card", "ModernButton", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "DashboardWrapper", "backgroundColor", "minHeight", "HeaderSection", "display", "justifyContent", "alignItems", "marginBottom", "StyledTabs", "fontSize", "fontWeight", "textTransform", "color", "FilterSection", "gap", "MetricsGrid", "gridTemplateColumns", "MetricCardContainer", "padding", "MetricIcon", "_ref", "width", "height", "borderRadius", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flex", "MetricTitle", "MetricValue", "MetricChange", "ChangeIndicator", "_ref2", "trend", "MetricCard", "_ref3", "title", "value", "change", "changeValue", "icon", "shadow", "hover", "children", "variant", "ModernDashboard", "selectedTab", "setSelectedTab", "tooltip", "setTooltip", "visible", "x", "y", "content", "showTooltip", "event", "rect", "currentTarget", "getBoundingClientRect", "scrollX", "window", "pageXOffset", "document", "documentElement", "scrollLeft", "scrollY", "pageYOffset", "scrollTop", "left", "top", "hideTooltip", "prev", "_objectSpread", "overviewMetrics", "analyticsMetrics", "aiPerformanceMetrics", "handleTabChange", "newValue", "renderTabContent", "startIcon", "size", "map", "metric", "index", "sx", "mb", "subtitle", "position", "viewBox", "style", "overflow", "id", "patternUnits", "d", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "textAnchor", "offset", "stopColor", "stopOpacity", "strokeLinecap", "className", "cx", "cy", "r", "cursor", "transition", "onMouseEnter", "e", "console", "log", "setAttribute", "onMouseLeave", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "transform", "flexDirection", "fullWidth", "user", "action", "name", "time", "type", "activity", "p", "border", "px", "py", "boxShadow", "mt", "min<PERSON><PERSON><PERSON>", "mr", "textAlign", "max<PERSON><PERSON><PERSON>", "onChange", "label", "zIndex", "pointerEvents", "fontFamily", "lineHeight"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/dashboard/ModernDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Box, Typography, IconButton, Container, Tabs, Tab } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport {\n  TrendingUp,\n  TrendingDown,\n  People,\n  CheckCircle,\n  Star,\n  Schedule,\n  FilterList,\n  CalendarToday\n} from '@mui/icons-material';\nimport Card from '../common/Card';\nimport ModernButton from '../common/ModernButton';\nimport ChartPlaceholder from './ChartPlaceholder';\n\ninterface MetricCardProps {\n  title: string;\n  value: string;\n  change: string;\n  changeValue: string;\n  trend: 'up' | 'down';\n  icon: React.ReactNode;\n  color: string;\n}\n\nconst DashboardWrapper = styled('div')({\n  backgroundColor: '#f6f9ff',\n  minHeight: '100vh',\n});\n\nconst HeaderSection = styled(Box)({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: 'var(--spacing-4)',\n});\n\nconst StyledTabs = styled(Tabs)({\n  marginBottom: 'var(--spacing-4)',\n  '& .MuiTabs-indicator': {\n    backgroundColor: 'var(--color-primary-600)',\n  },\n  '& .MuiTab-root': {\n    fontSize: 'var(--font-size-sm)',\n    fontWeight: 'var(--font-weight-medium)',\n    textTransform: 'none',\n    color: 'var(--color-gray-600)',\n    '&.Mui-selected': {\n      color: 'var(--color-primary-600)',\n      fontWeight: 'var(--font-weight-semibold)',\n    },\n  },\n});\n\nconst FilterSection = styled(Box)({\n  display: 'flex',\n  gap: 'var(--spacing-3)',\n  alignItems: 'center',\n});\n\nconst MetricsGrid = styled(Box)({\n  display: 'grid',\n  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n  gap: 'var(--spacing-4)',\n  marginBottom: 'var(--spacing-6)',\n});\n\nconst MetricCardContainer = styled(Card)({\n  padding: 'var(--spacing-5)',\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-4)',\n});\n\nconst MetricIcon = styled(Box)<{ color: string }>(({ color }) => ({\n  width: '48px',\n  height: '48px',\n  borderRadius: 'var(--radius-lg)',\n  backgroundColor: `${color}15`,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  \n  '& svg': {\n    color: color,\n    fontSize: '24px',\n  },\n}));\n\nconst MetricContent = styled(Box)({\n  flex: 1,\n});\n\nconst MetricTitle = styled(Typography)({\n  fontSize: 'var(--font-size-sm)',\n  color: 'var(--color-gray-600)',\n  marginBottom: 'var(--spacing-1)',\n});\n\nconst MetricValue = styled(Typography)({\n  fontSize: 'var(--font-size-2xl)',\n  fontWeight: 'var(--font-weight-bold)',\n  color: 'var(--color-gray-900)',\n  marginBottom: 'var(--spacing-1)',\n});\n\nconst MetricChange = styled(Box)({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)',\n});\n\nconst ChangeIndicator = styled(Box)<{ trend: 'up' | 'down' }>(({ trend }) => ({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)',\n  fontSize: 'var(--font-size-xs)',\n  fontWeight: 'var(--font-weight-medium)',\n  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',\n  \n  '& svg': {\n    fontSize: '16px',\n  },\n}));\n\nconst MetricCard: React.FC<MetricCardProps> = ({\n  title,\n  value,\n  change,\n  changeValue,\n  trend,\n  icon,\n  color,\n}) => {\n  return (\n    <MetricCardContainer shadow=\"sm\" hover>\n      <MetricIcon color={color}>\n        {icon}\n      </MetricIcon>\n      <MetricContent>\n        <MetricTitle>{title}</MetricTitle>\n        <MetricValue>{value}</MetricValue>\n        <MetricChange>\n          <ChangeIndicator trend={trend}>\n            {trend === 'up' ? <TrendingUp /> : <TrendingDown />}\n            {change}\n          </ChangeIndicator>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            {changeValue}\n          </Typography>\n        </MetricChange>\n      </MetricContent>\n    </MetricCardContainer>\n  );\n};\n\nconst ModernDashboard: React.FC = () => {\n  const [selectedTab, setSelectedTab] = useState(0);\n  const [tooltip, setTooltip] = useState<{\n    visible: boolean;\n    x: number;\n    y: number;\n    content: string;\n    title: string;\n  }>({\n    visible: false,\n    x: 0,\n    y: 0,\n    content: '',\n    title: ''\n  });\n\n  const showTooltip = (event: React.MouseEvent, title: string, content: string) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n\n    setTooltip({\n      visible: true,\n      x: rect.left + scrollX + rect.width / 2,\n      y: rect.top + scrollY - 10,\n      content,\n      title\n    });\n  };\n\n  const hideTooltip = () => {\n    setTooltip(prev => ({ ...prev, visible: false }));\n  };\n\n  const overviewMetrics = [\n    {\n      title: 'Completion Rate',\n      value: '87.3%',\n      change: '****%',\n      changeValue: '+2.8pp',\n      trend: 'up' as const,\n      icon: <CheckCircle />,\n      color: 'var(--color-success-600)',\n    },\n    {\n      title: 'User Satisfaction',\n      value: '4.6',\n      change: '+0.2',\n      changeValue: 'out of 5.0',\n      trend: 'up' as const,\n      icon: <Star />,\n      color: 'var(--color-warning-600)',\n    },\n    {\n      title: 'Hours Saved',\n      value: '2,847',\n      change: '+18.7%',\n      changeValue: '+447h',\n      trend: 'up' as const,\n      icon: <Schedule />,\n      color: 'var(--color-primary-600)',\n    },\n  ];\n\n  const analyticsMetrics = [\n    {\n      title: 'Active Users',\n      value: '12,847',\n      change: '+12.5%',\n      changeValue: '+1,432',\n      trend: 'up' as const,\n      icon: <People />,\n      color: 'var(--color-primary-600)',\n    },\n    {\n      title: 'Completion Rate',\n      value: '87.3%',\n      change: '****%',\n      changeValue: '+2.8pp',\n      trend: 'up' as const,\n      icon: <CheckCircle />,\n      color: 'var(--color-success-600)',\n    },\n    {\n      title: 'User Satisfaction',\n      value: '4.6',\n      change: '+0.2',\n      changeValue: 'out of 5.0',\n      trend: 'up' as const,\n      icon: <Star />,\n      color: 'var(--color-warning-600)',\n    },\n    {\n      title: 'Hours Saved',\n      value: '2,847',\n      change: '+18.7%',\n      changeValue: '+447h',\n      trend: 'up' as const,\n      icon: <Schedule />,\n      color: 'var(--color-primary-600)',\n    },\n  ];\n\n  const aiPerformanceMetrics = [\n    {\n      title: 'AI Response Time',\n      value: '1.2s',\n      change: '-15%',\n      changeValue: 'faster',\n      trend: 'up' as const,\n      icon: <Schedule />,\n      color: 'var(--color-success-600)',\n    },\n    {\n      title: 'AI Accuracy',\n      value: '94.8%',\n      change: '+2.1%',\n      changeValue: 'improved',\n      trend: 'up' as const,\n      icon: <CheckCircle />,\n      color: 'var(--color-primary-600)',\n    },\n    {\n      title: 'Model Confidence',\n      value: '89.3%',\n      change: '+1.8%',\n      changeValue: 'higher',\n      trend: 'up' as const,\n      icon: <Star />,\n      color: 'var(--color-warning-600)',\n    },\n    {\n      title: 'Processing Load',\n      value: '67%',\n      change: '+5%',\n      changeValue: 'capacity',\n      trend: 'up' as const,\n      icon: <People />,\n      color: 'var(--color-error-500)',\n    },\n  ];\n\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\n    setSelectedTab(newValue);\n  };\n\n  const renderTabContent = () => {\n    switch (selectedTab) {\n      case 0: // Overview\n        return (\n          <>\n            {/* Header with Filters */}\n            <HeaderSection>\n              <Box>\n                <Typography variant=\"h4\" fontWeight=\"bold\" color=\"text.primary\">\n                  Dashboard Overview\n                </Typography>\n              </Box>\n              <FilterSection>\n                <ModernButton\n                  variant=\"outline\"\n                  startIcon={<FilterList />}\n                  size=\"sm\"\n                >\n                  Filter\n                </ModernButton>\n                <ModernButton\n                  variant=\"outline\"\n                  startIcon={<CalendarToday />}\n                  size=\"sm\"\n                >\n                  Last 30 days\n                </ModernButton>\n              </FilterSection>\n            </HeaderSection>\n\n            {/* Overview Metrics Grid (4 cards like Analytics) */}\n            <MetricsGrid>\n              {analyticsMetrics.map((metric, index) => (\n                <MetricCard key={index} {...metric} />\n              ))}\n            </MetricsGrid>\n\n            {/* Overview Charts - Growth Trends and User Satisfaction */}\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>\n              {/* Growth Trends Chart */}\n              <Card title=\"📈 Growth Trends\" subtitle=\"User engagement over the last 6 months\" padding=\"lg\">\n                <Box sx={{ height: '300px', position: 'relative', backgroundColor: 'white', borderRadius: 'var(--radius-md)' }}>\n                  {/* Interactive Line Chart */}\n                  <svg width=\"100%\" height=\"280\" viewBox=\"0 0 450 250\" style={{ overflow: 'visible' }}>\n                    {/* Grid lines */}\n                    <defs>\n                      <pattern id=\"interactiveGrid\" width=\"75\" height=\"50\" patternUnits=\"userSpaceOnUse\">\n                        <path d=\"M 75 0 L 0 0 0 50\" fill=\"none\" stroke=\"#f1f5f9\" strokeWidth=\"1\"/>\n                      </pattern>\n                    </defs>\n                    <rect width=\"100%\" height=\"200\" fill=\"url(#interactiveGrid)\" />\n\n                    {/* Y-axis */}\n                    <line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"200\" stroke=\"#e2e8f0\" strokeWidth=\"1\"/>\n                    {/* X-axis */}\n                    <line x1=\"50\" y1=\"200\" x2=\"400\" y2=\"200\" stroke=\"#e2e8f0\" strokeWidth=\"1\"/>\n\n                    {/* Y-axis labels */}\n                    <text x=\"40\" y=\"25\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">14000</text>\n                    <text x=\"40\" y=\"75\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">10500</text>\n                    <text x=\"40\" y=\"125\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">7000</text>\n                    <text x=\"40\" y=\"175\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">3500</text>\n                    <text x=\"40\" y=\"205\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">0</text>\n\n                    {/* X-axis labels */}\n                    <text x=\"80\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Jan</text>\n                    <text x=\"140\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Feb</text>\n                    <text x=\"200\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Mar</text>\n                    <text x=\"260\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Apr</text>\n                    <text x=\"320\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">May</text>\n                    <text x=\"380\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Jun</text>\n\n                    {/* Area fill with gradient */}\n                    <defs>\n                      <linearGradient id=\"areaGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n                        <stop offset=\"0%\" stopColor=\"#3b82f6\" stopOpacity=\"0.3\"/>\n                        <stop offset=\"100%\" stopColor=\"#3b82f6\" stopOpacity=\"0.05\"/>\n                      </linearGradient>\n                    </defs>\n                    <path d=\"M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70 L 380 200 L 80 200 Z\" fill=\"url(#areaGradient)\"/>\n\n                    {/* Main line */}\n                    <path d=\"M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"3\" strokeLinecap=\"round\"/>\n\n                    {/* Interactive data points */}\n                    <g className=\"data-points\">\n                      <circle\n                        cx=\"80\" cy=\"160\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Jan point');\n                          showTooltip(e, 'Jan', '8,500 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"140\" cy=\"150\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Feb point');\n                          showTooltip(e, 'Feb', '9,200 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"200\" cy=\"130\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Mar point');\n                          showTooltip(e, 'Mar', '10,100 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"260\" cy=\"110\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Apr point');\n                          showTooltip(e, 'Apr', '11,100 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"320\" cy=\"90\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering May point');\n                          showTooltip(e, 'May', '12,300 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"380\" cy=\"70\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Jun point');\n                          showTooltip(e, 'Jun', '13,200 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                    </g>\n                  </svg>\n                </Box>\n              </Card>\n\n              {/* User Satisfaction Donut Chart */}\n              <Card title=\"😊 User Satisfaction\" subtitle=\"Rating distribution this month\" padding=\"lg\">\n                <Box sx={{ height: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', borderRadius: 'var(--radius-md)', position: 'relative' }}>\n                  {/* Interactive Donut Chart */}\n                  <svg width=\"220\" height=\"220\" viewBox=\"0 0 220 220\">\n                    {/* Donut segments with hover effects */}\n                    <g>\n                      {/* Excellent (5★) - 65% - Green */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#10b981\" strokeWidth=\"30\"\n                        strokeDasharray=\"305 470\" strokeDashoffset=\"0\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Excellent segment');\n                          showTooltip(e, 'Excellent (5★)', '65%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n\n                      {/* Good (4★) - 20% - Light Green */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#84cc16\" strokeWidth=\"30\"\n                        strokeDasharray=\"94 470\" strokeDashoffset=\"-305\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Good segment');\n                          showTooltip(e, 'Good (4★)', '20%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n\n                      {/* Average (3★) - 10% - Orange */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#f59e0b\" strokeWidth=\"30\"\n                        strokeDasharray=\"47 470\" strokeDashoffset=\"-399\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Average segment');\n                          showTooltip(e, 'Average (3★)', '10%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n\n                      {/* Poor (2★) - 3% - Orange-Red */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#f97316\" strokeWidth=\"30\"\n                        strokeDasharray=\"14 470\" strokeDashoffset=\"-446\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Poor segment');\n                          showTooltip(e, 'Poor (2★)', '3%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n\n                      {/* Very Poor (1★) - 2% - Red */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#ef4444\" strokeWidth=\"30\"\n                        strokeDasharray=\"9 470\" strokeDashoffset=\"-460\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Very Poor segment');\n                          showTooltip(e, 'Very Poor (1★)', '2%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n                    </g>\n\n                    {/* Center circle for donut effect */}\n                    <circle cx=\"110\" cy=\"110\" r=\"45\" fill=\"white\"/>\n                  </svg>\n                </Box>\n              </Card>\n            </Box>\n\n            {/* Quick Actions and Recent Activity - Same layout as Analytics */}\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: 'var(--spacing-6)', mb: 4 }}>\n              {/* Quick Actions */}\n              <Card title=\"Quick Actions\" subtitle=\"Take action based on your dashboard insights\" padding=\"lg\">\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>\n                  <ModernButton variant=\"primary\" fullWidth>\n                    Create New Guide\n                  </ModernButton>\n                  <ModernButton variant=\"outline\" fullWidth>\n                    View Full Analytics\n                  </ModernButton>\n                  <ModernButton variant=\"outline\" fullWidth>\n                    AI Assistant Settings\n                  </ModernButton>\n                </Box>\n              </Card>\n\n              {/* Recent Activity */}\n              <Card title=\"Recent Activity\" padding=\"lg\">\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                  {[\n                    { user: 'SC', action: 'New guide created', name: 'Sarah Chen', time: '2 min ago', type: 'create' },\n                    { user: 'S', action: 'Guide completed 47 times', name: 'System', time: '15 min ago', type: 'completion' },\n                    { user: 'MJ', action: 'AI response updated', name: 'Mike Johnson', time: '1 hour ago', type: 'update' },\n                    { user: 'S', action: 'Low performance alert', name: 'System', time: '2 hours ago', type: 'alert' },\n                  ].map((activity, index) => (\n                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-3)' }}>\n                      <Box sx={{\n                        width: '40px',\n                        height: '40px',\n                        borderRadius: '50%',\n                        backgroundColor: 'var(--color-primary-100)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: 'var(--font-size-sm)',\n                        fontWeight: 'var(--font-weight-semibold)',\n                        color: 'var(--color-primary-700)'\n                      }}>\n                        {activity.user}\n                      </Box>\n                      <Box sx={{ flex: 1 }}>\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\n                          {activity.action}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {activity.name} • {activity.time}\n                        </Typography>\n                      </Box>\n                      <Box sx={{\n                        width: '8px',\n                        height: '8px',\n                        borderRadius: '50%',\n                        backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'\n                      }} />\n                    </Box>\n                  ))}\n                </Box>\n              </Card>\n            </Box>\n          </>\n        );\n\n      case 1: // Analytics\n        return (\n          <>\n            {/* Guide Performance Overview */}\n            <Card title=\"Guide Performance Overview\" subtitle=\"Click on any guide to see detailed funnel analysis\" padding=\"lg\">\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                {/* Product Onboarding */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Product Onboarding\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e8f5e9',\n                        color: '#2e7d32',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        excellent\n                      </Box>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      }}>\n                        Onboarding\n                      </Box>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\n                        1,400 views\n                      </Box>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\n                        1,247 completed\n                      </Box>\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium' }}>\n                        11% drop-off\n                      </Box>\n                      <Box>\n                        Updated 2 days ago\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      89%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '89%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Feature Discovery */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Feature Discovery\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e3f2fd',\n                        color: '#1976d2',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        good\n                      </Box>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      }}>\n                        Feature\n                      </Box>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\n                        1,174 views\n                      </Box>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\n                        892 completed\n                      </Box>\n                      <Box sx={{ color: 'var(--color-warning-600)', fontWeight: 'medium' }}>\n                        24% drop-off\n                      </Box>\n                      <Box>\n                        Updated 1 day ago\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      76%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '76%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Advanced Settings */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Advanced Settings\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#fff3e0',\n                        color: '#f57c00',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        needs attention\n                      </Box>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      }}>\n                        Configuration\n                      </Box>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\n                        962 views\n                      </Box>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\n                        634 completed\n                      </Box>\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium' }}>\n                        32% drop-off\n                      </Box>\n                      <Box>\n                        Updated 5 days ago\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      65%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n              </Box>\n            </Card>\n          </>\n        );\n\n      case 2: // AI Performance\n        return (\n          <>\n            \n             {/* Bottom Metrics Cards */}\n            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 'var(--spacing-6)', mb: 4 }}>\n              {/* Total Interactions Card */}\n              <Box sx={{\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                  <Typography variant=\"caption\" color=\"#3b82f6\" sx={{ fontWeight: 'medium' }}>\n                    Total Interactions\n                  </Typography>\n                  <Box sx={{\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e3f2fd',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Box sx={{\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#3b82f6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    }}>\n                      💬\n                    </Box>\n                  </Box>\n                </Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\n                  2,847\n                </Typography>\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\n                  +12% from last month\n                </Typography>\n              </Box>\n\n              {/* Success Rate Card */}\n              <Box sx={{\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                  <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\n                    Success Rate\n                  </Typography>\n                  <Box sx={{\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e8f5e9',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Box sx={{\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#10b981',\n                      borderRadius: '50%',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    }}>\n                      ✓\n                    </Box>\n                  </Box>\n                </Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\n                  91%\n                </Typography>\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\n                  +3% improvement\n                </Typography>\n              </Box>\n\n              {/* Avg Response Time Card */}\n              <Box sx={{\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                  <Typography variant=\"caption\" color=\"#8b5cf6\" sx={{ fontWeight: 'medium' }}>\n                    Avg Response Time\n                  </Typography>\n                  <Box sx={{\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#f3e8ff',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Box sx={{\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#8b5cf6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    }}>\n                      ⚡\n                    </Box>\n                  </Box>\n                </Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\n                  1.9s\n                </Typography>\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\n                  -0.3s faster\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* AI Task Performance Section */}\n            <Box sx={{ mb: 4 }}>\n              <Card title=\"AI Task Performance\" padding=\"lg\">\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                {/* Password Reset */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Password Reset\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e8f5e9',\n                        color: '#2e7d32',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        96%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        342 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 1.2s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        +2% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      96%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '96%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Account Setup */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Account Setup\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e3f2fd',\n                        color: '#1976d2',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        89%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        198 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 1.4s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        -5% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      89%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '89%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Feature Explanation */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Feature Explanation\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e3f2fd',\n                        color: '#1976d2',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        90%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        267 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 2.1s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        -1% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      90%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '90%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Troubleshooting */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Troubleshooting\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#fff3e0',\n                        color: '#f57c00',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        88%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        156 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 3.1s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        -3% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      88%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '88%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Integration Help */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Integration Help\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#fff3e0',\n                        color: '#f57c00',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        87%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        123 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 2.5s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        +1% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      87%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '87%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n              </Box>\n            </Card>\n            </Box>\n\n            {/* AI Insights & Recommendations */}\n            <Card title=\"AI Insights & Recommendations\" padding=\"lg\">\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>\n                {/* Optimize Workflow */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#fffbeb',\n                  border: '1px solid #fbbf24',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                }}>\n                  <Box sx={{\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#f59e0b',\n                    borderRadius: '50%',\n                    mt: 1\n                  }} />\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\n                      Optimize Workflow\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Consider optimizing your workflow to reduce response time by 15%\n                    </Typography>\n                  </Box>\n                </Box>\n\n                {/* Excluded Performance */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                }}>\n                  <Box sx={{\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }} />\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\n                      Excluded Performance\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Excluded tasks are performing well with 94% accuracy rate\n                    </Typography>\n                  </Box>\n                </Box>\n\n                {/* Personalized Suggestions */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                }}>\n                  <Box sx={{\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }} />\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\n                      Personalized Suggestions\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      AI suggests implementing advanced filtering for better user experience\n                    </Typography>\n                  </Box>\n                </Box>\n              </Box>\n            </Card>\n          </>\n        );\n\n      case 3: // Feedback\n        return (\n          <>\n            {/* User Satisfaction Ratings and Satisfaction Trend */}\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>\n              {/* User Satisfaction Ratings */}\n              <Card title=\"⭐ User Satisfaction Ratings\" padding=\"lg\">\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                  {/* Excellent (5★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Excellent (5★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: '#10b981',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      1,247\n                    </Typography>\n                  </Box>\n\n                  {/* Good (4★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Good (4★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '45%',\n                        height: '100%',\n                        backgroundColor: '#84cc16',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      892\n                    </Typography>\n                  </Box>\n\n                  {/* Average (3★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Average (3★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '22%',\n                        height: '100%',\n                        backgroundColor: '#f59e0b',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      434\n                    </Typography>\n                  </Box>\n\n                  {/* Poor (2★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Poor (2★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '8%',\n                        height: '100%',\n                        backgroundColor: '#f97316',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      123\n                    </Typography>\n                  </Box>\n\n                  {/* Very Poor (1★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Very Poor (1★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '4%',\n                        height: '100%',\n                        backgroundColor: '#ef4444',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      57\n                    </Typography>\n                  </Box>\n\n                  {/* Summary Cards */}\n                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>\n                    <Box sx={{\n                      p: 2,\n                      backgroundColor: '#f0fdf4',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    }}>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#16a34a\">\n                        77%\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"#16a34a\">\n                        Positive\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      p: 2,\n                      backgroundColor: '#fffbeb',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    }}>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#d97706\">\n                        16%\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"#d97706\">\n                        Neutral\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      p: 2,\n                      backgroundColor: '#fef2f2',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    }}>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#dc2626\">\n                        7%\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"#dc2626\">\n                        Negative\n                      </Typography>\n                    </Box>\n                  </Box>\n                </Box>\n              </Card>\n\n              {/* Satisfaction Trend */}\n              <Card title=\"📈 Satisfaction Trend\" padding=\"lg\">\n                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8fafc', borderRadius: 'var(--radius-md)', position: 'relative' }}>\n                  {/* Satisfaction Trend Line Chart */}\n                  <svg width=\"100%\" height=\"250\" viewBox=\"0 0 400 200\" style={{ overflow: 'visible' }}>\n                    {/* Grid lines */}\n                    <defs>\n                      <pattern id=\"feedbackGrid\" width=\"66.67\" height=\"40\" patternUnits=\"userSpaceOnUse\">\n                        <path d=\"M 66.67 0 L 0 0 0 40\" fill=\"none\" stroke=\"#e2e8f0\" strokeWidth=\"0.5\"/>\n                      </pattern>\n                    </defs>\n                    <rect width=\"100%\" height=\"100%\" fill=\"url(#feedbackGrid)\" />\n\n                    {/* Y-axis labels */}\n                    <text x=\"10\" y=\"20\" fontSize=\"10\" fill=\"#64748b\">5</text>\n                    <text x=\"10\" y=\"60\" fontSize=\"10\" fill=\"#64748b\">4.75</text>\n                    <text x=\"10\" y=\"100\" fontSize=\"10\" fill=\"#64748b\">4.5</text>\n                    <text x=\"10\" y=\"140\" fontSize=\"10\" fill=\"#64748b\">4.25</text>\n                    <text x=\"10\" y=\"180\" fontSize=\"10\" fill=\"#64748b\">4</text>\n\n                    {/* X-axis labels */}\n                    <text x=\"50\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Jan</text>\n                    <text x=\"110\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Feb</text>\n                    <text x=\"170\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Mar</text>\n                    <text x=\"230\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Apr</text>\n                    <text x=\"290\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">May</text>\n                    <text x=\"350\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Jun</text>\n\n                    {/* Line showing satisfaction trend from 4.2 to 4.7 */}\n                    <path d=\"M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"3\"/>\n\n                    {/* Data points */}\n                    <circle cx=\"50\" cy=\"168\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"110\" cy=\"152\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"170\" cy=\"136\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"230\" cy=\"120\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"290\" cy=\"104\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"350\" cy=\"88\" r=\"4\" fill=\"#3b82f6\"/>\n\n                    {/* Value labels on data points */}\n                    <text x=\"50\" y=\"160\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.2</text>\n                    <text x=\"110\" y=\"144\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.3</text>\n                    <text x=\"170\" y=\"128\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.4</text>\n                    <text x=\"230\" y=\"112\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.5</text>\n                    <text x=\"290\" y=\"96\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.6</text>\n                    <text x=\"350\" y=\"80\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.7</text>\n                  </svg>\n                </Box>\n              </Card>\n            </Box>\n\n            {/* Feedback Summary */}\n            <Card title=\"📊 Feedback Summary\" padding=\"lg\">\n              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 'var(--spacing-6)' }}>\n                {/* Total Feedback */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#f8fafc',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1 }}>\n                    2,238\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Feedback\n                  </Typography>\n                </Box>\n\n                {/* Positive Sentiment */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#f0fdf4',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#16a34a\" sx={{ mb: 1 }}>\n                    85.8%\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Positive Sentiment\n                  </Typography>\n                </Box>\n\n                {/* Average Rating */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#eff6ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#2563eb\" sx={{ mb: 1 }}>\n                    4.6/5\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Average Rating\n                  </Typography>\n                </Box>\n\n                {/* Growth vs Last Month */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#fdf4ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#9333ea\" sx={{ mb: 1 }}>\n                    +12%\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    vs Last Month\n                  </Typography>\n                </Box>\n              </Box>\n            </Card>\n\n            {/* Recent Feedback */}\n            <Card title=\"Recent Feedback\" padding=\"lg\">\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                {[\n                  { user: 'JD', action: '5-star rating received', name: 'John Doe', time: '5 min ago', type: 'completion' },\n                  { user: 'SM', action: 'Improvement suggestion submitted', name: 'Sarah Miller', time: '20 min ago', type: 'create' },\n                  { user: 'RW', action: 'Bug report submitted', name: 'Robert Wilson', time: '45 min ago', type: 'alert' },\n                  { user: 'LB', action: 'Feature request submitted', name: 'Lisa Brown', time: '1 hour ago', type: 'update' },\n                ].map((activity, index) => (\n                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-3)' }}>\n                    <Box sx={{\n                      width: '40px',\n                      height: '40px',\n                      borderRadius: '50%',\n                      backgroundColor: 'var(--color-primary-100)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: 'var(--font-size-sm)',\n                      fontWeight: 'var(--font-weight-semibold)',\n                      color: 'var(--color-primary-700)'\n                    }}>\n                      {activity.user}\n                    </Box>\n                    <Box sx={{ flex: 1 }}>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {activity.action}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {activity.name} • {activity.time}\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      width: '8px',\n                      height: '8px',\n                      borderRadius: '50%',\n                      backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'\n                    }} />\n                  </Box>\n                ))}\n              </Box>\n            </Card>\n          </>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className='qadpt-web'>\n      <div className='qadpt-webcontent'>\n        <DashboardWrapper>\n          <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n            {/* Navigation Tabs */}\n            <StyledTabs value={selectedTab} onChange={handleTabChange}>\n              <Tab label=\"Overview\" />\n              <Tab label=\"Analytics\" />\n              <Tab label=\"AI Performance\" />\n              <Tab label=\"Feedback\" />\n            </StyledTabs>\n\n            {/* Render Tab Content */}\n            {renderTabContent()}\n          </Container>\n        </DashboardWrapper>\n\n        {/* Interactive Tooltip */}\n        {tooltip.visible && (\n          <Box\n            sx={{\n              position: 'fixed',\n              left: tooltip.x,\n              top: tooltip.y,\n              transform: 'translate(-50%, -100%)',\n              backgroundColor: 'white',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              padding: '6px 10px',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n              zIndex: 10000,\n              pointerEvents: 'none',\n              fontSize: '11px',\n              minWidth: '70px',\n              textAlign: 'center',\n              fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n            }}\n          >\n            <Typography variant=\"body2\" sx={{ fontSize: '10px', color: '#6b7280', mb: 0.2, lineHeight: 1.2 }}>\n              {tooltip.title}\n            </Typography>\n            <Typography variant=\"body2\" sx={{ fontSize: '11px', color: '#111827', fontWeight: '600', lineHeight: 1.2 }}>\n              {tooltip.content}\n            </Typography>\n          </Box>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ModernDashboard;\n"], "mappings": "2HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,GAAG,CAAEC,UAAU,CAAcC,SAAS,CAAEC,IAAI,CAAEC,GAAG,KAAQ,eAAe,CACjF,OAASC,MAAM,KAAQ,sBAAsB,CAC7C,OACEC,UAAU,CACVC,YAAY,CACZC,MAAM,CACNC,WAAW,CACXC,IAAI,CACJC,QAAQ,CACRC,UAAU,CACVC,aAAa,KACR,qBAAqB,CAC5B,MAAO,CAAAC,IAAI,KAAM,gBAAgB,CACjC,MAAO,CAAAC,YAAY,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAalD,KAAM,CAAAC,gBAAgB,CAAGjB,MAAM,CAAC,KAAK,CAAC,CAAC,CACrCkB,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE,OACb,CAAC,CAAC,CAEF,KAAM,CAAAC,aAAa,CAAGpB,MAAM,CAACL,GAAG,CAAC,CAAC,CAChC0B,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,kBAChB,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAU,CAAGzB,MAAM,CAACF,IAAI,CAAC,CAAC,CAC9B0B,YAAY,CAAE,kBAAkB,CAChC,sBAAsB,CAAE,CACtBN,eAAe,CAAE,0BACnB,CAAC,CACD,gBAAgB,CAAE,CAChBQ,QAAQ,CAAE,qBAAqB,CAC/BC,UAAU,CAAE,2BAA2B,CACvCC,aAAa,CAAE,MAAM,CACrBC,KAAK,CAAE,uBAAuB,CAC9B,gBAAgB,CAAE,CAChBA,KAAK,CAAE,0BAA0B,CACjCF,UAAU,CAAE,6BACd,CACF,CACF,CAAC,CAAC,CAEF,KAAM,CAAAG,aAAa,CAAG9B,MAAM,CAACL,GAAG,CAAC,CAAC,CAChC0B,OAAO,CAAE,MAAM,CACfU,GAAG,CAAE,kBAAkB,CACvBR,UAAU,CAAE,QACd,CAAC,CAAC,CAEF,KAAM,CAAAS,WAAW,CAAGhC,MAAM,CAACL,GAAG,CAAC,CAAC,CAC9B0B,OAAO,CAAE,MAAM,CACfY,mBAAmB,CAAE,sCAAsC,CAC3DF,GAAG,CAAE,kBAAkB,CACvBP,YAAY,CAAE,kBAChB,CAAC,CAAC,CAEF,KAAM,CAAAU,mBAAmB,CAAGlC,MAAM,CAACS,IAAI,CAAC,CAAC,CACvC0B,OAAO,CAAE,kBAAkB,CAC3Bd,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBQ,GAAG,CAAE,kBACP,CAAC,CAAC,CAEF,KAAM,CAAAK,UAAU,CAAGpC,MAAM,CAACL,GAAG,CAAC,CAAoB0C,IAAA,MAAC,CAAER,KAAM,CAAC,CAAAQ,IAAA,OAAM,CAChEC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,kBAAkB,CAChCtB,eAAe,IAAAuB,MAAA,CAAKZ,KAAK,MAAI,CAC7BR,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CAExB,OAAO,CAAE,CACPO,KAAK,CAAEA,KAAK,CACZH,QAAQ,CAAE,MACZ,CACF,CAAC,EAAC,CAAC,CAEH,KAAM,CAAAgB,aAAa,CAAG1C,MAAM,CAACL,GAAG,CAAC,CAAC,CAChCgD,IAAI,CAAE,CACR,CAAC,CAAC,CAEF,KAAM,CAAAC,WAAW,CAAG5C,MAAM,CAACJ,UAAU,CAAC,CAAC,CACrC8B,QAAQ,CAAE,qBAAqB,CAC/BG,KAAK,CAAE,uBAAuB,CAC9BL,YAAY,CAAE,kBAChB,CAAC,CAAC,CAEF,KAAM,CAAAqB,WAAW,CAAG7C,MAAM,CAACJ,UAAU,CAAC,CAAC,CACrC8B,QAAQ,CAAE,sBAAsB,CAChCC,UAAU,CAAE,yBAAyB,CACrCE,KAAK,CAAE,uBAAuB,CAC9BL,YAAY,CAAE,kBAChB,CAAC,CAAC,CAEF,KAAM,CAAAsB,YAAY,CAAG9C,MAAM,CAACL,GAAG,CAAC,CAAC,CAC/B0B,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBQ,GAAG,CAAE,kBACP,CAAC,CAAC,CAEF,KAAM,CAAAgB,eAAe,CAAG/C,MAAM,CAACL,GAAG,CAAC,CAA2BqD,KAAA,MAAC,CAAEC,KAAM,CAAC,CAAAD,KAAA,OAAM,CAC5E3B,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBQ,GAAG,CAAE,kBAAkB,CACvBL,QAAQ,CAAE,qBAAqB,CAC/BC,UAAU,CAAE,2BAA2B,CACvCE,KAAK,CAAEoB,KAAK,GAAK,IAAI,CAAG,0BAA0B,CAAG,wBAAwB,CAE7E,OAAO,CAAE,CACPvB,QAAQ,CAAE,MACZ,CACF,CAAC,EAAC,CAAC,CAEH,KAAM,CAAAwB,UAAqC,CAAGC,KAAA,EAQxC,IARyC,CAC7CC,KAAK,CACLC,KAAK,CACLC,MAAM,CACNC,WAAW,CACXN,KAAK,CACLO,IAAI,CACJ3B,KACF,CAAC,CAAAsB,KAAA,CACC,mBACErC,KAAA,CAACoB,mBAAmB,EAACuB,MAAM,CAAC,IAAI,CAACC,KAAK,MAAAC,QAAA,eACpC/C,IAAA,CAACwB,UAAU,EAACP,KAAK,CAAEA,KAAM,CAAA8B,QAAA,CACtBH,IAAI,CACK,CAAC,cACb1C,KAAA,CAAC4B,aAAa,EAAAiB,QAAA,eACZ/C,IAAA,CAACgC,WAAW,EAAAe,QAAA,CAAEP,KAAK,CAAc,CAAC,cAClCxC,IAAA,CAACiC,WAAW,EAAAc,QAAA,CAAEN,KAAK,CAAc,CAAC,cAClCvC,KAAA,CAACgC,YAAY,EAAAa,QAAA,eACX7C,KAAA,CAACiC,eAAe,EAACE,KAAK,CAAEA,KAAM,CAAAU,QAAA,EAC3BV,KAAK,GAAK,IAAI,cAAGrC,IAAA,CAACX,UAAU,GAAE,CAAC,cAAGW,IAAA,CAACV,YAAY,GAAE,CAAC,CAClDoD,MAAM,EACQ,CAAC,cAClB1C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CACjDJ,WAAW,CACF,CAAC,EACD,CAAC,EACF,CAAC,EACG,CAAC,CAE1B,CAAC,CAED,KAAM,CAAAM,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGrE,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACsE,OAAO,CAAEC,UAAU,CAAC,CAAGvE,QAAQ,CAMnC,CACDwE,OAAO,CAAE,KAAK,CACdC,CAAC,CAAE,CAAC,CACJC,CAAC,CAAE,CAAC,CACJC,OAAO,CAAE,EAAE,CACXjB,KAAK,CAAE,EACT,CAAC,CAAC,CAEF,KAAM,CAAAkB,WAAW,CAAGA,CAACC,KAAuB,CAAEnB,KAAa,CAAEiB,OAAe,GAAK,CAC/E,KAAM,CAAAG,IAAI,CAAGD,KAAK,CAACE,aAAa,CAACC,qBAAqB,CAAC,CAAC,CACxD,KAAM,CAAAC,OAAO,CAAGC,MAAM,CAACC,WAAW,EAAIC,QAAQ,CAACC,eAAe,CAACC,UAAU,CACzE,KAAM,CAAAC,OAAO,CAAGL,MAAM,CAACM,WAAW,EAAIJ,QAAQ,CAACC,eAAe,CAACI,SAAS,CAExElB,UAAU,CAAC,CACTC,OAAO,CAAE,IAAI,CACbC,CAAC,CAAEK,IAAI,CAACY,IAAI,CAAGT,OAAO,CAAGH,IAAI,CAAClC,KAAK,CAAG,CAAC,CACvC8B,CAAC,CAAEI,IAAI,CAACa,GAAG,CAAGJ,OAAO,CAAG,EAAE,CAC1BZ,OAAO,CACPjB,KACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAkC,WAAW,CAAGA,CAAA,GAAM,CACxBrB,UAAU,CAACsB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAErB,OAAO,CAAE,KAAK,EAAG,CAAC,CACnD,CAAC,CAED,KAAM,CAAAuB,eAAe,CAAG,CACtB,CACErC,KAAK,CAAE,iBAAiB,CACxBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,OAAO,CACfC,WAAW,CAAE,QAAQ,CACrBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACR,WAAW,GAAE,CAAC,CACrByB,KAAK,CAAE,0BACT,CAAC,CACD,CACEuB,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,YAAY,CACzBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACP,IAAI,GAAE,CAAC,CACdwB,KAAK,CAAE,0BACT,CAAC,CACD,CACEuB,KAAK,CAAE,aAAa,CACpBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,OAAO,CACpBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACN,QAAQ,GAAE,CAAC,CAClBuB,KAAK,CAAE,0BACT,CAAC,CACF,CAED,KAAM,CAAA6D,gBAAgB,CAAG,CACvB,CACEtC,KAAK,CAAE,cAAc,CACrBC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,QAAQ,CACrBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACT,MAAM,GAAE,CAAC,CAChB0B,KAAK,CAAE,0BACT,CAAC,CACD,CACEuB,KAAK,CAAE,iBAAiB,CACxBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,OAAO,CACfC,WAAW,CAAE,QAAQ,CACrBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACR,WAAW,GAAE,CAAC,CACrByB,KAAK,CAAE,0BACT,CAAC,CACD,CACEuB,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,YAAY,CACzBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACP,IAAI,GAAE,CAAC,CACdwB,KAAK,CAAE,0BACT,CAAC,CACD,CACEuB,KAAK,CAAE,aAAa,CACpBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,OAAO,CACpBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACN,QAAQ,GAAE,CAAC,CAClBuB,KAAK,CAAE,0BACT,CAAC,CACF,CAED,KAAM,CAAA8D,oBAAoB,CAAG,CAC3B,CACEvC,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,QAAQ,CACrBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACN,QAAQ,GAAE,CAAC,CAClBuB,KAAK,CAAE,0BACT,CAAC,CACD,CACEuB,KAAK,CAAE,aAAa,CACpBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,OAAO,CACfC,WAAW,CAAE,UAAU,CACvBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACR,WAAW,GAAE,CAAC,CACrByB,KAAK,CAAE,0BACT,CAAC,CACD,CACEuB,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,OAAO,CACfC,WAAW,CAAE,QAAQ,CACrBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACP,IAAI,GAAE,CAAC,CACdwB,KAAK,CAAE,0BACT,CAAC,CACD,CACEuB,KAAK,CAAE,iBAAiB,CACxBC,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,UAAU,CACvBN,KAAK,CAAE,IAAa,CACpBO,IAAI,cAAE5C,IAAA,CAACT,MAAM,GAAE,CAAC,CAChB0B,KAAK,CAAE,wBACT,CAAC,CACF,CAED,KAAM,CAAA+D,eAAe,CAAGA,CAACrB,KAA2B,CAAEsB,QAAgB,GAAK,CACzE9B,cAAc,CAAC8B,QAAQ,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,OAAQhC,WAAW,EACjB,IAAK,EAAC,CAAE;AACN,mBACEhD,KAAA,CAAAE,SAAA,EAAA2C,QAAA,eAEE7C,KAAA,CAACM,aAAa,EAAAuC,QAAA,eACZ/C,IAAA,CAACjB,GAAG,EAAAgE,QAAA,cACF/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,cAAc,CAAA8B,QAAA,CAAC,oBAEhE,CAAY,CAAC,CACV,CAAC,cACN7C,KAAA,CAACgB,aAAa,EAAA6B,QAAA,eACZ/C,IAAA,CAACF,YAAY,EACXkD,OAAO,CAAC,SAAS,CACjBmC,SAAS,cAAEnF,IAAA,CAACL,UAAU,GAAE,CAAE,CAC1ByF,IAAI,CAAC,IAAI,CAAArC,QAAA,CACV,QAED,CAAc,CAAC,cACf/C,IAAA,CAACF,YAAY,EACXkD,OAAO,CAAC,SAAS,CACjBmC,SAAS,cAAEnF,IAAA,CAACJ,aAAa,GAAE,CAAE,CAC7BwF,IAAI,CAAC,IAAI,CAAArC,QAAA,CACV,cAED,CAAc,CAAC,EACF,CAAC,EACH,CAAC,cAGhB/C,IAAA,CAACoB,WAAW,EAAA2B,QAAA,CACT+B,gBAAgB,CAACO,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,gBAClCvF,IAAA,CAACsC,UAAU,CAAAsC,aAAA,IAAiBU,MAAM,EAAjBC,KAAoB,CACtC,CAAC,CACS,CAAC,cAGdrF,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEY,mBAAmB,CAAE,SAAS,CAAEF,GAAG,CAAE,kBAAkB,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAE3F/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,4BAAkB,CAACkD,QAAQ,CAAC,wCAAwC,CAACnE,OAAO,CAAC,IAAI,CAAAwB,QAAA,cAC3F/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE7D,MAAM,CAAE,OAAO,CAAEgE,QAAQ,CAAE,UAAU,CAAErF,eAAe,CAAE,OAAO,CAAEsB,YAAY,CAAE,kBAAmB,CAAE,CAAAmB,QAAA,cAE7G7C,KAAA,QAAKwB,KAAK,CAAC,MAAM,CAACC,MAAM,CAAC,KAAK,CAACiE,OAAO,CAAC,aAAa,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,SAAU,CAAE,CAAA/C,QAAA,eAElF/C,IAAA,SAAA+C,QAAA,cACE/C,IAAA,YAAS+F,EAAE,CAAC,iBAAiB,CAACrE,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACqE,YAAY,CAAC,gBAAgB,CAAAjD,QAAA,cAChF/C,IAAA,SAAMiG,CAAC,CAAC,mBAAmB,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CAAC,CAAC,CACnE,CAAC,CACN,CAAC,cACPpG,IAAA,SAAM0B,KAAK,CAAC,MAAM,CAACC,MAAM,CAAC,KAAK,CAACuE,IAAI,CAAC,uBAAuB,CAAE,CAAC,cAG/DlG,IAAA,SAAMqG,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,KAAK,CAACL,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CAAC,CAAC,cAEzEpG,IAAA,SAAMqG,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACL,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CAAC,CAAC,cAG3EpG,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,KAAK,CAAA1D,QAAA,CAAC,OAAK,CAAM,CAAC,cAC9E/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,KAAK,CAAA1D,QAAA,CAAC,OAAK,CAAM,CAAC,cAC9E/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,KAAK,CAAA1D,QAAA,CAAC,MAAI,CAAM,CAAC,cAC9E/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,KAAK,CAAA1D,QAAA,CAAC,MAAI,CAAM,CAAC,cAC9E/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,KAAK,CAAA1D,QAAA,CAAC,GAAC,CAAM,CAAC,cAG3E/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cAChF/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cACjF/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cACjF/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cACjF/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cACjF/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cAGjF/C,IAAA,SAAA+C,QAAA,cACE7C,KAAA,mBAAgB6F,EAAE,CAAC,cAAc,CAACM,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAAAzD,QAAA,eACjE/C,IAAA,SAAM0G,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAAC,cACzD5G,IAAA,SAAM0G,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,SAAS,CAACC,WAAW,CAAC,MAAM,CAAC,CAAC,EAC9C,CAAC,CACb,CAAC,cACP5G,IAAA,SAAMiG,CAAC,CAAC,+EAA+E,CAACC,IAAI,CAAC,oBAAoB,CAAC,CAAC,cAGnHlG,IAAA,SAAMiG,CAAC,CAAC,0DAA0D,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CAACS,aAAa,CAAC,OAAO,CAAC,CAAC,cAGvI3G,KAAA,MAAG4G,SAAS,CAAC,aAAa,CAAA/D,QAAA,eACxB/C,IAAA,WACE+G,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,OAAO,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CACpEP,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,aAAc,CAAE,CACxDC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC7D,WAAW,CAAC2D,CAAC,CAAE,KAAK,CAAE,aAAa,CAAC,CACpCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,IAAI,CAAC,CACzC,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,GAAG,CAAC,CACxC,CAAE,CACH,CAAC,cACFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,OAAO,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CACrEP,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,aAAc,CAAE,CACxDC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC7D,WAAW,CAAC2D,CAAC,CAAE,KAAK,CAAE,aAAa,CAAC,CACpCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,IAAI,CAAC,CACzC,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,GAAG,CAAC,CACxC,CAAE,CACH,CAAC,cACFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,OAAO,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CACrEP,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,aAAc,CAAE,CACxDC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC7D,WAAW,CAAC2D,CAAC,CAAE,KAAK,CAAE,cAAc,CAAC,CACrCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,IAAI,CAAC,CACzC,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,GAAG,CAAC,CACxC,CAAE,CACH,CAAC,cACFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,OAAO,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CACrEP,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,aAAc,CAAE,CACxDC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC7D,WAAW,CAAC2D,CAAC,CAAE,KAAK,CAAE,cAAc,CAAC,CACrCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,IAAI,CAAC,CACzC,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,GAAG,CAAC,CACxC,CAAE,CACH,CAAC,cACFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,OAAO,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CACpEP,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,aAAc,CAAE,CACxDC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC7D,WAAW,CAAC2D,CAAC,CAAE,KAAK,CAAE,cAAc,CAAC,CACrCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,IAAI,CAAC,CACzC,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,GAAG,CAAC,CACxC,CAAE,CACH,CAAC,cACFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,OAAO,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CACpEP,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,aAAc,CAAE,CACxDC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC7D,WAAW,CAAC2D,CAAC,CAAE,KAAK,CAAE,cAAc,CAAC,CACrCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,IAAI,CAAC,CACzC,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,GAAG,CAAE,GAAG,CAAC,CACxC,CAAE,CACH,CAAC,EACD,CAAC,EACD,CAAC,CACH,CAAC,CACF,CAAC,cAGPxH,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,gCAAsB,CAACkD,QAAQ,CAAC,gCAAgC,CAACnE,OAAO,CAAC,IAAI,CAAAwB,QAAA,cACvF/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE7D,MAAM,CAAE,OAAO,CAAElB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEL,eAAe,CAAE,OAAO,CAAEsB,YAAY,CAAE,kBAAkB,CAAE+D,QAAQ,CAAE,UAAW,CAAE,CAAA5C,QAAA,cAE9K7C,KAAA,QAAKwB,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC,KAAK,CAACiE,OAAO,CAAC,aAAa,CAAA7C,QAAA,eAEjD7C,KAAA,MAAA6C,QAAA,eAEE/C,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,IAAI,CAACf,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,IAAI,CACtEsB,eAAe,CAAC,SAAS,CAACC,gBAAgB,CAAC,GAAG,CAACC,SAAS,CAAC,qBAAqB,CAC9E/B,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,wBAAyB,CAAE,CACnEC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CACzC7D,WAAW,CAAC2D,CAAC,CAAE,gBAAgB,CAAE,KAAK,CAAC,CACvCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACH,CAAC,cAGFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,IAAI,CAACf,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,IAAI,CACtEsB,eAAe,CAAC,QAAQ,CAACC,gBAAgB,CAAC,MAAM,CAACC,SAAS,CAAC,qBAAqB,CAChF/B,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,wBAAyB,CAAE,CACnEC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC,CACpC7D,WAAW,CAAC2D,CAAC,CAAE,WAAW,CAAE,KAAK,CAAC,CAClCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACH,CAAC,cAGFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,IAAI,CAACf,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,IAAI,CACtEsB,eAAe,CAAC,QAAQ,CAACC,gBAAgB,CAAC,MAAM,CAACC,SAAS,CAAC,qBAAqB,CAChF/B,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,wBAAyB,CAAE,CACnEC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CACvC7D,WAAW,CAAC2D,CAAC,CAAE,cAAc,CAAE,KAAK,CAAC,CACrCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACH,CAAC,cAGFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,IAAI,CAACf,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,IAAI,CACtEsB,eAAe,CAAC,QAAQ,CAACC,gBAAgB,CAAC,MAAM,CAACC,SAAS,CAAC,qBAAqB,CAChF/B,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,wBAAyB,CAAE,CACnEC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC,CACpC7D,WAAW,CAAC2D,CAAC,CAAE,WAAW,CAAE,IAAI,CAAC,CACjCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACH,CAAC,cAGFxH,IAAA,WACE+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,IAAI,CAACf,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,IAAI,CACtEsB,eAAe,CAAC,OAAO,CAACC,gBAAgB,CAAC,MAAM,CAACC,SAAS,CAAC,qBAAqB,CAC/E/B,KAAK,CAAE,CAAEqB,MAAM,CAAE,SAAS,CAAEC,UAAU,CAAE,wBAAyB,CAAE,CACnEC,YAAY,CAAGC,CAAC,EAAK,CACnBC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CACzC7D,WAAW,CAAC2D,CAAC,CAAE,gBAAgB,CAAE,IAAI,CAAC,CACtCA,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACFC,YAAY,CAAGJ,CAAC,EAAK,CACnB3C,WAAW,CAAC,CAAC,CACb2C,CAAC,CAACxD,aAAa,CAAC2D,YAAY,CAAC,cAAc,CAAE,IAAI,CAAC,CACpD,CAAE,CACH,CAAC,EACD,CAAC,cAGJxH,IAAA,WAAQ+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,IAAI,CAACf,IAAI,CAAC,OAAO,CAAC,CAAC,EAC5C,CAAC,CACH,CAAC,CACF,CAAC,EACJ,CAAC,cAGNhG,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEY,mBAAmB,CAAE,SAAS,CAAEF,GAAG,CAAE,kBAAkB,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAE3F/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,eAAe,CAACkD,QAAQ,CAAC,8CAA8C,CAACnE,OAAO,CAAC,IAAI,CAAAwB,QAAA,cAC9F7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEoH,aAAa,CAAE,QAAQ,CAAE1G,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,eAC7E/C,IAAA,CAACF,YAAY,EAACkD,OAAO,CAAC,SAAS,CAAC8E,SAAS,MAAA/E,QAAA,CAAC,kBAE1C,CAAc,CAAC,cACf/C,IAAA,CAACF,YAAY,EAACkD,OAAO,CAAC,SAAS,CAAC8E,SAAS,MAAA/E,QAAA,CAAC,qBAE1C,CAAc,CAAC,cACf/C,IAAA,CAACF,YAAY,EAACkD,OAAO,CAAC,SAAS,CAAC8E,SAAS,MAAA/E,QAAA,CAAC,uBAE1C,CAAc,CAAC,EACZ,CAAC,CACF,CAAC,cAGP/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,iBAAiB,CAACjB,OAAO,CAAC,IAAI,CAAAwB,QAAA,cACxC/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEoH,aAAa,CAAE,QAAQ,CAAE1G,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,CAC5E,CACC,CAAEgF,IAAI,CAAE,IAAI,CAAEC,MAAM,CAAE,mBAAmB,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,QAAS,CAAC,CAClG,CAAEJ,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,0BAA0B,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,YAAa,CAAC,CACzG,CAAEJ,IAAI,CAAE,IAAI,CAAEC,MAAM,CAAE,qBAAqB,CAAEC,IAAI,CAAE,cAAc,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,QAAS,CAAC,CACvG,CAAEJ,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,uBAAuB,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnG,CAAC9C,GAAG,CAAC,CAAC+C,QAAQ,CAAE7C,KAAK,gBACpBrF,KAAA,CAACnB,GAAG,EAAayG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,eACtF/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBtB,eAAe,CAAE,0BAA0B,CAC3CG,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBI,QAAQ,CAAE,qBAAqB,CAC/BC,UAAU,CAAE,6BAA6B,CACzCE,KAAK,CAAE,0BACT,CAAE,CAAA8B,QAAA,CACCqF,QAAQ,CAACL,IAAI,CACX,CAAC,cACN7H,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAAAgC,QAAA,CAC5CqF,QAAQ,CAACJ,MAAM,CACN,CAAC,cACb9H,KAAA,CAAClB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,EACjDqF,QAAQ,CAACH,IAAI,CAAC,UAAG,CAACG,QAAQ,CAACF,IAAI,EACtB,CAAC,EACV,CAAC,cACNlI,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,KAAK,CACbC,YAAY,CAAE,KAAK,CACnBtB,eAAe,CAAE8H,QAAQ,CAACD,IAAI,GAAK,OAAO,CAAG,wBAAwB,CAAG,0BAC1E,CAAE,CAAE,CAAC,GA5BG5C,KA6BL,CACN,CAAC,CACC,CAAC,CACF,CAAC,EACJ,CAAC,EACN,CAAC,CAGP,IAAK,EAAC,CAAE;AACN,mBACEvF,IAAA,CAAAI,SAAA,EAAA2C,QAAA,cAEE/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,4BAA4B,CAACkD,QAAQ,CAAC,oDAAoD,CAACnE,OAAO,CAAC,IAAI,CAAAwB,QAAA,cACjH7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEoH,aAAa,CAAE,QAAQ,CAAE1G,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,eAE7E7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP/E,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,eAAe,CAC/B2H,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,iCAAiC,CACzC1G,YAAY,CAAE,kBAAkB,CAChC,SAAS,CAAE,CACTtB,eAAe,CAAE,sBAAsB,CACvC4G,MAAM,CAAE,SACV,CACF,CAAE,CAAAnE,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAChE/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,UAAU,CAAAgC,QAAA,CAAC,oBAE/C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,SAAS,CAC1BW,KAAK,CAAE,SAAS,CAChBW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,QACd,CAAE,CAAAgC,QAAA,CAAC,WAEH,CAAK,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,uBAAuB,CACxCW,KAAK,CAAE,uBAAuB,CAC9BW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MACZ,CAAE,CAAAiC,QAAA,CAAC,YAEH,CAAK,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEF,KAAK,CAAE,uBAAuB,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,eAC3G7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,GAAI,CAAE,CAAA4B,QAAA,eAC3D/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE9D,KAAK,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,YAAY,CAAE,KAAK,CAAEtB,eAAe,CAAE,uBAAwB,CAAE,CAAE,CAAC,cAErG,EAAK,CAAC,cACNJ,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,GAAI,CAAE,CAAA4B,QAAA,eAC3D/C,IAAA,CAACR,WAAW,EAACgG,EAAE,CAAE,CAAE1E,QAAQ,CAAE,EAAE,CAAEG,KAAK,CAAE,0BAA2B,CAAE,CAAE,CAAC,kBAE1E,EAAK,CAAC,cACNjB,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEvE,KAAK,CAAE,0BAA0B,CAAEF,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,cAEtE,CAAK,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAAAgE,QAAA,CAAC,oBAEL,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAAAgC,QAAA,CAAC,KAE3C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QACZ,CAAE,CAAA/C,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,cAGN1B,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP/E,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,eAAe,CAC/B2H,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,iCAAiC,CACzC1G,YAAY,CAAE,kBAAkB,CAChC,SAAS,CAAE,CACTtB,eAAe,CAAE,sBAAsB,CACvC4G,MAAM,CAAE,SACV,CACF,CAAE,CAAAnE,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAChE/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,UAAU,CAAAgC,QAAA,CAAC,mBAE/C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,SAAS,CAC1BW,KAAK,CAAE,SAAS,CAChBW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,QACd,CAAE,CAAAgC,QAAA,CAAC,MAEH,CAAK,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,uBAAuB,CACxCW,KAAK,CAAE,uBAAuB,CAC9BW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MACZ,CAAE,CAAAiC,QAAA,CAAC,SAEH,CAAK,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEF,KAAK,CAAE,uBAAuB,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,eAC3G7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,GAAI,CAAE,CAAA4B,QAAA,eAC3D/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE9D,KAAK,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,YAAY,CAAE,KAAK,CAAEtB,eAAe,CAAE,uBAAwB,CAAE,CAAE,CAAC,cAErG,EAAK,CAAC,cACNJ,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,GAAI,CAAE,CAAA4B,QAAA,eAC3D/C,IAAA,CAACR,WAAW,EAACgG,EAAE,CAAE,CAAE1E,QAAQ,CAAE,EAAE,CAAEG,KAAK,CAAE,0BAA2B,CAAE,CAAE,CAAC,gBAE1E,EAAK,CAAC,cACNjB,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEvE,KAAK,CAAE,0BAA0B,CAAEF,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,cAEtE,CAAK,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAAAgE,QAAA,CAAC,mBAEL,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAAAgC,QAAA,CAAC,KAE3C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QACZ,CAAE,CAAA/C,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,cAGN1B,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP/E,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,eAAe,CAC/B2H,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,iCAAiC,CACzC1G,YAAY,CAAE,kBAAkB,CAChC,SAAS,CAAE,CACTtB,eAAe,CAAE,sBAAsB,CACvC4G,MAAM,CAAE,SACV,CACF,CAAE,CAAAnE,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAChE/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,UAAU,CAAAgC,QAAA,CAAC,mBAE/C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,SAAS,CAC1BW,KAAK,CAAE,SAAS,CAChBW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,QACd,CAAE,CAAAgC,QAAA,CAAC,iBAEH,CAAK,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,uBAAuB,CACxCW,KAAK,CAAE,uBAAuB,CAC9BW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MACZ,CAAE,CAAAiC,QAAA,CAAC,eAEH,CAAK,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEF,KAAK,CAAE,uBAAuB,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,eAC3G7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,GAAI,CAAE,CAAA4B,QAAA,eAC3D/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE9D,KAAK,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,YAAY,CAAE,KAAK,CAAEtB,eAAe,CAAE,uBAAwB,CAAE,CAAE,CAAC,YAErG,EAAK,CAAC,cACNJ,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,GAAI,CAAE,CAAA4B,QAAA,eAC3D/C,IAAA,CAACR,WAAW,EAACgG,EAAE,CAAE,CAAE1E,QAAQ,CAAE,EAAE,CAAEG,KAAK,CAAE,0BAA2B,CAAE,CAAE,CAAC,gBAE1E,EAAK,CAAC,cACNjB,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEvE,KAAK,CAAE,wBAAwB,CAAEF,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,cAEpE,CAAK,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAAAgE,QAAA,CAAC,oBAEL,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAAAgC,QAAA,CAAC,KAE3C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QACZ,CAAE,CAAA/C,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,CACP,CAAC,CAGP,IAAK,EAAC,CAAE;AACN,mBACE1B,KAAA,CAAAE,SAAA,EAAA2C,QAAA,eAGE7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEY,mBAAmB,CAAE,gBAAgB,CAAEF,GAAG,CAAE,kBAAkB,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAElG7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,OAAO,CACxBsB,YAAY,CAAE,kBAAkB,CAChC0G,MAAM,CAAE,iCAAiC,CACzCG,SAAS,CAAE,8BAA8B,CACzC9C,QAAQ,CAAE,UACZ,CAAE,CAAA5C,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,YAAY,CAAE8E,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAC7F/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEzE,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,oBAE5E,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAClB,CAAE,CAAAqC,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBO,KAAK,CAAE,OAAO,CACdH,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,MACd,CAAE,CAAAgC,QAAA,CAAC,cAEH,CAAK,CAAC,CACH,CAAC,EACH,CAAC,cACN/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,cAAc,CAACuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE3E,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,CAAC,OAEjG,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEzE,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,sBAE5E,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,OAAO,CACxBsB,YAAY,CAAE,kBAAkB,CAChC0G,MAAM,CAAE,iCAAiC,CACzCG,SAAS,CAAE,8BAA8B,CACzC9C,QAAQ,CAAE,UACZ,CAAE,CAAA5C,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,YAAY,CAAE8E,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAC7F/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEzE,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,cAE5E,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAClB,CAAE,CAAAqC,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,KAAK,CACnBnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBO,KAAK,CAAE,OAAO,CACdH,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,MACd,CAAE,CAAAgC,QAAA,CAAC,QAEH,CAAK,CAAC,CACH,CAAC,EACH,CAAC,cACN/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,cAAc,CAACuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE3E,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,CAAC,KAEjG,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEzE,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,iBAE5E,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,OAAO,CACxBsB,YAAY,CAAE,kBAAkB,CAChC0G,MAAM,CAAE,iCAAiC,CACzCG,SAAS,CAAE,8BAA8B,CACzC9C,QAAQ,CAAE,UACZ,CAAE,CAAA5C,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,YAAY,CAAE8E,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAC7F/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEzE,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,mBAE5E,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAClB,CAAE,CAAAqC,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBO,KAAK,CAAE,OAAO,CACdH,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,MACd,CAAE,CAAAgC,QAAA,CAAC,QAEH,CAAK,CAAC,CACH,CAAC,EACH,CAAC,cACN/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,cAAc,CAACuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE3E,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,CAAC,MAEjG,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEzE,UAAU,CAAE,QAAS,CAAE,CAAAgC,QAAA,CAAC,cAE5E,CAAY,CAAC,EACV,CAAC,EACH,CAAC,cAGN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,cACjB/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,qBAAqB,CAACjB,OAAO,CAAC,IAAI,CAAAwB,QAAA,cAC9C7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEoH,aAAa,CAAE,QAAQ,CAAE1G,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,eAE7E7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP/E,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,eAAe,CAC/B2H,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,iCAAiC,CACzC1G,YAAY,CAAE,kBAAkB,CAChC,SAAS,CAAE,CACTtB,eAAe,CAAE,sBAAsB,CACvC4G,MAAM,CAAE,SACV,CACF,CAAE,CAAAnE,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAChE/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,UAAU,CAAAgC,QAAA,CAAC,gBAE/C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,SAAS,CAC1BW,KAAK,CAAE,SAAS,CAChBW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,QACd,CAAE,CAAAgC,QAAA,CAAC,KAEH,CAAK,CAAC,cACN/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,kBAErD,CAAY,CAAC,EACV,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEF,KAAK,CAAE,uBAAuB,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,eAC3G/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,gBAErD,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEvE,KAAK,CAAE,0BAA0B,CAAEF,UAAU,CAAE,QAAQ,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,CAAC,WAExF,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAAAgC,QAAA,CAAC,KAE3C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QACZ,CAAE,CAAA/C,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,cAGN1B,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP/E,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,eAAe,CAC/B2H,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,iCAAiC,CACzC1G,YAAY,CAAE,kBAAkB,CAChC,SAAS,CAAE,CACTtB,eAAe,CAAE,sBAAsB,CACvC4G,MAAM,CAAE,SACV,CACF,CAAE,CAAAnE,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAChE/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,UAAU,CAAAgC,QAAA,CAAC,eAE/C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,SAAS,CAC1BW,KAAK,CAAE,SAAS,CAChBW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,QACd,CAAE,CAAAgC,QAAA,CAAC,KAEH,CAAK,CAAC,cACN/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,kBAErD,CAAY,CAAC,EACV,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEF,KAAK,CAAE,uBAAuB,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,eAC3G/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,gBAErD,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEvE,KAAK,CAAE,wBAAwB,CAAEF,UAAU,CAAE,QAAQ,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,CAAC,WAEtF,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAAAgC,QAAA,CAAC,KAE3C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QACZ,CAAE,CAAA/C,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,cAGN1B,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP/E,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,eAAe,CAC/B2H,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,iCAAiC,CACzC1G,YAAY,CAAE,kBAAkB,CAChC,SAAS,CAAE,CACTtB,eAAe,CAAE,sBAAsB,CACvC4G,MAAM,CAAE,SACV,CACF,CAAE,CAAAnE,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAChE/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,UAAU,CAAAgC,QAAA,CAAC,qBAE/C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,SAAS,CAC1BW,KAAK,CAAE,SAAS,CAChBW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,QACd,CAAE,CAAAgC,QAAA,CAAC,KAEH,CAAK,CAAC,cACN/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,kBAErD,CAAY,CAAC,EACV,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEF,KAAK,CAAE,uBAAuB,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,eAC3G/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,gBAErD,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEvE,KAAK,CAAE,wBAAwB,CAAEF,UAAU,CAAE,QAAQ,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,CAAC,WAEtF,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAAAgC,QAAA,CAAC,KAE3C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QACZ,CAAE,CAAA/C,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,cAGN1B,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP/E,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,eAAe,CAC/B2H,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,iCAAiC,CACzC1G,YAAY,CAAE,kBAAkB,CAChC,SAAS,CAAE,CACTtB,eAAe,CAAE,sBAAsB,CACvC4G,MAAM,CAAE,SACV,CACF,CAAE,CAAAnE,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAChE/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,UAAU,CAAAgC,QAAA,CAAC,iBAE/C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,SAAS,CAC1BW,KAAK,CAAE,SAAS,CAChBW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,QACd,CAAE,CAAAgC,QAAA,CAAC,KAEH,CAAK,CAAC,cACN/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,kBAErD,CAAY,CAAC,EACV,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEF,KAAK,CAAE,uBAAuB,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,eAC3G/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,gBAErD,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEvE,KAAK,CAAE,wBAAwB,CAAEF,UAAU,CAAE,QAAQ,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,CAAC,WAEtF,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAAAgC,QAAA,CAAC,KAE3C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QACZ,CAAE,CAAA/C,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,cAGN1B,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP/E,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,eAAe,CAC/B2H,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,iCAAiC,CACzC1G,YAAY,CAAE,kBAAkB,CAChC,SAAS,CAAE,CACTtB,eAAe,CAAE,sBAAsB,CACvC4G,MAAM,CAAE,SACV,CACF,CAAE,CAAAnE,QAAA,eACA7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAChE/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,UAAU,CAAAgC,QAAA,CAAC,kBAE/C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP+C,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPlI,eAAe,CAAE,SAAS,CAC1BW,KAAK,CAAE,SAAS,CAChBW,YAAY,CAAE,kBAAkB,CAChCd,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,QACd,CAAE,CAAAgC,QAAA,CAAC,KAEH,CAAK,CAAC,cACN/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,kBAErD,CAAY,CAAC,EACV,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEF,KAAK,CAAE,uBAAuB,CAAEH,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,eAC3G/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,gBAErD,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAEvE,KAAK,CAAE,0BAA0B,CAAEF,UAAU,CAAE,QAAQ,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAAiC,QAAA,CAAC,WAExF,CAAK,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAAAgC,QAAA,CAAC,KAE3C,CAAY,CAAC,cACb/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QACZ,CAAE,CAAA/C,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,CACF,CAAC,cAGN5B,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,+BAA+B,CAACjB,OAAO,CAAC,IAAI,CAAAwB,QAAA,cACtD7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEoH,aAAa,CAAE,QAAQ,CAAE1G,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,eAE7E7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BgI,MAAM,CAAE,mBAAmB,CAC3B1G,YAAY,CAAE,kBAAkB,CAChCnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,YAAY,CACxBQ,GAAG,CAAE,CACP,CAAE,CAAA4B,QAAA,eACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,KAAK,CACnB8G,EAAE,CAAE,CACN,CAAE,CAAE,CAAC,cACLxI,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAACyE,EAAE,CAAE,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAA1C,QAAA,CAAC,mBAEjE,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,kEAErD,CAAY,CAAC,EACV,CAAC,EACH,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BgI,MAAM,CAAE,mBAAmB,CAC3B1G,YAAY,CAAE,kBAAkB,CAChCnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,YAAY,CACxBQ,GAAG,CAAE,CACP,CAAE,CAAA4B,QAAA,eACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,KAAK,CACnB8G,EAAE,CAAE,CACN,CAAE,CAAE,CAAC,cACLxI,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAACyE,EAAE,CAAE,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAA1C,QAAA,CAAC,sBAEjE,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,2DAErD,CAAY,CAAC,EACV,CAAC,EACH,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BgI,MAAM,CAAE,mBAAmB,CAC3B1G,YAAY,CAAE,kBAAkB,CAChCnB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,YAAY,CACxBQ,GAAG,CAAE,CACP,CAAE,CAAA4B,QAAA,eACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,KAAK,CACnB8G,EAAE,CAAE,CACN,CAAE,CAAE,CAAC,cACLxI,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAACyE,EAAE,CAAE,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAA1C,QAAA,CAAC,0BAEjE,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,wEAErD,CAAY,CAAC,EACV,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,EACP,CAAC,CAGP,IAAK,EAAC,CAAE;AACN,mBACE7C,KAAA,CAAAE,SAAA,EAAA2C,QAAA,eAEE7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEY,mBAAmB,CAAE,SAAS,CAAEF,GAAG,CAAE,kBAAkB,CAAEsE,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAE3F/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,kCAA6B,CAACjB,OAAO,CAAC,IAAI,CAAAwB,QAAA,cACpD7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEoH,aAAa,CAAE,QAAQ,CAAE1G,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,eAE7E7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEwH,QAAQ,CAAE,GAAI,CAAE,CAAA5F,QAAA,eACxE/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE9D,KAAK,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAErB,eAAe,CAAE,SAAS,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAE,CAAC,cACrF5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAAAgC,QAAA,CAAC,qBAEhD,CAAY,CAAC,EACV,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACPzD,IAAI,CAAE,CAAC,CACPJ,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QAAQ,CAClB8C,EAAE,CAAE,CACN,CAAE,CAAA7F,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,cACN5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,MAAM,CAACyE,EAAE,CAAE,CAAEmD,QAAQ,CAAE,EAAE,CAAEE,SAAS,CAAE,OAAQ,CAAE,CAAA9F,QAAA,CAAC,OAExF,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEwH,QAAQ,CAAE,GAAI,CAAE,CAAA5F,QAAA,eACxE/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE9D,KAAK,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAErB,eAAe,CAAE,SAAS,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAE,CAAC,cACrF5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAAAgC,QAAA,CAAC,gBAEhD,CAAY,CAAC,EACV,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACPzD,IAAI,CAAE,CAAC,CACPJ,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QAAQ,CAClB8C,EAAE,CAAE,CACN,CAAE,CAAA7F,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,cACN5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,MAAM,CAACyE,EAAE,CAAE,CAAEmD,QAAQ,CAAE,EAAE,CAAEE,SAAS,CAAE,OAAQ,CAAE,CAAA9F,QAAA,CAAC,KAExF,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEwH,QAAQ,CAAE,GAAI,CAAE,CAAA5F,QAAA,eACxE/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE9D,KAAK,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAErB,eAAe,CAAE,SAAS,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAE,CAAC,cACrF5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAAAgC,QAAA,CAAC,mBAEhD,CAAY,CAAC,EACV,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACPzD,IAAI,CAAE,CAAC,CACPJ,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QAAQ,CAClB8C,EAAE,CAAE,CACN,CAAE,CAAA7F,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,cACN5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,MAAM,CAACyE,EAAE,CAAE,CAAEmD,QAAQ,CAAE,EAAE,CAAEE,SAAS,CAAE,OAAQ,CAAE,CAAA9F,QAAA,CAAC,KAExF,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEwH,QAAQ,CAAE,GAAI,CAAE,CAAA5F,QAAA,eACxE/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE9D,KAAK,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAErB,eAAe,CAAE,SAAS,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAE,CAAC,cACrF5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAAAgC,QAAA,CAAC,gBAEhD,CAAY,CAAC,EACV,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACPzD,IAAI,CAAE,CAAC,CACPJ,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QAAQ,CAClB8C,EAAE,CAAE,CACN,CAAE,CAAA7F,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,IAAI,CACXC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,cACN5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,MAAM,CAACyE,EAAE,CAAE,CAAEmD,QAAQ,CAAE,EAAE,CAAEE,SAAS,CAAE,OAAQ,CAAE,CAAA9F,QAAA,CAAC,KAExF,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAA4B,QAAA,eACzD7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,CAAC,CAAEwH,QAAQ,CAAE,GAAI,CAAE,CAAA5F,QAAA,eACxE/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE9D,KAAK,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAErB,eAAe,CAAE,SAAS,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAE,CAAC,cACrF5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAAAgC,QAAA,CAAC,qBAEhD,CAAY,CAAC,EACV,CAAC,cACN/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACPzD,IAAI,CAAE,CAAC,CACPJ,MAAM,CAAE,CAAC,CACTrB,eAAe,CAAE,uBAAuB,CACxCsB,YAAY,CAAE,oBAAoB,CAClCkE,QAAQ,CAAE,QAAQ,CAClB8C,EAAE,CAAE,CACN,CAAE,CAAA7F,QAAA,cACA/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,IAAI,CACXC,MAAM,CAAE,MAAM,CACdrB,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,oBAChB,CAAE,CAAE,CAAC,CACF,CAAC,cACN5B,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,MAAM,CAACyE,EAAE,CAAE,CAAEmD,QAAQ,CAAE,EAAE,CAAEE,SAAS,CAAE,OAAQ,CAAE,CAAA9F,QAAA,CAAC,IAExF,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEY,mBAAmB,CAAE,gBAAgB,CAAEF,GAAG,CAAE,CAAC,CAAEuH,EAAE,CAAE,CAAE,CAAE,CAAA3F,QAAA,eACjF7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCiH,SAAS,CAAE,QACb,CAAE,CAAA9F,QAAA,eACA/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,SAAS,CAAA8B,QAAA,CAAC,KAE3D,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAAA8B,QAAA,CAAC,UAE9C,CAAY,CAAC,EACV,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCiH,SAAS,CAAE,QACb,CAAE,CAAA9F,QAAA,eACA/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,SAAS,CAAA8B,QAAA,CAAC,KAE3D,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAAA8B,QAAA,CAAC,SAE9C,CAAY,CAAC,EACV,CAAC,cACN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCiH,SAAS,CAAE,QACb,CAAE,CAAA9F,QAAA,eACA/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,SAAS,CAAA8B,QAAA,CAAC,IAE3D,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,SAAS,CAAA8B,QAAA,CAAC,UAE9C,CAAY,CAAC,EACV,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,cAGP/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,iCAAuB,CAACjB,OAAO,CAAC,IAAI,CAAAwB,QAAA,cAC9C/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE7D,MAAM,CAAE,OAAO,CAAElB,OAAO,CAAE,MAAM,CAAEoH,aAAa,CAAE,QAAQ,CAAEnH,cAAc,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEL,eAAe,CAAE,SAAS,CAAEsB,YAAY,CAAE,kBAAkB,CAAE+D,QAAQ,CAAE,UAAW,CAAE,CAAA5C,QAAA,cAEzM7C,KAAA,QAAKwB,KAAK,CAAC,MAAM,CAACC,MAAM,CAAC,KAAK,CAACiE,OAAO,CAAC,aAAa,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,SAAU,CAAE,CAAA/C,QAAA,eAElF/C,IAAA,SAAA+C,QAAA,cACE/C,IAAA,YAAS+F,EAAE,CAAC,cAAc,CAACrE,KAAK,CAAC,OAAO,CAACC,MAAM,CAAC,IAAI,CAACqE,YAAY,CAAC,gBAAgB,CAAAjD,QAAA,cAChF/C,IAAA,SAAMiG,CAAC,CAAC,sBAAsB,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAAC,CACxE,CAAC,CACN,CAAC,cACPpG,IAAA,SAAM0B,KAAK,CAAC,MAAM,CAACC,MAAM,CAAC,MAAM,CAACuE,IAAI,CAAC,oBAAoB,CAAE,CAAC,cAG7DlG,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,GAAC,CAAM,CAAC,cACzD/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,MAAI,CAAM,CAAC,cAC5D/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,KAAG,CAAM,CAAC,cAC5D/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,MAAI,CAAM,CAAC,cAC7D/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,GAAC,CAAM,CAAC,cAG1D/C,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,KAAG,CAAM,CAAC,cAC5D/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,KAAG,CAAM,CAAC,cAC7D/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,KAAG,CAAM,CAAC,cAC7D/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,KAAG,CAAM,CAAC,cAC7D/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,KAAG,CAAM,CAAC,cAC7D/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,IAAI,CAACoF,IAAI,CAAC,SAAS,CAAAnD,QAAA,CAAC,KAAG,CAAM,CAAC,cAG7D/C,IAAA,SAAMiG,CAAC,CAAC,2DAA2D,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,SAAS,CAACC,WAAW,CAAC,GAAG,CAAC,CAAC,cAGlHpG,IAAA,WAAQ+G,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,SAAS,CAAC,CAAC,cAC/ClG,IAAA,WAAQ+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,SAAS,CAAC,CAAC,cAChDlG,IAAA,WAAQ+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,SAAS,CAAC,CAAC,cAChDlG,IAAA,WAAQ+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,SAAS,CAAC,CAAC,cAChDlG,IAAA,WAAQ+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,SAAS,CAAC,CAAC,cAChDlG,IAAA,WAAQ+G,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACf,IAAI,CAAC,SAAS,CAAC,CAAC,cAG/ClG,IAAA,SAAMuD,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,GAAG,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cAC/E/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,GAAG,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cAChF/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,GAAG,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cAChF/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAC1C,QAAQ,CAAC,GAAG,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cAChF/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,IAAI,CAAC1C,QAAQ,CAAC,GAAG,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,cAC/E/C,IAAA,SAAMuD,CAAC,CAAC,KAAK,CAACC,CAAC,CAAC,IAAI,CAAC1C,QAAQ,CAAC,GAAG,CAACoF,IAAI,CAAC,SAAS,CAACO,UAAU,CAAC,QAAQ,CAAA1D,QAAA,CAAC,KAAG,CAAM,CAAC,EAC5E,CAAC,CACH,CAAC,CACF,CAAC,EACJ,CAAC,cAGN/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,+BAAqB,CAACjB,OAAO,CAAC,IAAI,CAAAwB,QAAA,cAC5C7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEY,mBAAmB,CAAE,gBAAgB,CAAEF,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,eAE3F7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCiH,SAAS,CAAE,QACb,CAAE,CAAA9F,QAAA,eACA/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,cAAc,CAACuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,CAAC,OAE/E,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,gBAEnD,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCiH,SAAS,CAAE,QACb,CAAE,CAAA9F,QAAA,eACA/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,CAAC,OAE1E,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,oBAEnD,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCiH,SAAS,CAAE,QACb,CAAE,CAAA9F,QAAA,eACA/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,CAAC,OAE1E,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,gBAEnD,CAAY,CAAC,EACV,CAAC,cAGN7C,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CACP6C,CAAC,CAAE,CAAC,CACJ/H,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,kBAAkB,CAChCiH,SAAS,CAAE,QACb,CAAE,CAAA9F,QAAA,eACA/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,IAAI,CAACjC,UAAU,CAAC,MAAM,CAACE,KAAK,CAAC,SAAS,CAACuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,CAAC,MAE1E,CAAY,CAAC,cACb/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,CAAC,eAEnD,CAAY,CAAC,EACV,CAAC,EACH,CAAC,CACF,CAAC,cAGP/C,IAAA,CAACH,IAAI,EAAC2C,KAAK,CAAC,iBAAiB,CAACjB,OAAO,CAAC,IAAI,CAAAwB,QAAA,cACxC/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEoH,aAAa,CAAE,QAAQ,CAAE1G,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,CAC5E,CACC,CAAEgF,IAAI,CAAE,IAAI,CAAEC,MAAM,CAAE,wBAAwB,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,YAAa,CAAC,CACzG,CAAEJ,IAAI,CAAE,IAAI,CAAEC,MAAM,CAAE,kCAAkC,CAAEC,IAAI,CAAE,cAAc,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,QAAS,CAAC,CACpH,CAAEJ,IAAI,CAAE,IAAI,CAAEC,MAAM,CAAE,sBAAsB,CAAEC,IAAI,CAAE,eAAe,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACxG,CAAEJ,IAAI,CAAE,IAAI,CAAEC,MAAM,CAAE,2BAA2B,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,QAAS,CAAC,CAC5G,CAAC9C,GAAG,CAAC,CAAC+C,QAAQ,CAAE7C,KAAK,gBACpBrF,KAAA,CAACnB,GAAG,EAAayG,EAAE,CAAE,CAAE/E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,GAAG,CAAE,kBAAmB,CAAE,CAAA4B,QAAA,eACtF/C,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBtB,eAAe,CAAE,0BAA0B,CAC3CG,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBI,QAAQ,CAAE,qBAAqB,CAC/BC,UAAU,CAAE,6BAA6B,CACzCE,KAAK,CAAE,0BACT,CAAE,CAAA8B,QAAA,CACCqF,QAAQ,CAACL,IAAI,CACX,CAAC,cACN7H,KAAA,CAACnB,GAAG,EAACyG,EAAE,CAAE,CAAEzD,IAAI,CAAE,CAAE,CAAE,CAAAgB,QAAA,eACnB/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACjC,UAAU,CAAC,QAAQ,CAAAgC,QAAA,CAC5CqF,QAAQ,CAACJ,MAAM,CACN,CAAC,cACb9H,KAAA,CAAClB,UAAU,EAACgE,OAAO,CAAC,SAAS,CAAC/B,KAAK,CAAC,gBAAgB,CAAA8B,QAAA,EACjDqF,QAAQ,CAACH,IAAI,CAAC,UAAG,CAACG,QAAQ,CAACF,IAAI,EACtB,CAAC,EACV,CAAC,cACNlI,IAAA,CAACjB,GAAG,EAACyG,EAAE,CAAE,CACP9D,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,KAAK,CACbC,YAAY,CAAE,KAAK,CACnBtB,eAAe,CAAE8H,QAAQ,CAACD,IAAI,GAAK,OAAO,CAAG,wBAAwB,CAAG,0BAC1E,CAAE,CAAE,CAAC,GA5BG5C,KA6BL,CACN,CAAC,CACC,CAAC,CACF,CAAC,EACP,CAAC,CAGP,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,mBACEvF,IAAA,QAAK8G,SAAS,CAAC,WAAW,CAAA/D,QAAA,cACxB7C,KAAA,QAAK4G,SAAS,CAAC,kBAAkB,CAAA/D,QAAA,eAC/B/C,IAAA,CAACK,gBAAgB,EAAA0C,QAAA,cACf7C,KAAA,CAACjB,SAAS,EAAC6J,QAAQ,CAAC,IAAI,CAACtD,EAAE,CAAE,CAAEgD,EAAE,CAAE,CAAE,CAAE,CAAAzF,QAAA,eAErC7C,KAAA,CAACW,UAAU,EAAC4B,KAAK,CAAES,WAAY,CAAC6F,QAAQ,CAAE/D,eAAgB,CAAAjC,QAAA,eACxD/C,IAAA,CAACb,GAAG,EAAC6J,KAAK,CAAC,UAAU,CAAE,CAAC,cACxBhJ,IAAA,CAACb,GAAG,EAAC6J,KAAK,CAAC,WAAW,CAAE,CAAC,cACzBhJ,IAAA,CAACb,GAAG,EAAC6J,KAAK,CAAC,gBAAgB,CAAE,CAAC,cAC9BhJ,IAAA,CAACb,GAAG,EAAC6J,KAAK,CAAC,UAAU,CAAE,CAAC,EACd,CAAC,CAGZ9D,gBAAgB,CAAC,CAAC,EACV,CAAC,CACI,CAAC,CAGlB9B,OAAO,CAACE,OAAO,eACdpD,KAAA,CAACnB,GAAG,EACFyG,EAAE,CAAE,CACFG,QAAQ,CAAE,OAAO,CACjBnB,IAAI,CAAEpB,OAAO,CAACG,CAAC,CACfkB,GAAG,CAAErB,OAAO,CAACI,CAAC,CACdoE,SAAS,CAAE,wBAAwB,CACnCtH,eAAe,CAAE,OAAO,CACxBgI,MAAM,CAAE,mBAAmB,CAC3B1G,YAAY,CAAE,KAAK,CACnBL,OAAO,CAAE,UAAU,CACnBkH,SAAS,CAAE,uEAAuE,CAClFQ,MAAM,CAAE,KAAK,CACbC,aAAa,CAAE,MAAM,CACrBpI,QAAQ,CAAE,MAAM,CAChB6H,QAAQ,CAAE,MAAM,CAChBE,SAAS,CAAE,QAAQ,CACnBM,UAAU,CAAE,mEACd,CAAE,CAAApG,QAAA,eAEF/C,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACwC,EAAE,CAAE,CAAE1E,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAS,CAAEwE,EAAE,CAAE,GAAG,CAAE2D,UAAU,CAAE,GAAI,CAAE,CAAArG,QAAA,CAC9FK,OAAO,CAACZ,KAAK,CACJ,CAAC,cACbxC,IAAA,CAAChB,UAAU,EAACgE,OAAO,CAAC,OAAO,CAACwC,EAAE,CAAE,CAAE1E,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAE,SAAS,CAAEF,UAAU,CAAE,KAAK,CAAEqI,UAAU,CAAE,GAAI,CAAE,CAAArG,QAAA,CACxGK,OAAO,CAACK,OAAO,CACN,CAAC,EACV,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}