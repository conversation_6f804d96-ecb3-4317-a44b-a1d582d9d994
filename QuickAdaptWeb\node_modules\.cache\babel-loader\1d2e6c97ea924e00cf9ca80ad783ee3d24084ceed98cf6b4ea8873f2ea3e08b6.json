{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\dashboard\\\\ModernDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Container, Tabs, Tab } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { TrendingUp, TrendingDown, People, CheckCircle, Star, Schedule, FilterList, CalendarToday } from '@mui/icons-material';\nimport Card from '../common/Card';\nimport ModernButton from '../common/ModernButton';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DashboardWrapper = styled('div')({\n  backgroundColor: '#f6f9ff',\n  minHeight: '100vh'\n});\n_c = DashboardWrapper;\nconst HeaderSection = styled(Box)({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: 'var(--spacing-4)'\n});\n_c2 = HeaderSection;\nconst StyledTabs = styled(Tabs)({\n  marginBottom: 'var(--spacing-4)',\n  '& .MuiTabs-indicator': {\n    backgroundColor: 'var(--color-primary-600)'\n  },\n  '& .MuiTab-root': {\n    fontSize: 'var(--font-size-sm)',\n    fontWeight: 'var(--font-weight-medium)',\n    textTransform: 'none',\n    color: 'var(--color-gray-600)',\n    '&.Mui-selected': {\n      color: 'var(--color-primary-600)',\n      fontWeight: 'var(--font-weight-semibold)'\n    }\n  }\n});\n_c3 = StyledTabs;\nconst FilterSection = styled(Box)({\n  display: 'flex',\n  gap: 'var(--spacing-3)',\n  alignItems: 'center'\n});\n_c4 = FilterSection;\nconst MetricsGrid = styled(Box)({\n  display: 'grid',\n  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n  gap: 'var(--spacing-4)',\n  marginBottom: 'var(--spacing-6)'\n});\n_c5 = MetricsGrid;\nconst MetricCardContainer = styled(Card)({\n  padding: 'var(--spacing-5)',\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-4)'\n});\n_c6 = MetricCardContainer;\nconst MetricIcon = styled(Box)(({\n  color\n}) => ({\n  width: '48px',\n  height: '48px',\n  borderRadius: 'var(--radius-lg)',\n  backgroundColor: `${color}15`,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  '& svg': {\n    color: color,\n    fontSize: '24px'\n  }\n}));\n_c7 = MetricIcon;\nconst MetricContent = styled(Box)({\n  flex: 1\n});\n_c8 = MetricContent;\nconst MetricTitle = styled(Typography)({\n  fontSize: 'var(--font-size-sm)',\n  color: 'var(--color-gray-600)',\n  marginBottom: 'var(--spacing-1)'\n});\n_c9 = MetricTitle;\nconst MetricValue = styled(Typography)({\n  fontSize: 'var(--font-size-2xl)',\n  fontWeight: 'var(--font-weight-bold)',\n  color: 'var(--color-gray-900)',\n  marginBottom: 'var(--spacing-1)'\n});\n_c0 = MetricValue;\nconst MetricChange = styled(Box)({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)'\n});\n_c1 = MetricChange;\nconst ChangeIndicator = styled(Box)(({\n  trend\n}) => ({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)',\n  fontSize: 'var(--font-size-xs)',\n  fontWeight: 'var(--font-weight-medium)',\n  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',\n  '& svg': {\n    fontSize: '16px'\n  }\n}));\n_c10 = ChangeIndicator;\nconst MetricCard = ({\n  title,\n  value,\n  change,\n  changeValue,\n  trend,\n  icon,\n  color\n}) => {\n  return /*#__PURE__*/_jsxDEV(MetricCardContainer, {\n    shadow: \"sm\",\n    hover: true,\n    children: [/*#__PURE__*/_jsxDEV(MetricIcon, {\n      color: color,\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricContent, {\n      children: [/*#__PURE__*/_jsxDEV(MetricTitle, {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricChange, {\n        children: [/*#__PURE__*/_jsxDEV(ChangeIndicator, {\n          trend: trend,\n          children: [trend === 'up' ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 48\n          }, this), change]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: changeValue\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_c11 = MetricCard;\nconst ModernDashboard = () => {\n  _s();\n  const [selectedTab, setSelectedTab] = useState(0);\n  const [tooltip, setTooltip] = useState({\n    visible: false,\n    x: 0,\n    y: 0,\n    content: '',\n    title: ''\n  });\n  const showTooltip = (event, title, content) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n    setTooltip({\n      visible: true,\n      x: rect.left + scrollX + rect.width / 2,\n      y: rect.top + scrollY - 10,\n      content,\n      title\n    });\n  };\n  const hideTooltip = () => {\n    setTooltip(prev => ({\n      ...prev,\n      visible: false\n    }));\n  };\n  const overviewMetrics = [{\n    title: 'Completion Rate',\n    value: '87.3%',\n    change: '****%',\n    changeValue: '+2.8pp',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'User Satisfaction',\n    value: '4.6',\n    change: '+0.2',\n    changeValue: 'out of 5.0',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Hours Saved',\n    value: '2,847',\n    change: '+18.7%',\n    changeValue: '+447h',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }];\n  const analyticsMetrics = [{\n    title: 'Active Users',\n    value: '12,847',\n    change: '+12.5%',\n    changeValue: '+1,432',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }, {\n    title: 'Completion Rate',\n    value: '87.3%',\n    change: '****%',\n    changeValue: '+2.8pp',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'User Satisfaction',\n    value: '4.6',\n    change: '+0.2',\n    changeValue: 'out of 5.0',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Hours Saved',\n    value: '2,847',\n    change: '+18.7%',\n    changeValue: '+447h',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }];\n  const aiPerformanceMetrics = [{\n    title: 'AI Response Time',\n    value: '1.2s',\n    change: '-15%',\n    changeValue: 'faster',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'AI Accuracy',\n    value: '94.8%',\n    change: '+2.1%',\n    changeValue: 'improved',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }, {\n    title: 'Model Confidence',\n    value: '89.3%',\n    change: '+1.8%',\n    changeValue: 'higher',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Processing Load',\n    value: '67%',\n    change: '+5%',\n    changeValue: 'capacity',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-error-500)'\n  }];\n  const handleTabChange = (event, newValue) => {\n    setSelectedTab(newValue);\n  };\n  const renderTabContent = () => {\n    switch (selectedTab) {\n      case 0:\n        // Overview\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(HeaderSection, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                children: \"Dashboard Overview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FilterSection, {\n              children: [/*#__PURE__*/_jsxDEV(ModernButton, {\n                variant: \"outline\",\n                startIcon: /*#__PURE__*/_jsxDEV(FilterList, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 30\n                }, this),\n                size: \"sm\",\n                children: \"Filter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ModernButton, {\n                variant: \"outline\",\n                startIcon: /*#__PURE__*/_jsxDEV(CalendarToday, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 30\n                }, this),\n                size: \"sm\",\n                children: \"Last 30 days\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricsGrid, {\n            children: analyticsMetrics.map((metric, index) => /*#__PURE__*/_jsxDEV(MetricCard, {\n              ...metric\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDCC8 Growth Trends\",\n              subtitle: \"User engagement over the last 6 months\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  position: 'relative',\n                  backgroundColor: 'white',\n                  borderRadius: 'var(--radius-md)'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"100%\",\n                  height: \"280\",\n                  viewBox: \"0 0 450 250\",\n                  style: {\n                    overflow: 'visible'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n                    children: /*#__PURE__*/_jsxDEV(\"pattern\", {\n                      id: \"interactiveGrid\",\n                      width: \"75\",\n                      height: \"50\",\n                      patternUnits: \"userSpaceOnUse\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M 75 0 L 0 0 0 50\",\n                        fill: \"none\",\n                        stroke: \"#f1f5f9\",\n                        strokeWidth: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    width: \"100%\",\n                    height: \"200\",\n                    fill: \"url(#interactiveGrid)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"50\",\n                    y1: \"20\",\n                    x2: \"50\",\n                    y2: \"200\",\n                    stroke: \"#e2e8f0\",\n                    strokeWidth: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"50\",\n                    y1: \"200\",\n                    x2: \"400\",\n                    y2: \"200\",\n                    stroke: \"#e2e8f0\",\n                    strokeWidth: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"25\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"14000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"75\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"10500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"125\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"7000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"175\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"3500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"205\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"80\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Jan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"140\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Feb\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"200\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Mar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"260\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Apr\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"320\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"May\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"380\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Jun\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n                    children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                      id: \"areaGradient\",\n                      x1: \"0%\",\n                      y1: \"0%\",\n                      x2: \"0%\",\n                      y2: \"100%\",\n                      children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                        offset: \"0%\",\n                        stopColor: \"#3b82f6\",\n                        stopOpacity: \"0.3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 380,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                        offset: \"100%\",\n                        stopColor: \"#3b82f6\",\n                        stopOpacity: \"0.05\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70 L 380 200 L 80 200 Z\",\n                    fill: \"url(#areaGradient)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70\",\n                    fill: \"none\",\n                    stroke: \"#3b82f6\",\n                    strokeWidth: \"3\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n                    className: \"data-points\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"80\",\n                      cy: \"160\",\n                      r: \"8\",\n                      fill: \"white\",\n                      stroke: \"#3b82f6\",\n                      strokeWidth: \"3\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'r 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Jan point');\n                        showTooltip(e, 'Jan', '8,500 users');\n                        e.currentTarget.setAttribute('r', '10');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('r', '8');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 391,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"140\",\n                      cy: \"150\",\n                      r: \"8\",\n                      fill: \"white\",\n                      stroke: \"#3b82f6\",\n                      strokeWidth: \"3\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'r 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Feb point');\n                        showTooltip(e, 'Feb', '9,200 users');\n                        e.currentTarget.setAttribute('r', '10');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('r', '8');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"200\",\n                      cy: \"130\",\n                      r: \"8\",\n                      fill: \"white\",\n                      stroke: \"#3b82f6\",\n                      strokeWidth: \"3\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'r 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Mar point');\n                        showTooltip(e, 'Mar', '10,100 users');\n                        e.currentTarget.setAttribute('r', '10');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('r', '8');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"260\",\n                      cy: \"110\",\n                      r: \"8\",\n                      fill: \"white\",\n                      stroke: \"#3b82f6\",\n                      strokeWidth: \"3\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'r 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Apr point');\n                        showTooltip(e, 'Apr', '11,100 users');\n                        e.currentTarget.setAttribute('r', '10');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('r', '8');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"320\",\n                      cy: \"90\",\n                      r: \"8\",\n                      fill: \"white\",\n                      stroke: \"#3b82f6\",\n                      strokeWidth: \"3\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'r 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering May point');\n                        showTooltip(e, 'May', '12,300 users');\n                        e.currentTarget.setAttribute('r', '10');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('r', '8');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"380\",\n                      cy: \"70\",\n                      r: \"8\",\n                      fill: \"white\",\n                      stroke: \"#3b82f6\",\n                      strokeWidth: \"3\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'r 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Jun point');\n                        showTooltip(e, 'Jun', '13,200 users');\n                        e.currentTarget.setAttribute('r', '10');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('r', '8');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDE0A User Satisfaction\",\n              subtitle: \"Rating distribution this month\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  display: 'flex',\n                  justifyContent: 'center',\n                  alignItems: 'center',\n                  backgroundColor: 'white',\n                  borderRadius: 'var(--radius-md)',\n                  position: 'relative'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"220\",\n                  height: \"220\",\n                  viewBox: \"0 0 220 220\",\n                  children: [/*#__PURE__*/_jsxDEV(\"g\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"110\",\n                      cy: \"110\",\n                      r: \"75\",\n                      fill: \"none\",\n                      stroke: \"#10b981\",\n                      strokeWidth: \"30\",\n                      strokeDasharray: \"305 470\",\n                      strokeDashoffset: \"0\",\n                      transform: \"rotate(-90 110 110)\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'stroke-width 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Excellent segment');\n                        showTooltip(e, 'Excellent (5★)', '65%');\n                        e.currentTarget.setAttribute('stroke-width', '35');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('stroke-width', '30');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"110\",\n                      cy: \"110\",\n                      r: \"75\",\n                      fill: \"none\",\n                      stroke: \"#84cc16\",\n                      strokeWidth: \"30\",\n                      strokeDasharray: \"94 470\",\n                      strokeDashoffset: \"-305\",\n                      transform: \"rotate(-90 110 110)\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'stroke-width 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Good segment');\n                        showTooltip(e, 'Good (4★)', '20%');\n                        e.currentTarget.setAttribute('stroke-width', '35');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('stroke-width', '30');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"110\",\n                      cy: \"110\",\n                      r: \"75\",\n                      fill: \"none\",\n                      stroke: \"#f59e0b\",\n                      strokeWidth: \"30\",\n                      strokeDasharray: \"47 470\",\n                      strokeDashoffset: \"-399\",\n                      transform: \"rotate(-90 110 110)\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'stroke-width 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Average segment');\n                        showTooltip(e, 'Average (3★)', '10%');\n                        e.currentTarget.setAttribute('stroke-width', '35');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('stroke-width', '30');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"110\",\n                      cy: \"110\",\n                      r: \"75\",\n                      fill: \"none\",\n                      stroke: \"#f97316\",\n                      strokeWidth: \"30\",\n                      strokeDasharray: \"14 470\",\n                      strokeDashoffset: \"-446\",\n                      transform: \"rotate(-90 110 110)\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'stroke-width 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Poor segment');\n                        showTooltip(e, 'Poor (2★)', '3%');\n                        e.currentTarget.setAttribute('stroke-width', '35');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('stroke-width', '30');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"110\",\n                      cy: \"110\",\n                      r: \"75\",\n                      fill: \"none\",\n                      stroke: \"#ef4444\",\n                      strokeWidth: \"30\",\n                      strokeDasharray: \"9 470\",\n                      strokeDashoffset: \"-460\",\n                      transform: \"rotate(-90 110 110)\",\n                      style: {\n                        cursor: 'pointer',\n                        transition: 'stroke-width 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        console.log('Hovering Very Poor segment');\n                        showTooltip(e, 'Very Poor (1★)', '2%');\n                        e.currentTarget.setAttribute('stroke-width', '35');\n                      },\n                      onMouseLeave: e => {\n                        hideTooltip();\n                        e.currentTarget.setAttribute('stroke-width', '30');\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"110\",\n                    cy: \"110\",\n                    r: \"45\",\n                    fill: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u2B50 User Satisfaction Ratings\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 'var(--spacing-4)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#10b981',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 640,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Excellent (5\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: '#10b981',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"1,247\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#84cc16',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Good (4\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '45%',\n                        height: '100%',\n                        backgroundColor: '#84cc16',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"892\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#f59e0b',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 696,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Average (3\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 697,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '22%',\n                        height: '100%',\n                        backgroundColor: '#f59e0b',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"434\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#f97316',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Poor (2\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '8%',\n                        height: '100%',\n                        backgroundColor: '#f97316',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 737,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"123\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#ef4444',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 752,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Very Poor (1\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '4%',\n                        height: '100%',\n                        backgroundColor: '#ef4444',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 765,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 757,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"57\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(3, 1fr)',\n                    gap: 2,\n                    mt: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#f0fdf4',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#16a34a\",\n                      children: \"77%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 785,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#16a34a\",\n                      children: \"Positive\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 788,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#fffbeb',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#d97706\",\n                      children: \"16%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 798,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#d97706\",\n                      children: \"Neutral\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 801,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#fef2f2',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#dc2626\",\n                      children: \"7%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 811,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#dc2626\",\n                      children: \"Negative\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 778,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDCC8 Satisfaction Trend\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center',\n                  alignItems: 'center',\n                  backgroundColor: '#f8fafc',\n                  borderRadius: 'var(--radius-md)',\n                  position: 'relative'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"100%\",\n                  height: \"250\",\n                  viewBox: \"0 0 400 200\",\n                  style: {\n                    overflow: 'visible'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n                    children: /*#__PURE__*/_jsxDEV(\"pattern\", {\n                      id: \"feedbackGrid\",\n                      width: \"66.67\",\n                      height: \"40\",\n                      patternUnits: \"userSpaceOnUse\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M 66.67 0 L 0 0 0 40\",\n                        fill: \"none\",\n                        stroke: \"#e2e8f0\",\n                        strokeWidth: \"0.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 830,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 829,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    width: \"100%\",\n                    height: \"100%\",\n                    fill: \"url(#feedbackGrid)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"20\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 836,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"60\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.75\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"100\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"140\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.25\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 839,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"180\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 840,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"50\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Jan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 843,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"110\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Feb\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"170\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Mar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"230\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Apr\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"290\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"May\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"350\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Jun\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88\",\n                    fill: \"none\",\n                    stroke: \"#3b82f6\",\n                    strokeWidth: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 851,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"168\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"110\",\n                    cy: \"152\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"170\",\n                    cy: \"136\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"230\",\n                    cy: \"120\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"290\",\n                    cy: \"104\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"350\",\n                    cy: \"88\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"50\",\n                    y: \"160\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"110\",\n                    y: \"144\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"170\",\n                    y: \"128\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"230\",\n                    y: \"112\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"290\",\n                    y: \"96\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"350\",\n                    y: \"80\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\uD83D\\uDCCA Feedback Summary\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(4, 1fr)',\n                gap: 'var(--spacing-6)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f8fafc',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"text.primary\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"2,238\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Feedback\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0fdf4',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#16a34a\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"85.8%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Positive Sentiment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#eff6ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#2563eb\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"4.6/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Average Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 916,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#fdf4ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#9333ea\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"+12%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 928,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"vs Last Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"Recent Feedback\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 'var(--spacing-4)'\n              },\n              children: [{\n                user: 'JD',\n                action: '5-star rating received',\n                name: 'John Doe',\n                time: '5 min ago',\n                type: 'completion'\n              }, {\n                user: 'SM',\n                action: 'Improvement suggestion submitted',\n                name: 'Sarah Miller',\n                time: '20 min ago',\n                type: 'create'\n              }, {\n                user: 'RW',\n                action: 'Bug report submitted',\n                name: 'Robert Wilson',\n                time: '45 min ago',\n                type: 'alert'\n              }, {\n                user: 'LB',\n                action: 'Feature request submitted',\n                name: 'Lisa Brown',\n                time: '1 hour ago',\n                type: 'update'\n              }].map((activity, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 'var(--spacing-3)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '40px',\n                    height: '40px',\n                    borderRadius: '50%',\n                    backgroundColor: 'var(--color-primary-100)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: 'var(--font-size-sm)',\n                    fontWeight: 'var(--font-weight-semibold)',\n                    color: 'var(--color-primary-700)'\n                  },\n                  children: activity.user\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: activity.action\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 963,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [activity.name, \" \\u2022 \", activity.time]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 940,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 1:\n        // Analytics\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"Guide Performance Overview\",\n            subtitle: \"Click on any guide to see detailed funnel analysis\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 'var(--spacing-4)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"semibold\",\n                      children: \"Product Onboarding\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1004,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e8f5e9',\n                        color: '#2e7d32',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      },\n                      children: \"excellent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1007,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      },\n                      children: \"Onboarding\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1018,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 3,\n                      color: 'var(--color-gray-600)',\n                      fontSize: '14px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 4,\n                          height: 4,\n                          borderRadius: '50%',\n                          backgroundColor: 'var(--color-gray-400)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1031,\n                        columnNumber: 25\n                      }, this), \"1,400 views\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1030,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        sx: {\n                          fontSize: 16,\n                          color: 'var(--color-success-500)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1035,\n                        columnNumber: 25\n                      }, this), \"1,247 completed\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        color: 'var(--color-success-600)',\n                        fontWeight: 'medium'\n                      },\n                      children: \"11% drop-off\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1038,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      children: \"Updated 2 days ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1041,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: \"89%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1047,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '89%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1057,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1050,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"semibold\",\n                      children: \"Feature Discovery\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1082,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e3f2fd',\n                        color: '#1976d2',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      },\n                      children: \"good\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1085,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      },\n                      children: \"Feature\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1096,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1081,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 3,\n                      color: 'var(--color-gray-600)',\n                      fontSize: '14px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 4,\n                          height: 4,\n                          borderRadius: '50%',\n                          backgroundColor: 'var(--color-gray-400)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1109,\n                        columnNumber: 25\n                      }, this), \"1,174 views\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1108,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        sx: {\n                          fontSize: 16,\n                          color: 'var(--color-success-500)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1113,\n                        columnNumber: 25\n                      }, this), \"892 completed\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1112,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        color: 'var(--color-warning-600)',\n                        fontWeight: 'medium'\n                      },\n                      children: \"24% drop-off\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1116,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      children: \"Updated 1 day ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1119,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1107,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: \"76%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1125,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '76%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1135,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1128,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1124,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"semibold\",\n                      children: \"Advanced Settings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1160,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#fff3e0',\n                        color: '#f57c00',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      },\n                      children: \"needs attention\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1163,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      },\n                      children: \"Configuration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1174,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1159,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 3,\n                      color: 'var(--color-gray-600)',\n                      fontSize: '14px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 4,\n                          height: 4,\n                          borderRadius: '50%',\n                          backgroundColor: 'var(--color-gray-400)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1187,\n                        columnNumber: 25\n                      }, this), \"962 views\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1186,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        sx: {\n                          fontSize: 16,\n                          color: 'var(--color-success-500)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1191,\n                        columnNumber: 25\n                      }, this), \"634 completed\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1190,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        color: 'var(--color-error-600)',\n                        fontWeight: 'medium'\n                      },\n                      children: \"32% drop-off\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1194,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      children: \"Updated 5 days ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1197,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1185,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1158,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: \"65%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1203,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1213,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1206,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1202,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 988,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 987,\n            columnNumber: 13\n          }, this)\n        }, void 0, false);\n      case 2:\n        // AI Performance\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(3, 1fr)',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#3b82f6\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Total Interactions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e3f2fd',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#3b82f6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1255,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1246,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"2,847\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"+12% from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#10b981\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Success Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e8f5e9',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#10b981',\n                      borderRadius: '50%',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1301,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1292,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"91%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"+3% improvement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#8b5cf6\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Avg Response Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1335,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#f3e8ff',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#8b5cf6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\u26A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1347,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1338,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"1.9s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"-0.3s faster\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1366,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1326,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"AI Task Performance\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 'var(--spacing-4)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Password Reset\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1391,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e8f5e9',\n                          color: '#2e7d32',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"96%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1394,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"342 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1405,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1390,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 1.2s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1410,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-success-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"+2% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1413,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1409,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"96%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1419,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '96%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1429,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1422,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1418,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1377,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Account Setup\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1454,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e3f2fd',\n                          color: '#1976d2',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"89%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1457,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"198 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1468,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1453,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 1.4s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1473,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-5% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1476,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1472,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1452,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"89%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1482,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '89%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1492,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1485,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1481,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1440,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Feature Explanation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1517,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e3f2fd',\n                          color: '#1976d2',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"90%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1520,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"267 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1531,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1516,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 2.1s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1536,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-1% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1539,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1535,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1515,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"90%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1545,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '90%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1555,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1548,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1544,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1503,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Troubleshooting\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1580,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#fff3e0',\n                          color: '#f57c00',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"88%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1583,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"156 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1594,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1579,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 3.1s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1599,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-3% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1602,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1598,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1578,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"88%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1608,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '88%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1618,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1611,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1607,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1566,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Integration Help\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1643,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#fff3e0',\n                          color: '#f57c00',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"87%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1646,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"123 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1657,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1642,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 2.5s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1662,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-success-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"+1% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1665,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1661,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1641,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"87%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1671,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '87%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1681,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1674,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1670,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1629,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1375,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1374,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"AI Insights & Recommendations\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 'var(--spacing-3)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#fffbeb',\n                  border: '1px solid #fbbf24',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#f59e0b',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1707,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Optimize Workflow\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1715,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Consider optimizing your workflow to reduce response time by 15%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1718,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1714,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1698,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1734,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Excluded Performance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1742,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Excluded tasks are performing well with 94% accuracy rate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1745,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1741,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1725,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1761,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Personalized Suggestions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1769,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"AI suggests implementing advanced filtering for better user experience\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1772,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1768,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1752,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1696,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1695,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-web\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-webcontent\",\n      children: [/*#__PURE__*/_jsxDEV(DashboardWrapper, {\n        children: /*#__PURE__*/_jsxDEV(Container, {\n          maxWidth: \"xl\",\n          sx: {\n            py: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(StyledTabs, {\n            value: selectedTab,\n            onChange: handleTabChange,\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1797,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"AI Performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1798,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1795,\n            columnNumber: 13\n          }, this), renderTabContent()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1793,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1792,\n        columnNumber: 9\n      }, this), tooltip.visible && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          left: tooltip.x,\n          top: tooltip.y,\n          transform: 'translate(-50%, -100%)',\n          backgroundColor: 'white',\n          border: '1px solid #d1d5db',\n          borderRadius: '6px',\n          padding: '6px 10px',\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n          zIndex: 10000,\n          pointerEvents: 'none',\n          fontSize: '11px',\n          minWidth: '70px',\n          textAlign: 'center',\n          fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontSize: '10px',\n            color: '#6b7280',\n            mb: 0.2,\n            lineHeight: 1.2\n          },\n          children: tooltip.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1827,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontSize: '11px',\n            color: '#111827',\n            fontWeight: '600',\n            lineHeight: 1.2\n          },\n          children: tooltip.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1830,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1808,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1791,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1790,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernDashboard, \"yT1d7MwnA1evZ1LMxG43x4THgP8=\");\n_c12 = ModernDashboard;\nexport default ModernDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"DashboardWrapper\");\n$RefreshReg$(_c2, \"HeaderSection\");\n$RefreshReg$(_c3, \"StyledTabs\");\n$RefreshReg$(_c4, \"FilterSection\");\n$RefreshReg$(_c5, \"MetricsGrid\");\n$RefreshReg$(_c6, \"MetricCardContainer\");\n$RefreshReg$(_c7, \"MetricIcon\");\n$RefreshReg$(_c8, \"MetricContent\");\n$RefreshReg$(_c9, \"MetricTitle\");\n$RefreshReg$(_c0, \"MetricValue\");\n$RefreshReg$(_c1, \"MetricChange\");\n$RefreshReg$(_c10, \"ChangeIndicator\");\n$RefreshReg$(_c11, \"MetricCard\");\n$RefreshReg$(_c12, \"ModernDashboard\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Container", "Tabs", "Tab", "styled", "TrendingUp", "TrendingDown", "People", "CheckCircle", "Star", "Schedule", "FilterList", "CalendarToday", "Card", "ModernButton", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DashboardWrapper", "backgroundColor", "minHeight", "_c", "HeaderSection", "display", "justifyContent", "alignItems", "marginBottom", "_c2", "StyledTabs", "fontSize", "fontWeight", "textTransform", "color", "_c3", "FilterSection", "gap", "_c4", "MetricsGrid", "gridTemplateColumns", "_c5", "MetricCardContainer", "padding", "_c6", "MetricIcon", "width", "height", "borderRadius", "_c7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flex", "_c8", "MetricTitle", "_c9", "MetricValue", "_c0", "MetricChange", "_c1", "ChangeIndicator", "trend", "_c10", "MetricCard", "title", "value", "change", "changeValue", "icon", "shadow", "hover", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "_c11", "ModernDashboard", "_s", "selectedTab", "setSelectedTab", "tooltip", "setTooltip", "visible", "x", "y", "content", "showTooltip", "event", "rect", "currentTarget", "getBoundingClientRect", "scrollX", "window", "pageXOffset", "document", "documentElement", "scrollLeft", "scrollY", "pageYOffset", "scrollTop", "left", "top", "hideTooltip", "prev", "overviewMetrics", "analyticsMetrics", "aiPerformanceMetrics", "handleTabChange", "newValue", "renderTabContent", "startIcon", "size", "map", "metric", "index", "sx", "mb", "subtitle", "position", "viewBox", "style", "overflow", "id", "patternUnits", "d", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "textAnchor", "offset", "stopColor", "stopOpacity", "strokeLinecap", "className", "cx", "cy", "r", "cursor", "transition", "onMouseEnter", "e", "console", "log", "setAttribute", "onMouseLeave", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "transform", "flexDirection", "min<PERSON><PERSON><PERSON>", "mr", "textAlign", "mt", "p", "user", "action", "name", "time", "type", "activity", "border", "px", "py", "boxShadow", "max<PERSON><PERSON><PERSON>", "onChange", "label", "zIndex", "pointerEvents", "fontFamily", "lineHeight", "_c12", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/dashboard/ModernDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Box, Typography, IconButton, Container, Tabs, Tab } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport {\n  TrendingUp,\n  TrendingDown,\n  People,\n  CheckCircle,\n  Star,\n  Schedule,\n  FilterList,\n  CalendarToday\n} from '@mui/icons-material';\nimport Card from '../common/Card';\nimport ModernButton from '../common/ModernButton';\nimport ChartPlaceholder from './ChartPlaceholder';\n\ninterface MetricCardProps {\n  title: string;\n  value: string;\n  change: string;\n  changeValue: string;\n  trend: 'up' | 'down';\n  icon: React.ReactNode;\n  color: string;\n}\n\nconst DashboardWrapper = styled('div')({\n  backgroundColor: '#f6f9ff',\n  minHeight: '100vh',\n});\n\nconst HeaderSection = styled(Box)({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: 'var(--spacing-4)',\n});\n\nconst StyledTabs = styled(Tabs)({\n  marginBottom: 'var(--spacing-4)',\n  '& .MuiTabs-indicator': {\n    backgroundColor: 'var(--color-primary-600)',\n  },\n  '& .MuiTab-root': {\n    fontSize: 'var(--font-size-sm)',\n    fontWeight: 'var(--font-weight-medium)',\n    textTransform: 'none',\n    color: 'var(--color-gray-600)',\n    '&.Mui-selected': {\n      color: 'var(--color-primary-600)',\n      fontWeight: 'var(--font-weight-semibold)',\n    },\n  },\n});\n\nconst FilterSection = styled(Box)({\n  display: 'flex',\n  gap: 'var(--spacing-3)',\n  alignItems: 'center',\n});\n\nconst MetricsGrid = styled(Box)({\n  display: 'grid',\n  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n  gap: 'var(--spacing-4)',\n  marginBottom: 'var(--spacing-6)',\n});\n\nconst MetricCardContainer = styled(Card)({\n  padding: 'var(--spacing-5)',\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-4)',\n});\n\nconst MetricIcon = styled(Box)<{ color: string }>(({ color }) => ({\n  width: '48px',\n  height: '48px',\n  borderRadius: 'var(--radius-lg)',\n  backgroundColor: `${color}15`,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  \n  '& svg': {\n    color: color,\n    fontSize: '24px',\n  },\n}));\n\nconst MetricContent = styled(Box)({\n  flex: 1,\n});\n\nconst MetricTitle = styled(Typography)({\n  fontSize: 'var(--font-size-sm)',\n  color: 'var(--color-gray-600)',\n  marginBottom: 'var(--spacing-1)',\n});\n\nconst MetricValue = styled(Typography)({\n  fontSize: 'var(--font-size-2xl)',\n  fontWeight: 'var(--font-weight-bold)',\n  color: 'var(--color-gray-900)',\n  marginBottom: 'var(--spacing-1)',\n});\n\nconst MetricChange = styled(Box)({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)',\n});\n\nconst ChangeIndicator = styled(Box)<{ trend: 'up' | 'down' }>(({ trend }) => ({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)',\n  fontSize: 'var(--font-size-xs)',\n  fontWeight: 'var(--font-weight-medium)',\n  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',\n  \n  '& svg': {\n    fontSize: '16px',\n  },\n}));\n\nconst MetricCard: React.FC<MetricCardProps> = ({\n  title,\n  value,\n  change,\n  changeValue,\n  trend,\n  icon,\n  color,\n}) => {\n  return (\n    <MetricCardContainer shadow=\"sm\" hover>\n      <MetricIcon color={color}>\n        {icon}\n      </MetricIcon>\n      <MetricContent>\n        <MetricTitle>{title}</MetricTitle>\n        <MetricValue>{value}</MetricValue>\n        <MetricChange>\n          <ChangeIndicator trend={trend}>\n            {trend === 'up' ? <TrendingUp /> : <TrendingDown />}\n            {change}\n          </ChangeIndicator>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            {changeValue}\n          </Typography>\n        </MetricChange>\n      </MetricContent>\n    </MetricCardContainer>\n  );\n};\n\nconst ModernDashboard: React.FC = () => {\n  const [selectedTab, setSelectedTab] = useState(0);\n  const [tooltip, setTooltip] = useState<{\n    visible: boolean;\n    x: number;\n    y: number;\n    content: string;\n    title: string;\n  }>({\n    visible: false,\n    x: 0,\n    y: 0,\n    content: '',\n    title: ''\n  });\n\n  const showTooltip = (event: React.MouseEvent, title: string, content: string) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n\n    setTooltip({\n      visible: true,\n      x: rect.left + scrollX + rect.width / 2,\n      y: rect.top + scrollY - 10,\n      content,\n      title\n    });\n  };\n\n  const hideTooltip = () => {\n    setTooltip(prev => ({ ...prev, visible: false }));\n  };\n\n  const overviewMetrics = [\n    {\n      title: 'Completion Rate',\n      value: '87.3%',\n      change: '****%',\n      changeValue: '+2.8pp',\n      trend: 'up' as const,\n      icon: <CheckCircle />,\n      color: 'var(--color-success-600)',\n    },\n    {\n      title: 'User Satisfaction',\n      value: '4.6',\n      change: '+0.2',\n      changeValue: 'out of 5.0',\n      trend: 'up' as const,\n      icon: <Star />,\n      color: 'var(--color-warning-600)',\n    },\n    {\n      title: 'Hours Saved',\n      value: '2,847',\n      change: '+18.7%',\n      changeValue: '+447h',\n      trend: 'up' as const,\n      icon: <Schedule />,\n      color: 'var(--color-primary-600)',\n    },\n  ];\n\n  const analyticsMetrics = [\n    {\n      title: 'Active Users',\n      value: '12,847',\n      change: '+12.5%',\n      changeValue: '+1,432',\n      trend: 'up' as const,\n      icon: <People />,\n      color: 'var(--color-primary-600)',\n    },\n    {\n      title: 'Completion Rate',\n      value: '87.3%',\n      change: '****%',\n      changeValue: '+2.8pp',\n      trend: 'up' as const,\n      icon: <CheckCircle />,\n      color: 'var(--color-success-600)',\n    },\n    {\n      title: 'User Satisfaction',\n      value: '4.6',\n      change: '+0.2',\n      changeValue: 'out of 5.0',\n      trend: 'up' as const,\n      icon: <Star />,\n      color: 'var(--color-warning-600)',\n    },\n    {\n      title: 'Hours Saved',\n      value: '2,847',\n      change: '+18.7%',\n      changeValue: '+447h',\n      trend: 'up' as const,\n      icon: <Schedule />,\n      color: 'var(--color-primary-600)',\n    },\n  ];\n\n  const aiPerformanceMetrics = [\n    {\n      title: 'AI Response Time',\n      value: '1.2s',\n      change: '-15%',\n      changeValue: 'faster',\n      trend: 'up' as const,\n      icon: <Schedule />,\n      color: 'var(--color-success-600)',\n    },\n    {\n      title: 'AI Accuracy',\n      value: '94.8%',\n      change: '+2.1%',\n      changeValue: 'improved',\n      trend: 'up' as const,\n      icon: <CheckCircle />,\n      color: 'var(--color-primary-600)',\n    },\n    {\n      title: 'Model Confidence',\n      value: '89.3%',\n      change: '+1.8%',\n      changeValue: 'higher',\n      trend: 'up' as const,\n      icon: <Star />,\n      color: 'var(--color-warning-600)',\n    },\n    {\n      title: 'Processing Load',\n      value: '67%',\n      change: '+5%',\n      changeValue: 'capacity',\n      trend: 'up' as const,\n      icon: <People />,\n      color: 'var(--color-error-500)',\n    },\n  ];\n\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\n    setSelectedTab(newValue);\n  };\n\n  const renderTabContent = () => {\n    switch (selectedTab) {\n      case 0: // Overview\n        return (\n          <>\n            {/* Header with Filters */}\n            <HeaderSection>\n              <Box>\n                <Typography variant=\"h4\" fontWeight=\"bold\" color=\"text.primary\">\n                  Dashboard Overview\n                </Typography>\n              </Box>\n              <FilterSection>\n                <ModernButton\n                  variant=\"outline\"\n                  startIcon={<FilterList />}\n                  size=\"sm\"\n                >\n                  Filter\n                </ModernButton>\n                <ModernButton\n                  variant=\"outline\"\n                  startIcon={<CalendarToday />}\n                  size=\"sm\"\n                >\n                  Last 30 days\n                </ModernButton>\n              </FilterSection>\n            </HeaderSection>\n\n            {/* Overview Metrics Grid (4 cards like Analytics) */}\n            <MetricsGrid>\n              {analyticsMetrics.map((metric, index) => (\n                <MetricCard key={index} {...metric} />\n              ))}\n            </MetricsGrid>\n\n            {/* Overview Charts - Growth Trends and User Satisfaction */}\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>\n              {/* Growth Trends Chart */}\n              <Card title=\"📈 Growth Trends\" subtitle=\"User engagement over the last 6 months\" padding=\"lg\">\n                <Box sx={{ height: '300px', position: 'relative', backgroundColor: 'white', borderRadius: 'var(--radius-md)' }}>\n                  {/* Interactive Line Chart */}\n                  <svg width=\"100%\" height=\"280\" viewBox=\"0 0 450 250\" style={{ overflow: 'visible' }}>\n                    {/* Grid lines */}\n                    <defs>\n                      <pattern id=\"interactiveGrid\" width=\"75\" height=\"50\" patternUnits=\"userSpaceOnUse\">\n                        <path d=\"M 75 0 L 0 0 0 50\" fill=\"none\" stroke=\"#f1f5f9\" strokeWidth=\"1\"/>\n                      </pattern>\n                    </defs>\n                    <rect width=\"100%\" height=\"200\" fill=\"url(#interactiveGrid)\" />\n\n                    {/* Y-axis */}\n                    <line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"200\" stroke=\"#e2e8f0\" strokeWidth=\"1\"/>\n                    {/* X-axis */}\n                    <line x1=\"50\" y1=\"200\" x2=\"400\" y2=\"200\" stroke=\"#e2e8f0\" strokeWidth=\"1\"/>\n\n                    {/* Y-axis labels */}\n                    <text x=\"40\" y=\"25\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">14000</text>\n                    <text x=\"40\" y=\"75\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">10500</text>\n                    <text x=\"40\" y=\"125\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">7000</text>\n                    <text x=\"40\" y=\"175\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">3500</text>\n                    <text x=\"40\" y=\"205\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">0</text>\n\n                    {/* X-axis labels */}\n                    <text x=\"80\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Jan</text>\n                    <text x=\"140\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Feb</text>\n                    <text x=\"200\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Mar</text>\n                    <text x=\"260\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Apr</text>\n                    <text x=\"320\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">May</text>\n                    <text x=\"380\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Jun</text>\n\n                    {/* Area fill with gradient */}\n                    <defs>\n                      <linearGradient id=\"areaGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n                        <stop offset=\"0%\" stopColor=\"#3b82f6\" stopOpacity=\"0.3\"/>\n                        <stop offset=\"100%\" stopColor=\"#3b82f6\" stopOpacity=\"0.05\"/>\n                      </linearGradient>\n                    </defs>\n                    <path d=\"M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70 L 380 200 L 80 200 Z\" fill=\"url(#areaGradient)\"/>\n\n                    {/* Main line */}\n                    <path d=\"M 80 160 L 140 150 L 200 130 L 260 110 L 320 90 L 380 70\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"3\" strokeLinecap=\"round\"/>\n\n                    {/* Interactive data points */}\n                    <g className=\"data-points\">\n                      <circle\n                        cx=\"80\" cy=\"160\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Jan point');\n                          showTooltip(e, 'Jan', '8,500 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"140\" cy=\"150\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Feb point');\n                          showTooltip(e, 'Feb', '9,200 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"200\" cy=\"130\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Mar point');\n                          showTooltip(e, 'Mar', '10,100 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"260\" cy=\"110\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Apr point');\n                          showTooltip(e, 'Apr', '11,100 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"320\" cy=\"90\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering May point');\n                          showTooltip(e, 'May', '12,300 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                      <circle\n                        cx=\"380\" cy=\"70\" r=\"8\" fill=\"white\" stroke=\"#3b82f6\" strokeWidth=\"3\"\n                        style={{ cursor: 'pointer', transition: 'r 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Jun point');\n                          showTooltip(e, 'Jun', '13,200 users');\n                          e.currentTarget.setAttribute('r', '10');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('r', '8');\n                        }}\n                      />\n                    </g>\n                  </svg>\n                </Box>\n              </Card>\n\n              {/* User Satisfaction Donut Chart */}\n              <Card title=\"😊 User Satisfaction\" subtitle=\"Rating distribution this month\" padding=\"lg\">\n                <Box sx={{ height: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', borderRadius: 'var(--radius-md)', position: 'relative' }}>\n                  {/* Interactive Donut Chart */}\n                  <svg width=\"220\" height=\"220\" viewBox=\"0 0 220 220\">\n                    {/* Donut segments with hover effects */}\n                    <g>\n                      {/* Excellent (5★) - 65% - Green */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#10b981\" strokeWidth=\"30\"\n                        strokeDasharray=\"305 470\" strokeDashoffset=\"0\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Excellent segment');\n                          showTooltip(e, 'Excellent (5★)', '65%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n\n                      {/* Good (4★) - 20% - Light Green */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#84cc16\" strokeWidth=\"30\"\n                        strokeDasharray=\"94 470\" strokeDashoffset=\"-305\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Good segment');\n                          showTooltip(e, 'Good (4★)', '20%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n\n                      {/* Average (3★) - 10% - Orange */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#f59e0b\" strokeWidth=\"30\"\n                        strokeDasharray=\"47 470\" strokeDashoffset=\"-399\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Average segment');\n                          showTooltip(e, 'Average (3★)', '10%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n\n                      {/* Poor (2★) - 3% - Orange-Red */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#f97316\" strokeWidth=\"30\"\n                        strokeDasharray=\"14 470\" strokeDashoffset=\"-446\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Poor segment');\n                          showTooltip(e, 'Poor (2★)', '3%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n\n                      {/* Very Poor (1★) - 2% - Red */}\n                      <circle\n                        cx=\"110\" cy=\"110\" r=\"75\" fill=\"none\" stroke=\"#ef4444\" strokeWidth=\"30\"\n                        strokeDasharray=\"9 470\" strokeDashoffset=\"-460\" transform=\"rotate(-90 110 110)\"\n                        style={{ cursor: 'pointer', transition: 'stroke-width 0.2s ease' }}\n                        onMouseEnter={(e) => {\n                          console.log('Hovering Very Poor segment');\n                          showTooltip(e, 'Very Poor (1★)', '2%');\n                          e.currentTarget.setAttribute('stroke-width', '35');\n                        }}\n                        onMouseLeave={(e) => {\n                          hideTooltip();\n                          e.currentTarget.setAttribute('stroke-width', '30');\n                        }}\n                      />\n                    </g>\n\n                    {/* Center circle for donut effect */}\n                    <circle cx=\"110\" cy=\"110\" r=\"45\" fill=\"white\"/>\n                  </svg>\n                </Box>\n              </Card>\n            </Box>\n\n            {/* Quick Actions and Recent Activity - Same layout as Analytics */}\n            {/* <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: 'var(--spacing-6)', mb: 4 }}>\n             \n              <Card title=\"Quick Actions\" subtitle=\"Take action based on your dashboard insights\" padding=\"lg\">\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>\n                  <ModernButton variant=\"primary\" fullWidth>\n                    Create New Guide\n                  </ModernButton>\n                  <ModernButton variant=\"outline\" fullWidth>\n                    View Full Analytics\n                  </ModernButton>\n                  <ModernButton variant=\"outline\" fullWidth>\n                    AI Assistant Settings\n                  </ModernButton>\n                </Box>\n              </Card>\n\n              \n              <Card title=\"Recent Activity\" padding=\"lg\">\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                  {[\n                    { user: 'SC', action: 'New guide created', name: 'Sarah Chen', time: '2 min ago', type: 'create' },\n                    { user: 'S', action: 'Guide completed 47 times', name: 'System', time: '15 min ago', type: 'completion' },\n                    { user: 'MJ', action: 'AI response updated', name: 'Mike Johnson', time: '1 hour ago', type: 'update' },\n                    { user: 'S', action: 'Low performance alert', name: 'System', time: '2 hours ago', type: 'alert' },\n                  ].map((activity, index) => (\n                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-3)' }}>\n                      <Box sx={{\n                        width: '40px',\n                        height: '40px',\n                        borderRadius: '50%',\n                        backgroundColor: 'var(--color-primary-100)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: 'var(--font-size-sm)',\n                        fontWeight: 'var(--font-weight-semibold)',\n                        color: 'var(--color-primary-700)'\n                      }}>\n                        {activity.user}\n                      </Box>\n                      <Box sx={{ flex: 1 }}>\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\n                          {activity.action}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {activity.name} • {activity.time}\n                        </Typography>\n                      </Box>\n                      <Box sx={{\n                        width: '8px',\n                        height: '8px',\n                        borderRadius: '50%',\n                        backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'\n                      }} />\n                    </Box>\n                  ))}\n                </Box>\n              </Card>\n            </Box> \n            */}\n\n            {/* User Feedback & Satisfaction Section */}\n            {/* User Satisfaction Ratings and Satisfaction Trend */}\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>\n              {/* User Satisfaction Ratings */}\n              <Card title=\"⭐ User Satisfaction Ratings\" padding=\"lg\">\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                  {/* Excellent (5★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Excellent (5★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: '#10b981',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      1,247\n                    </Typography>\n                  </Box>\n\n                  {/* Good (4★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Good (4★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '45%',\n                        height: '100%',\n                        backgroundColor: '#84cc16',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      892\n                    </Typography>\n                  </Box>\n\n                  {/* Average (3★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Average (3★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '22%',\n                        height: '100%',\n                        backgroundColor: '#f59e0b',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      434\n                    </Typography>\n                  </Box>\n\n                  {/* Poor (2★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Poor (2★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '8%',\n                        height: '100%',\n                        backgroundColor: '#f97316',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      123\n                    </Typography>\n                  </Box>\n\n                  {/* Very Poor (1★) */}\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        Very Poor (1★)\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    }}>\n                      <Box sx={{\n                        width: '4%',\n                        height: '100%',\n                        backgroundColor: '#ef4444',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\n                      57\n                    </Typography>\n                  </Box>\n\n                  {/* Summary Cards */}\n                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>\n                    <Box sx={{\n                      p: 2,\n                      backgroundColor: '#f0fdf4',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    }}>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#16a34a\">\n                        77%\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"#16a34a\">\n                        Positive\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      p: 2,\n                      backgroundColor: '#fffbeb',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    }}>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#d97706\">\n                        16%\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"#d97706\">\n                        Neutral\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      p: 2,\n                      backgroundColor: '#fef2f2',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    }}>\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#dc2626\">\n                        7%\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"#dc2626\">\n                        Negative\n                      </Typography>\n                    </Box>\n                  </Box>\n                </Box>\n              </Card>\n\n              {/* Satisfaction Trend */}\n              <Card title=\"📈 Satisfaction Trend\" padding=\"lg\">\n                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8fafc', borderRadius: 'var(--radius-md)', position: 'relative' }}>\n                  {/* Satisfaction Trend Line Chart */}\n                  <svg width=\"100%\" height=\"250\" viewBox=\"0 0 400 200\" style={{ overflow: 'visible' }}>\n                    {/* Grid lines */}\n                    <defs>\n                      <pattern id=\"feedbackGrid\" width=\"66.67\" height=\"40\" patternUnits=\"userSpaceOnUse\">\n                        <path d=\"M 66.67 0 L 0 0 0 40\" fill=\"none\" stroke=\"#e2e8f0\" strokeWidth=\"0.5\"/>\n                      </pattern>\n                    </defs>\n                    <rect width=\"100%\" height=\"100%\" fill=\"url(#feedbackGrid)\" />\n\n                    {/* Y-axis labels */}\n                    <text x=\"10\" y=\"20\" fontSize=\"10\" fill=\"#64748b\">5</text>\n                    <text x=\"10\" y=\"60\" fontSize=\"10\" fill=\"#64748b\">4.75</text>\n                    <text x=\"10\" y=\"100\" fontSize=\"10\" fill=\"#64748b\">4.5</text>\n                    <text x=\"10\" y=\"140\" fontSize=\"10\" fill=\"#64748b\">4.25</text>\n                    <text x=\"10\" y=\"180\" fontSize=\"10\" fill=\"#64748b\">4</text>\n\n                    {/* X-axis labels */}\n                    <text x=\"50\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Jan</text>\n                    <text x=\"110\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Feb</text>\n                    <text x=\"170\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Mar</text>\n                    <text x=\"230\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Apr</text>\n                    <text x=\"290\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">May</text>\n                    <text x=\"350\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Jun</text>\n\n                    {/* Line showing satisfaction trend from 4.2 to 4.7 */}\n                    <path d=\"M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"3\"/>\n\n                    {/* Data points */}\n                    <circle cx=\"50\" cy=\"168\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"110\" cy=\"152\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"170\" cy=\"136\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"230\" cy=\"120\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"290\" cy=\"104\" r=\"4\" fill=\"#3b82f6\"/>\n                    <circle cx=\"350\" cy=\"88\" r=\"4\" fill=\"#3b82f6\"/>\n\n                    {/* Value labels on data points */}\n                    <text x=\"50\" y=\"160\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.2</text>\n                    <text x=\"110\" y=\"144\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.3</text>\n                    <text x=\"170\" y=\"128\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.4</text>\n                    <text x=\"230\" y=\"112\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.5</text>\n                    <text x=\"290\" y=\"96\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.6</text>\n                    <text x=\"350\" y=\"80\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.7</text>\n                  </svg>\n                </Box>\n              </Card>\n            </Box>\n\n            {/* Feedback Summary */}\n            <Card title=\"📊 Feedback Summary\" padding=\"lg\">\n              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 'var(--spacing-6)' }}>\n                {/* Total Feedback */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#f8fafc',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1 }}>\n                    2,238\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Feedback\n                  </Typography>\n                </Box>\n\n                {/* Positive Sentiment */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#f0fdf4',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#16a34a\" sx={{ mb: 1 }}>\n                    85.8%\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Positive Sentiment\n                  </Typography>\n                </Box>\n\n                {/* Average Rating */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#eff6ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#2563eb\" sx={{ mb: 1 }}>\n                    4.6/5\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Average Rating\n                  </Typography>\n                </Box>\n\n                {/* Growth vs Last Month */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#fdf4ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#9333ea\" sx={{ mb: 1 }}>\n                    +12%\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    vs Last Month\n                  </Typography>\n                </Box>\n              </Box>\n            </Card>\n\n            {/* Recent Feedback */}\n            <Card title=\"Recent Feedback\" padding=\"lg\">\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                {[\n                  { user: 'JD', action: '5-star rating received', name: 'John Doe', time: '5 min ago', type: 'completion' },\n                  { user: 'SM', action: 'Improvement suggestion submitted', name: 'Sarah Miller', time: '20 min ago', type: 'create' },\n                  { user: 'RW', action: 'Bug report submitted', name: 'Robert Wilson', time: '45 min ago', type: 'alert' },\n                  { user: 'LB', action: 'Feature request submitted', name: 'Lisa Brown', time: '1 hour ago', type: 'update' },\n                ].map((activity, index) => (\n                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-3)' }}>\n                    <Box sx={{\n                      width: '40px',\n                      height: '40px',\n                      borderRadius: '50%',\n                      backgroundColor: 'var(--color-primary-100)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: 'var(--font-size-sm)',\n                      fontWeight: 'var(--font-weight-semibold)',\n                      color: 'var(--color-primary-700)'\n                    }}>\n                      {activity.user}\n                    </Box>\n                    <Box sx={{ flex: 1 }}>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {activity.action}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {activity.name} • {activity.time}\n                      </Typography>\n                    </Box>\n                    <Box sx={{\n                      width: '8px',\n                      height: '8px',\n                      borderRadius: '50%',\n                      backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'\n                    }} />\n                  </Box>\n                ))}\n              </Box>\n            </Card>\n          </>\n        );\n\n      case 1: // Analytics\n        return (\n          <>\n            {/* Guide Performance Overview */}\n            <Card title=\"Guide Performance Overview\" subtitle=\"Click on any guide to see detailed funnel analysis\" padding=\"lg\">\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                {/* Product Onboarding */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Product Onboarding\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e8f5e9',\n                        color: '#2e7d32',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        excellent\n                      </Box>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      }}>\n                        Onboarding\n                      </Box>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\n                        1,400 views\n                      </Box>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\n                        1,247 completed\n                      </Box>\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium' }}>\n                        11% drop-off\n                      </Box>\n                      <Box>\n                        Updated 2 days ago\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      89%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '89%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Feature Discovery */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Feature Discovery\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e3f2fd',\n                        color: '#1976d2',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        good\n                      </Box>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      }}>\n                        Feature\n                      </Box>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\n                        1,174 views\n                      </Box>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\n                        892 completed\n                      </Box>\n                      <Box sx={{ color: 'var(--color-warning-600)', fontWeight: 'medium' }}>\n                        24% drop-off\n                      </Box>\n                      <Box>\n                        Updated 1 day ago\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      76%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '76%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Advanced Settings */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Advanced Settings\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#fff3e0',\n                        color: '#f57c00',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        needs attention\n                      </Box>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      }}>\n                        Configuration\n                      </Box>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\n                        962 views\n                      </Box>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\n                        634 completed\n                      </Box>\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium' }}>\n                        32% drop-off\n                      </Box>\n                      <Box>\n                        Updated 5 days ago\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      65%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n              </Box>\n            </Card>\n          </>\n        );\n\n      case 2: // AI Performance\n        return (\n          <>\n            \n             {/* Bottom Metrics Cards */}\n            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 'var(--spacing-6)', mb: 4 }}>\n              {/* Total Interactions Card */}\n              <Box sx={{\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                  <Typography variant=\"caption\" color=\"#3b82f6\" sx={{ fontWeight: 'medium' }}>\n                    Total Interactions\n                  </Typography>\n                  <Box sx={{\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e3f2fd',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Box sx={{\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#3b82f6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    }}>\n                      💬\n                    </Box>\n                  </Box>\n                </Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\n                  2,847\n                </Typography>\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\n                  +12% from last month\n                </Typography>\n              </Box>\n\n              {/* Success Rate Card */}\n              <Box sx={{\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                  <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\n                    Success Rate\n                  </Typography>\n                  <Box sx={{\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e8f5e9',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Box sx={{\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#10b981',\n                      borderRadius: '50%',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    }}>\n                      ✓\n                    </Box>\n                  </Box>\n                </Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\n                  91%\n                </Typography>\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\n                  +3% improvement\n                </Typography>\n              </Box>\n\n              {/* Avg Response Time Card */}\n              <Box sx={{\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                  <Typography variant=\"caption\" color=\"#8b5cf6\" sx={{ fontWeight: 'medium' }}>\n                    Avg Response Time\n                  </Typography>\n                  <Box sx={{\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#f3e8ff',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Box sx={{\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#8b5cf6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    }}>\n                      ⚡\n                    </Box>\n                  </Box>\n                </Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\n                  1.9s\n                </Typography>\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\n                  -0.3s faster\n                </Typography>\n              </Box>\n            </Box>\n\n            {/* AI Task Performance Section */}\n            <Box sx={{ mb: 4 }}>\n              <Card title=\"AI Task Performance\" padding=\"lg\">\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\n                {/* Password Reset */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Password Reset\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e8f5e9',\n                        color: '#2e7d32',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        96%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        342 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 1.2s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        +2% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      96%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '96%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Account Setup */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Account Setup\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e3f2fd',\n                        color: '#1976d2',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        89%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        198 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 1.4s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        -5% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      89%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '89%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Feature Explanation */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Feature Explanation\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e3f2fd',\n                        color: '#1976d2',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        90%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        267 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 2.1s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        -1% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      90%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '90%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Troubleshooting */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Troubleshooting\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#fff3e0',\n                        color: '#f57c00',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        88%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        156 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 3.1s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        -3% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      88%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '88%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {/* Integration Help */}\n                <Box sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                }}>\n                  <Box sx={{ flex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\n                        Integration Help\n                      </Typography>\n                      <Box sx={{\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#fff3e0',\n                        color: '#f57c00',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      }}>\n                        87%\n                      </Box>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        123 interactions\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Avg time: 2.5s\n                      </Typography>\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>\n                        +1% trend\n                      </Box>\n                    </Box>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\n                      87%\n                    </Typography>\n                    <Box sx={{\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    }}>\n                      <Box sx={{\n                        width: '87%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }} />\n                    </Box>\n                  </Box>\n                </Box>\n              </Box>\n            </Card>\n            </Box>\n\n            {/* AI Insights & Recommendations */}\n            <Card title=\"AI Insights & Recommendations\" padding=\"lg\">\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>\n                {/* Optimize Workflow */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#fffbeb',\n                  border: '1px solid #fbbf24',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                }}>\n                  <Box sx={{\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#f59e0b',\n                    borderRadius: '50%',\n                    mt: 1\n                  }} />\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\n                      Optimize Workflow\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Consider optimizing your workflow to reduce response time by 15%\n                    </Typography>\n                  </Box>\n                </Box>\n\n                {/* Excluded Performance */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                }}>\n                  <Box sx={{\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }} />\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\n                      Excluded Performance\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Excluded tasks are performing well with 94% accuracy rate\n                    </Typography>\n                  </Box>\n                </Box>\n\n                {/* Personalized Suggestions */}\n                <Box sx={{\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                }}>\n                  <Box sx={{\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }} />\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\n                      Personalized Suggestions\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      AI suggests implementing advanced filtering for better user experience\n                    </Typography>\n                  </Box>\n                </Box>\n              </Box>\n            </Card>\n          </>\n        );\n\n\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className='qadpt-web'>\n      <div className='qadpt-webcontent'>\n        <DashboardWrapper>\n          <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n            {/* Navigation Tabs */}\n            <StyledTabs value={selectedTab} onChange={handleTabChange}>\n              <Tab label=\"Overview\" />\n              <Tab label=\"Analytics\" />\n              <Tab label=\"AI Performance\" />\n            </StyledTabs>\n\n            {/* Render Tab Content */}\n            {renderTabContent()}\n          </Container>\n        </DashboardWrapper>\n\n        {/* Interactive Tooltip */}\n        {tooltip.visible && (\n          <Box\n            sx={{\n              position: 'fixed',\n              left: tooltip.x,\n              top: tooltip.y,\n              transform: 'translate(-50%, -100%)',\n              backgroundColor: 'white',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              padding: '6px 10px',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n              zIndex: 10000,\n              pointerEvents: 'none',\n              fontSize: '11px',\n              minWidth: '70px',\n              textAlign: 'center',\n              fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n            }}\n          >\n            <Typography variant=\"body2\" sx={{ fontSize: '10px', color: '#6b7280', mb: 0.2, lineHeight: 1.2 }}>\n              {tooltip.title}\n            </Typography>\n            <Typography variant=\"body2\" sx={{ fontSize: '11px', color: '#111827', fontWeight: '600', lineHeight: 1.2 }}>\n              {tooltip.content}\n            </Typography>\n          </Box>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ModernDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,UAAU,EAAcC,SAAS,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AACjF,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SACEC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,WAAW,EACXC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,aAAa,QACR,qBAAqB;AAC5B,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,YAAY,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAalD,MAAMC,gBAAgB,GAAGf,MAAM,CAAC,KAAK,CAAC,CAAC;EACrCgB,eAAe,EAAE,SAAS;EAC1BC,SAAS,EAAE;AACb,CAAC,CAAC;AAACC,EAAA,GAHGH,gBAAgB;AAKtB,MAAMI,aAAa,GAAGnB,MAAM,CAACL,GAAG,CAAC,CAAC;EAChCyB,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,eAAe;EAC/BC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAE;AAChB,CAAC,CAAC;AAACC,GAAA,GALGL,aAAa;AAOnB,MAAMM,UAAU,GAAGzB,MAAM,CAACF,IAAI,CAAC,CAAC;EAC9ByB,YAAY,EAAE,kBAAkB;EAChC,sBAAsB,EAAE;IACtBP,eAAe,EAAE;EACnB,CAAC;EACD,gBAAgB,EAAE;IAChBU,QAAQ,EAAE,qBAAqB;IAC/BC,UAAU,EAAE,2BAA2B;IACvCC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,uBAAuB;IAC9B,gBAAgB,EAAE;MAChBA,KAAK,EAAE,0BAA0B;MACjCF,UAAU,EAAE;IACd;EACF;AACF,CAAC,CAAC;AAACG,GAAA,GAfGL,UAAU;AAiBhB,MAAMM,aAAa,GAAG/B,MAAM,CAACL,GAAG,CAAC,CAAC;EAChCyB,OAAO,EAAE,MAAM;EACfY,GAAG,EAAE,kBAAkB;EACvBV,UAAU,EAAE;AACd,CAAC,CAAC;AAACW,GAAA,GAJGF,aAAa;AAMnB,MAAMG,WAAW,GAAGlC,MAAM,CAACL,GAAG,CAAC,CAAC;EAC9ByB,OAAO,EAAE,MAAM;EACfe,mBAAmB,EAAE,sCAAsC;EAC3DH,GAAG,EAAE,kBAAkB;EACvBT,YAAY,EAAE;AAChB,CAAC,CAAC;AAACa,GAAA,GALGF,WAAW;AAOjB,MAAMG,mBAAmB,GAAGrC,MAAM,CAACS,IAAI,CAAC,CAAC;EACvC6B,OAAO,EAAE,kBAAkB;EAC3BlB,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBU,GAAG,EAAE;AACP,CAAC,CAAC;AAACO,GAAA,GALGF,mBAAmB;AAOzB,MAAMG,UAAU,GAAGxC,MAAM,CAACL,GAAG,CAAC,CAAoB,CAAC;EAAEkC;AAAM,CAAC,MAAM;EAChEY,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,YAAY,EAAE,kBAAkB;EAChC3B,eAAe,EAAE,GAAGa,KAAK,IAAI;EAC7BT,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBD,cAAc,EAAE,QAAQ;EAExB,OAAO,EAAE;IACPQ,KAAK,EAAEA,KAAK;IACZH,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC,CAAC;AAACkB,GAAA,GAbEJ,UAAU;AAehB,MAAMK,aAAa,GAAG7C,MAAM,CAACL,GAAG,CAAC,CAAC;EAChCmD,IAAI,EAAE;AACR,CAAC,CAAC;AAACC,GAAA,GAFGF,aAAa;AAInB,MAAMG,WAAW,GAAGhD,MAAM,CAACJ,UAAU,CAAC,CAAC;EACrC8B,QAAQ,EAAE,qBAAqB;EAC/BG,KAAK,EAAE,uBAAuB;EAC9BN,YAAY,EAAE;AAChB,CAAC,CAAC;AAAC0B,GAAA,GAJGD,WAAW;AAMjB,MAAME,WAAW,GAAGlD,MAAM,CAACJ,UAAU,CAAC,CAAC;EACrC8B,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,yBAAyB;EACrCE,KAAK,EAAE,uBAAuB;EAC9BN,YAAY,EAAE;AAChB,CAAC,CAAC;AAAC4B,GAAA,GALGD,WAAW;AAOjB,MAAME,YAAY,GAAGpD,MAAM,CAACL,GAAG,CAAC,CAAC;EAC/ByB,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBU,GAAG,EAAE;AACP,CAAC,CAAC;AAACqB,GAAA,GAJGD,YAAY;AAMlB,MAAME,eAAe,GAAGtD,MAAM,CAACL,GAAG,CAAC,CAA2B,CAAC;EAAE4D;AAAM,CAAC,MAAM;EAC5EnC,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBU,GAAG,EAAE,kBAAkB;EACvBN,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,2BAA2B;EACvCE,KAAK,EAAE0B,KAAK,KAAK,IAAI,GAAG,0BAA0B,GAAG,wBAAwB;EAE7E,OAAO,EAAE;IACP7B,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC,CAAC;AAAC8B,IAAA,GAXEF,eAAe;AAarB,MAAMG,UAAqC,GAAGA,CAAC;EAC7CC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,WAAW;EACXN,KAAK;EACLO,IAAI;EACJjC;AACF,CAAC,KAAK;EACJ,oBACEjB,OAAA,CAACyB,mBAAmB;IAAC0B,MAAM,EAAC,IAAI;IAACC,KAAK;IAAAC,QAAA,gBACpCrD,OAAA,CAAC4B,UAAU;MAACX,KAAK,EAAEA,KAAM;MAAAoC,QAAA,EACtBH;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACbzD,OAAA,CAACiC,aAAa;MAAAoB,QAAA,gBACZrD,OAAA,CAACoC,WAAW;QAAAiB,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAClCzD,OAAA,CAACsC,WAAW;QAAAe,QAAA,EAAEN;MAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAClCzD,OAAA,CAACwC,YAAY;QAAAa,QAAA,gBACXrD,OAAA,CAAC0C,eAAe;UAACC,KAAK,EAAEA,KAAM;UAAAU,QAAA,GAC3BV,KAAK,KAAK,IAAI,gBAAG3C,OAAA,CAACX,UAAU;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACV,YAAY;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAClDT,MAAM;QAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAClBzD,OAAA,CAAChB,UAAU;UAAC0E,OAAO,EAAC,SAAS;UAACzC,KAAK,EAAC,gBAAgB;UAAAoC,QAAA,EACjDJ;QAAW;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAE1B,CAAC;AAACE,IAAA,GA7BId,UAAqC;AA+B3C,MAAMe,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkF,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAMnC;IACDoF,OAAO,EAAE,KAAK;IACdC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,OAAO,EAAE,EAAE;IACXvB,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMwB,WAAW,GAAGA,CAACC,KAAuB,EAAEzB,KAAa,EAAEuB,OAAe,KAAK;IAC/E,MAAMG,IAAI,GAAGD,KAAK,CAACE,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,OAAO,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACC,UAAU;IACzE,MAAMC,OAAO,GAAGL,MAAM,CAACM,WAAW,IAAIJ,QAAQ,CAACC,eAAe,CAACI,SAAS;IAExElB,UAAU,CAAC;MACTC,OAAO,EAAE,IAAI;MACbC,CAAC,EAAEK,IAAI,CAACY,IAAI,GAAGT,OAAO,GAAGH,IAAI,CAAC3C,KAAK,GAAG,CAAC;MACvCuC,CAAC,EAAEI,IAAI,CAACa,GAAG,GAAGJ,OAAO,GAAG,EAAE;MAC1BZ,OAAO;MACPvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwC,WAAW,GAAGA,CAAA,KAAM;IACxBrB,UAAU,CAACsB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErB,OAAO,EAAE;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMsB,eAAe,GAAG,CACtB;IACE1C,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACR,WAAW;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,YAAY;IACzBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACP,IAAI;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,OAAO;IACpBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACN,QAAQ;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBxC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMwE,gBAAgB,GAAG,CACvB;IACE3C,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACT,MAAM;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACR,WAAW;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,YAAY;IACzBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACP,IAAI;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,OAAO;IACpBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACN,QAAQ;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBxC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMyE,oBAAoB,GAAG,CAC3B;IACE5C,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACN,QAAQ;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,UAAU;IACvBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACR,WAAW;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACP,IAAI;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE,UAAU;IACvBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACT,MAAM;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBxC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM0E,eAAe,GAAGA,CAACpB,KAA2B,EAAEqB,QAAgB,KAAK;IACzE7B,cAAc,CAAC6B,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQ/B,WAAW;MACjB,KAAK,CAAC;QAAE;QACN,oBACE9D,OAAA,CAAAE,SAAA;UAAAmD,QAAA,gBAEErD,OAAA,CAACO,aAAa;YAAA8C,QAAA,gBACZrD,OAAA,CAACjB,GAAG;cAAAsE,QAAA,eACFrD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,IAAI;gBAAC3C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAAAoC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNzD,OAAA,CAACmB,aAAa;cAAAkC,QAAA,gBACZrD,OAAA,CAACF,YAAY;gBACX4D,OAAO,EAAC,SAAS;gBACjBoC,SAAS,eAAE9F,OAAA,CAACL,UAAU;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BsC,IAAI,EAAC,IAAI;gBAAA1C,QAAA,EACV;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC,eACfzD,OAAA,CAACF,YAAY;gBACX4D,OAAO,EAAC,SAAS;gBACjBoC,SAAS,eAAE9F,OAAA,CAACJ,aAAa;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7BsC,IAAI,EAAC,IAAI;gBAAA1C,QAAA,EACV;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGhBzD,OAAA,CAACsB,WAAW;YAAA+B,QAAA,EACToC,gBAAgB,CAACO,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAClClG,OAAA,CAAC6C,UAAU;cAAA,GAAiBoD;YAAM,GAAjBC,KAAK;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAGdzD,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAE;cAAE3F,OAAO,EAAE,MAAM;cAAEe,mBAAmB,EAAE,SAAS;cAAEH,GAAG,EAAE,kBAAkB;cAAEgF,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,gBAE3FrD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,4BAAkB;cAACuD,QAAQ,EAAC,wCAAwC;cAAC3E,OAAO,EAAC,IAAI;cAAA2B,QAAA,eAC3FrD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBAAErE,MAAM,EAAE,OAAO;kBAAEwE,QAAQ,EAAE,UAAU;kBAAElG,eAAe,EAAE,OAAO;kBAAE2B,YAAY,EAAE;gBAAmB,CAAE;gBAAAsB,QAAA,eAE7GrD,OAAA;kBAAK6B,KAAK,EAAC,MAAM;kBAACC,MAAM,EAAC,KAAK;kBAACyE,OAAO,EAAC,aAAa;kBAACC,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAU,CAAE;kBAAApD,QAAA,gBAElFrD,OAAA;oBAAAqD,QAAA,eACErD,OAAA;sBAAS0G,EAAE,EAAC,iBAAiB;sBAAC7E,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAAC6E,YAAY,EAAC,gBAAgB;sBAAAtD,QAAA,eAChFrD,OAAA;wBAAM4G,CAAC,EAAC,mBAAmB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAG;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACPzD,OAAA;oBAAM6B,KAAK,EAAC,MAAM;oBAACC,MAAM,EAAC,KAAK;oBAAC+E,IAAI,EAAC;kBAAuB;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG/DzD,OAAA;oBAAMgH,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACL,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAEzEzD,OAAA;oBAAMgH,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACL,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG3EzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAA/D,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAA/D,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAA/D,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAA/D,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAA/D,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG3EzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjFzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjFzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjFzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjFzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGjFzD,OAAA;oBAAAqD,QAAA,eACErD,OAAA;sBAAgB0G,EAAE,EAAC,cAAc;sBAACM,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,MAAM;sBAAA9D,QAAA,gBACjErD,OAAA;wBAAMqH,MAAM,EAAC,IAAI;wBAACC,SAAS,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAjE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACzDzD,OAAA;wBAAMqH,MAAM,EAAC,MAAM;wBAACC,SAAS,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAM;wBAAAjE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACPzD,OAAA;oBAAM4G,CAAC,EAAC,+EAA+E;oBAACC,IAAI,EAAC;kBAAoB;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAGnHzD,OAAA;oBAAM4G,CAAC,EAAC,0DAA0D;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACS,aAAa,EAAC;kBAAO;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAGvIzD,OAAA;oBAAGyH,SAAS,EAAC,aAAa;oBAAApE,QAAA,gBACxBrD,OAAA;sBACE0H,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,GAAG;sBAACf,IAAI,EAAC,OAAO;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBACpEP,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAc,CAAE;sBACxDC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;wBACjC5D,WAAW,CAAC0D,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC;wBACpCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;sBACzC,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;sBACxC;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,GAAG;sBAACf,IAAI,EAAC,OAAO;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBACrEP,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAc,CAAE;sBACxDC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;wBACjC5D,WAAW,CAAC0D,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC;wBACpCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;sBACzC,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;sBACxC;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,GAAG;sBAACf,IAAI,EAAC,OAAO;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBACrEP,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAc,CAAE;sBACxDC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;wBACjC5D,WAAW,CAAC0D,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC;wBACrCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;sBACzC,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;sBACxC;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,GAAG;sBAACf,IAAI,EAAC,OAAO;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBACrEP,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAc,CAAE;sBACxDC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;wBACjC5D,WAAW,CAAC0D,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC;wBACrCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;sBACzC,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;sBACxC;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACf,IAAI,EAAC,OAAO;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBACpEP,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAc,CAAE;sBACxDC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;wBACjC5D,WAAW,CAAC0D,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC;wBACrCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;sBACzC,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;sBACxC;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACf,IAAI,EAAC,OAAO;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBACpEP,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAc,CAAE;sBACxDC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;wBACjC5D,WAAW,CAAC0D,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC;wBACrCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;sBACzC,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;sBACxC;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGPzD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,gCAAsB;cAACuD,QAAQ,EAAC,gCAAgC;cAAC3E,OAAO,EAAC,IAAI;cAAA2B,QAAA,eACvFrD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBAAErE,MAAM,EAAE,OAAO;kBAAEtB,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,QAAQ;kBAAEC,UAAU,EAAE,QAAQ;kBAAEN,eAAe,EAAE,OAAO;kBAAE2B,YAAY,EAAE,kBAAkB;kBAAEuE,QAAQ,EAAE;gBAAW,CAAE;gBAAAjD,QAAA,eAE9KrD,OAAA;kBAAK6B,KAAK,EAAC,KAAK;kBAACC,MAAM,EAAC,KAAK;kBAACyE,OAAO,EAAC,aAAa;kBAAAlD,QAAA,gBAEjDrD,OAAA;oBAAAqD,QAAA,gBAEErD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,IAAI;sBAACf,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,IAAI;sBACtEsB,eAAe,EAAC,SAAS;sBAACC,gBAAgB,EAAC,GAAG;sBAACC,SAAS,EAAC,qBAAqB;sBAC9E/B,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAyB,CAAE;sBACnEC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;wBACzC5D,WAAW,CAAC0D,CAAC,EAAE,gBAAgB,EAAE,KAAK,CAAC;wBACvCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,IAAI;sBAACf,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,IAAI;sBACtEsB,eAAe,EAAC,QAAQ;sBAACC,gBAAgB,EAAC,MAAM;sBAACC,SAAS,EAAC,qBAAqB;sBAChF/B,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAyB,CAAE;sBACnEC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;wBACpC5D,WAAW,CAAC0D,CAAC,EAAE,WAAW,EAAE,KAAK,CAAC;wBAClCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,IAAI;sBAACf,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,IAAI;sBACtEsB,eAAe,EAAC,QAAQ;sBAACC,gBAAgB,EAAC,MAAM;sBAACC,SAAS,EAAC,qBAAqB;sBAChF/B,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAyB,CAAE;sBACnEC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;wBACvC5D,WAAW,CAAC0D,CAAC,EAAE,cAAc,EAAE,KAAK,CAAC;wBACrCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,IAAI;sBAACf,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,IAAI;sBACtEsB,eAAe,EAAC,QAAQ;sBAACC,gBAAgB,EAAC,MAAM;sBAACC,SAAS,EAAC,qBAAqB;sBAChF/B,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAyB,CAAE;sBACnEC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;wBACpC5D,WAAW,CAAC0D,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC;wBACjCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGFzD,OAAA;sBACE0H,EAAE,EAAC,KAAK;sBAACC,EAAE,EAAC,KAAK;sBAACC,CAAC,EAAC,IAAI;sBAACf,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,IAAI;sBACtEsB,eAAe,EAAC,OAAO;sBAACC,gBAAgB,EAAC,MAAM;sBAACC,SAAS,EAAC,qBAAqB;sBAC/E/B,KAAK,EAAE;wBAAEqB,MAAM,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAyB,CAAE;sBACnEC,YAAY,EAAGC,CAAC,IAAK;wBACnBC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;wBACzC5D,WAAW,CAAC0D,CAAC,EAAE,gBAAgB,EAAE,IAAI,CAAC;wBACtCA,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD,CAAE;sBACFC,YAAY,EAAGJ,CAAC,IAAK;wBACnB1C,WAAW,CAAC,CAAC;wBACb0C,CAAC,CAACvD,aAAa,CAAC0D,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC;sBACpD;oBAAE;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAGJzD,OAAA;oBAAQ0H,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACf,IAAI,EAAC;kBAAO;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAkENzD,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAE;cAAE3F,OAAO,EAAE,MAAM;cAAEe,mBAAmB,EAAE,SAAS;cAAEH,GAAG,EAAE,kBAAkB;cAAEgF,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,gBAE3FrD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,kCAA6B;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eACpDrD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBAAE3F,OAAO,EAAE,MAAM;kBAAEgI,aAAa,EAAE,QAAQ;kBAAEpH,GAAG,EAAE;gBAAmB,CAAE;gBAAAiC,QAAA,gBAE7ErD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEqH,QAAQ,EAAE;oBAAI,CAAE;oBAAApF,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAEtE,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPjE,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC0E,QAAQ,EAAE,QAAQ;sBAClBiC,EAAE,EAAE;oBACN,CAAE;oBAAArF,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACoF,EAAE,EAAE;sBAAEsC,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAtF,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEqH,QAAQ,EAAE;oBAAI,CAAE;oBAAApF,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAEtE,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPjE,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC0E,QAAQ,EAAE,QAAQ;sBAClBiC,EAAE,EAAE;oBACN,CAAE;oBAAArF,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACoF,EAAE,EAAE;sBAAEsC,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAtF,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEqH,QAAQ,EAAE;oBAAI,CAAE;oBAAApF,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAEtE,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPjE,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC0E,QAAQ,EAAE,QAAQ;sBAClBiC,EAAE,EAAE;oBACN,CAAE;oBAAArF,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACoF,EAAE,EAAE;sBAAEsC,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAtF,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEqH,QAAQ,EAAE;oBAAI,CAAE;oBAAApF,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAEtE,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPjE,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC0E,QAAQ,EAAE,QAAQ;sBAClBiC,EAAE,EAAE;oBACN,CAAE;oBAAArF,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,IAAI;wBACXC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACoF,EAAE,EAAE;sBAAEsC,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAtF,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEqH,QAAQ,EAAE;oBAAI,CAAE;oBAAApF,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAEtE,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPjE,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC0E,QAAQ,EAAE,QAAQ;sBAClBiC,EAAE,EAAE;oBACN,CAAE;oBAAArF,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,IAAI;wBACXC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACoF,EAAE,EAAE;sBAAEsC,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAtF,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEe,mBAAmB,EAAE,gBAAgB;oBAAEH,GAAG,EAAE,CAAC;oBAAEwH,EAAE,EAAE;kBAAE,CAAE;kBAAAvF,QAAA,gBACjFrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACP0C,CAAC,EAAE,CAAC;sBACJzI,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChC4G,SAAS,EAAE;oBACb,CAAE;oBAAAtF,QAAA,gBACArD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAACzC,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACP0C,CAAC,EAAE,CAAC;sBACJzI,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChC4G,SAAS,EAAE;oBACb,CAAE;oBAAAtF,QAAA,gBACArD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAACzC,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACP0C,CAAC,EAAE,CAAC;sBACJzI,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChC4G,SAAS,EAAE;oBACb,CAAE;oBAAAtF,QAAA,gBACArD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAACzC,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGPzD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,iCAAuB;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eAC9CrD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBAAErE,MAAM,EAAE,OAAO;kBAAEtB,OAAO,EAAE,MAAM;kBAAEgI,aAAa,EAAE,QAAQ;kBAAE/H,cAAc,EAAE,QAAQ;kBAAEC,UAAU,EAAE,QAAQ;kBAAEN,eAAe,EAAE,SAAS;kBAAE2B,YAAY,EAAE,kBAAkB;kBAAEuE,QAAQ,EAAE;gBAAW,CAAE;gBAAAjD,QAAA,eAEzMrD,OAAA;kBAAK6B,KAAK,EAAC,MAAM;kBAACC,MAAM,EAAC,KAAK;kBAACyE,OAAO,EAAC,aAAa;kBAACC,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAU,CAAE;kBAAApD,QAAA,gBAElFrD,OAAA;oBAAAqD,QAAA,eACErD,OAAA;sBAAS0G,EAAE,EAAC,cAAc;sBAAC7E,KAAK,EAAC,OAAO;sBAACC,MAAM,EAAC,IAAI;sBAAC6E,YAAY,EAAC,gBAAgB;sBAAAtD,QAAA,eAChFrD,OAAA;wBAAM4G,CAAC,EAAC,sBAAsB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACPzD,OAAA;oBAAM6B,KAAK,EAAC,MAAM;oBAACC,MAAM,EAAC,MAAM;oBAAC+E,IAAI,EAAC;kBAAoB;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG7DzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzDzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG1DzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,IAAI;oBAAC+F,IAAI,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG7DzD,OAAA;oBAAM4G,CAAC,EAAC,2DAA2D;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAGlHzD,OAAA;oBAAQ0H,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACf,IAAI,EAAC;kBAAS;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/CzD,OAAA;oBAAQ0H,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACf,IAAI,EAAC;kBAAS;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0H,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACf,IAAI,EAAC;kBAAS;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0H,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACf,IAAI,EAAC;kBAAS;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0H,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACf,IAAI,EAAC;kBAAS;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0H,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACf,IAAI,EAAC;kBAAS;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG/CzD,OAAA;oBAAMmE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,GAAG;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,GAAG;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,GAAG;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACtD,QAAQ,EAAC,GAAG;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACtD,QAAQ,EAAC,GAAG;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EzD,OAAA;oBAAMmE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACtD,QAAQ,EAAC,GAAG;oBAAC+F,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAA/D,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNzD,OAAA,CAACH,IAAI;YAACiD,KAAK,EAAC,+BAAqB;YAACpB,OAAO,EAAC,IAAI;YAAA2B,QAAA,eAC5CrD,OAAA,CAACjB,GAAG;cAACoH,EAAE,EAAE;gBAAE3F,OAAO,EAAE,MAAM;gBAAEe,mBAAmB,EAAE,gBAAgB;gBAAEH,GAAG,EAAE;cAAmB,CAAE;cAAAiC,QAAA,gBAE3FrD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP0C,CAAC,EAAE,CAAC;kBACJzI,eAAe,EAAE,SAAS;kBAC1B2B,YAAY,EAAE,kBAAkB;kBAChC4G,SAAS,EAAE;gBACb,CAAE;gBAAAtF,QAAA,gBACArD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,IAAI;kBAAC3C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,cAAc;kBAACkF,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAA/C,QAAA,EAAC;gBAE/E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACzC,KAAK,EAAC,gBAAgB;kBAAAoC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP0C,CAAC,EAAE,CAAC;kBACJzI,eAAe,EAAE,SAAS;kBAC1B2B,YAAY,EAAE,kBAAkB;kBAChC4G,SAAS,EAAE;gBACb,CAAE;gBAAAtF,QAAA,gBACArD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,IAAI;kBAAC3C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACkF,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAA/C,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACzC,KAAK,EAAC,gBAAgB;kBAAAoC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP0C,CAAC,EAAE,CAAC;kBACJzI,eAAe,EAAE,SAAS;kBAC1B2B,YAAY,EAAE,kBAAkB;kBAChC4G,SAAS,EAAE;gBACb,CAAE;gBAAAtF,QAAA,gBACArD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,IAAI;kBAAC3C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACkF,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAA/C,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACzC,KAAK,EAAC,gBAAgB;kBAAAoC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP0C,CAAC,EAAE,CAAC;kBACJzI,eAAe,EAAE,SAAS;kBAC1B2B,YAAY,EAAE,kBAAkB;kBAChC4G,SAAS,EAAE;gBACb,CAAE;gBAAAtF,QAAA,gBACArD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,IAAI;kBAAC3C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACkF,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAA/C,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACzC,KAAK,EAAC,gBAAgB;kBAAAoC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPzD,OAAA,CAACH,IAAI;YAACiD,KAAK,EAAC,iBAAiB;YAACpB,OAAO,EAAC,IAAI;YAAA2B,QAAA,eACxCrD,OAAA,CAACjB,GAAG;cAACoH,EAAE,EAAE;gBAAE3F,OAAO,EAAE,MAAM;gBAAEgI,aAAa,EAAE,QAAQ;gBAAEpH,GAAG,EAAE;cAAmB,CAAE;cAAAiC,QAAA,EAC5E,CACC;gBAAEyF,IAAI,EAAE,IAAI;gBAAEC,MAAM,EAAE,wBAAwB;gBAAEC,IAAI,EAAE,UAAU;gBAAEC,IAAI,EAAE,WAAW;gBAAEC,IAAI,EAAE;cAAa,CAAC,EACzG;gBAAEJ,IAAI,EAAE,IAAI;gBAAEC,MAAM,EAAE,kCAAkC;gBAAEC,IAAI,EAAE,cAAc;gBAAEC,IAAI,EAAE,YAAY;gBAAEC,IAAI,EAAE;cAAS,CAAC,EACpH;gBAAEJ,IAAI,EAAE,IAAI;gBAAEC,MAAM,EAAE,sBAAsB;gBAAEC,IAAI,EAAE,eAAe;gBAAEC,IAAI,EAAE,YAAY;gBAAEC,IAAI,EAAE;cAAQ,CAAC,EACxG;gBAAEJ,IAAI,EAAE,IAAI;gBAAEC,MAAM,EAAE,2BAA2B;gBAAEC,IAAI,EAAE,YAAY;gBAAEC,IAAI,EAAE,YAAY;gBAAEC,IAAI,EAAE;cAAS,CAAC,CAC5G,CAAClD,GAAG,CAAC,CAACmD,QAAQ,EAAEjD,KAAK,kBACpBlG,OAAA,CAACjB,GAAG;gBAAaoH,EAAE,EAAE;kBAAE3F,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEU,GAAG,EAAE;gBAAmB,CAAE;gBAAAiC,QAAA,gBACtFrD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACPtE,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdC,YAAY,EAAE,KAAK;oBACnB3B,eAAe,EAAE,0BAA0B;oBAC3CI,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxBK,QAAQ,EAAE,qBAAqB;oBAC/BC,UAAU,EAAE,6BAA6B;oBACzCE,KAAK,EAAE;kBACT,CAAE;kBAAAoC,QAAA,EACC8F,QAAQ,CAACL;gBAAI;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAEjE,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,QAAQ;oBAAAsC,QAAA,EAC5C8F,QAAQ,CAACJ;kBAAM;oBAAAzF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACbzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,SAAS;oBAACzC,KAAK,EAAC,gBAAgB;oBAAAoC,QAAA,GACjD8F,QAAQ,CAACH,IAAI,EAAC,UAAG,EAACG,QAAQ,CAACF,IAAI;kBAAA;oBAAA3F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACPtE,KAAK,EAAE,KAAK;oBACZC,MAAM,EAAE,KAAK;oBACbC,YAAY,EAAE,KAAK;oBACnB3B,eAAe,EAAE+I,QAAQ,CAACD,IAAI,KAAK,OAAO,GAAG,wBAAwB,GAAG;kBAC1E;gBAAE;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GA5BGyC,KAAK;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,eACP,CAAC;MAGP,KAAK,CAAC;QAAE;QACN,oBACEzD,OAAA,CAAAE,SAAA;UAAAmD,QAAA,eAEErD,OAAA,CAACH,IAAI;YAACiD,KAAK,EAAC,4BAA4B;YAACuD,QAAQ,EAAC,oDAAoD;YAAC3E,OAAO,EAAC,IAAI;YAAA2B,QAAA,eACjHrD,OAAA,CAACjB,GAAG;cAACoH,EAAE,EAAE;gBAAE3F,OAAO,EAAE,MAAM;gBAAEgI,aAAa,EAAE,QAAQ;gBAAEpH,GAAG,EAAE;cAAmB,CAAE;cAAAiC,QAAA,gBAE7ErD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP3F,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,eAAe;kBAC/BoI,CAAC,EAAE,CAAC;kBACJO,MAAM,EAAE,iCAAiC;kBACzCrH,YAAY,EAAE,kBAAkB;kBAChC,SAAS,EAAE;oBACT3B,eAAe,EAAE,sBAAsB;oBACvCyH,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAAxE,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAEjE,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEgF,EAAE,EAAE;oBAAE,CAAE;oBAAA/C,QAAA,gBAChErD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,UAAU;sBAAAsC,QAAA,EAAC;oBAE/C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPkD,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPlJ,eAAe,EAAE,SAAS;wBAC1Ba,KAAK,EAAE,SAAS;wBAChBc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE;sBACd,CAAE;sBAAAsC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPkD,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPlJ,eAAe,EAAE,uBAAuB;wBACxCa,KAAK,EAAE,uBAAuB;wBAC9Bc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE;sBACZ,CAAE;sBAAAuC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEH,KAAK,EAAE,uBAAuB;sBAAEH,QAAQ,EAAE;oBAAO,CAAE;oBAAAuC,QAAA,gBAC3GrD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BAAEtE,KAAK,EAAE,CAAC;0BAAEC,MAAM,EAAE,CAAC;0BAAEC,YAAY,EAAE,KAAK;0BAAE3B,eAAe,EAAE;wBAAwB;sBAAE;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAErG;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACR,WAAW;wBAAC2G,EAAE,EAAE;0BAAErF,QAAQ,EAAE,EAAE;0BAAEG,KAAK,EAAE;wBAA2B;sBAAE;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,mBAE1E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAElF,KAAK,EAAE,0BAA0B;wBAAEF,UAAU,EAAE;sBAAS,CAAE;sBAAAsC,QAAA,EAAC;oBAEtE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAAAsE,QAAA,EAAC;oBAEL;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,IAAI;oBAAC3C,UAAU,EAAC,MAAM;oBAAAsC,QAAA,EAAC;kBAE3C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPtE,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC0E,QAAQ,EAAE;oBACZ,CAAE;oBAAApD,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP3F,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,eAAe;kBAC/BoI,CAAC,EAAE,CAAC;kBACJO,MAAM,EAAE,iCAAiC;kBACzCrH,YAAY,EAAE,kBAAkB;kBAChC,SAAS,EAAE;oBACT3B,eAAe,EAAE,sBAAsB;oBACvCyH,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAAxE,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAEjE,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEgF,EAAE,EAAE;oBAAE,CAAE;oBAAA/C,QAAA,gBAChErD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,UAAU;sBAAAsC,QAAA,EAAC;oBAE/C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPkD,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPlJ,eAAe,EAAE,SAAS;wBAC1Ba,KAAK,EAAE,SAAS;wBAChBc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE;sBACd,CAAE;sBAAAsC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPkD,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPlJ,eAAe,EAAE,uBAAuB;wBACxCa,KAAK,EAAE,uBAAuB;wBAC9Bc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE;sBACZ,CAAE;sBAAAuC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEH,KAAK,EAAE,uBAAuB;sBAAEH,QAAQ,EAAE;oBAAO,CAAE;oBAAAuC,QAAA,gBAC3GrD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BAAEtE,KAAK,EAAE,CAAC;0BAAEC,MAAM,EAAE,CAAC;0BAAEC,YAAY,EAAE,KAAK;0BAAE3B,eAAe,EAAE;wBAAwB;sBAAE;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAErG;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACR,WAAW;wBAAC2G,EAAE,EAAE;0BAAErF,QAAQ,EAAE,EAAE;0BAAEG,KAAK,EAAE;wBAA2B;sBAAE;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,iBAE1E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAElF,KAAK,EAAE,0BAA0B;wBAAEF,UAAU,EAAE;sBAAS,CAAE;sBAAAsC,QAAA,EAAC;oBAEtE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAAAsE,QAAA,EAAC;oBAEL;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,IAAI;oBAAC3C,UAAU,EAAC,MAAM;oBAAAsC,QAAA,EAAC;kBAE3C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPtE,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC0E,QAAQ,EAAE;oBACZ,CAAE;oBAAApD,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP3F,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,eAAe;kBAC/BoI,CAAC,EAAE,CAAC;kBACJO,MAAM,EAAE,iCAAiC;kBACzCrH,YAAY,EAAE,kBAAkB;kBAChC,SAAS,EAAE;oBACT3B,eAAe,EAAE,sBAAsB;oBACvCyH,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAAxE,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAEjE,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEgF,EAAE,EAAE;oBAAE,CAAE;oBAAA/C,QAAA,gBAChErD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,UAAU;sBAAAsC,QAAA,EAAC;oBAE/C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPkD,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPlJ,eAAe,EAAE,SAAS;wBAC1Ba,KAAK,EAAE,SAAS;wBAChBc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE;sBACd,CAAE;sBAAAsC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPkD,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPlJ,eAAe,EAAE,uBAAuB;wBACxCa,KAAK,EAAE,uBAAuB;wBAC9Bc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE;sBACZ,CAAE;sBAAAuC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEH,KAAK,EAAE,uBAAuB;sBAAEH,QAAQ,EAAE;oBAAO,CAAE;oBAAAuC,QAAA,gBAC3GrD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BAAEtE,KAAK,EAAE,CAAC;0BAAEC,MAAM,EAAE,CAAC;0BAAEC,YAAY,EAAE,KAAK;0BAAE3B,eAAe,EAAE;wBAAwB;sBAAE;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAErG;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACR,WAAW;wBAAC2G,EAAE,EAAE;0BAAErF,QAAQ,EAAE,EAAE;0BAAEG,KAAK,EAAE;wBAA2B;sBAAE;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,iBAE1E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAElF,KAAK,EAAE,wBAAwB;wBAAEF,UAAU,EAAE;sBAAS,CAAE;sBAAAsC,QAAA,EAAC;oBAEpE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAAAsE,QAAA,EAAC;oBAEL;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAE3F,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,IAAI;oBAAC3C,UAAU,EAAC,MAAM;oBAAAsC,QAAA,EAAC;kBAE3C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPtE,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC0E,QAAQ,EAAE;oBACZ,CAAE;oBAAApD,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,gBACP,CAAC;MAGP,KAAK,CAAC;QAAE;QACN,oBACEzD,OAAA,CAAAE,SAAA;UAAAmD,QAAA,gBAGErD,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAE;cAAE3F,OAAO,EAAE,MAAM;cAAEe,mBAAmB,EAAE,gBAAgB;cAAEH,GAAG,EAAE,kBAAkB;cAAEgF,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,gBAElGrD,OAAA,CAACjB,GAAG;cAACoH,EAAE,EAAE;gBACP0C,CAAC,EAAE,CAAC;gBACJzI,eAAe,EAAE,OAAO;gBACxB2B,YAAY,EAAE,kBAAkB;gBAChCqH,MAAM,EAAE,iCAAiC;gBACzCG,SAAS,EAAE,8BAA8B;gBACzCjD,QAAQ,EAAE;cACZ,CAAE;cAAAjD,QAAA,gBACArD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBAAE3F,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE0F,EAAE,EAAE;gBAAE,CAAE;gBAAA/C,QAAA,gBAC7FrD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,SAAS;kBAACzC,KAAK,EAAC,SAAS;kBAACkF,EAAE,EAAE;oBAAEpF,UAAU,EAAE;kBAAS,CAAE;kBAAAsC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACPtE,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACV1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,kBAAkB;oBAChCvB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA4C,QAAA,eACArD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPtE,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACV1B,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChCvB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBQ,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAsC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,IAAI;gBAAC3C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACkF,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEtF,QAAQ,EAAE;gBAAO,CAAE;gBAAAuC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,SAAS;gBAACzC,KAAK,EAAC,SAAS;gBAACkF,EAAE,EAAE;kBAAEpF,UAAU,EAAE;gBAAS,CAAE;gBAAAsC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;cAACoH,EAAE,EAAE;gBACP0C,CAAC,EAAE,CAAC;gBACJzI,eAAe,EAAE,OAAO;gBACxB2B,YAAY,EAAE,kBAAkB;gBAChCqH,MAAM,EAAE,iCAAiC;gBACzCG,SAAS,EAAE,8BAA8B;gBACzCjD,QAAQ,EAAE;cACZ,CAAE;cAAAjD,QAAA,gBACArD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBAAE3F,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE0F,EAAE,EAAE;gBAAE,CAAE;gBAAA/C,QAAA,gBAC7FrD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,SAAS;kBAACzC,KAAK,EAAC,SAAS;kBAACkF,EAAE,EAAE;oBAAEpF,UAAU,EAAE;kBAAS,CAAE;kBAAAsC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACPtE,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACV1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,kBAAkB;oBAChCvB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA4C,QAAA,eACArD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPtE,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACV1B,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,KAAK;sBACnBvB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBQ,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAsC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,IAAI;gBAAC3C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACkF,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEtF,QAAQ,EAAE;gBAAO,CAAE;gBAAAuC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,SAAS;gBAACzC,KAAK,EAAC,SAAS;gBAACkF,EAAE,EAAE;kBAAEpF,UAAU,EAAE;gBAAS,CAAE;gBAAAsC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;cAACoH,EAAE,EAAE;gBACP0C,CAAC,EAAE,CAAC;gBACJzI,eAAe,EAAE,OAAO;gBACxB2B,YAAY,EAAE,kBAAkB;gBAChCqH,MAAM,EAAE,iCAAiC;gBACzCG,SAAS,EAAE,8BAA8B;gBACzCjD,QAAQ,EAAE;cACZ,CAAE;cAAAjD,QAAA,gBACArD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBAAE3F,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE0F,EAAE,EAAE;gBAAE,CAAE;gBAAA/C,QAAA,gBAC7FrD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,SAAS;kBAACzC,KAAK,EAAC,SAAS;kBAACkF,EAAE,EAAE;oBAAEpF,UAAU,EAAE;kBAAS,CAAE;kBAAAsC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACPtE,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACV1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,kBAAkB;oBAChCvB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA4C,QAAA,eACArD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBACPtE,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACV1B,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChCvB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBQ,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAsC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,IAAI;gBAAC3C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACkF,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEtF,QAAQ,EAAE;gBAAO,CAAE;gBAAAuC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,SAAS;gBAACzC,KAAK,EAAC,SAAS;gBAACkF,EAAE,EAAE;kBAAEpF,UAAU,EAAE;gBAAS,CAAE;gBAAAsC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;YAACoH,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,eACjBrD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,qBAAqB;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eAC9CrD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBAAE3F,OAAO,EAAE,MAAM;kBAAEgI,aAAa,EAAE,QAAQ;kBAAEpH,GAAG,EAAE;gBAAmB,CAAE;gBAAAiC,QAAA,gBAE7ErD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACP3F,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/BoI,CAAC,EAAE,CAAC;oBACJO,MAAM,EAAE,iCAAiC;oBACzCrH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCyH,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAAxE,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAEjE,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEgF,EAAE,EAAE;sBAAE,CAAE;sBAAA/C,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPkD,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPlJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BAAElF,KAAK,EAAE,0BAA0B;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAExF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC0E,QAAQ,EAAE;sBACZ,CAAE;sBAAApD,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPtE,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACP3F,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/BoI,CAAC,EAAE,CAAC;oBACJO,MAAM,EAAE,iCAAiC;oBACzCrH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCyH,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAAxE,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAEjE,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEgF,EAAE,EAAE;sBAAE,CAAE;sBAAA/C,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPkD,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPlJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BAAElF,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC0E,QAAQ,EAAE;sBACZ,CAAE;sBAAApD,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPtE,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACP3F,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/BoI,CAAC,EAAE,CAAC;oBACJO,MAAM,EAAE,iCAAiC;oBACzCrH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCyH,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAAxE,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAEjE,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEgF,EAAE,EAAE;sBAAE,CAAE;sBAAA/C,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPkD,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPlJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BAAElF,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC0E,QAAQ,EAAE;sBACZ,CAAE;sBAAApD,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPtE,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACP3F,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/BoI,CAAC,EAAE,CAAC;oBACJO,MAAM,EAAE,iCAAiC;oBACzCrH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCyH,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAAxE,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAEjE,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEgF,EAAE,EAAE;sBAAE,CAAE;sBAAA/C,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPkD,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPlJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BAAElF,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC0E,QAAQ,EAAE;sBACZ,CAAE;sBAAApD,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPtE,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACP3F,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/BoI,CAAC,EAAE,CAAC;oBACJO,MAAM,EAAE,iCAAiC;oBACzCrH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCyH,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAAxE,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAEjE,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEgF,EAAE,EAAE;sBAAE,CAAE;sBAAA/C,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPkD,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPlJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBAAE3F,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BAAElF,KAAK,EAAE,0BAA0B;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAExF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACoH,EAAE,EAAE;sBAAE3F,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACoH,EAAE,EAAE;wBACPtE,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC0E,QAAQ,EAAE;sBACZ,CAAE;sBAAApD,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACoH,EAAE,EAAE;0BACPtE,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGNzD,OAAA,CAACH,IAAI;YAACiD,KAAK,EAAC,+BAA+B;YAACpB,OAAO,EAAC,IAAI;YAAA2B,QAAA,eACtDrD,OAAA,CAACjB,GAAG;cAACoH,EAAE,EAAE;gBAAE3F,OAAO,EAAE,MAAM;gBAAEgI,aAAa,EAAE,QAAQ;gBAAEpH,GAAG,EAAE;cAAmB,CAAE;cAAAiC,QAAA,gBAE7ErD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP0C,CAAC,EAAE,CAAC;kBACJzI,eAAe,EAAE,SAAS;kBAC1BgJ,MAAM,EAAE,mBAAmB;kBAC3BrH,YAAY,EAAE,kBAAkB;kBAChCvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBU,GAAG,EAAE;gBACP,CAAE;gBAAAiC,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACPtE,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACT1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,KAAK;oBACnB6G,EAAE,EAAE;kBACN;gBAAE;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAEjE,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,QAAQ;oBAACoF,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAA/C,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,SAAS;oBAACzC,KAAK,EAAC,gBAAgB;oBAAAoC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP0C,CAAC,EAAE,CAAC;kBACJzI,eAAe,EAAE,SAAS;kBAC1BgJ,MAAM,EAAE,mBAAmB;kBAC3BrH,YAAY,EAAE,kBAAkB;kBAChCvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBU,GAAG,EAAE;gBACP,CAAE;gBAAAiC,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACPtE,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACT1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,KAAK;oBACnB6G,EAAE,EAAE;kBACN;gBAAE;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAEjE,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,QAAQ;oBAACoF,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAA/C,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,SAAS;oBAACzC,KAAK,EAAC,gBAAgB;oBAAAoC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACoH,EAAE,EAAE;kBACP0C,CAAC,EAAE,CAAC;kBACJzI,eAAe,EAAE,SAAS;kBAC1BgJ,MAAM,EAAE,mBAAmB;kBAC3BrH,YAAY,EAAE,kBAAkB;kBAChCvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBU,GAAG,EAAE;gBACP,CAAE;gBAAAiC,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBACPtE,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACT1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,KAAK;oBACnB6G,EAAE,EAAE;kBACN;gBAAE;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLzD,OAAA,CAACjB,GAAG;kBAACoH,EAAE,EAAE;oBAAEjE,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,QAAQ;oBAACoF,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAA/C,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,SAAS;oBAACzC,KAAK,EAAC,gBAAgB;oBAAAoC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,eACP,CAAC;MAKP;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEzD,OAAA;IAAKyH,SAAS,EAAC,WAAW;IAAApE,QAAA,eACxBrD,OAAA;MAAKyH,SAAS,EAAC,kBAAkB;MAAApE,QAAA,gBAC/BrD,OAAA,CAACG,gBAAgB;QAAAkD,QAAA,eACfrD,OAAA,CAACf,SAAS;UAACuK,QAAQ,EAAC,IAAI;UAACrD,EAAE,EAAE;YAAEmD,EAAE,EAAE;UAAE,CAAE;UAAAjG,QAAA,gBAErCrD,OAAA,CAACa,UAAU;YAACkC,KAAK,EAAEe,WAAY;YAAC2F,QAAQ,EAAE9D,eAAgB;YAAAtC,QAAA,gBACxDrD,OAAA,CAACb,GAAG;cAACuK,KAAK,EAAC;YAAU;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxBzD,OAAA,CAACb,GAAG;cAACuK,KAAK,EAAC;YAAW;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBzD,OAAA,CAACb,GAAG;cAACuK,KAAK,EAAC;YAAgB;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,EAGZoC,gBAAgB,CAAC,CAAC;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGlBO,OAAO,CAACE,OAAO,iBACdlE,OAAA,CAACjB,GAAG;QACFoH,EAAE,EAAE;UACFG,QAAQ,EAAE,OAAO;UACjBlB,IAAI,EAAEpB,OAAO,CAACG,CAAC;UACfkB,GAAG,EAAErB,OAAO,CAACI,CAAC;UACdmE,SAAS,EAAE,wBAAwB;UACnCnI,eAAe,EAAE,OAAO;UACxBgJ,MAAM,EAAE,mBAAmB;UAC3BrH,YAAY,EAAE,KAAK;UACnBL,OAAO,EAAE,UAAU;UACnB6H,SAAS,EAAE,uEAAuE;UAClFI,MAAM,EAAE,KAAK;UACbC,aAAa,EAAE,MAAM;UACrB9I,QAAQ,EAAE,MAAM;UAChB2H,QAAQ,EAAE,MAAM;UAChBE,SAAS,EAAE,QAAQ;UACnBkB,UAAU,EAAE;QACd,CAAE;QAAAxG,QAAA,gBAEFrD,OAAA,CAAChB,UAAU;UAAC0E,OAAO,EAAC,OAAO;UAACyC,EAAE,EAAE;YAAErF,QAAQ,EAAE,MAAM;YAAEG,KAAK,EAAE,SAAS;YAAEmF,EAAE,EAAE,GAAG;YAAE0D,UAAU,EAAE;UAAI,CAAE;UAAAzG,QAAA,EAC9FW,OAAO,CAAClB;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbzD,OAAA,CAAChB,UAAU;UAAC0E,OAAO,EAAC,OAAO;UAACyC,EAAE,EAAE;YAAErF,QAAQ,EAAE,MAAM;YAAEG,KAAK,EAAE,SAAS;YAAEF,UAAU,EAAE,KAAK;YAAE+I,UAAU,EAAE;UAAI,CAAE;UAAAzG,QAAA,EACxGW,OAAO,CAACK;QAAO;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,CA/oDID,eAAyB;AAAAmG,IAAA,GAAzBnG,eAAyB;AAipD/B,eAAeA,eAAe;AAAC,IAAAtD,EAAA,EAAAM,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAe,IAAA,EAAAoG,IAAA;AAAAC,YAAA,CAAA1J,EAAA;AAAA0J,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAA3I,GAAA;AAAA2I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA7H,GAAA;AAAA6H,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAzH,GAAA;AAAAyH,YAAA,CAAAvH,GAAA;AAAAuH,YAAA,CAAApH,IAAA;AAAAoH,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}