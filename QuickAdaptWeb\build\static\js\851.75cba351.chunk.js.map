{"version": 3, "file": "static/js/851.75cba351.chunk.js", "mappings": "0JAwNA,QAxNA,WAwBC,OACCA,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SAxBc,CACd,CACCC,KAAM,WACNC,GAAI,KAEL,CACCD,KAAM,WACNC,GAAI,KAEL,CACCD,KAAM,WACNC,GAAI,KAEL,CACCD,KAAM,WACNC,GAAI,KAEL,CACCD,KAAM,WACNC,GAAI,MAMIC,KAAI,SAAUC,GACrB,OACCN,EAAAA,EAAAA,KAAA,OAAAE,UACCF,EAAAA,EAAAA,KAAA,OACC,cAAY,uBACZ,gBAAc,QACdO,UAAU,cAAaL,UAEvBM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,cAAaL,SAAA,EAC3BM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,iBAAgBL,SAAA,EAC9BM,EAAAA,EAAAA,MAAA,SACC,cAAY,6BACZD,UAAU,cAAaL,SAAA,EAEvBM,EAAAA,EAAAA,MAAA,OAAAN,SAAA,EACCF,EAAAA,EAAAA,KAAA,SAAOS,KAAK,cACZT,EAAAA,EAAAA,KAAA,cAEDA,EAAAA,EAAAA,KAAA,OAAAE,UACCF,EAAAA,EAAAA,KAAA,iBAGFQ,EAAAA,EAAAA,MAAA,OAAAN,SAAA,EACCF,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcL,SAAEI,EAAKH,QACrCH,EAAAA,EAAAA,KAAA,OAAKO,UAAU,YAAWL,SAAC,yCAC3BM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,iBAAgBL,SAAA,EAC9BM,EAAAA,EAAAA,MAAA,OACCE,MAAM,YACNH,UAAU,UAASL,SAAA,EAEnBF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRH,MAAM,UACNI,OAAO,KACPC,MAAM,KACNC,MAAM,6BACNC,MAAO,CAAEP,MAAO,sBAAuBR,UAEvCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,qCAETlB,EAAAA,EAAAA,KAAA,QAAMU,MAAM,YAAWR,SAAC,iBAEzBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,eACfP,EAAAA,EAAAA,KAAA,OACCI,GAAG,iCACH,cAAY,wBAAuBF,UAEnCF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,kBAAiBL,UAC/BF,EAAAA,EAAAA,KAAA,OAAKI,GAAG,+BAA8BF,UACrCM,EAAAA,EAAAA,MAAA,UACCJ,GAAG,kBACH,aAAW,QACXG,UAAU,iBACVU,MAAO,CAAEE,QAAS,OAAQjB,SAAA,EAE1BF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,4eAETlB,EAAAA,EAAAA,KAAA,QAAAE,SAAM,iCAQbM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,cAAaL,SAAA,EAC3BM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,gBAAeL,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,kBAAiBL,UAC/BF,EAAAA,EAAAA,KAAA,UACCI,GAAG,cACH,cAAY,cACZG,UAAU,aAAYL,UAEtBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,YAAWL,UACzBF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,qYAKZV,EAAAA,EAAAA,MAAA,OAAKS,MAAO,CAAEG,SAAU,WAAYC,QAAS,IAAKC,IAAK,QAASC,KAAM,YAAarB,SAAA,EAClFF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,eACNF,EAAAA,EAAAA,KAAA,OAAKiB,MAAO,CAAEK,IAAK,OAAQE,UAAW,gBAAiBD,KAAM,gBAE9DvB,EAAAA,EAAAA,KAAA,OAAKO,UAAU,kBAAiBL,UAC/BF,EAAAA,EAAAA,KAAA,UACCI,GAAG,eACH,cAAY,eACZG,UAAU,aAAYL,UAEtBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,YAAWL,UACzBF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,4YAKZV,EAAAA,EAAAA,MAAA,OAAKS,MAAO,CAAEG,SAAU,WAAYC,QAAS,IAAKC,IAAK,QAASC,KAAM,YAAarB,SAAA,EAClFF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,WACNF,EAAAA,EAAAA,KAAA,OAAKiB,MAAO,CAAEK,IAAK,OAAQE,UAAW,gBAAiBD,KAAM,iBAE9DvB,EAAAA,EAAAA,KAAA,OAAKO,UAAU,kBAAiBL,UAC/BF,EAAAA,EAAAA,KAAA,UACCI,GAAG,kBACH,cAAY,kBACZG,UAAU,aAAYL,UAEtBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,YAAWL,UACzBF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,2oEAKZV,EAAAA,EAAAA,MAAA,OAAKS,MAAO,CAAEG,SAAU,WAAYC,QAAS,IAAKC,IAAK,QAASC,KAAM,YAAarB,SAAA,EAClFF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,cACNF,EAAAA,EAAAA,KAAA,OAAKiB,MAAO,CAAEK,IAAK,OAAQE,UAAW,gBAAiBD,KAAM,gBAE9DvB,EAAAA,EAAAA,KAAA,OAAKO,UAAU,kBAAiBL,UAC/BF,EAAAA,EAAAA,KAAA,UACCI,GAAG,gBACHG,UAAU,WAAUL,UAEpBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,YAAWL,UACzBF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,oJAKZV,EAAAA,EAAAA,MAAA,OAAKS,MAAO,CAAEG,SAAU,WAAYC,QAAS,IAAKC,IAAK,QAASC,KAAM,UAAWrB,SAAA,EAChFF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,YACNF,EAAAA,EAAAA,KAAA,OAAKiB,MAAO,CAAEK,IAAK,OAAQE,UAAW,gBAAiBD,KAAM,iBAG/DvB,EAAAA,EAAAA,KAAA,OAAKO,UAAU,eAAcL,SAAC,qBAMpC,KAGH,ECjCA,EAjLmBuB,KAEjBjB,EAAAA,EAAAA,MAAA,OAAKD,UAAU,SAAQL,SAAA,EACtBF,EAAAA,EAAAA,KAAA,QAAMO,UAAU,cAAaL,SAAC,YAC9BM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,eAAcL,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,eAAcL,UAC5BM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,eAAcL,SAAA,EAC5BM,EAAAA,EAAAA,MAAA,SAAOD,UAAU,eAAcL,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRH,MAAM,UACNI,OAAO,MACPC,MAAM,MACNC,MAAM,6BACNC,MAAO,CAAEP,MAAO,uBAAwBR,UAExCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,0SAETlB,EAAAA,EAAAA,KAAA,SACCO,UAAU,eACVE,KAAK,OACLiB,YAAY,SACZC,MAAM,SAGR3B,EAAAA,EAAAA,KAAA,OAAKO,UAAU,gBAAeL,UAC7BM,EAAAA,EAAAA,MAAA,UACCJ,GAAG,gBACHG,UAAU,gBAAeL,SAAA,EAEzBF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,iDAETlB,EAAAA,EAAAA,KAAA,QAAMO,UAAU,cAAaL,SAAC,iBAGhCM,EAAAA,EAAAA,MAAA,UAAQD,UAAU,kBAAiBL,SAAA,EAClCF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,iDAETlB,EAAAA,EAAAA,KAAA,QAAMO,UAAU,mBAAkBL,SAAC,uBAItCM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,aAAYL,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,uBAAsBL,UACpCM,EAAAA,EAAAA,MAAA,SACC,cAAY,6BACZD,UAAU,mBAAkBL,SAAA,EAE5BM,EAAAA,EAAAA,MAAA,OAAAN,SAAA,EACCF,EAAAA,EAAAA,KAAA,SAAOS,KAAK,cACZT,EAAAA,EAAAA,KAAA,cAEDA,EAAAA,EAAAA,KAAA,OAAAE,UACCF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,uBAITM,EAAAA,EAAAA,MAAA,OAAKD,UAAU,eAAcL,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,UACCI,GAAG,eACHG,UAAU,gBAAeL,UAEzBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,uBAAsBL,UACpCF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,0YAIXlB,EAAAA,EAAAA,KAAA,UACCI,GAAG,gBACHG,UAAU,aAAYL,UAEtBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,uBAAsBL,UACpCF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,kJAIXlB,EAAAA,EAAAA,KAAA,UACCI,GAAG,gBACHG,UAAU,gBAAeL,UAEzBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,uBAAsBL,UACpCF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,yCAIXlB,EAAAA,EAAAA,KAAA,UACCI,GAAG,eACHG,UAAU,gBAAeL,UAEzBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,uBAAsBL,UACpCF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,i4BAIXlB,EAAAA,EAAAA,KAAA,UACCI,GAAG,mBACHG,UAAU,gBAAeL,UAEzBF,EAAAA,EAAAA,KAAA,OAAKO,UAAU,uBAAsBL,UACpCF,EAAAA,EAAAA,KAAA,OACCW,OAAO,eACPC,KAAK,eACL,eAAa,IACbC,QAAQ,YACRC,OAAO,KACPC,MAAM,KACNC,MAAM,6BAA4Bd,UAElCF,EAAAA,EAAAA,KAAA,QAAMkB,EAAE,8OAMblB,EAAAA,EAAAA,KAAC4B,EAAU,S", "sources": ["components/guide/GuideTable.tsx", "components/guide/GuideList.tsx"], "sourcesContent": ["function GuideTable() {\r\n\tconst guides = [\r\n\t\t{\r\n\t\t\tname: \"Sample 1\",\r\n\t\t\tid: \"0\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Sample 2\",\r\n\t\t\tid: \"1\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Sample 3\",\r\n\t\t\tid: \"2\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Sample 4\",\r\n\t\t\tid: \"3\",\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: \"Sample 5\",\r\n\t\t\tid: \"4\",\r\n\t\t},\r\n\t]\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{guides.map(function (data) {\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tdata-testid=\"material-table-row-0\"\r\n\t\t\t\t\t\t\tdata-selected=\"false\"\r\n\t\t\t\t\t\t\tclassName=\"guide-table\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"guide-block\">\r\n\t\t\t\t\t\t\t\t<div className=\"checkbox-block\">\r\n\t\t\t\t\t\t\t\t\t<label\r\n\t\t\t\t\t\t\t\t\t\tdata-testid=\"checkbox-container-test-id\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"check-label\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"checkbox\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<div></div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"updated-date\">{data.name}</span>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"date-text\">Last Updated: Jun 04 2024, 02:31 PM</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"inactive-block\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"lightGrey\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"grey-bg\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"#74819B\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\theight=\"14\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twidth=\"14\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"rgb(116, 129, 155)\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<path d=\"M13 10H20L11 23V14H4L13 1V10Z\"></path>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span color=\"lightGrey\">Inactive</span>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"vert-line\"></div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"guide-117566-dropdown-dropdown\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"guide-117566-dropdown\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"DropdownWrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"guide-117566-dropdown-toggle\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"edittags-button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-hover=\"false\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"edittag-button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ padding: \"0px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight=\"16\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth=\"16\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<path d=\"M10.9042 2.1001L20.8037 3.51431L22.2179 13.4138L13.0255 22.6062C12.635 22.9967 12.0019 22.9967 11.6113 22.6062L1.71184 12.7067C1.32131 12.3162 1.32131 11.683 1.71184 11.2925L10.9042 2.1001ZM11.6113 4.22142L3.83316 11.9996L12.3184 20.4849L20.0966 12.7067L19.036 5.28208L11.6113 4.22142ZM13.7327 10.5854C12.9516 9.80433 12.9516 8.538 13.7327 7.75695C14.5137 6.9759 15.78 6.9759 16.5611 7.75695C17.3421 8.538 17.3421 9.80433 16.5611 10.5854C15.78 11.3664 14.5137 11.3664 13.7327 10.5854Z\"></path>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span>Edit Tags</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div className=\"link-button\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"right-buttons\">\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"tooltip-wrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"link-button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"action-link\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"right-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"svg-block\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<path d=\"M14 13.5V8C14 5.79086 12.2091 4 10 4C7.79086 4 6 5.79086 6 8V13.5C6 17.0899 8.91015 20 12.5 20C16.0899 20 19 17.0899 19 13.5V4H21V13.5C21 18.1944 17.1944 22 12.5 22C7.80558 22 4 18.1944 4 13.5V8C4 4.68629 6.68629 2 10 2C13.3137 2 16 4.68629 16 8V13.5C16 15.433 14.433 17 12.5 17C10.567 17 9 15.433 9 13.5V8H11V13.5C11 14.3284 11.6716 15 12.5 15C13.3284 15 14 14.3284 14 13.5Z\"></path>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ position: \"absolute\", opacity: \"0\", top: \"331px\", left: \"1146.5px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>Hyperlink</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ top: \"-3px\", transform: \"rotate(45deg)\", left: \"30.5px\" }}></div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"tooltip-wrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"clone-button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"action-clone\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"right-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"svg-block\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<path d=\"M6.99979 7V3C6.99979 2.44772 7.4475 2 7.99979 2H20.9998C21.5521 2 21.9998 2.44772 21.9998 3V16C21.9998 16.5523 21.5521 17 20.9998 17H17V20.9925C17 21.5489 16.551 22 15.9925 22H3.00728C2.45086 22 2 21.5511 2 20.9925L2.00276 8.00748C2.00288 7.45107 2.4518 7 3.01025 7H6.99979ZM8.99979 7H15.9927C16.549 7 17 7.44892 17 8.00748V15H19.9998V4H8.99979V7ZM4.00255 9L4.00021 20H15V9H4.00255Z\"></path>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ position: \"absolute\", opacity: \"0\", top: \"331px\", left: \"1187.5px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>Clone</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ top: \"-3px\", transform: \"rotate(45deg)\", left: \"21.5px;\" }}></div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"tooltip-wrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"settings-button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdata-testid=\"action-settings\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"right-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"svg-block\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<path d=\"M2 11.9998C2 11.1353 2.1097 10.2964 2.31595 9.49631C3.40622 9.55283 4.48848 9.01015 5.0718 7.99982C5.65467 6.99025 5.58406 5.78271 4.99121 4.86701C6.18354 3.69529 7.66832 2.82022 9.32603 2.36133C9.8222 3.33385 10.8333 3.99982 12 3.99982C13.1667 3.99982 14.1778 3.33385 14.674 2.36133C16.3317 2.82022 17.8165 3.69529 19.0088 4.86701C18.4159 5.78271 18.3453 6.99025 18.9282 7.99982C19.5115 9.01015 20.5938 9.55283 21.6841 9.49631C21.8903 10.2964 22 11.1353 22 11.9998C22 12.8643 21.8903 13.7032 21.6841 14.5033C20.5938 14.4468 19.5115 14.9895 18.9282 15.9998C18.3453 17.0094 18.4159 18.2169 19.0088 19.1326C17.8165 20.3043 16.3317 21.1794 14.674 21.6383C14.1778 20.6658 13.1667 19.9998 12 19.9998C10.8333 19.9998 9.8222 20.6658 9.32603 21.6383C7.66832 21.1794 6.18354 20.3043 4.99121 19.1326C5.58406 18.2169 5.65467 17.0094 5.0718 15.9998C4.48848 14.9895 3.40622 14.4468 2.31595 14.5033C2.1097 13.7032 2 12.8643 2 11.9998ZM6.80385 14.9998C7.43395 16.0912 7.61458 17.3459 7.36818 18.5236C7.77597 18.8138 8.21005 19.0652 8.66489 19.2741C9.56176 18.4712 10.7392 17.9998 12 17.9998C13.2608 17.9998 14.4382 18.4712 15.3351 19.2741C15.7899 19.0652 16.224 18.8138 16.6318 18.5236C16.3854 17.3459 16.566 16.0912 17.1962 14.9998C17.8262 13.9085 18.8225 13.1248 19.9655 12.7493C19.9884 12.5015 20 12.2516 20 11.9998C20 11.7481 19.9884 11.4981 19.9655 11.2504C18.8225 10.8749 17.8262 10.0912 17.1962 8.99982C16.566 7.90845 16.3854 6.65378 16.6318 5.47605C16.224 5.18588 15.7899 4.93447 15.3351 4.72552C14.4382 5.52844 13.2608 5.99982 12 5.99982C10.7392 5.99982 9.56176 5.52844 8.66489 4.72552C8.21005 4.93447 7.77597 5.18588 7.36818 5.47605C7.61458 6.65378 7.43395 7.90845 6.80385 8.99982C6.17376 10.0912 5.17754 10.8749 4.03451 11.2504C4.01157 11.4981 4 11.7481 4 11.9998C4 12.2516 4.01157 12.5015 4.03451 12.7493C5.17754 13.1248 6.17376 13.9085 6.80385 14.9998ZM12 14.9998C10.3431 14.9998 9 13.6567 9 11.9998C9 10.343 10.3431 8.99982 12 8.99982C13.6569 8.99982 15 10.343 15 11.9998C15 13.6567 13.6569 14.9998 12 14.9998ZM12 12.9998C12.5523 12.9998 13 12.5521 13 11.9998C13 11.4475 12.5523 10.9998 12 10.9998C11.4477 10.9998 11 11.4475 11 11.9998C11 12.5521 11.4477 12.9998 12 12.9998Z\"></path>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ position: \"absolute\", opacity: \"0\", top: \"331px\", left: \"1213.5px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>Settings</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ top: \"-3px\", transform: \"rotate(45deg)\", left: \"27.5px\" }}></div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"tooltip-wrapper\">\r\n\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"delete-button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"del-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"svg-block\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<path d=\"M7 4V2H17V4H22V6H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V6H2V4H7ZM6 6V20H18V6H6ZM9 9H11V17H9V9ZM13 9H15V17H13V9Z\"></path>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ position: \"absolute\", opacity: \"0\", top: \"331px\", left: \"1250px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>Delete</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ top: \"-3px\", transform: \"rotate(45deg)\", left: \"23px\" }}></div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"default-text\">Default</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t)\r\n\t\t\t})}\r\n\t\t</>\r\n\t)\r\n}\r\n\r\nexport default GuideTable\r\n", "  \r\nimport React from 'react';\r\nimport GuideTable from \"./GuideTable\"\r\n\r\nconst guidesList = () => {\r\n  return (\r\n\t\t<div className=\"guides\">\r\n\t\t\t<span className=\"guides-text\">Guides</span>\r\n\t\t\t<div className=\"guides-table\">\r\n\t\t\t\t<div className=\"search-panel\">\r\n\t\t\t\t\t<div className=\"search-block\">\r\n\t\t\t\t\t\t<label className=\"search-label\">\r\n\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\tcolor=\"#919DB5\"\r\n\t\t\t\t\t\t\t\theight=\"1em\"\r\n\t\t\t\t\t\t\t\twidth=\"1em\"\r\n\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\tstyle={{ color: \" rgb(145, 157, 181)\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<path d=\"M11 2C15.968 2 20 6.032 20 11C20 15.968 15.968 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2ZM11 18C14.8675 18 18 14.8675 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18ZM19.4853 18.0711L22.3137 20.8995L20.8995 22.3137L18.0711 19.4853L19.4853 18.0711Z\"></path>\r\n\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\tclassName=\"search-input\"\r\n\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"Search\"\r\n\t\t\t\t\t\t\t\tvalue=\"\"\r\n\t\t\t\t\t\t\t></input>\r\n\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t<div className=\"search-filter\">\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tid=\"filter-button\"\r\n\t\t\t\t\t\t\t\tclassName=\"filter-button\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\theight=\"17\"\r\n\t\t\t\t\t\t\t\t\twidth=\"17\"\r\n\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<path d=\"M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z\"></path>\r\n\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t\t<span className=\"filter-span\">Filter</span>\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<button className=\"newguide-button\">\r\n\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\theight=\"17\"\r\n\t\t\t\t\t\t\t\twidth=\"17\"\r\n\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<path d=\"M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z\"></path>\r\n\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t<span className=\"sc-cwHptR lnyVkX\">New Guide</span>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"select-box\">\r\n\t\t\t\t\t<div className=\"sc-dAlyuH select-svg\">\r\n\t\t\t\t\t\t<label\r\n\t\t\t\t\t\t\tdata-testid=\"checkbox-container-test-id\"\r\n\t\t\t\t\t\t\tclassName=\"sc-dAbbOL jvijPe\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<input type=\"checkbox\" />\r\n\t\t\t\t\t\t\t\t<div></div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<span>Select All</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</label>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"select-panel\">\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tid=\"clone-button\"\r\n\t\t\t\t\t\t\tclassName=\"select-button\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"sc-dAlyuH select-svg\">\r\n\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<path d=\"M6.99979 7V3C6.99979 2.44772 7.4475 2 7.99979 2H20.9998C21.5521 2 21.9998 2.44772 21.9998 3V16C21.9998 16.5523 21.5521 17 20.9998 17H17V20.9925C17 21.5489 16.551 22 15.9925 22H3.00728C2.45086 22 2 21.5511 2 20.9925L2.00276 8.00748C2.00288 7.45107 2.4518 7 3.01025 7H6.99979ZM8.99979 7H15.9927C16.549 7 17 7.44892 17 8.00748V15H19.9998V4H8.99979V7ZM4.00255 9L4.00021 20H15V9H4.00255Z\"></path>\r\n\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tid=\"delete-button\"\r\n\t\t\t\t\t\t\tclassName=\"del-button\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"sc-dAlyuH select-svg\">\r\n\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<path d=\"M7 4V2H17V4H22V6H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V6H2V4H7ZM6 6V20H18V6H6ZM9 9H11V17H9V9ZM13 9H15V17H13V9Z\"></path>\r\n\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tid=\"status-button\"\r\n\t\t\t\t\t\t\tclassName=\"select-button\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"sc-dAlyuH select-svg\">\r\n\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<path d=\"M13 10H20L11 23V14H4L13 1V10Z\"></path>\r\n\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tid=\"theme-button\"\r\n\t\t\t\t\t\t\tclassName=\"select-button\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"sc-dAlyuH select-svg\">\r\n\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<path d=\"M15.4565 9.67491L15.3144 9.53285C14.6661 8.90783 13.8549 8.43357 12.9235 8.18399C10.0168 7.40515 7.22541 9.05261 6.43185 12.0142C6.38901 12.1741 6.36574 12.3536 6.3285 12.805C6.17423 14.675 5.73449 16.0696 4.5286 17.4841C6.78847 18.3726 9.46572 18.9984 11.5016 18.9984C13.9702 18.9984 16.1644 17.3393 16.8126 14.9201C17.3306 12.9868 16.7513 11.018 15.4565 9.67491ZM13.2886 6.21289L18.2278 2.3713C18.6259 2.06168 19.1922 2.09694 19.5488 2.45355L22.543 5.44774C22.8997 5.80435 22.9349 6.3707 22.6253 6.76879L18.7847 11.7067C19.0778 12.895 19.0836 14.1719 18.7444 15.4377C17.8463 18.7896 14.8142 20.9984 11.5016 20.9984C8 20.9984 3.5 19.4966 1 17.9966C4.97978 14.9966 4.04722 13.1864 4.5 11.4966C5.55843 7.54646 9.34224 5.23923 13.2886 6.21289ZM16.7015 8.09149C16.7673 8.15494 16.8319 8.21952 16.8952 8.2852L18.0297 9.41972L20.5046 6.23774L18.7589 4.49198L15.5769 6.96685L16.7015 8.09149Z\"></path>\r\n\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tid=\"container-button\"\r\n\t\t\t\t\t\t\tclassName=\"select-button\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"sc-dAlyuH select-svg\">\r\n\t\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\t\t\t\t\t\tstroke-width=\"0\"\r\n\t\t\t\t\t\t\t\t\tviewBox=\"0 0 24 24\"\r\n\t\t\t\t\t\t\t\t\theight=\"20\"\r\n\t\t\t\t\t\t\t\t\twidth=\"20\"\r\n\t\t\t\t\t\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<path d=\"M3 10H2V4.00293C2 3.44903 2.45531 3 2.9918 3H21.0082C21.556 3 22 3.43788 22 4.00293V10H21V20.0015C21 20.553 20.5551 21 20.0066 21H3.9934C3.44476 21 3 20.5525 3 20.0015V10ZM19 10H5V19H19V10ZM4 5V8H20V5H4ZM9 12H15V14H9V12Z\"></path>\r\n\t\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<GuideTable />\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t)\r\n  };\r\n\r\nexport default guidesList\r\n "], "names": ["_jsx", "_Fragment", "children", "name", "id", "map", "data", "className", "_jsxs", "type", "color", "stroke", "fill", "viewBox", "height", "width", "xmlns", "style", "d", "padding", "position", "opacity", "top", "left", "transform", "guidesList", "placeholder", "value", "GuideTable"], "sourceRoot": ""}