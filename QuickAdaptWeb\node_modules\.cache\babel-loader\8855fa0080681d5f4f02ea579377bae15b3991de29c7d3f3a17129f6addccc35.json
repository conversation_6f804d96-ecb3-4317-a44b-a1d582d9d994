{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\dashboard\\\\ModernDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Container, Tabs, Tab } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { TrendingUp, TrendingDown, People, CheckCircle, Star, Schedule, FilterList, CalendarToday } from '@mui/icons-material';\nimport Card from '../common/Card';\nimport ModernButton from '../common/ModernButton';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DashboardWrapper = styled('div')({\n  backgroundColor: '#f6f9ff',\n  minHeight: '100vh'\n});\n_c = DashboardWrapper;\nconst HeaderSection = styled(Box)({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: 'var(--spacing-4)'\n});\n_c2 = HeaderSection;\nconst StyledTabs = styled(Tabs)({\n  marginBottom: 'var(--spacing-4)',\n  '& .MuiTabs-indicator': {\n    backgroundColor: 'var(--color-primary-600)'\n  },\n  '& .MuiTab-root': {\n    fontSize: 'var(--font-size-sm)',\n    fontWeight: 'var(--font-weight-medium)',\n    textTransform: 'none',\n    color: 'var(--color-gray-600)',\n    '&.Mui-selected': {\n      color: 'var(--color-primary-600)',\n      fontWeight: 'var(--font-weight-semibold)'\n    }\n  }\n});\n_c3 = StyledTabs;\nconst FilterSection = styled(Box)({\n  display: 'flex',\n  gap: 'var(--spacing-3)',\n  alignItems: 'center'\n});\n_c4 = FilterSection;\nconst MetricsGrid = styled(Box)({\n  display: 'grid',\n  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n  gap: 'var(--spacing-4)',\n  marginBottom: 'var(--spacing-6)'\n});\n_c5 = MetricsGrid;\nconst MetricCardContainer = styled(Card)({\n  padding: 'var(--spacing-5)',\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-4)'\n});\n_c6 = MetricCardContainer;\nconst MetricIcon = styled(Box)(({\n  color\n}) => ({\n  width: '48px',\n  height: '48px',\n  borderRadius: 'var(--radius-lg)',\n  backgroundColor: `${color}15`,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  '& svg': {\n    color: color,\n    fontSize: '24px'\n  }\n}));\n_c7 = MetricIcon;\nconst MetricContent = styled(Box)({\n  flex: 1\n});\n_c8 = MetricContent;\nconst MetricTitle = styled(Typography)({\n  fontSize: 'var(--font-size-sm)',\n  color: 'var(--color-gray-600)',\n  marginBottom: 'var(--spacing-1)'\n});\n_c9 = MetricTitle;\nconst MetricValue = styled(Typography)({\n  fontSize: 'var(--font-size-2xl)',\n  fontWeight: 'var(--font-weight-bold)',\n  color: 'var(--color-gray-900)',\n  marginBottom: 'var(--spacing-1)'\n});\n_c0 = MetricValue;\nconst MetricChange = styled(Box)({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)'\n});\n_c1 = MetricChange;\nconst ChangeIndicator = styled(Box)(({\n  trend\n}) => ({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)',\n  fontSize: 'var(--font-size-xs)',\n  fontWeight: 'var(--font-weight-medium)',\n  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',\n  '& svg': {\n    fontSize: '16px'\n  }\n}));\n_c10 = ChangeIndicator;\nconst MetricCard = ({\n  title,\n  value,\n  change,\n  changeValue,\n  trend,\n  icon,\n  color\n}) => {\n  return /*#__PURE__*/_jsxDEV(MetricCardContainer, {\n    shadow: \"sm\",\n    hover: true,\n    children: [/*#__PURE__*/_jsxDEV(MetricIcon, {\n      color: color,\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricContent, {\n      children: [/*#__PURE__*/_jsxDEV(MetricTitle, {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricChange, {\n        children: [/*#__PURE__*/_jsxDEV(ChangeIndicator, {\n          trend: trend,\n          children: [trend === 'up' ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 48\n          }, this), change]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: changeValue\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_c11 = MetricCard;\nconst ModernDashboard = () => {\n  _s();\n  const [selectedTab, setSelectedTab] = useState(0);\n  const [selectedGuideType, setSelectedGuideType] = useState(null);\n  const [selectedGuideFromList, setSelectedGuideFromList] = useState(null);\n  const [tooltip, setTooltip] = useState({\n    visible: false,\n    x: 0,\n    y: 0,\n    content: '',\n    title: ''\n  });\n\n  // Mock data for guide types and guides\n  const guideTypes = [{\n    id: 'tours',\n    name: 'Tours',\n    count: 8,\n    completionRate: 85,\n    color: '#8b5cf6'\n  }, {\n    id: 'banners',\n    name: 'Banners',\n    count: 12,\n    completionRate: 92,\n    color: '#06b6d4'\n  }, {\n    id: 'announcements',\n    name: 'Announcements',\n    count: 6,\n    completionRate: 78,\n    color: '#f59e0b'\n  }, {\n    id: 'tooltips',\n    name: 'Tooltips',\n    count: 15,\n    completionRate: 88,\n    color: '#10b981'\n  }, {\n    id: 'modals',\n    name: 'Modals',\n    count: 4,\n    completionRate: 73,\n    color: '#ef4444'\n  }, {\n    id: 'hotspots',\n    name: 'Hotspots',\n    count: 9,\n    completionRate: 81,\n    color: '#f97316'\n  }];\n  const guidesByType = {\n    tours: [{\n      id: 'tour-1',\n      name: 'Product Onboarding',\n      views: 1400,\n      completed: 1247,\n      dropOff: 11,\n      status: 'excellent',\n      lastUpdated: '2 days ago'\n    }, {\n      id: 'tour-2',\n      name: 'Feature Discovery',\n      views: 1174,\n      completed: 892,\n      dropOff: 24,\n      status: 'good',\n      lastUpdated: '1 day ago'\n    }, {\n      id: 'tour-3',\n      name: 'Advanced Settings',\n      views: 962,\n      completed: 634,\n      dropOff: 32,\n      status: 'needs attention',\n      lastUpdated: '5 days ago'\n    }],\n    banners: [{\n      id: 'banner-1',\n      name: 'Welcome Banner',\n      views: 2100,\n      completed: 1932,\n      dropOff: 8,\n      status: 'excellent',\n      lastUpdated: '1 day ago'\n    }, {\n      id: 'banner-2',\n      name: 'Feature Announcement',\n      views: 1850,\n      completed: 1665,\n      dropOff: 10,\n      status: 'excellent',\n      lastUpdated: '3 days ago'\n    }],\n    announcements: [{\n      id: 'announcement-1',\n      name: 'New Feature Release',\n      views: 980,\n      completed: 764,\n      dropOff: 22,\n      status: 'good',\n      lastUpdated: '1 week ago'\n    }, {\n      id: 'announcement-2',\n      name: 'Maintenance Notice',\n      views: 1200,\n      completed: 936,\n      dropOff: 22,\n      status: 'good',\n      lastUpdated: '2 days ago'\n    }],\n    tooltips: [{\n      id: 'tooltip-1',\n      name: 'Button Helper',\n      views: 3200,\n      completed: 2816,\n      dropOff: 12,\n      status: 'excellent',\n      lastUpdated: '1 day ago'\n    }, {\n      id: 'tooltip-2',\n      name: 'Form Guidance',\n      views: 2800,\n      completed: 2464,\n      dropOff: 12,\n      status: 'excellent',\n      lastUpdated: '2 days ago'\n    }],\n    modals: [{\n      id: 'modal-1',\n      name: 'Confirmation Dialog',\n      views: 1500,\n      completed: 1095,\n      dropOff: 27,\n      status: 'needs attention',\n      lastUpdated: '4 days ago'\n    }],\n    hotspots: [{\n      id: 'hotspot-1',\n      name: 'Navigation Helper',\n      views: 1100,\n      completed: 891,\n      dropOff: 19,\n      status: 'good',\n      lastUpdated: '3 days ago'\n    }]\n  };\n  const showTooltip = (event, title, content) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n    setTooltip({\n      visible: true,\n      x: rect.left + scrollX + rect.width / 2,\n      y: rect.top + scrollY - 10,\n      content,\n      title\n    });\n  };\n  const hideTooltip = () => {\n    setTooltip(prev => ({\n      ...prev,\n      visible: false\n    }));\n  };\n  const overviewMetrics = [{\n    title: 'Completion Rate',\n    value: '87.3%',\n    change: '****%',\n    changeValue: '+2.8pp',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'User Satisfaction',\n    value: '4.6',\n    change: '+0.2',\n    changeValue: 'out of 5.0',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Hours Saved',\n    value: '2,847',\n    change: '+18.7%',\n    changeValue: '+447h',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }];\n  const analyticsMetrics = [{\n    title: 'Active Users',\n    value: '12,847',\n    change: '+12.5%',\n    changeValue: '+1,432',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }, {\n    title: 'Completion Rate',\n    value: '87.3%',\n    change: '****%',\n    changeValue: '+2.8pp',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'User Satisfaction',\n    value: '4.6',\n    change: '+0.2',\n    changeValue: 'out of 5.0',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Hours Saved',\n    value: '2,847',\n    change: '+18.7%',\n    changeValue: '+447h',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }];\n  const aiPerformanceMetrics = [{\n    title: 'AI Response Time',\n    value: '1.2s',\n    change: '-15%',\n    changeValue: 'faster',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'AI Accuracy',\n    value: '94.8%',\n    change: '+2.1%',\n    changeValue: 'improved',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }, {\n    title: 'Model Confidence',\n    value: '89.3%',\n    change: '+1.8%',\n    changeValue: 'higher',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Processing Load',\n    value: '67%',\n    change: '+5%',\n    changeValue: 'capacity',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-error-500)'\n  }];\n  const handleTabChange = (event, newValue) => {\n    setSelectedTab(newValue);\n  };\n  const renderTabContent = () => {\n    switch (selectedTab) {\n      case 0:\n        // Overview\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(HeaderSection, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                children: \"Dashboard Overview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FilterSection, {\n              children: [/*#__PURE__*/_jsxDEV(ModernButton, {\n                variant: \"outline\",\n                startIcon: /*#__PURE__*/_jsxDEV(FilterList, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 30\n                }, this),\n                size: \"sm\",\n                children: \"Filter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ModernButton, {\n                variant: \"outline\",\n                startIcon: /*#__PURE__*/_jsxDEV(CalendarToday, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 30\n                }, this),\n                size: \"sm\",\n                children: \"Last 30 days\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MetricsGrid, {\n            children: analyticsMetrics.map((metric, index) => /*#__PURE__*/_jsxDEV(MetricCard, {\n              ...metric\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDCC8 User Activity Trends\",\n              subtitle: \"Active, retained, and total users over time\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  position: 'relative',\n                  backgroundColor: 'white',\n                  borderRadius: 'var(--radius-md)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"100%\",\n                  height: \"280\",\n                  viewBox: \"0 0 450 250\",\n                  style: {\n                    overflow: 'visible'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n                    children: /*#__PURE__*/_jsxDEV(\"pattern\", {\n                      id: \"activityGrid\",\n                      width: \"200\",\n                      height: \"50\",\n                      patternUnits: \"userSpaceOnUse\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M 200 0 L 0 0 0 50\",\n                        fill: \"none\",\n                        stroke: \"#f1f5f9\",\n                        strokeWidth: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    width: \"100%\",\n                    height: \"200\",\n                    fill: \"url(#activityGrid)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"50\",\n                    y1: \"20\",\n                    x2: \"50\",\n                    y2: \"200\",\n                    stroke: \"#e2e8f0\",\n                    strokeWidth: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"50\",\n                    y1: \"200\",\n                    x2: \"400\",\n                    y2: \"200\",\n                    stroke: \"#e2e8f0\",\n                    strokeWidth: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"25\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"60000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"65\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"45000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"105\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"30000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"145\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"15000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"205\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"125\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Week 1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"325\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Week 2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 125 50 L 325 45\",\n                    fill: \"none\",\n                    stroke: \"#8b5cf6\",\n                    strokeWidth: \"3\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"125\",\n                    cy: \"50\",\n                    r: \"5\",\n                    fill: \"#8b5cf6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"325\",\n                    cy: \"45\",\n                    r: \"5\",\n                    fill: \"#8b5cf6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 125 80 L 325 75\",\n                    fill: \"none\",\n                    stroke: \"#3b82f6\",\n                    strokeWidth: \"3\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"125\",\n                    cy: \"80\",\n                    r: \"5\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"325\",\n                    cy: \"75\",\n                    r: \"5\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 125 110 L 325 105\",\n                    fill: \"none\",\n                    stroke: \"#10b981\",\n                    strokeWidth: \"3\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"125\",\n                    cy: \"110\",\n                    r: \"5\",\n                    fill: \"#10b981\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"325\",\n                    cy: \"105\",\n                    r: \"5\",\n                    fill: \"#10b981\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"125\",\n                    y: \"40\",\n                    fontSize: \"10\",\n                    fill: \"#8b5cf6\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"36000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"325\",\n                    y: \"35\",\n                    fontSize: \"10\",\n                    fill: \"#8b5cf6\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"37000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"125\",\n                    y: \"70\",\n                    fontSize: \"10\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"30000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"325\",\n                    y: \"65\",\n                    fontSize: \"10\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"31000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"125\",\n                    y: \"100\",\n                    fontSize: \"10\",\n                    fill: \"#10b981\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"24000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"325\",\n                    y: \"95\",\n                    fontSize: \"10\",\n                    fill: \"#10b981\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"25000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    position: 'absolute',\n                    bottom: 10,\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    display: 'flex',\n                    gap: 3,\n                    backgroundColor: 'rgba(255,255,255,0.9)',\n                    padding: '8px 16px',\n                    borderRadius: '8px',\n                    border: '1px solid #e2e8f0'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 12,\n                        height: 12,\n                        backgroundColor: '#3b82f6',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b'\n                      },\n                      children: \"Active\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 12,\n                        height: 12,\n                        backgroundColor: '#10b981',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b'\n                      },\n                      children: \"Retained\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 12,\n                        height: 12,\n                        backgroundColor: '#8b5cf6',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b'\n                      },\n                      children: \"Total\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uFFFD Feature Adoption Distribution\",\n              subtitle: \"Interactive feature usage metrics\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center',\n                  alignItems: 'center',\n                  backgroundColor: 'white',\n                  borderRadius: 'var(--radius-md)',\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"180\",\n                  height: \"180\",\n                  viewBox: \"0 0 180 180\",\n                  style: {\n                    marginBottom: '20px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#3b82f6\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"94 377\",\n                    strokeDashoffset: \"0\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#f97316\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"68 377\",\n                    strokeDashoffset: \"-94\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#14b8a6\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"45 377\",\n                    strokeDashoffset: \"-162\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#10b981\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"75 377\",\n                    strokeDashoffset: \"-207\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#8b5cf6\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"71 377\",\n                    strokeDashoffset: \"-282\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#ef4444\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"23 377\",\n                    strokeDashoffset: \"-353\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"35\",\n                    fill: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"90\",\n                    y: \"85\",\n                    fontSize: \"16\",\n                    fill: \"#1f2937\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"bold\",\n                    children: \"100%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"90\",\n                    y: \"100\",\n                    fontSize: \"11\",\n                    fill: \"#6b7280\",\n                    textAnchor: \"middle\",\n                    children: \"Total\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(3, 1fr)',\n                    gap: 2,\n                    width: '100%',\n                    maxWidth: '400px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#3b82f6',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 526,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Tours\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"25%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#10b981',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 534,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Tooltips\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 535,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"20%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#8b5cf6',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 542,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"AI Assistant\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 543,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 541,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"19%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#f97316',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 550,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Banners\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 551,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"18%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#14b8a6',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 558,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Hotspots\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 559,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"12%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 561,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#ef4444',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 566,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Checklists\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 567,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"6%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 569,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u2B50 User Satisfaction Ratings\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 'var(--spacing-4)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#10b981',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Excellent (5\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 646,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: '#10b981',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 658,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"1,247\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#84cc16',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Good (4\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '45%',\n                        height: '100%',\n                        backgroundColor: '#84cc16',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 686,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"892\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 693,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#f59e0b',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Average (3\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '22%',\n                        height: '100%',\n                        backgroundColor: '#f59e0b',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"434\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#f97316',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Poor (2\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '8%',\n                        height: '100%',\n                        backgroundColor: '#f97316',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"123\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#ef4444',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Very Poor (1\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 758,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 756,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '4%',\n                        height: '100%',\n                        backgroundColor: '#ef4444',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 770,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"57\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(3, 1fr)',\n                    gap: 2,\n                    mt: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#f0fdf4',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#16a34a\",\n                      children: \"77%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 790,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#16a34a\",\n                      children: \"Positive\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 793,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#fffbeb',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#d97706\",\n                      children: \"16%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 803,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#d97706\",\n                      children: \"Neutral\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#fef2f2',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#dc2626\",\n                      children: \"7%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#dc2626\",\n                      children: \"Negative\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 819,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 810,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDCC8 Satisfaction Trend\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center',\n                  alignItems: 'center',\n                  backgroundColor: '#f8fafc',\n                  borderRadius: 'var(--radius-md)',\n                  position: 'relative'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"100%\",\n                  height: \"250\",\n                  viewBox: \"0 0 400 200\",\n                  style: {\n                    overflow: 'visible'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n                    children: /*#__PURE__*/_jsxDEV(\"pattern\", {\n                      id: \"feedbackGrid\",\n                      width: \"66.67\",\n                      height: \"40\",\n                      patternUnits: \"userSpaceOnUse\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M 66.67 0 L 0 0 0 40\",\n                        fill: \"none\",\n                        stroke: \"#e2e8f0\",\n                        strokeWidth: \"0.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    width: \"100%\",\n                    height: \"100%\",\n                    fill: \"url(#feedbackGrid)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"20\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"60\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.75\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"100\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 843,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"140\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.25\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"180\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"50\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Jan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"110\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Feb\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"170\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Mar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"230\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Apr\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 851,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"290\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"May\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"350\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Jun\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 853,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88\",\n                    fill: \"none\",\n                    stroke: \"#3b82f6\",\n                    strokeWidth: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"168\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"110\",\n                    cy: \"152\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"170\",\n                    cy: \"136\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"230\",\n                    cy: \"120\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"290\",\n                    cy: \"104\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"350\",\n                    cy: \"88\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"50\",\n                    y: \"160\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"110\",\n                    y: \"144\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"170\",\n                    y: \"128\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"230\",\n                    y: \"112\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"290\",\n                    y: \"96\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"350\",\n                    y: \"80\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\uD83D\\uDCCA Feedback Summary\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(4, 1fr)',\n                gap: 'var(--spacing-6)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f8fafc',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"text.primary\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"2,238\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Feedback\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0fdf4',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#16a34a\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"85.8%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 903,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Positive Sentiment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 906,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#eff6ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#2563eb\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"4.6/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Average Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 912,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#fdf4ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#9333ea\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"+12%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"vs Last Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 1:\n        // Analytics\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"Guide Performance Overview\",\n            subtitle: \"Click on any guide to see detailed funnel analysis\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 'var(--spacing-4)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"semibold\",\n                      children: \"Product Onboarding\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1009,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e8f5e9',\n                        color: '#2e7d32',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      },\n                      children: \"excellent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1012,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      },\n                      children: \"Onboarding\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1008,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 3,\n                      color: 'var(--color-gray-600)',\n                      fontSize: '14px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 4,\n                          height: 4,\n                          borderRadius: '50%',\n                          backgroundColor: 'var(--color-gray-400)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1036,\n                        columnNumber: 25\n                      }, this), \"1,400 views\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1035,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        sx: {\n                          fontSize: 16,\n                          color: 'var(--color-success-500)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1040,\n                        columnNumber: 25\n                      }, this), \"1,247 completed\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1039,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        color: 'var(--color-success-600)',\n                        fontWeight: 'medium'\n                      },\n                      children: \"11% drop-off\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1043,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      children: \"Updated 2 days ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1046,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1034,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: \"89%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1052,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '89%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1055,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"semibold\",\n                      children: \"Feature Discovery\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1087,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#e3f2fd',\n                        color: '#1976d2',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      },\n                      children: \"good\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1090,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      },\n                      children: \"Feature\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1101,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1086,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 3,\n                      color: 'var(--color-gray-600)',\n                      fontSize: '14px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 4,\n                          height: 4,\n                          borderRadius: '50%',\n                          backgroundColor: 'var(--color-gray-400)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1114,\n                        columnNumber: 25\n                      }, this), \"1,174 views\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1113,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        sx: {\n                          fontSize: 16,\n                          color: 'var(--color-success-500)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1118,\n                        columnNumber: 25\n                      }, this), \"892 completed\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1117,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        color: 'var(--color-warning-600)',\n                        fontWeight: 'medium'\n                      },\n                      children: \"24% drop-off\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1121,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      children: \"Updated 1 day ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1124,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1112,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: \"76%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1130,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '76%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1140,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1133,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1129,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1073,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                  p: 3,\n                  border: '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  '&:hover': {\n                    backgroundColor: 'var(--color-gray-50)',\n                    cursor: 'pointer'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"semibold\",\n                      children: \"Advanced Settings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1165,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: '#fff3e0',\n                        color: '#f57c00',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px',\n                        fontWeight: 'medium'\n                      },\n                      children: \"needs attention\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1168,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        px: 1.5,\n                        py: 0.5,\n                        backgroundColor: 'var(--color-gray-100)',\n                        color: 'var(--color-gray-700)',\n                        borderRadius: 'var(--radius-sm)',\n                        fontSize: '12px'\n                      },\n                      children: \"Configuration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1179,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1164,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 3,\n                      color: 'var(--color-gray-600)',\n                      fontSize: '14px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 4,\n                          height: 4,\n                          borderRadius: '50%',\n                          backgroundColor: 'var(--color-gray-400)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1192,\n                        columnNumber: 25\n                      }, this), \"962 views\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1191,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        sx: {\n                          fontSize: 16,\n                          color: 'var(--color-success-500)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1196,\n                        columnNumber: 25\n                      }, this), \"634 completed\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1195,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        color: 'var(--color-error-600)',\n                        fontWeight: 'medium'\n                      },\n                      children: \"32% drop-off\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1199,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      children: \"Updated 5 days ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1202,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1190,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    children: \"65%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1208,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 80,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: 'var(--color-gray-800)',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1218,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1211,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1207,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 993,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 13\n          }, this)\n        }, void 0, false);\n      case 2:\n        // AI Performance\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(3, 1fr)',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#3b82f6\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Total Interactions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1248,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e3f2fd',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#3b82f6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1260,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1251,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"2,847\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"+12% from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1279,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#10b981\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Success Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e8f5e9',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#10b981',\n                      borderRadius: '50%',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1306,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1297,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"91%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"+3% improvement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#8b5cf6\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Avg Response Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#f3e8ff',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#8b5cf6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\u26A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1352,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1343,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"1.9s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1368,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"-0.3s faster\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1371,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1331,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"AI Task Performance\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 'var(--spacing-4)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Password Reset\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1396,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e8f5e9',\n                          color: '#2e7d32',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"96%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1399,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"342 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1410,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1395,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 1.2s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1415,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-success-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"+2% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1418,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1414,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1394,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"96%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1424,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '96%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1434,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1427,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1423,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Account Setup\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1459,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e3f2fd',\n                          color: '#1976d2',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"89%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1462,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"198 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1473,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1458,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 1.4s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1478,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-5% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1481,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1477,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1457,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"89%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1487,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '89%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1497,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1490,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1486,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1445,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Feature Explanation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1522,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e3f2fd',\n                          color: '#1976d2',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"90%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1525,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"267 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1536,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1521,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 2.1s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1541,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-1% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1544,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1540,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1520,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"90%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1550,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '90%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1560,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1553,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1549,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1508,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Troubleshooting\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1585,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#fff3e0',\n                          color: '#f57c00',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"88%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1588,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"156 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1599,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1584,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 3.1s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1604,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-3% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1607,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1603,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1583,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"88%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1613,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '88%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1623,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1616,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1612,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1571,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Integration Help\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1648,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#fff3e0',\n                          color: '#f57c00',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"87%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1651,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"123 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1662,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1647,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 2.5s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1667,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-success-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"+1% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1670,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1666,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1646,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"87%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1676,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '87%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1686,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1679,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1675,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1634,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1379,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"AI Insights & Recommendations\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 'var(--spacing-3)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#fffbeb',\n                  border: '1px solid #fbbf24',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#f59e0b',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1712,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Optimize Workflow\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1720,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Consider optimizing your workflow to reduce response time by 15%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1723,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1719,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1703,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1739,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Excluded Performance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1747,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Excluded tasks are performing well with 94% accuracy rate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1750,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1746,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1730,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1766,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Personalized Suggestions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1774,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"AI suggests implementing advanced filtering for better user experience\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1777,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1773,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1757,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1701,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1700,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-web\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-webcontent\",\n      children: [/*#__PURE__*/_jsxDEV(DashboardWrapper, {\n        children: /*#__PURE__*/_jsxDEV(Container, {\n          maxWidth: \"xl\",\n          sx: {\n            py: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(StyledTabs, {\n            value: selectedTab,\n            onChange: handleTabChange,\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1801,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1802,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"AI Performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1803,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1800,\n            columnNumber: 13\n          }, this), renderTabContent()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1798,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1797,\n        columnNumber: 9\n      }, this), tooltip.visible && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          left: tooltip.x,\n          top: tooltip.y,\n          transform: 'translate(-50%, -100%)',\n          backgroundColor: 'white',\n          border: '1px solid #d1d5db',\n          borderRadius: '6px',\n          padding: '6px 10px',\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n          zIndex: 10000,\n          pointerEvents: 'none',\n          fontSize: '11px',\n          minWidth: '70px',\n          textAlign: 'center',\n          fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontSize: '10px',\n            color: '#6b7280',\n            mb: 0.2,\n            lineHeight: 1.2\n          },\n          children: tooltip.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1832,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontSize: '11px',\n            color: '#111827',\n            fontWeight: '600',\n            lineHeight: 1.2\n          },\n          children: tooltip.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1835,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1813,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1796,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1795,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernDashboard, \"1JmCDWGblFA/3wVdJ/vZ4uKnqII=\");\n_c12 = ModernDashboard;\nexport default ModernDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"DashboardWrapper\");\n$RefreshReg$(_c2, \"HeaderSection\");\n$RefreshReg$(_c3, \"StyledTabs\");\n$RefreshReg$(_c4, \"FilterSection\");\n$RefreshReg$(_c5, \"MetricsGrid\");\n$RefreshReg$(_c6, \"MetricCardContainer\");\n$RefreshReg$(_c7, \"MetricIcon\");\n$RefreshReg$(_c8, \"MetricContent\");\n$RefreshReg$(_c9, \"MetricTitle\");\n$RefreshReg$(_c0, \"MetricValue\");\n$RefreshReg$(_c1, \"MetricChange\");\n$RefreshReg$(_c10, \"ChangeIndicator\");\n$RefreshReg$(_c11, \"MetricCard\");\n$RefreshReg$(_c12, \"ModernDashboard\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Container", "Tabs", "Tab", "styled", "TrendingUp", "TrendingDown", "People", "CheckCircle", "Star", "Schedule", "FilterList", "CalendarToday", "Card", "ModernButton", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DashboardWrapper", "backgroundColor", "minHeight", "_c", "HeaderSection", "display", "justifyContent", "alignItems", "marginBottom", "_c2", "StyledTabs", "fontSize", "fontWeight", "textTransform", "color", "_c3", "FilterSection", "gap", "_c4", "MetricsGrid", "gridTemplateColumns", "_c5", "MetricCardContainer", "padding", "_c6", "MetricIcon", "width", "height", "borderRadius", "_c7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flex", "_c8", "MetricTitle", "_c9", "MetricValue", "_c0", "MetricChange", "_c1", "ChangeIndicator", "trend", "_c10", "MetricCard", "title", "value", "change", "changeValue", "icon", "shadow", "hover", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "_c11", "ModernDashboard", "_s", "selectedTab", "setSelectedTab", "selectedGuideType", "setSelectedGuideType", "selectedGuideFromList", "setSelectedGuideFromList", "tooltip", "setTooltip", "visible", "x", "y", "content", "guideTypes", "id", "name", "count", "completionRate", "guidesByType", "tours", "views", "completed", "dropOff", "status", "lastUpdated", "banners", "announcements", "tooltips", "modals", "hotspots", "showTooltip", "event", "rect", "currentTarget", "getBoundingClientRect", "scrollX", "window", "pageXOffset", "document", "documentElement", "scrollLeft", "scrollY", "pageYOffset", "scrollTop", "left", "top", "hideTooltip", "prev", "overviewMetrics", "analyticsMetrics", "aiPerformanceMetrics", "handleTabChange", "newValue", "renderTabContent", "startIcon", "size", "map", "metric", "index", "sx", "mb", "subtitle", "position", "viewBox", "style", "overflow", "patternUnits", "d", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "textAnchor", "strokeLinecap", "cx", "cy", "r", "bottom", "transform", "border", "flexDirection", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "mr", "textAlign", "mt", "p", "cursor", "px", "py", "boxShadow", "className", "onChange", "label", "zIndex", "pointerEvents", "fontFamily", "lineHeight", "_c12", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/dashboard/ModernDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Box, Typography, IconButton, Container, Tabs, Tab } from '@mui/material';\r\nimport { styled } from '@mui/material/styles';\r\nimport {\r\n  TrendingUp,\r\n  TrendingDown,\r\n  People,\r\n  CheckCircle,\r\n  Star,\r\n  Schedule,\r\n  FilterList,\r\n  CalendarToday\r\n} from '@mui/icons-material';\r\nimport Card from '../common/Card';\r\nimport ModernButton from '../common/ModernButton';\r\nimport ChartPlaceholder from './ChartPlaceholder';\r\n\r\ninterface MetricCardProps {\r\n  title: string;\r\n  value: string;\r\n  change: string;\r\n  changeValue: string;\r\n  trend: 'up' | 'down';\r\n  icon: React.ReactNode;\r\n  color: string;\r\n}\r\n\r\nconst DashboardWrapper = styled('div')({\r\n  backgroundColor: '#f6f9ff',\r\n  minHeight: '100vh',\r\n});\r\n\r\nconst HeaderSection = styled(Box)({\r\n  display: 'flex',\r\n  justifyContent: 'space-between',\r\n  alignItems: 'center',\r\n  marginBottom: 'var(--spacing-4)',\r\n});\r\n\r\nconst StyledTabs = styled(Tabs)({\r\n  marginBottom: 'var(--spacing-4)',\r\n  '& .MuiTabs-indicator': {\r\n    backgroundColor: 'var(--color-primary-600)',\r\n  },\r\n  '& .MuiTab-root': {\r\n    fontSize: 'var(--font-size-sm)',\r\n    fontWeight: 'var(--font-weight-medium)',\r\n    textTransform: 'none',\r\n    color: 'var(--color-gray-600)',\r\n    '&.Mui-selected': {\r\n      color: 'var(--color-primary-600)',\r\n      fontWeight: 'var(--font-weight-semibold)',\r\n    },\r\n  },\r\n});\r\n\r\nconst FilterSection = styled(Box)({\r\n  display: 'flex',\r\n  gap: 'var(--spacing-3)',\r\n  alignItems: 'center',\r\n});\r\n\r\nconst MetricsGrid = styled(Box)({\r\n  display: 'grid',\r\n  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\r\n  gap: 'var(--spacing-4)',\r\n  marginBottom: 'var(--spacing-6)',\r\n});\r\n\r\nconst MetricCardContainer = styled(Card)({\r\n  padding: 'var(--spacing-5)',\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  gap: 'var(--spacing-4)',\r\n});\r\n\r\nconst MetricIcon = styled(Box)<{ color: string }>(({ color }) => ({\r\n  width: '48px',\r\n  height: '48px',\r\n  borderRadius: 'var(--radius-lg)',\r\n  backgroundColor: `${color}15`,\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  justifyContent: 'center',\r\n  \r\n  '& svg': {\r\n    color: color,\r\n    fontSize: '24px',\r\n  },\r\n}));\r\n\r\nconst MetricContent = styled(Box)({\r\n  flex: 1,\r\n});\r\n\r\nconst MetricTitle = styled(Typography)({\r\n  fontSize: 'var(--font-size-sm)',\r\n  color: 'var(--color-gray-600)',\r\n  marginBottom: 'var(--spacing-1)',\r\n});\r\n\r\nconst MetricValue = styled(Typography)({\r\n  fontSize: 'var(--font-size-2xl)',\r\n  fontWeight: 'var(--font-weight-bold)',\r\n  color: 'var(--color-gray-900)',\r\n  marginBottom: 'var(--spacing-1)',\r\n});\r\n\r\nconst MetricChange = styled(Box)({\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  gap: 'var(--spacing-1)',\r\n});\r\n\r\nconst ChangeIndicator = styled(Box)<{ trend: 'up' | 'down' }>(({ trend }) => ({\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  gap: 'var(--spacing-1)',\r\n  fontSize: 'var(--font-size-xs)',\r\n  fontWeight: 'var(--font-weight-medium)',\r\n  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',\r\n  \r\n  '& svg': {\r\n    fontSize: '16px',\r\n  },\r\n}));\r\n\r\nconst MetricCard: React.FC<MetricCardProps> = ({\r\n  title,\r\n  value,\r\n  change,\r\n  changeValue,\r\n  trend,\r\n  icon,\r\n  color,\r\n}) => {\r\n  return (\r\n    <MetricCardContainer shadow=\"sm\" hover>\r\n      <MetricIcon color={color}>\r\n        {icon}\r\n      </MetricIcon>\r\n      <MetricContent>\r\n        <MetricTitle>{title}</MetricTitle>\r\n        <MetricValue>{value}</MetricValue>\r\n        <MetricChange>\r\n          <ChangeIndicator trend={trend}>\r\n            {trend === 'up' ? <TrendingUp /> : <TrendingDown />}\r\n            {change}\r\n          </ChangeIndicator>\r\n          <Typography variant=\"caption\" color=\"text.secondary\">\r\n            {changeValue}\r\n          </Typography>\r\n        </MetricChange>\r\n      </MetricContent>\r\n    </MetricCardContainer>\r\n  );\r\n};\r\n\r\nconst ModernDashboard: React.FC = () => {\r\n  const [selectedTab, setSelectedTab] = useState(0);\r\n  const [selectedGuideType, setSelectedGuideType] = useState<string | null>(null);\r\n  const [selectedGuideFromList, setSelectedGuideFromList] = useState<string | null>(null);\r\n  const [tooltip, setTooltip] = useState<{\r\n    visible: boolean;\r\n    x: number;\r\n    y: number;\r\n    content: string;\r\n    title: string;\r\n  }>({\r\n    visible: false,\r\n    x: 0,\r\n    y: 0,\r\n    content: '',\r\n    title: ''\r\n  });\r\n\r\n  // Mock data for guide types and guides\r\n  const guideTypes = [\r\n    { id: 'tours', name: 'Tours', count: 8, completionRate: 85, color: '#8b5cf6' },\r\n    { id: 'banners', name: 'Banners', count: 12, completionRate: 92, color: '#06b6d4' },\r\n    { id: 'announcements', name: 'Announcements', count: 6, completionRate: 78, color: '#f59e0b' },\r\n    { id: 'tooltips', name: 'Tooltips', count: 15, completionRate: 88, color: '#10b981' },\r\n    { id: 'modals', name: 'Modals', count: 4, completionRate: 73, color: '#ef4444' },\r\n    { id: 'hotspots', name: 'Hotspots', count: 9, completionRate: 81, color: '#f97316' }\r\n  ];\r\n\r\n  const guidesByType: Record<string, any[]> = {\r\n    tours: [\r\n      { id: 'tour-1', name: 'Product Onboarding', views: 1400, completed: 1247, dropOff: 11, status: 'excellent', lastUpdated: '2 days ago' },\r\n      { id: 'tour-2', name: 'Feature Discovery', views: 1174, completed: 892, dropOff: 24, status: 'good', lastUpdated: '1 day ago' },\r\n      { id: 'tour-3', name: 'Advanced Settings', views: 962, completed: 634, dropOff: 32, status: 'needs attention', lastUpdated: '5 days ago' }\r\n    ],\r\n    banners: [\r\n      { id: 'banner-1', name: 'Welcome Banner', views: 2100, completed: 1932, dropOff: 8, status: 'excellent', lastUpdated: '1 day ago' },\r\n      { id: 'banner-2', name: 'Feature Announcement', views: 1850, completed: 1665, dropOff: 10, status: 'excellent', lastUpdated: '3 days ago' }\r\n    ],\r\n    announcements: [\r\n      { id: 'announcement-1', name: 'New Feature Release', views: 980, completed: 764, dropOff: 22, status: 'good', lastUpdated: '1 week ago' },\r\n      { id: 'announcement-2', name: 'Maintenance Notice', views: 1200, completed: 936, dropOff: 22, status: 'good', lastUpdated: '2 days ago' }\r\n    ],\r\n    tooltips: [\r\n      { id: 'tooltip-1', name: 'Button Helper', views: 3200, completed: 2816, dropOff: 12, status: 'excellent', lastUpdated: '1 day ago' },\r\n      { id: 'tooltip-2', name: 'Form Guidance', views: 2800, completed: 2464, dropOff: 12, status: 'excellent', lastUpdated: '2 days ago' }\r\n    ],\r\n    modals: [\r\n      { id: 'modal-1', name: 'Confirmation Dialog', views: 1500, completed: 1095, dropOff: 27, status: 'needs attention', lastUpdated: '4 days ago' }\r\n    ],\r\n    hotspots: [\r\n      { id: 'hotspot-1', name: 'Navigation Helper', views: 1100, completed: 891, dropOff: 19, status: 'good', lastUpdated: '3 days ago' }\r\n    ]\r\n  };\r\n\r\n  const showTooltip = (event: React.MouseEvent, title: string, content: string) => {\r\n    const rect = event.currentTarget.getBoundingClientRect();\r\n    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\r\n    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\r\n\r\n    setTooltip({\r\n      visible: true,\r\n      x: rect.left + scrollX + rect.width / 2,\r\n      y: rect.top + scrollY - 10,\r\n      content,\r\n      title\r\n    });\r\n  };\r\n\r\n  const hideTooltip = () => {\r\n    setTooltip(prev => ({ ...prev, visible: false }));\r\n  };\r\n\r\n  const overviewMetrics = [\r\n    {\r\n      title: 'Completion Rate',\r\n      value: '87.3%',\r\n      change: '****%',\r\n      changeValue: '+2.8pp',\r\n      trend: 'up' as const,\r\n      icon: <CheckCircle />,\r\n      color: 'var(--color-success-600)',\r\n    },\r\n    {\r\n      title: 'User Satisfaction',\r\n      value: '4.6',\r\n      change: '+0.2',\r\n      changeValue: 'out of 5.0',\r\n      trend: 'up' as const,\r\n      icon: <Star />,\r\n      color: 'var(--color-warning-600)',\r\n    },\r\n    {\r\n      title: 'Hours Saved',\r\n      value: '2,847',\r\n      change: '+18.7%',\r\n      changeValue: '+447h',\r\n      trend: 'up' as const,\r\n      icon: <Schedule />,\r\n      color: 'var(--color-primary-600)',\r\n    },\r\n  ];\r\n\r\n  const analyticsMetrics = [\r\n    {\r\n      title: 'Active Users',\r\n      value: '12,847',\r\n      change: '+12.5%',\r\n      changeValue: '+1,432',\r\n      trend: 'up' as const,\r\n      icon: <People />,\r\n      color: 'var(--color-primary-600)',\r\n    },\r\n    {\r\n      title: 'Completion Rate',\r\n      value: '87.3%',\r\n      change: '****%',\r\n      changeValue: '+2.8pp',\r\n      trend: 'up' as const,\r\n      icon: <CheckCircle />,\r\n      color: 'var(--color-success-600)',\r\n    },\r\n    {\r\n      title: 'User Satisfaction',\r\n      value: '4.6',\r\n      change: '+0.2',\r\n      changeValue: 'out of 5.0',\r\n      trend: 'up' as const,\r\n      icon: <Star />,\r\n      color: 'var(--color-warning-600)',\r\n    },\r\n    {\r\n      title: 'Hours Saved',\r\n      value: '2,847',\r\n      change: '+18.7%',\r\n      changeValue: '+447h',\r\n      trend: 'up' as const,\r\n      icon: <Schedule />,\r\n      color: 'var(--color-primary-600)',\r\n    },\r\n  ];\r\n\r\n  const aiPerformanceMetrics = [\r\n    {\r\n      title: 'AI Response Time',\r\n      value: '1.2s',\r\n      change: '-15%',\r\n      changeValue: 'faster',\r\n      trend: 'up' as const,\r\n      icon: <Schedule />,\r\n      color: 'var(--color-success-600)',\r\n    },\r\n    {\r\n      title: 'AI Accuracy',\r\n      value: '94.8%',\r\n      change: '+2.1%',\r\n      changeValue: 'improved',\r\n      trend: 'up' as const,\r\n      icon: <CheckCircle />,\r\n      color: 'var(--color-primary-600)',\r\n    },\r\n    {\r\n      title: 'Model Confidence',\r\n      value: '89.3%',\r\n      change: '+1.8%',\r\n      changeValue: 'higher',\r\n      trend: 'up' as const,\r\n      icon: <Star />,\r\n      color: 'var(--color-warning-600)',\r\n    },\r\n    {\r\n      title: 'Processing Load',\r\n      value: '67%',\r\n      change: '+5%',\r\n      changeValue: 'capacity',\r\n      trend: 'up' as const,\r\n      icon: <People />,\r\n      color: 'var(--color-error-500)',\r\n    },\r\n  ];\r\n\r\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\r\n    setSelectedTab(newValue);\r\n  };\r\n\r\n  const renderTabContent = () => {\r\n    switch (selectedTab) {\r\n      case 0: // Overview\r\n        return (\r\n          <>\r\n            {/* Header with Filters */}\r\n            <HeaderSection>\r\n              <Box>\r\n                <Typography variant=\"h4\" fontWeight=\"bold\" color=\"text.primary\">\r\n                  Dashboard Overview\r\n                </Typography>\r\n              </Box>\r\n              <FilterSection>\r\n                <ModernButton\r\n                  variant=\"outline\"\r\n                  startIcon={<FilterList />}\r\n                  size=\"sm\"\r\n                >\r\n                  Filter\r\n                </ModernButton>\r\n                <ModernButton\r\n                  variant=\"outline\"\r\n                  startIcon={<CalendarToday />}\r\n                  size=\"sm\"\r\n                >\r\n                  Last 30 days\r\n                </ModernButton>\r\n              </FilterSection>\r\n            </HeaderSection>\r\n\r\n            {/* Overview Metrics Grid (4 cards like Analytics) */}\r\n            <MetricsGrid>\r\n              {analyticsMetrics.map((metric, index) => (\r\n                <MetricCard key={index} {...metric} />\r\n              ))}\r\n            </MetricsGrid>\r\n\r\n            {/* Overview Charts - Growth Trends and User Satisfaction */}\r\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>\r\n              {/* User Activity Trends Chart */}\r\n              <Card title=\"📈 User Activity Trends\" subtitle=\"Active, retained, and total users over time\" padding=\"lg\">\r\n                <Box sx={{ height: '300px', position: 'relative', backgroundColor: 'white', borderRadius: 'var(--radius-md)' }}>\r\n\r\n                  \r\n\r\n                  {/* Multi-line Chart */}\r\n                  <svg width=\"100%\" height=\"280\" viewBox=\"0 0 450 250\" style={{ overflow: 'visible' }}>\r\n                    {/* Grid lines */}\r\n                    <defs>\r\n                      <pattern id=\"activityGrid\" width=\"200\" height=\"50\" patternUnits=\"userSpaceOnUse\">\r\n                        <path d=\"M 200 0 L 0 0 0 50\" fill=\"none\" stroke=\"#f1f5f9\" strokeWidth=\"1\"/>\r\n                      </pattern>\r\n                    </defs>\r\n                    <rect width=\"100%\" height=\"200\" fill=\"url(#activityGrid)\" />\r\n\r\n                    {/* Y-axis */}\r\n                    <line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"200\" stroke=\"#e2e8f0\" strokeWidth=\"1\"/>\r\n                    {/* X-axis */}\r\n                    <line x1=\"50\" y1=\"200\" x2=\"400\" y2=\"200\" stroke=\"#e2e8f0\" strokeWidth=\"1\"/>\r\n\r\n                    {/* Y-axis labels */}\r\n                    <text x=\"40\" y=\"25\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">60000</text>\r\n                    <text x=\"40\" y=\"65\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">45000</text>\r\n                    <text x=\"40\" y=\"105\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">30000</text>\r\n                    <text x=\"40\" y=\"145\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">15000</text>\r\n                    <text x=\"40\" y=\"205\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">0</text>\r\n\r\n                    {/* X-axis labels */}\r\n                    <text x=\"125\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Week 1</text>\r\n                    <text x=\"325\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Week 2</text>\r\n\r\n                    {/* Total Users Line (Purple) */}\r\n                    <path d=\"M 125 50 L 325 45\" fill=\"none\" stroke=\"#8b5cf6\" strokeWidth=\"3\" strokeLinecap=\"round\"/>\r\n                    <circle cx=\"125\" cy=\"50\" r=\"5\" fill=\"#8b5cf6\"/>\r\n                    <circle cx=\"325\" cy=\"45\" r=\"5\" fill=\"#8b5cf6\"/>\r\n\r\n                    {/* Active Users Line (Blue) */}\r\n                    <path d=\"M 125 80 L 325 75\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"3\" strokeLinecap=\"round\"/>\r\n                    <circle cx=\"125\" cy=\"80\" r=\"5\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"325\" cy=\"75\" r=\"5\" fill=\"#3b82f6\"/>\r\n\r\n                    {/* Retained Users Line (Green) */}\r\n                    <path d=\"M 125 110 L 325 105\" fill=\"none\" stroke=\"#10b981\" strokeWidth=\"3\" strokeLinecap=\"round\"/>\r\n                    <circle cx=\"125\" cy=\"110\" r=\"5\" fill=\"#10b981\"/>\r\n                    <circle cx=\"325\" cy=\"105\" r=\"5\" fill=\"#10b981\"/>\r\n\r\n                    {/* Data point values */}\r\n                    <text x=\"125\" y=\"40\" fontSize=\"10\" fill=\"#8b5cf6\" textAnchor=\"middle\" fontWeight=\"600\">36000</text>\r\n                    <text x=\"325\" y=\"35\" fontSize=\"10\" fill=\"#8b5cf6\" textAnchor=\"middle\" fontWeight=\"600\">37000</text>\r\n\r\n                    <text x=\"125\" y=\"70\" fontSize=\"10\" fill=\"#3b82f6\" textAnchor=\"middle\" fontWeight=\"600\">30000</text>\r\n                    <text x=\"325\" y=\"65\" fontSize=\"10\" fill=\"#3b82f6\" textAnchor=\"middle\" fontWeight=\"600\">31000</text>\r\n\r\n                    <text x=\"125\" y=\"100\" fontSize=\"10\" fill=\"#10b981\" textAnchor=\"middle\" fontWeight=\"600\">24000</text>\r\n                    <text x=\"325\" y=\"95\" fontSize=\"10\" fill=\"#10b981\" textAnchor=\"middle\" fontWeight=\"600\">25000</text>\r\n                  </svg>\r\n\r\n                  {/* Legend */}\r\n                  <Box sx={{\r\n                    position: 'absolute',\r\n                    bottom: 10,\r\n                    left: '50%',\r\n                    transform: 'translateX(-50%)',\r\n                    display: 'flex',\r\n                    gap: 3,\r\n                    backgroundColor: 'rgba(255,255,255,0.9)',\r\n                    padding: '8px 16px',\r\n                    borderRadius: '8px',\r\n                    border: '1px solid #e2e8f0'\r\n                  }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                      <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Active</Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                      <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Retained</Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                      <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Total</Typography>\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Card>\r\n\r\n              {/* Feature Adoption Distribution Chart */}\r\n              <Card title=\"� Feature Adoption Distribution\" subtitle=\"Interactive feature usage metrics\" padding=\"lg\">\r\n                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', borderRadius: 'var(--radius-md)', position: 'relative' }}>\r\n                  {/* Donut Chart */}\r\n                  <svg width=\"180\" height=\"180\" viewBox=\"0 0 180 180\" style={{ marginBottom: '20px' }}>\r\n                    {/* Tours - 25% - Blue */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"94 377\" strokeDashoffset=\"0\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Banners - 18% - Orange */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#f97316\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"68 377\" strokeDashoffset=\"-94\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Hotspots - 12% - Teal */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#14b8a6\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"45 377\" strokeDashoffset=\"-162\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Tooltips - 20% - Green */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#10b981\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"75 377\" strokeDashoffset=\"-207\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* AI Assistant - 19% - Purple */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#8b5cf6\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"71 377\" strokeDashoffset=\"-282\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Checklists - 6% - Red */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#ef4444\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"23 377\" strokeDashoffset=\"-353\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Center circle for donut effect with 100% Total */}\r\n                    <circle cx=\"90\" cy=\"90\" r=\"35\" fill=\"white\"/>\r\n                    <text x=\"90\" y=\"85\" fontSize=\"16\" fill=\"#1f2937\" textAnchor=\"middle\" fontWeight=\"bold\">100%</text>\r\n                    <text x=\"90\" y=\"100\" fontSize=\"11\" fill=\"#6b7280\" textAnchor=\"middle\">Total</text>\r\n                  </svg>\r\n\r\n                  {/* Legend */}\r\n                  <Box sx={{\r\n                    display: 'grid',\r\n                    gridTemplateColumns: 'repeat(3, 1fr)',\r\n                    gap: 2,\r\n                    width: '100%',\r\n                    maxWidth: '400px'\r\n                  }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Tours</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>25%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Tooltips</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>20%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>AI Assistant</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>19%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#f97316', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Banners</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>18%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#14b8a6', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Hotspots</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>12%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#ef4444', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Checklists</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>6%</Typography>\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Card>\r\n            </Box>\r\n\r\n            {/* Quick Actions and Recent Activity - Commented out for now\r\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: 'var(--spacing-6)', mb: 4 }}>\r\n              <Card title=\"Quick Actions\" subtitle=\"Take action based on your dashboard insights\" padding=\"lg\">\r\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>\r\n                  <ModernButton variant=\"primary\" fullWidth>\r\n                    Create New Guide\r\n                  </ModernButton>\r\n                  <ModernButton variant=\"outline\" fullWidth>\r\n                    View Full Analytics\r\n                  </ModernButton>\r\n                  <ModernButton variant=\"outline\" fullWidth>\r\n                    AI Assistant Settings\r\n                  </ModernButton>\r\n                </Box>\r\n              </Card>\r\n\r\n              <Card title=\"Recent Activity\" padding=\"lg\">\r\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\r\n                  {[\r\n                    { user: 'SC', action: 'New guide created', name: 'Sarah Chen', time: '2 min ago', type: 'create' },\r\n                    { user: 'S', action: 'Guide completed 47 times', name: 'System', time: '15 min ago', type: 'completion' },\r\n                    { user: 'MJ', action: 'AI response updated', name: 'Mike Johnson', time: '1 hour ago', type: 'update' },\r\n                    { user: 'S', action: 'Low performance alert', name: 'System', time: '2 hours ago', type: 'alert' },\r\n                  ].map((activity, index) => (\r\n                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-3)' }}>\r\n                      <Box sx={{\r\n                        width: '40px',\r\n                        height: '40px',\r\n                        borderRadius: '50%',\r\n                        backgroundColor: 'var(--color-primary-100)',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center',\r\n                        fontSize: 'var(--font-size-sm)',\r\n                        fontWeight: 'var(--font-weight-semibold)',\r\n                        color: 'var(--color-primary-700)'\r\n                      }}>\r\n                        {activity.user}\r\n                      </Box>\r\n                      <Box sx={{ flex: 1 }}>\r\n                        <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                          {activity.action}\r\n                        </Typography>\r\n                        <Typography variant=\"caption\" color=\"text.secondary\">\r\n                          {activity.name} • {activity.time}\r\n                        </Typography>\r\n                      </Box>\r\n                      <Box sx={{\r\n                        width: '8px',\r\n                        height: '8px',\r\n                        borderRadius: '50%',\r\n                        backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'\r\n                      }} />\r\n                    </Box>\r\n                  ))}\r\n                </Box>\r\n              </Card>\r\n            </Box>\r\n            */}\r\n\r\n            {/* User Feedback & Satisfaction Section */}\r\n            {/* User Satisfaction Ratings and Satisfaction Trend */}\r\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>\r\n              {/* User Satisfaction Ratings */}\r\n              <Card title=\"⭐ User Satisfaction Ratings\" padding=\"lg\">\r\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\r\n                  {/* Excellent (5★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Excellent (5★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '65%',\r\n                        height: '100%',\r\n                        backgroundColor: '#10b981',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      1,247\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Good (4★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Good (4★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '45%',\r\n                        height: '100%',\r\n                        backgroundColor: '#84cc16',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      892\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Average (3★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Average (3★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '22%',\r\n                        height: '100%',\r\n                        backgroundColor: '#f59e0b',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      434\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Poor (2★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Poor (2★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '8%',\r\n                        height: '100%',\r\n                        backgroundColor: '#f97316',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      123\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Very Poor (1★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Very Poor (1★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '4%',\r\n                        height: '100%',\r\n                        backgroundColor: '#ef4444',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      57\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Summary Cards */}\r\n                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>\r\n                    <Box sx={{\r\n                      p: 2,\r\n                      backgroundColor: '#f0fdf4',\r\n                      borderRadius: 'var(--radius-md)',\r\n                      textAlign: 'center'\r\n                    }}>\r\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#16a34a\">\r\n                        77%\r\n                      </Typography>\r\n                      <Typography variant=\"caption\" color=\"#16a34a\">\r\n                        Positive\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      p: 2,\r\n                      backgroundColor: '#fffbeb',\r\n                      borderRadius: 'var(--radius-md)',\r\n                      textAlign: 'center'\r\n                    }}>\r\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#d97706\">\r\n                        16%\r\n                      </Typography>\r\n                      <Typography variant=\"caption\" color=\"#d97706\">\r\n                        Neutral\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      p: 2,\r\n                      backgroundColor: '#fef2f2',\r\n                      borderRadius: 'var(--radius-md)',\r\n                      textAlign: 'center'\r\n                    }}>\r\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#dc2626\">\r\n                        7%\r\n                      </Typography>\r\n                      <Typography variant=\"caption\" color=\"#dc2626\">\r\n                        Negative\r\n                      </Typography>\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Card>\r\n\r\n              {/* Satisfaction Trend */}\r\n              <Card title=\"📈 Satisfaction Trend\" padding=\"lg\">\r\n                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8fafc', borderRadius: 'var(--radius-md)', position: 'relative' }}>\r\n                  {/* Satisfaction Trend Line Chart */}\r\n                  <svg width=\"100%\" height=\"250\" viewBox=\"0 0 400 200\" style={{ overflow: 'visible' }}>\r\n                    {/* Grid lines */}\r\n                    <defs>\r\n                      <pattern id=\"feedbackGrid\" width=\"66.67\" height=\"40\" patternUnits=\"userSpaceOnUse\">\r\n                        <path d=\"M 66.67 0 L 0 0 0 40\" fill=\"none\" stroke=\"#e2e8f0\" strokeWidth=\"0.5\"/>\r\n                      </pattern>\r\n                    </defs>\r\n                    <rect width=\"100%\" height=\"100%\" fill=\"url(#feedbackGrid)\" />\r\n\r\n                    {/* Y-axis labels */}\r\n                    <text x=\"10\" y=\"20\" fontSize=\"10\" fill=\"#64748b\">5</text>\r\n                    <text x=\"10\" y=\"60\" fontSize=\"10\" fill=\"#64748b\">4.75</text>\r\n                    <text x=\"10\" y=\"100\" fontSize=\"10\" fill=\"#64748b\">4.5</text>\r\n                    <text x=\"10\" y=\"140\" fontSize=\"10\" fill=\"#64748b\">4.25</text>\r\n                    <text x=\"10\" y=\"180\" fontSize=\"10\" fill=\"#64748b\">4</text>\r\n\r\n                    {/* X-axis labels */}\r\n                    <text x=\"50\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Jan</text>\r\n                    <text x=\"110\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Feb</text>\r\n                    <text x=\"170\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Mar</text>\r\n                    <text x=\"230\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Apr</text>\r\n                    <text x=\"290\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">May</text>\r\n                    <text x=\"350\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Jun</text>\r\n\r\n                    {/* Line showing satisfaction trend from 4.2 to 4.7 */}\r\n                    <path d=\"M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"3\"/>\r\n\r\n                    {/* Data points */}\r\n                    <circle cx=\"50\" cy=\"168\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"110\" cy=\"152\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"170\" cy=\"136\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"230\" cy=\"120\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"290\" cy=\"104\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"350\" cy=\"88\" r=\"4\" fill=\"#3b82f6\"/>\r\n\r\n                    {/* Value labels on data points */}\r\n                    <text x=\"50\" y=\"160\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.2</text>\r\n                    <text x=\"110\" y=\"144\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.3</text>\r\n                    <text x=\"170\" y=\"128\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.4</text>\r\n                    <text x=\"230\" y=\"112\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.5</text>\r\n                    <text x=\"290\" y=\"96\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.6</text>\r\n                    <text x=\"350\" y=\"80\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.7</text>\r\n                  </svg>\r\n                </Box>\r\n              </Card>\r\n            </Box>\r\n\r\n            {/* Feedback Summary */}\r\n            <Card title=\"📊 Feedback Summary\" padding=\"lg\">\r\n              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 'var(--spacing-6)' }}>\r\n                {/* Total Feedback */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#f8fafc',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1 }}>\r\n                    2,238\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Total Feedback\r\n                  </Typography>\r\n                </Box>\r\n\r\n                {/* Positive Sentiment */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#f0fdf4',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#16a34a\" sx={{ mb: 1 }}>\r\n                    85.8%\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Positive Sentiment\r\n                  </Typography>\r\n                </Box>\r\n\r\n                {/* Average Rating */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#eff6ff',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#2563eb\" sx={{ mb: 1 }}>\r\n                    4.6/5\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Average Rating\r\n                  </Typography>\r\n                </Box>\r\n\r\n                {/* Growth vs Last Month */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#fdf4ff',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#9333ea\" sx={{ mb: 1 }}>\r\n                    +12%\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    vs Last Month\r\n                  </Typography>\r\n                </Box>\r\n              </Box>\r\n            </Card>\r\n\r\n            {/* Recent Feedback */}\r\n            {/* <Card title=\"Recent Feedback\" padding=\"lg\">\r\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\r\n                {[\r\n                  { user: 'JD', action: '5-star rating received', name: 'John Doe', time: '5 min ago', type: 'completion' },\r\n                  { user: 'SM', action: 'Improvement suggestion submitted', name: 'Sarah Miller', time: '20 min ago', type: 'create' },\r\n                  { user: 'RW', action: 'Bug report submitted', name: 'Robert Wilson', time: '45 min ago', type: 'alert' },\r\n                  { user: 'LB', action: 'Feature request submitted', name: 'Lisa Brown', time: '1 hour ago', type: 'update' },\r\n                ].map((activity, index) => (\r\n                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-3)' }}>\r\n                    <Box sx={{\r\n                      width: '40px',\r\n                      height: '40px',\r\n                      borderRadius: '50%',\r\n                      backgroundColor: 'var(--color-primary-100)',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      fontSize: 'var(--font-size-sm)',\r\n                      fontWeight: 'var(--font-weight-semibold)',\r\n                      color: 'var(--color-primary-700)'\r\n                    }}>\r\n                      {activity.user}\r\n                    </Box>\r\n                    <Box sx={{ flex: 1 }}>\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        {activity.action}\r\n                      </Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        {activity.name} • {activity.time}\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      width: '8px',\r\n                      height: '8px',\r\n                      borderRadius: '50%',\r\n                      backgroundColor: activity.type === 'alert' ? 'var(--color-error-500)' : 'var(--color-success-500)'\r\n                    }} />\r\n                  </Box>\r\n                ))}\r\n              </Box>\r\n            </Card> */}\r\n          </>\r\n        );\r\n\r\n      case 1: // Analytics\r\n        return (\r\n          <>\r\n            {/* Guide Performance Overview */}\r\n            <Card title=\"Guide Performance Overview\" subtitle=\"Click on any guide to see detailed funnel analysis\" padding=\"lg\">\r\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\r\n                {/* Product Onboarding */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Product Onboarding\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#e8f5e9',\r\n                        color: '#2e7d32',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        excellent\r\n                      </Box>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: 'var(--color-gray-100)',\r\n                        color: 'var(--color-gray-700)',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px'\r\n                      }}>\r\n                        Onboarding\r\n                      </Box>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\r\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\r\n                        1,400 views\r\n                      </Box>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\r\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\r\n                        1,247 completed\r\n                      </Box>\r\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium' }}>\r\n                        11% drop-off\r\n                      </Box>\r\n                      <Box>\r\n                        Updated 2 days ago\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      89%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '89%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Feature Discovery */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Feature Discovery\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#e3f2fd',\r\n                        color: '#1976d2',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        good\r\n                      </Box>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: 'var(--color-gray-100)',\r\n                        color: 'var(--color-gray-700)',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px'\r\n                      }}>\r\n                        Feature\r\n                      </Box>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\r\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\r\n                        1,174 views\r\n                      </Box>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\r\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\r\n                        892 completed\r\n                      </Box>\r\n                      <Box sx={{ color: 'var(--color-warning-600)', fontWeight: 'medium' }}>\r\n                        24% drop-off\r\n                      </Box>\r\n                      <Box>\r\n                        Updated 1 day ago\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      76%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '76%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Advanced Settings */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Advanced Settings\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#fff3e0',\r\n                        color: '#f57c00',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        needs attention\r\n                      </Box>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: 'var(--color-gray-100)',\r\n                        color: 'var(--color-gray-700)',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px'\r\n                      }}>\r\n                        Configuration\r\n                      </Box>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\r\n                        <Box sx={{ width: 4, height: 4, borderRadius: '50%', backgroundColor: 'var(--color-gray-400)' }} />\r\n                        962 views\r\n                      </Box>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\r\n                        <CheckCircle sx={{ fontSize: 16, color: 'var(--color-success-500)' }} />\r\n                        634 completed\r\n                      </Box>\r\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium' }}>\r\n                        32% drop-off\r\n                      </Box>\r\n                      <Box>\r\n                        Updated 5 days ago\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      65%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '65%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            </Card>\r\n          </>\r\n        );\r\n\r\n      case 2: // AI Performance\r\n        return (\r\n          <>\r\n            \r\n             {/* Bottom Metrics Cards */}\r\n            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 'var(--spacing-6)', mb: 4 }}>\r\n              {/* Total Interactions Card */}\r\n              <Box sx={{\r\n                p: 4,\r\n                backgroundColor: 'white',\r\n                borderRadius: 'var(--radius-lg)',\r\n                border: '1px solid var(--color-gray-200)',\r\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\r\n                position: 'relative'\r\n              }}>\r\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\r\n                  <Typography variant=\"caption\" color=\"#3b82f6\" sx={{ fontWeight: 'medium' }}>\r\n                    Total Interactions\r\n                  </Typography>\r\n                  <Box sx={{\r\n                    width: 40,\r\n                    height: 40,\r\n                    backgroundColor: '#e3f2fd',\r\n                    borderRadius: 'var(--radius-md)',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Box sx={{\r\n                      width: 24,\r\n                      height: 24,\r\n                      backgroundColor: '#3b82f6',\r\n                      borderRadius: 'var(--radius-sm)',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      color: 'white',\r\n                      fontSize: '14px',\r\n                      fontWeight: 'bold'\r\n                    }}>\r\n                      💬\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\r\n                  2,847\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\r\n                  +12% from last month\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Success Rate Card */}\r\n              <Box sx={{\r\n                p: 4,\r\n                backgroundColor: 'white',\r\n                borderRadius: 'var(--radius-lg)',\r\n                border: '1px solid var(--color-gray-200)',\r\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\r\n                position: 'relative'\r\n              }}>\r\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\r\n                  <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\r\n                    Success Rate\r\n                  </Typography>\r\n                  <Box sx={{\r\n                    width: 40,\r\n                    height: 40,\r\n                    backgroundColor: '#e8f5e9',\r\n                    borderRadius: 'var(--radius-md)',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Box sx={{\r\n                      width: 24,\r\n                      height: 24,\r\n                      backgroundColor: '#10b981',\r\n                      borderRadius: '50%',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      color: 'white',\r\n                      fontSize: '14px',\r\n                      fontWeight: 'bold'\r\n                    }}>\r\n                      ✓\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\r\n                  91%\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\r\n                  +3% improvement\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Avg Response Time Card */}\r\n              <Box sx={{\r\n                p: 4,\r\n                backgroundColor: 'white',\r\n                borderRadius: 'var(--radius-lg)',\r\n                border: '1px solid var(--color-gray-200)',\r\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\r\n                position: 'relative'\r\n              }}>\r\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\r\n                  <Typography variant=\"caption\" color=\"#8b5cf6\" sx={{ fontWeight: 'medium' }}>\r\n                    Avg Response Time\r\n                  </Typography>\r\n                  <Box sx={{\r\n                    width: 40,\r\n                    height: 40,\r\n                    backgroundColor: '#f3e8ff',\r\n                    borderRadius: 'var(--radius-md)',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Box sx={{\r\n                      width: 24,\r\n                      height: 24,\r\n                      backgroundColor: '#8b5cf6',\r\n                      borderRadius: 'var(--radius-sm)',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      color: 'white',\r\n                      fontSize: '14px',\r\n                      fontWeight: 'bold'\r\n                    }}>\r\n                      ⚡\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\r\n                  1.9s\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\r\n                  -0.3s faster\r\n                </Typography>\r\n              </Box>\r\n            </Box>\r\n\r\n            {/* AI Task Performance Section */}\r\n            <Box sx={{ mb: 4 }}>\r\n              <Card title=\"AI Task Performance\" padding=\"lg\">\r\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\r\n                {/* Password Reset */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Password Reset\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#e8f5e9',\r\n                        color: '#2e7d32',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        96%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        342 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 1.2s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        +2% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      96%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '96%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Account Setup */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Account Setup\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#e3f2fd',\r\n                        color: '#1976d2',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        89%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        198 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 1.4s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        -5% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      89%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '89%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Feature Explanation */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Feature Explanation\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#e3f2fd',\r\n                        color: '#1976d2',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        90%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        267 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 2.1s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        -1% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      90%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '90%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Troubleshooting */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Troubleshooting\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#fff3e0',\r\n                        color: '#f57c00',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        88%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        156 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 3.1s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        -3% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      88%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '88%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Integration Help */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Integration Help\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#fff3e0',\r\n                        color: '#f57c00',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        87%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        123 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 2.5s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        +1% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      87%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '87%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            </Card>\r\n            </Box>\r\n\r\n            {/* AI Insights & Recommendations */}\r\n            <Card title=\"AI Insights & Recommendations\" padding=\"lg\">\r\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>\r\n                {/* Optimize Workflow */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#fffbeb',\r\n                  border: '1px solid #fbbf24',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  display: 'flex',\r\n                  alignItems: 'flex-start',\r\n                  gap: 2\r\n                }}>\r\n                  <Box sx={{\r\n                    width: 6,\r\n                    height: 6,\r\n                    backgroundColor: '#f59e0b',\r\n                    borderRadius: '50%',\r\n                    mt: 1\r\n                  }} />\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\r\n                      Optimize Workflow\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      Consider optimizing your workflow to reduce response time by 15%\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Excluded Performance */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#f0f9ff',\r\n                  border: '1px solid #3b82f6',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  display: 'flex',\r\n                  alignItems: 'flex-start',\r\n                  gap: 2\r\n                }}>\r\n                  <Box sx={{\r\n                    width: 6,\r\n                    height: 6,\r\n                    backgroundColor: '#3b82f6',\r\n                    borderRadius: '50%',\r\n                    mt: 1\r\n                  }} />\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\r\n                      Excluded Performance\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      Excluded tasks are performing well with 94% accuracy rate\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Personalized Suggestions */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#f0f9ff',\r\n                  border: '1px solid #3b82f6',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  display: 'flex',\r\n                  alignItems: 'flex-start',\r\n                  gap: 2\r\n                }}>\r\n                  <Box sx={{\r\n                    width: 6,\r\n                    height: 6,\r\n                    backgroundColor: '#3b82f6',\r\n                    borderRadius: '50%',\r\n                    mt: 1\r\n                  }} />\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\r\n                      Personalized Suggestions\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      AI suggests implementing advanced filtering for better user experience\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            </Card>\r\n          </>\r\n        );\r\n\r\n\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className='qadpt-web'>\r\n      <div className='qadpt-webcontent'>\r\n        <DashboardWrapper>\r\n          <Container maxWidth=\"xl\" sx={{ py: 3 }}>\r\n            {/* Navigation Tabs */}\r\n            <StyledTabs value={selectedTab} onChange={handleTabChange}>\r\n              <Tab label=\"Overview\" />\r\n              <Tab label=\"Analytics\" />\r\n              <Tab label=\"AI Performance\" />\r\n            </StyledTabs>\r\n\r\n            {/* Render Tab Content */}\r\n            {renderTabContent()}\r\n          </Container>\r\n        </DashboardWrapper>\r\n\r\n        {/* Interactive Tooltip */}\r\n        {tooltip.visible && (\r\n          <Box\r\n            sx={{\r\n              position: 'fixed',\r\n              left: tooltip.x,\r\n              top: tooltip.y,\r\n              transform: 'translate(-50%, -100%)',\r\n              backgroundColor: 'white',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '6px',\r\n              padding: '6px 10px',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n              zIndex: 10000,\r\n              pointerEvents: 'none',\r\n              fontSize: '11px',\r\n              minWidth: '70px',\r\n              textAlign: 'center',\r\n              fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\r\n            }}\r\n          >\r\n            <Typography variant=\"body2\" sx={{ fontSize: '10px', color: '#6b7280', mb: 0.2, lineHeight: 1.2 }}>\r\n              {tooltip.title}\r\n            </Typography>\r\n            <Typography variant=\"body2\" sx={{ fontSize: '11px', color: '#111827', fontWeight: '600', lineHeight: 1.2 }}>\r\n              {tooltip.content}\r\n            </Typography>\r\n          </Box>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ModernDashboard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,UAAU,EAAcC,SAAS,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AACjF,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SACEC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,WAAW,EACXC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,aAAa,QACR,qBAAqB;AAC5B,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,YAAY,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAalD,MAAMC,gBAAgB,GAAGf,MAAM,CAAC,KAAK,CAAC,CAAC;EACrCgB,eAAe,EAAE,SAAS;EAC1BC,SAAS,EAAE;AACb,CAAC,CAAC;AAACC,EAAA,GAHGH,gBAAgB;AAKtB,MAAMI,aAAa,GAAGnB,MAAM,CAACL,GAAG,CAAC,CAAC;EAChCyB,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,eAAe;EAC/BC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAE;AAChB,CAAC,CAAC;AAACC,GAAA,GALGL,aAAa;AAOnB,MAAMM,UAAU,GAAGzB,MAAM,CAACF,IAAI,CAAC,CAAC;EAC9ByB,YAAY,EAAE,kBAAkB;EAChC,sBAAsB,EAAE;IACtBP,eAAe,EAAE;EACnB,CAAC;EACD,gBAAgB,EAAE;IAChBU,QAAQ,EAAE,qBAAqB;IAC/BC,UAAU,EAAE,2BAA2B;IACvCC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,uBAAuB;IAC9B,gBAAgB,EAAE;MAChBA,KAAK,EAAE,0BAA0B;MACjCF,UAAU,EAAE;IACd;EACF;AACF,CAAC,CAAC;AAACG,GAAA,GAfGL,UAAU;AAiBhB,MAAMM,aAAa,GAAG/B,MAAM,CAACL,GAAG,CAAC,CAAC;EAChCyB,OAAO,EAAE,MAAM;EACfY,GAAG,EAAE,kBAAkB;EACvBV,UAAU,EAAE;AACd,CAAC,CAAC;AAACW,GAAA,GAJGF,aAAa;AAMnB,MAAMG,WAAW,GAAGlC,MAAM,CAACL,GAAG,CAAC,CAAC;EAC9ByB,OAAO,EAAE,MAAM;EACfe,mBAAmB,EAAE,sCAAsC;EAC3DH,GAAG,EAAE,kBAAkB;EACvBT,YAAY,EAAE;AAChB,CAAC,CAAC;AAACa,GAAA,GALGF,WAAW;AAOjB,MAAMG,mBAAmB,GAAGrC,MAAM,CAACS,IAAI,CAAC,CAAC;EACvC6B,OAAO,EAAE,kBAAkB;EAC3BlB,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBU,GAAG,EAAE;AACP,CAAC,CAAC;AAACO,GAAA,GALGF,mBAAmB;AAOzB,MAAMG,UAAU,GAAGxC,MAAM,CAACL,GAAG,CAAC,CAAoB,CAAC;EAAEkC;AAAM,CAAC,MAAM;EAChEY,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,YAAY,EAAE,kBAAkB;EAChC3B,eAAe,EAAE,GAAGa,KAAK,IAAI;EAC7BT,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBD,cAAc,EAAE,QAAQ;EAExB,OAAO,EAAE;IACPQ,KAAK,EAAEA,KAAK;IACZH,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC,CAAC;AAACkB,GAAA,GAbEJ,UAAU;AAehB,MAAMK,aAAa,GAAG7C,MAAM,CAACL,GAAG,CAAC,CAAC;EAChCmD,IAAI,EAAE;AACR,CAAC,CAAC;AAACC,GAAA,GAFGF,aAAa;AAInB,MAAMG,WAAW,GAAGhD,MAAM,CAACJ,UAAU,CAAC,CAAC;EACrC8B,QAAQ,EAAE,qBAAqB;EAC/BG,KAAK,EAAE,uBAAuB;EAC9BN,YAAY,EAAE;AAChB,CAAC,CAAC;AAAC0B,GAAA,GAJGD,WAAW;AAMjB,MAAME,WAAW,GAAGlD,MAAM,CAACJ,UAAU,CAAC,CAAC;EACrC8B,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,yBAAyB;EACrCE,KAAK,EAAE,uBAAuB;EAC9BN,YAAY,EAAE;AAChB,CAAC,CAAC;AAAC4B,GAAA,GALGD,WAAW;AAOjB,MAAME,YAAY,GAAGpD,MAAM,CAACL,GAAG,CAAC,CAAC;EAC/ByB,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBU,GAAG,EAAE;AACP,CAAC,CAAC;AAACqB,GAAA,GAJGD,YAAY;AAMlB,MAAME,eAAe,GAAGtD,MAAM,CAACL,GAAG,CAAC,CAA2B,CAAC;EAAE4D;AAAM,CAAC,MAAM;EAC5EnC,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBU,GAAG,EAAE,kBAAkB;EACvBN,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,2BAA2B;EACvCE,KAAK,EAAE0B,KAAK,KAAK,IAAI,GAAG,0BAA0B,GAAG,wBAAwB;EAE7E,OAAO,EAAE;IACP7B,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC,CAAC;AAAC8B,IAAA,GAXEF,eAAe;AAarB,MAAMG,UAAqC,GAAGA,CAAC;EAC7CC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,WAAW;EACXN,KAAK;EACLO,IAAI;EACJjC;AACF,CAAC,KAAK;EACJ,oBACEjB,OAAA,CAACyB,mBAAmB;IAAC0B,MAAM,EAAC,IAAI;IAACC,KAAK;IAAAC,QAAA,gBACpCrD,OAAA,CAAC4B,UAAU;MAACX,KAAK,EAAEA,KAAM;MAAAoC,QAAA,EACtBH;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACbzD,OAAA,CAACiC,aAAa;MAAAoB,QAAA,gBACZrD,OAAA,CAACoC,WAAW;QAAAiB,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAClCzD,OAAA,CAACsC,WAAW;QAAAe,QAAA,EAAEN;MAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAClCzD,OAAA,CAACwC,YAAY;QAAAa,QAAA,gBACXrD,OAAA,CAAC0C,eAAe;UAACC,KAAK,EAAEA,KAAM;UAAAU,QAAA,GAC3BV,KAAK,KAAK,IAAI,gBAAG3C,OAAA,CAACX,UAAU;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACV,YAAY;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAClDT,MAAM;QAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAClBzD,OAAA,CAAChB,UAAU;UAAC0E,OAAO,EAAC,SAAS;UAACzC,KAAK,EAAC,gBAAgB;UAAAoC,QAAA,EACjDJ;QAAW;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAE1B,CAAC;AAACE,IAAA,GA7BId,UAAqC;AA+B3C,MAAMe,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnF,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAACoF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrF,QAAQ,CAAgB,IAAI,CAAC;EACvF,MAAM,CAACsF,OAAO,EAAEC,UAAU,CAAC,GAAGvF,QAAQ,CAMnC;IACDwF,OAAO,EAAE,KAAK;IACdC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,OAAO,EAAE,EAAE;IACX3B,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAM4B,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,CAAC;IAAEC,cAAc,EAAE,EAAE;IAAE7D,KAAK,EAAE;EAAU,CAAC,EAC9E;IAAE0D,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,EAAE;IAAEC,cAAc,EAAE,EAAE;IAAE7D,KAAK,EAAE;EAAU,CAAC,EACnF;IAAE0D,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,CAAC;IAAEC,cAAc,EAAE,EAAE;IAAE7D,KAAK,EAAE;EAAU,CAAC,EAC9F;IAAE0D,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,EAAE;IAAEC,cAAc,EAAE,EAAE;IAAE7D,KAAK,EAAE;EAAU,CAAC,EACrF;IAAE0D,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC;IAAEC,cAAc,EAAE,EAAE;IAAE7D,KAAK,EAAE;EAAU,CAAC,EAChF;IAAE0D,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC;IAAEC,cAAc,EAAE,EAAE;IAAE7D,KAAK,EAAE;EAAU,CAAC,CACrF;EAED,MAAM8D,YAAmC,GAAG;IAC1CC,KAAK,EAAE,CACL;MAAEL,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,oBAAoB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAa,CAAC,EACvI;MAAEV,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,mBAAmB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAY,CAAC,EAC/H;MAAEV,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,mBAAmB;MAAEK,KAAK,EAAE,GAAG;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,iBAAiB;MAAEC,WAAW,EAAE;IAAa,CAAC,CAC3I;IACDC,OAAO,EAAE,CACP;MAAEX,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,gBAAgB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,CAAC;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAY,CAAC,EACnI;MAAEV,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,sBAAsB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAa,CAAC,CAC5I;IACDE,aAAa,EAAE,CACb;MAAEZ,EAAE,EAAE,gBAAgB;MAAEC,IAAI,EAAE,qBAAqB;MAAEK,KAAK,EAAE,GAAG;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAa,CAAC,EACzI;MAAEV,EAAE,EAAE,gBAAgB;MAAEC,IAAI,EAAE,oBAAoB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAa,CAAC,CAC1I;IACDG,QAAQ,EAAE,CACR;MAAEb,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAE,eAAe;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAY,CAAC,EACpI;MAAEV,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAE,eAAe;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAa,CAAC,CACtI;IACDI,MAAM,EAAE,CACN;MAAEd,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,qBAAqB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,iBAAiB;MAAEC,WAAW,EAAE;IAAa,CAAC,CAChJ;IACDK,QAAQ,EAAE,CACR;MAAEf,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAE,mBAAmB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAa,CAAC;EAEvI,CAAC;EAED,MAAMM,WAAW,GAAGA,CAACC,KAAuB,EAAE9C,KAAa,EAAE2B,OAAe,KAAK;IAC/E,MAAMoB,IAAI,GAAGD,KAAK,CAACE,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,OAAO,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACC,UAAU;IACzE,MAAMC,OAAO,GAAGL,MAAM,CAACM,WAAW,IAAIJ,QAAQ,CAACC,eAAe,CAACI,SAAS;IAExEnC,UAAU,CAAC;MACTC,OAAO,EAAE,IAAI;MACbC,CAAC,EAAEsB,IAAI,CAACY,IAAI,GAAGT,OAAO,GAAGH,IAAI,CAAChE,KAAK,GAAG,CAAC;MACvC2C,CAAC,EAAEqB,IAAI,CAACa,GAAG,GAAGJ,OAAO,GAAG,EAAE;MAC1B7B,OAAO;MACP3B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6D,WAAW,GAAGA,CAAA,KAAM;IACxBtC,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtC,OAAO,EAAE;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMuC,eAAe,GAAG,CACtB;IACE/D,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACR,WAAW;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,YAAY;IACzBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACP,IAAI;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,OAAO;IACpBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACN,QAAQ;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBxC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM6F,gBAAgB,GAAG,CACvB;IACEhE,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACT,MAAM;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACR,WAAW;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,YAAY;IACzBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACP,IAAI;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,OAAO;IACpBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACN,QAAQ;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBxC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM8F,oBAAoB,GAAG,CAC3B;IACEjE,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACN,QAAQ;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,UAAU;IACvBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACR,WAAW;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACP,IAAI;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdxC,KAAK,EAAE;EACT,CAAC,EACD;IACE6B,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE,UAAU;IACvBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAElD,OAAA,CAACT,MAAM;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBxC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM+F,eAAe,GAAGA,CAACpB,KAA2B,EAAEqB,QAAgB,KAAK;IACzElD,cAAc,CAACkD,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQpD,WAAW;MACjB,KAAK,CAAC;QAAE;QACN,oBACE9D,OAAA,CAAAE,SAAA;UAAAmD,QAAA,gBAEErD,OAAA,CAACO,aAAa;YAAA8C,QAAA,gBACZrD,OAAA,CAACjB,GAAG;cAAAsE,QAAA,eACFrD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,IAAI;gBAAC3C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAAAoC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNzD,OAAA,CAACmB,aAAa;cAAAkC,QAAA,gBACZrD,OAAA,CAACF,YAAY;gBACX4D,OAAO,EAAC,SAAS;gBACjByD,SAAS,eAAEnH,OAAA,CAACL,UAAU;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1B2D,IAAI,EAAC,IAAI;gBAAA/D,QAAA,EACV;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC,eACfzD,OAAA,CAACF,YAAY;gBACX4D,OAAO,EAAC,SAAS;gBACjByD,SAAS,eAAEnH,OAAA,CAACJ,aAAa;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7B2D,IAAI,EAAC,IAAI;gBAAA/D,QAAA,EACV;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGhBzD,OAAA,CAACsB,WAAW;YAAA+B,QAAA,EACTyD,gBAAgB,CAACO,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAClCvH,OAAA,CAAC6C,UAAU;cAAA,GAAiByE;YAAM,GAAjBC,KAAK;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAGdzD,OAAA,CAACjB,GAAG;YAACyI,EAAE,EAAE;cAAEhH,OAAO,EAAE,MAAM;cAAEe,mBAAmB,EAAE,SAAS;cAAEH,GAAG,EAAE,kBAAkB;cAAEqG,EAAE,EAAE;YAAE,CAAE;YAAApE,QAAA,gBAE3FrD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,mCAAyB;cAAC4E,QAAQ,EAAC,6CAA6C;cAAChG,OAAO,EAAC,IAAI;cAAA2B,QAAA,eACvGrD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBAAE1F,MAAM,EAAE,OAAO;kBAAE6F,QAAQ,EAAE,UAAU;kBAAEvH,eAAe,EAAE,OAAO;kBAAE2B,YAAY,EAAE;gBAAmB,CAAE;gBAAAsB,QAAA,gBAK7GrD,OAAA;kBAAK6B,KAAK,EAAC,MAAM;kBAACC,MAAM,EAAC,KAAK;kBAAC8F,OAAO,EAAC,aAAa;kBAACC,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAU,CAAE;kBAAAzE,QAAA,gBAElFrD,OAAA;oBAAAqD,QAAA,eACErD,OAAA;sBAAS2E,EAAE,EAAC,cAAc;sBAAC9C,KAAK,EAAC,KAAK;sBAACC,MAAM,EAAC,IAAI;sBAACiG,YAAY,EAAC,gBAAgB;sBAAA1E,QAAA,eAC9ErD,OAAA;wBAAMgI,CAAC,EAAC,oBAAoB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAG;wBAAA7E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACPzD,OAAA;oBAAM6B,KAAK,EAAC,MAAM;oBAACC,MAAM,EAAC,KAAK;oBAACmG,IAAI,EAAC;kBAAoB;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG5DzD,OAAA;oBAAMoI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACL,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAEzEzD,OAAA;oBAAMoI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACL,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG3EzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAnF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAnF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAnF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAnF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAnF,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG3EzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpFzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGpFzD,OAAA;oBAAMgI,CAAC,EAAC,mBAAmB;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACM,aAAa,EAAC;kBAAO;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChGzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/CzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG/CzD,OAAA;oBAAMgI,CAAC,EAAC,mBAAmB;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACM,aAAa,EAAC;kBAAO;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChGzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/CzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG/CzD,OAAA;oBAAMgI,CAAC,EAAC,qBAAqB;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACM,aAAa,EAAC;kBAAO;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAClGzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAGhDzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACzH,UAAU,EAAC,KAAK;oBAAAsC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnGzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACzH,UAAU,EAAC,KAAK;oBAAAsC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAEnGzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACzH,UAAU,EAAC,KAAK;oBAAAsC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnGzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACzH,UAAU,EAAC,KAAK;oBAAAsC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAEnGzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACzH,UAAU,EAAC,KAAK;oBAAAsC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpGzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACzH,UAAU,EAAC,KAAK;oBAAAsC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACPG,QAAQ,EAAE,UAAU;oBACpBkB,MAAM,EAAE,EAAE;oBACVpC,IAAI,EAAE,KAAK;oBACXqC,SAAS,EAAE,kBAAkB;oBAC7BtI,OAAO,EAAE,MAAM;oBACfY,GAAG,EAAE,CAAC;oBACNhB,eAAe,EAAE,uBAAuB;oBACxCsB,OAAO,EAAE,UAAU;oBACnBK,YAAY,EAAE,KAAK;oBACnBgH,MAAM,EAAE;kBACV,CAAE;kBAAA1F,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAE3F,KAAK,EAAE,EAAE;wBAAEC,MAAM,EAAE,EAAE;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE;sBAAU,CAAE;sBAAAoC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAE3F,KAAK,EAAE,EAAE;wBAAEC,MAAM,EAAE,EAAE;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE;sBAAU,CAAE;sBAAAoC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAE3F,KAAK,EAAE,EAAE;wBAAEC,MAAM,EAAE,EAAE;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE;sBAAU,CAAE;sBAAAoC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGPzD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,sCAAiC;cAAC4E,QAAQ,EAAC,mCAAmC;cAAChG,OAAO,EAAC,IAAI;cAAA2B,QAAA,eACrGrD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBAAE1F,MAAM,EAAE,OAAO;kBAAEtB,OAAO,EAAE,MAAM;kBAAEwI,aAAa,EAAE,QAAQ;kBAAEvI,cAAc,EAAE,QAAQ;kBAAEC,UAAU,EAAE,QAAQ;kBAAEN,eAAe,EAAE,OAAO;kBAAE2B,YAAY,EAAE,kBAAkB;kBAAE4F,QAAQ,EAAE;gBAAW,CAAE;gBAAAtE,QAAA,gBAEvMrD,OAAA;kBAAK6B,KAAK,EAAC,KAAK;kBAACC,MAAM,EAAC,KAAK;kBAAC8F,OAAO,EAAC,aAAa;kBAACC,KAAK,EAAE;oBAAElH,YAAY,EAAE;kBAAO,CAAE;kBAAA0C,QAAA,gBAElFrD,OAAA;oBACE0I,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,GAAG;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC,eAGFzD,OAAA;oBACE0I,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,KAAK;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eAGFzD,OAAA;oBACE0I,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,MAAM;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eAGFzD,OAAA;oBACE0I,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,MAAM;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eAGFzD,OAAA;oBACE0I,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,MAAM;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eAGFzD,OAAA;oBACE0I,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,MAAM;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eAGFzD,OAAA;oBAAQ0I,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC;kBAAO;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC7CzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACzH,UAAU,EAAC,MAAM;oBAAAsC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClGzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACPhH,OAAO,EAAE,MAAM;oBACfe,mBAAmB,EAAE,gBAAgB;oBACrCH,GAAG,EAAE,CAAC;oBACNS,KAAK,EAAE,MAAM;oBACbsH,QAAQ,EAAE;kBACZ,CAAE;kBAAA9F,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA4C,QAAA,gBAClFrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAE,CAAE;sBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAE1B,eAAe,EAAE,SAAS;0BAAE2B,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAAC8D,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAoC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,eACNzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAsC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA4C,QAAA,gBAClFrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAE,CAAE;sBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAE1B,eAAe,EAAE,SAAS;0BAAE2B,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAAC8D,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAoC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC,eACNzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAsC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA4C,QAAA,gBAClFrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAE,CAAE;sBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAE1B,eAAe,EAAE,SAAS;0BAAE2B,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAAC8D,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAoC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACNzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAsC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA4C,QAAA,gBAClFrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAE,CAAE;sBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAE1B,eAAe,EAAE,SAAS;0BAAE2B,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAAC8D,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAoC,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACNzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAsC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA4C,QAAA,gBAClFrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAE,CAAE;sBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAE1B,eAAe,EAAE,SAAS;0BAAE2B,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAAC8D,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAoC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC,eACNzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAsC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA4C,QAAA,gBAClFrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAE,CAAE;sBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAE1B,eAAe,EAAE,SAAS;0BAAE2B,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAAC8D,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAoC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,eACNzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAAC8D,EAAE,EAAE;wBAAE1G,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAsC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAgENzD,OAAA,CAACjB,GAAG;YAACyI,EAAE,EAAE;cAAEhH,OAAO,EAAE,MAAM;cAAEe,mBAAmB,EAAE,SAAS;cAAEH,GAAG,EAAE,kBAAkB;cAAEqG,EAAE,EAAE;YAAE,CAAE;YAAApE,QAAA,gBAE3FrD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,kCAA6B;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eACpDrD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBAAEhH,OAAO,EAAE,MAAM;kBAAEwI,aAAa,EAAE,QAAQ;kBAAE5H,GAAG,EAAE;gBAAmB,CAAE;gBAAAiC,QAAA,gBAE7ErD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEgI,QAAQ,EAAE;oBAAI,CAAE;oBAAA/F,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAE3F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACPtF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC+F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAAhG,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACyG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAjG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEgI,QAAQ,EAAE;oBAAI,CAAE;oBAAA/F,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAE3F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACPtF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC+F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAAhG,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACyG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAjG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEgI,QAAQ,EAAE;oBAAI,CAAE;oBAAA/F,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAE3F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACPtF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC+F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAAhG,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACyG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAjG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEgI,QAAQ,EAAE;oBAAI,CAAE;oBAAA/F,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAE3F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACPtF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC+F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAAhG,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,IAAI;wBACXC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACyG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAjG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEgI,QAAQ,EAAE;oBAAI,CAAE;oBAAA/F,QAAA,gBACxErD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAE3F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAE1B,eAAe,EAAE,SAAS;wBAAE2B,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,OAAO;sBAAC3C,UAAU,EAAC,QAAQ;sBAAAsC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACPtF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC+F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAAhG,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,IAAI;wBACXC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,SAAS;wBAC1B2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,MAAM;oBAACyG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAjG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEe,mBAAmB,EAAE,gBAAgB;oBAAEH,GAAG,EAAE,CAAC;oBAAEmI,EAAE,EAAE;kBAAE,CAAE;kBAAAlG,QAAA,gBACjFrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACPgC,CAAC,EAAE,CAAC;sBACJpJ,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChCuH,SAAS,EAAE;oBACb,CAAE;oBAAAjG,QAAA,gBACArD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAACzC,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACPgC,CAAC,EAAE,CAAC;sBACJpJ,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChCuH,SAAS,EAAE;oBACb,CAAE;oBAAAjG,QAAA,gBACArD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAACzC,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACPgC,CAAC,EAAE,CAAC;sBACJpJ,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChCuH,SAAS,EAAE;oBACb,CAAE;oBAAAjG,QAAA,gBACArD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,SAAS;sBAACzC,KAAK,EAAC,SAAS;sBAAAoC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGPzD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,iCAAuB;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eAC9CrD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBAAE1F,MAAM,EAAE,OAAO;kBAAEtB,OAAO,EAAE,MAAM;kBAAEwI,aAAa,EAAE,QAAQ;kBAAEvI,cAAc,EAAE,QAAQ;kBAAEC,UAAU,EAAE,QAAQ;kBAAEN,eAAe,EAAE,SAAS;kBAAE2B,YAAY,EAAE,kBAAkB;kBAAE4F,QAAQ,EAAE;gBAAW,CAAE;gBAAAtE,QAAA,eAEzMrD,OAAA;kBAAK6B,KAAK,EAAC,MAAM;kBAACC,MAAM,EAAC,KAAK;kBAAC8F,OAAO,EAAC,aAAa;kBAACC,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAU,CAAE;kBAAAzE,QAAA,gBAElFrD,OAAA;oBAAAqD,QAAA,eACErD,OAAA;sBAAS2E,EAAE,EAAC,cAAc;sBAAC9C,KAAK,EAAC,OAAO;sBAACC,MAAM,EAAC,IAAI;sBAACiG,YAAY,EAAC,gBAAgB;sBAAA1E,QAAA,eAChFrD,OAAA;wBAAMgI,CAAC,EAAC,sBAAsB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAA7E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACPzD,OAAA;oBAAM6B,KAAK,EAAC,MAAM;oBAACC,MAAM,EAAC,MAAM;oBAACmG,IAAI,EAAC;kBAAoB;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG7DzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzDzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG1DzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,IAAI;oBAACmH,IAAI,EAAC,SAAS;oBAAA5E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG7DzD,OAAA;oBAAMgI,CAAC,EAAC,2DAA2D;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAGlHzD,OAAA;oBAAQ0I,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/CzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDzD,OAAA;oBAAQ0I,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG/CzD,OAAA;oBAAMuE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,GAAG;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,GAAG;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,GAAG;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAAC1D,QAAQ,EAAC,GAAG;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,GAAG;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EzD,OAAA;oBAAMuE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAAC1D,QAAQ,EAAC,GAAG;oBAACmH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAnF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNzD,OAAA,CAACH,IAAI;YAACiD,KAAK,EAAC,+BAAqB;YAACpB,OAAO,EAAC,IAAI;YAAA2B,QAAA,eAC5CrD,OAAA,CAACjB,GAAG;cAACyI,EAAE,EAAE;gBAAEhH,OAAO,EAAE,MAAM;gBAAEe,mBAAmB,EAAE,gBAAgB;gBAAEH,GAAG,EAAE;cAAmB,CAAE;cAAAiC,QAAA,gBAE3FrD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJpJ,eAAe,EAAE,SAAS;kBAC1B2B,YAAY,EAAE,kBAAkB;kBAChCuH,SAAS,EAAE;gBACb,CAAE;gBAAAjG,QAAA,gBACArD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,IAAI;kBAAC3C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,cAAc;kBAACuG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAApE,QAAA,EAAC;gBAE/E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACzC,KAAK,EAAC,gBAAgB;kBAAAoC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJpJ,eAAe,EAAE,SAAS;kBAC1B2B,YAAY,EAAE,kBAAkB;kBAChCuH,SAAS,EAAE;gBACb,CAAE;gBAAAjG,QAAA,gBACArD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,IAAI;kBAAC3C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACuG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAApE,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACzC,KAAK,EAAC,gBAAgB;kBAAAoC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJpJ,eAAe,EAAE,SAAS;kBAC1B2B,YAAY,EAAE,kBAAkB;kBAChCuH,SAAS,EAAE;gBACb,CAAE;gBAAAjG,QAAA,gBACArD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,IAAI;kBAAC3C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACuG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAApE,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACzC,KAAK,EAAC,gBAAgB;kBAAAoC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJpJ,eAAe,EAAE,SAAS;kBAC1B2B,YAAY,EAAE,kBAAkB;kBAChCuH,SAAS,EAAE;gBACb,CAAE;gBAAAjG,QAAA,gBACArD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,IAAI;kBAAC3C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACuG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAApE,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,OAAO;kBAACzC,KAAK,EAAC,gBAAgB;kBAAAoC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,eA4CP,CAAC;MAGP,KAAK,CAAC;QAAE;QACN,oBACEzD,OAAA,CAAAE,SAAA;UAAAmD,QAAA,eAEErD,OAAA,CAACH,IAAI;YAACiD,KAAK,EAAC,4BAA4B;YAAC4E,QAAQ,EAAC,oDAAoD;YAAChG,OAAO,EAAC,IAAI;YAAA2B,QAAA,eACjHrD,OAAA,CAACjB,GAAG;cAACyI,EAAE,EAAE;gBAAEhH,OAAO,EAAE,MAAM;gBAAEwI,aAAa,EAAE,QAAQ;gBAAE5H,GAAG,EAAE;cAAmB,CAAE;cAAAiC,QAAA,gBAE7ErD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPhH,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,eAAe;kBAC/B+I,CAAC,EAAE,CAAC;kBACJT,MAAM,EAAE,iCAAiC;kBACzChH,YAAY,EAAE,kBAAkB;kBAChC,SAAS,EAAE;oBACT3B,eAAe,EAAE,sBAAsB;oBACvCqJ,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAApG,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEtF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEqG,EAAE,EAAE;oBAAE,CAAE;oBAAApE,QAAA,gBAChErD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,UAAU;sBAAAsC,QAAA,EAAC;oBAE/C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACPkC,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPvJ,eAAe,EAAE,SAAS;wBAC1Ba,KAAK,EAAE,SAAS;wBAChBc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE;sBACd,CAAE;sBAAAsC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACPkC,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPvJ,eAAe,EAAE,uBAAuB;wBACxCa,KAAK,EAAE,uBAAuB;wBAC9Bc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE;sBACZ,CAAE;sBAAAuC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEH,KAAK,EAAE,uBAAuB;sBAAEH,QAAQ,EAAE;oBAAO,CAAE;oBAAAuC,QAAA,gBAC3GrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,CAAC;0BAAEC,MAAM,EAAE,CAAC;0BAAEC,YAAY,EAAE,KAAK;0BAAE3B,eAAe,EAAE;wBAAwB;sBAAE;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAErG;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACR,WAAW;wBAACgI,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,EAAE;0BAAEG,KAAK,EAAE;wBAA2B;sBAAE;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,mBAE1E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEvG,KAAK,EAAE,0BAA0B;wBAAEF,UAAU,EAAE;sBAAS,CAAE;sBAAAsC,QAAA,EAAC;oBAEtE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAAAsE,QAAA,EAAC;oBAEL;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,IAAI;oBAAC3C,UAAU,EAAC,MAAM;oBAAAsC,QAAA,EAAC;kBAE3C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACP3F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC+F,QAAQ,EAAE;oBACZ,CAAE;oBAAAzE,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPhH,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,eAAe;kBAC/B+I,CAAC,EAAE,CAAC;kBACJT,MAAM,EAAE,iCAAiC;kBACzChH,YAAY,EAAE,kBAAkB;kBAChC,SAAS,EAAE;oBACT3B,eAAe,EAAE,sBAAsB;oBACvCqJ,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAApG,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEtF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEqG,EAAE,EAAE;oBAAE,CAAE;oBAAApE,QAAA,gBAChErD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,UAAU;sBAAAsC,QAAA,EAAC;oBAE/C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACPkC,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPvJ,eAAe,EAAE,SAAS;wBAC1Ba,KAAK,EAAE,SAAS;wBAChBc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE;sBACd,CAAE;sBAAAsC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACPkC,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPvJ,eAAe,EAAE,uBAAuB;wBACxCa,KAAK,EAAE,uBAAuB;wBAC9Bc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE;sBACZ,CAAE;sBAAAuC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEH,KAAK,EAAE,uBAAuB;sBAAEH,QAAQ,EAAE;oBAAO,CAAE;oBAAAuC,QAAA,gBAC3GrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,CAAC;0BAAEC,MAAM,EAAE,CAAC;0BAAEC,YAAY,EAAE,KAAK;0BAAE3B,eAAe,EAAE;wBAAwB;sBAAE;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAErG;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACR,WAAW;wBAACgI,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,EAAE;0BAAEG,KAAK,EAAE;wBAA2B;sBAAE;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,iBAE1E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEvG,KAAK,EAAE,0BAA0B;wBAAEF,UAAU,EAAE;sBAAS,CAAE;sBAAAsC,QAAA,EAAC;oBAEtE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAAAsE,QAAA,EAAC;oBAEL;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,IAAI;oBAAC3C,UAAU,EAAC,MAAM;oBAAAsC,QAAA,EAAC;kBAE3C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACP3F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC+F,QAAQ,EAAE;oBACZ,CAAE;oBAAAzE,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPhH,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,eAAe;kBAC/B+I,CAAC,EAAE,CAAC;kBACJT,MAAM,EAAE,iCAAiC;kBACzChH,YAAY,EAAE,kBAAkB;kBAChC,SAAS,EAAE;oBACT3B,eAAe,EAAE,sBAAsB;oBACvCqJ,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAApG,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEtF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEqG,EAAE,EAAE;oBAAE,CAAE;oBAAApE,QAAA,gBAChErD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,UAAU;sBAAAsC,QAAA,EAAC;oBAE/C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACPkC,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPvJ,eAAe,EAAE,SAAS;wBAC1Ba,KAAK,EAAE,SAAS;wBAChBc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE;sBACd,CAAE;sBAAAsC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACPkC,EAAE,EAAE,GAAG;wBACPC,EAAE,EAAE,GAAG;wBACPvJ,eAAe,EAAE,uBAAuB;wBACxCa,KAAK,EAAE,uBAAuB;wBAC9Bc,YAAY,EAAE,kBAAkB;wBAChCjB,QAAQ,EAAE;sBACZ,CAAE;sBAAAuC,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE,CAAC;sBAAEH,KAAK,EAAE,uBAAuB;sBAAEH,QAAQ,EAAE;oBAAO,CAAE;oBAAAuC,QAAA,gBAC3GrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAE3F,KAAK,EAAE,CAAC;0BAAEC,MAAM,EAAE,CAAC;0BAAEC,YAAY,EAAE,KAAK;0BAAE3B,eAAe,EAAE;wBAAwB;sBAAE;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAErG;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE;sBAAI,CAAE;sBAAAiC,QAAA,gBAC3DrD,OAAA,CAACR,WAAW;wBAACgI,EAAE,EAAE;0BAAE1G,QAAQ,EAAE,EAAE;0BAAEG,KAAK,EAAE;wBAA2B;sBAAE;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,iBAE1E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEvG,KAAK,EAAE,wBAAwB;wBAAEF,UAAU,EAAE;sBAAS,CAAE;sBAAAsC,QAAA,EAAC;oBAEpE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAAAsE,QAAA,EAAC;oBAEL;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEhH,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEU,GAAG,EAAE;kBAAE,CAAE;kBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,IAAI;oBAAC3C,UAAU,EAAC,MAAM;oBAAAsC,QAAA,EAAC;kBAE3C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACP3F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,CAAC;sBACT1B,eAAe,EAAE,uBAAuB;sBACxC2B,YAAY,EAAE,oBAAoB;sBAClC+F,QAAQ,EAAE;oBACZ,CAAE;oBAAAzE,QAAA,eACArD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACd1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,gBACP,CAAC;MAGP,KAAK,CAAC;QAAE;QACN,oBACEzD,OAAA,CAAAE,SAAA;UAAAmD,QAAA,gBAGErD,OAAA,CAACjB,GAAG;YAACyI,EAAE,EAAE;cAAEhH,OAAO,EAAE,MAAM;cAAEe,mBAAmB,EAAE,gBAAgB;cAAEH,GAAG,EAAE,kBAAkB;cAAEqG,EAAE,EAAE;YAAE,CAAE;YAAApE,QAAA,gBAElGrD,OAAA,CAACjB,GAAG;cAACyI,EAAE,EAAE;gBACPgC,CAAC,EAAE,CAAC;gBACJpJ,eAAe,EAAE,OAAO;gBACxB2B,YAAY,EAAE,kBAAkB;gBAChCgH,MAAM,EAAE,iCAAiC;gBACzCa,SAAS,EAAE,8BAA8B;gBACzCjC,QAAQ,EAAE;cACZ,CAAE;cAAAtE,QAAA,gBACArD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBAAEhH,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE+G,EAAE,EAAE;gBAAE,CAAE;gBAAApE,QAAA,gBAC7FrD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,SAAS;kBAACzC,KAAK,EAAC,SAAS;kBAACuG,EAAE,EAAE;oBAAEzG,UAAU,EAAE;kBAAS,CAAE;kBAAAsC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACP3F,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACV1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,kBAAkB;oBAChCvB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA4C,QAAA,eACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACP3F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACV1B,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChCvB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBQ,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAsC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,IAAI;gBAAC3C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACuG,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAE3G,QAAQ,EAAE;gBAAO,CAAE;gBAAAuC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,SAAS;gBAACzC,KAAK,EAAC,SAAS;gBAACuG,EAAE,EAAE;kBAAEzG,UAAU,EAAE;gBAAS,CAAE;gBAAAsC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;cAACyI,EAAE,EAAE;gBACPgC,CAAC,EAAE,CAAC;gBACJpJ,eAAe,EAAE,OAAO;gBACxB2B,YAAY,EAAE,kBAAkB;gBAChCgH,MAAM,EAAE,iCAAiC;gBACzCa,SAAS,EAAE,8BAA8B;gBACzCjC,QAAQ,EAAE;cACZ,CAAE;cAAAtE,QAAA,gBACArD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBAAEhH,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE+G,EAAE,EAAE;gBAAE,CAAE;gBAAApE,QAAA,gBAC7FrD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,SAAS;kBAACzC,KAAK,EAAC,SAAS;kBAACuG,EAAE,EAAE;oBAAEzG,UAAU,EAAE;kBAAS,CAAE;kBAAAsC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACP3F,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACV1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,kBAAkB;oBAChCvB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA4C,QAAA,eACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACP3F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACV1B,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,KAAK;sBACnBvB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBQ,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAsC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,IAAI;gBAAC3C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACuG,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAE3G,QAAQ,EAAE;gBAAO,CAAE;gBAAAuC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,SAAS;gBAACzC,KAAK,EAAC,SAAS;gBAACuG,EAAE,EAAE;kBAAEzG,UAAU,EAAE;gBAAS,CAAE;gBAAAsC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNzD,OAAA,CAACjB,GAAG;cAACyI,EAAE,EAAE;gBACPgC,CAAC,EAAE,CAAC;gBACJpJ,eAAe,EAAE,OAAO;gBACxB2B,YAAY,EAAE,kBAAkB;gBAChCgH,MAAM,EAAE,iCAAiC;gBACzCa,SAAS,EAAE,8BAA8B;gBACzCjC,QAAQ,EAAE;cACZ,CAAE;cAAAtE,QAAA,gBACArD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBAAEhH,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE+G,EAAE,EAAE;gBAAE,CAAE;gBAAApE,QAAA,gBAC7FrD,OAAA,CAAChB,UAAU;kBAAC0E,OAAO,EAAC,SAAS;kBAACzC,KAAK,EAAC,SAAS;kBAACuG,EAAE,EAAE;oBAAEzG,UAAU,EAAE;kBAAS,CAAE;kBAAAsC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACP3F,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACV1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,kBAAkB;oBAChCvB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA4C,QAAA,eACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBACP3F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACV1B,eAAe,EAAE,SAAS;sBAC1B2B,YAAY,EAAE,kBAAkB;sBAChCvB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBQ,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAsC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,IAAI;gBAAC3C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACuG,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAE3G,QAAQ,EAAE;gBAAO,CAAE;gBAAAuC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;gBAAC0E,OAAO,EAAC,SAAS;gBAACzC,KAAK,EAAC,SAAS;gBAACuG,EAAE,EAAE;kBAAEzG,UAAU,EAAE;gBAAS,CAAE;gBAAAsC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;YAACyI,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAApE,QAAA,eACjBrD,OAAA,CAACH,IAAI;cAACiD,KAAK,EAAC,qBAAqB;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eAC9CrD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBAAEhH,OAAO,EAAE,MAAM;kBAAEwI,aAAa,EAAE,QAAQ;kBAAE5H,GAAG,EAAE;gBAAmB,CAAE;gBAAAiC,QAAA,gBAE7ErD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACPhH,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B+I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzChH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCqJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAApG,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEtF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEqG,EAAE,EAAE;sBAAE,CAAE;sBAAApE,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACPkC,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPvJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAEvG,KAAK,EAAE,0BAA0B;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAExF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC+F,QAAQ,EAAE;sBACZ,CAAE;sBAAAzE,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACP3F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACPhH,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B+I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzChH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCqJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAApG,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEtF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEqG,EAAE,EAAE;sBAAE,CAAE;sBAAApE,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACPkC,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPvJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAEvG,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC+F,QAAQ,EAAE;sBACZ,CAAE;sBAAAzE,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACP3F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACPhH,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B+I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzChH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCqJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAApG,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEtF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEqG,EAAE,EAAE;sBAAE,CAAE;sBAAApE,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACPkC,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPvJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAEvG,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC+F,QAAQ,EAAE;sBACZ,CAAE;sBAAAzE,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACP3F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACPhH,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B+I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzChH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCqJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAApG,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEtF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEqG,EAAE,EAAE;sBAAE,CAAE;sBAAApE,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACPkC,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPvJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAEvG,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC+F,QAAQ,EAAE;sBACZ,CAAE;sBAAAzE,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACP3F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACPhH,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B+I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzChH,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACT3B,eAAe,EAAE,sBAAsB;sBACvCqJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAApG,QAAA,gBACArD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEtF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBrD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEqG,EAAE,EAAE;sBAAE,CAAE;sBAAApE,QAAA,gBAChErD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,IAAI;wBAAC3C,UAAU,EAAC,UAAU;wBAAAsC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACPkC,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACPvJ,eAAe,EAAE,SAAS;0BAC1Ba,KAAK,EAAE,SAAS;0BAChBc,YAAY,EAAE,kBAAkB;0BAChCjB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAsC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNzD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBAAEhH,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEU,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAuC,QAAA,gBAC3GrD,OAAA,CAAChB,UAAU;wBAAC0E,OAAO,EAAC,SAAS;wBAACzC,KAAK,EAAC,gBAAgB;wBAAAoC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BAAEvG,KAAK,EAAE,0BAA0B;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAuC,QAAA,EAAC;sBAExF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzD,OAAA,CAACjB,GAAG;oBAACyI,EAAE,EAAE;sBAAEhH,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAiC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;sBAAC0E,OAAO,EAAC,IAAI;sBAAC3C,UAAU,EAAC,MAAM;sBAAAsC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzD,OAAA,CAACjB,GAAG;sBAACyI,EAAE,EAAE;wBACP3F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACT1B,eAAe,EAAE,uBAAuB;wBACxC2B,YAAY,EAAE,oBAAoB;wBAClC+F,QAAQ,EAAE;sBACZ,CAAE;sBAAAzE,QAAA,eACArD,OAAA,CAACjB,GAAG;wBAACyI,EAAE,EAAE;0BACP3F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACd1B,eAAe,EAAE,uBAAuB;0BACxC2B,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGNzD,OAAA,CAACH,IAAI;YAACiD,KAAK,EAAC,+BAA+B;YAACpB,OAAO,EAAC,IAAI;YAAA2B,QAAA,eACtDrD,OAAA,CAACjB,GAAG;cAACyI,EAAE,EAAE;gBAAEhH,OAAO,EAAE,MAAM;gBAAEwI,aAAa,EAAE,QAAQ;gBAAE5H,GAAG,EAAE;cAAmB,CAAE;cAAAiC,QAAA,gBAE7ErD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJpJ,eAAe,EAAE,SAAS;kBAC1B2I,MAAM,EAAE,mBAAmB;kBAC3BhH,YAAY,EAAE,kBAAkB;kBAChCvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBU,GAAG,EAAE;gBACP,CAAE;gBAAAiC,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACP3F,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACT1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,KAAK;oBACnBwH,EAAE,EAAE;kBACN;gBAAE;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEtF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,QAAQ;oBAACyG,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAApE,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,SAAS;oBAACzC,KAAK,EAAC,gBAAgB;oBAAAoC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJpJ,eAAe,EAAE,SAAS;kBAC1B2I,MAAM,EAAE,mBAAmB;kBAC3BhH,YAAY,EAAE,kBAAkB;kBAChCvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBU,GAAG,EAAE;gBACP,CAAE;gBAAAiC,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACP3F,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACT1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,KAAK;oBACnBwH,EAAE,EAAE;kBACN;gBAAE;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEtF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,QAAQ;oBAACyG,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAApE,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,SAAS;oBAACzC,KAAK,EAAC,gBAAgB;oBAAAoC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA,CAACjB,GAAG;gBAACyI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJpJ,eAAe,EAAE,SAAS;kBAC1B2I,MAAM,EAAE,mBAAmB;kBAC3BhH,YAAY,EAAE,kBAAkB;kBAChCvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBU,GAAG,EAAE;gBACP,CAAE;gBAAAiC,QAAA,gBACArD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBACP3F,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACT1B,eAAe,EAAE,SAAS;oBAC1B2B,YAAY,EAAE,KAAK;oBACnBwH,EAAE,EAAE;kBACN;gBAAE;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLzD,OAAA,CAACjB,GAAG;kBAACyI,EAAE,EAAE;oBAAEtF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBrD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,OAAO;oBAAC3C,UAAU,EAAC,QAAQ;oBAACyG,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAApE,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzD,OAAA,CAAChB,UAAU;oBAAC0E,OAAO,EAAC,SAAS;oBAACzC,KAAK,EAAC,gBAAgB;oBAAAoC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,eACP,CAAC;MAKP;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEzD,OAAA;IAAK6J,SAAS,EAAC,WAAW;IAAAxG,QAAA,eACxBrD,OAAA;MAAK6J,SAAS,EAAC,kBAAkB;MAAAxG,QAAA,gBAC/BrD,OAAA,CAACG,gBAAgB;QAAAkD,QAAA,eACfrD,OAAA,CAACf,SAAS;UAACkK,QAAQ,EAAC,IAAI;UAAC3B,EAAE,EAAE;YAAEmC,EAAE,EAAE;UAAE,CAAE;UAAAtG,QAAA,gBAErCrD,OAAA,CAACa,UAAU;YAACkC,KAAK,EAAEe,WAAY;YAACgG,QAAQ,EAAE9C,eAAgB;YAAA3D,QAAA,gBACxDrD,OAAA,CAACb,GAAG;cAAC4K,KAAK,EAAC;YAAU;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxBzD,OAAA,CAACb,GAAG;cAAC4K,KAAK,EAAC;YAAW;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBzD,OAAA,CAACb,GAAG;cAAC4K,KAAK,EAAC;YAAgB;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,EAGZyD,gBAAgB,CAAC,CAAC;QAAA;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGlBW,OAAO,CAACE,OAAO,iBACdtE,OAAA,CAACjB,GAAG;QACFyI,EAAE,EAAE;UACFG,QAAQ,EAAE,OAAO;UACjBlB,IAAI,EAAErC,OAAO,CAACG,CAAC;UACfmC,GAAG,EAAEtC,OAAO,CAACI,CAAC;UACdsE,SAAS,EAAE,wBAAwB;UACnC1I,eAAe,EAAE,OAAO;UACxB2I,MAAM,EAAE,mBAAmB;UAC3BhH,YAAY,EAAE,KAAK;UACnBL,OAAO,EAAE,UAAU;UACnBkI,SAAS,EAAE,uEAAuE;UAClFI,MAAM,EAAE,KAAK;UACbC,aAAa,EAAE,MAAM;UACrBnJ,QAAQ,EAAE,MAAM;UAChBsI,QAAQ,EAAE,MAAM;UAChBE,SAAS,EAAE,QAAQ;UACnBY,UAAU,EAAE;QACd,CAAE;QAAA7G,QAAA,gBAEFrD,OAAA,CAAChB,UAAU;UAAC0E,OAAO,EAAC,OAAO;UAAC8D,EAAE,EAAE;YAAE1G,QAAQ,EAAE,MAAM;YAAEG,KAAK,EAAE,SAAS;YAAEwG,EAAE,EAAE,GAAG;YAAE0C,UAAU,EAAE;UAAI,CAAE;UAAA9G,QAAA,EAC9Fe,OAAO,CAACtB;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbzD,OAAA,CAAChB,UAAU;UAAC0E,OAAO,EAAC,OAAO;UAAC8D,EAAE,EAAE;YAAE1G,QAAQ,EAAE,MAAM;YAAEG,KAAK,EAAE,SAAS;YAAEF,UAAU,EAAE,KAAK;YAAEoJ,UAAU,EAAE;UAAI,CAAE;UAAA9G,QAAA,EACxGe,OAAO,CAACK;QAAO;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,CAppDID,eAAyB;AAAAwG,IAAA,GAAzBxG,eAAyB;AAspD/B,eAAeA,eAAe;AAAC,IAAAtD,EAAA,EAAAM,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAe,IAAA,EAAAyG,IAAA;AAAAC,YAAA,CAAA/J,EAAA;AAAA+J,YAAA,CAAAzJ,GAAA;AAAAyJ,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAA7I,GAAA;AAAA6I,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAAzH,IAAA;AAAAyH,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}