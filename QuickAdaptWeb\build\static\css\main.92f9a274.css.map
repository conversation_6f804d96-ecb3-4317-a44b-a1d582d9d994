{"version": 3, "file": "static/css/main.92f9a274.css", "mappings": "AAAA,aCKC,eACA,oBAJA,WACA,CACA,WACA,CAFA,gBACA,CAJD,iBACC,WAMA,qBAMA,WAHD,kCACC,eACA,gBAEA,qBAGD,WACC,+BAED,WACC,2BAED,WACC,yBAED,WACC,mCAED,WACC,gCAED,WACC,gCAED,WACC,uCAED,WACC,kCAED,WACC,kCAED,WACC,4BAED,WACC,kCAED,WACC,kBAED,WACC,mBAED,WACC,0BAED,WACC,gCAED,WACC,oBAED,WACC,gBAED,WACC,mBAED,WACC,oBAED,WACC,mBAED,WACC,qBAED,WACC,wBAED,WACC,sBAED,WACC,8BAED,WACC,wBAED,WACC,gCAED,WACC,6BAED,WACC,2BAED,WACC,yBAED,WACC,iCAED,WACC,uCAED,WACC,gBAED,WACC,8BAED,WACC,6BAED,WACC,uCAED,WACC,2BAED,<PERSON>CC,0BA<PERSON>,WACC,8BAED,WACC,oCAED,WACC,gBAED,WACC,wBAED,WACC,6BAED,WACC,iCAED,WACC,sCAED,WACC,2BAED,WACC,2BAED,WACC,oCAED,WACC,yBAED,WACC,uBAED,WACC,oBAED,WACC,uBAED,WACC,iCAED,WACC,wBAED,WACC,sBAED,WACC,qBAED,WACC,oBAED,WACC,gBAED,WACC,uBAED,WACC,qCAED,WACC,uBAED,WACC,qCAED,WACC,4BAED,WACC,yBAED,WACC,qBAED,WACC,oBAED,WACC,0BAED,WACC,qBAED,WACC,2BAED,WACC,wBAED,WACC,sBAED,WACC,2BAED,WACC,mBAED,WACC,0BAED,WACC,wBAED,WACC,yBAED,WACC,6BAED,WACC,kBAED,WACC,qBAED,WACC,qBAED,WACC,wBAED,WACC,gCAED,WACC,yBAED,WACC,mCAED,WACC,oBAED,WACC,+BAED,WACC,wCAED,WACC,4BAED,WACC,yBAED,WACC,4BAED,WACC,oBAED,WACC,wBAED,WACC,sBAED,WACC,wBAED,WACC,yBAED,WACC,mBAED,WACC,yBAED,WACC,qBAED,WACC,qBAED,WACC,uBAED,WACC,mCAED,WACC,uBAED,WACC,yBAED,WACC,2BAED,WACC,oBAED,WACC,uBAED,WACC,iBAED,WACC,yBAED,WACC,uBAED,WACC,kBAED,WACC,yBAED,WACC,kBAED,WACC,gBAED,WACC,6BAED,WACC,4BAED,WACC,yBAED,WACC,qBAED,WACC,0BAED,WACC,8BAED,WACC,yBAED,WACC,sBAED,WACC,qBAED,WACC,8BAED,WACC,2BAED,WACC,2BAED,WACC,+BAED,WACC,+BAED,WACC,2BAED,WACC,qBAED,WACC,2BAED,WACC,4BAED,WACC,qBAED,WACC,+BAED,WACC,qCAED,WACC,gCAED,WACC,mBAED,WACC,gCAED,WACC,2BAED,WACC,4BAED,WACC,wBAED,WACC,sBAED,WACC,yBAED,WACC,6BAED,WACC,qBAED,WACC,oBAED,WACC,0BAED,WACC,oBAED,WACC,yBAED,WACC,oBAED,WACC,0BAED,WACC,wBAED,WACC,oBAED,WACC,0BAED,WACC,2BAED,WACC,mBAED,WACC,wBAED,WACC,mBAED,WACC,iBAED,WACC,qBAED,WACC,qBAED,WACC,mBAED,WACC,uBAED,WACC,kBAED,WACC,oBAED,WACC,qBAED,WACC,qBAED,WACC,iBAED,WACC,uBAED,WACC,iBAED,WACC,uBAED,WACC,qBAED,WACC,oBAED,WACC,iBAED,WACC,sBAED,WACC,qBAED,WACC,mBAED,WACC,uBAED,WACC,kBAED,WACC,oBAED,WACC,sBAED,WACC,iBAED,WACC,wBAED,WACC,qBAED,WACC,yBAED,WACC,mBAED,WACC,6BAED,WACC,4BAED,WACC,iBAED,WACC,6BAED,WACC,mBAED,WACC,0BAED,WACC,wBAED,WACC,oBAED,WACC,kCAED,WACC,yBAED,WACC,qBAED,WACC,0BAED,WACC,wBAED,WACC,iBAED,WACC,uBAED,WACC,uBAED,WACC,kBAED,WACC,sBAED,WACC,sBAED,WACC,uBAED,WACC,iBAED,WACC,yBAED,WACC,oBAED,WACC,qBAED,WACC,yBAED,WACC,sBAED,WACC,yBAED,WACC,+BAED,WACC,2BAED,WACC,yBAED,WACC,0BAED,WACC,sBAED,WACC,4BAED,WACC,wBAED,WACC,sBAED,WACC,uBAED,WACC,kBAED,WACC,sBAED,WACC,mBAED,WACC,wBAED,WACC,mBAED,WACC,mBAED,WACC,uBAED,WACC,wBAED,WACC,iBAED,WACC,sBAED,WACC,wBAED,WACC,wBAED,WACC,yBAED,WACC,yBAED,WACC,sBAED,WACC,uBAED,WACC,0BAED,WACC,yBAED,WACC,6BAED,WACC,sBAED,WACC,mBAED,WACC,0BAED,WACC,yBAED,WACC,4BAED,WACC,0BAED,WACC,qBAED,WACC,sBAED,WACC,uBAED,WACC,iCAED,WACC,0BAED,WACC,uBAED,WACC,gCAED,WACC,4BAED,WACC,4BAED,WACC,kCAED,WACC,2BAED,WACC,4BAED,WACC,uBAED,WACC,yBAED,WACC,wBAED,WACC,4BAED,WACC,uBAED,WACC,2BAED,WACC,6BAED,WACC,wBAED,WACC,4BAED,WACC,+BAED,WACC,iCAED,WACC,4BAED,WACC,0BAED,WACC,6BAED,WACC,oBAED,WACC,kCAED,WACC,gCAED,WACC,yBAED,WACC,gCAED,WACC,iCAED,WACC,2BAED,WACC,wBAED,WACC,0BAED,WACC,yBAED,WACC,uBAED,WACC,2BAED,WACC,6BAED,WACC,+BAED,WACC,iCAED,WACC,sCAED,WACC,oBAED,WACC,8BAED,WACC,iBAED,WACC,qBAED,WACC,uBAED,WACC,8BAED,WACC,yCAED,WACC,wBAED,WACC,iCAED,WACC,0BAED,WACC,kBAED,WACC,mBAED,WACC,kBAED,WACC,iBAED,WACC,iBAED,WACC,iBAED,WACC,eAED,WACC,kBAED,WACC,kBAED,WACC,wBAED,WACC,yBAED,WACC,sBAED,WACC,mBAED,WACC,gBAED,WACC,iBAED,WACC,kBAED,WACC,iBAED,WACC,qBAED,WACC,kBAED,WACC,iBAED,WACC,iBAED,WACC,qBAED,WACC,iBAED,WACC,iBAED,WACC,uBAED,WACC,sBAED,WACC,mBAED,WACC,oBAED,WACC,gBAED,WACC,iBAED,WACC,qBAED,WACC,kBAED,WACC,mBAED,WACC,iBAED,WACC,iBAED,WACC,mBAED,WACC,uBAED,WACC,uBAED,WACC,yBAED,WACC,wBAED,WACC,0BAED,WACC,iBAED,WACC,mBAED,WACC,kBAED,WACC,kBAED,WACC,mBAED,WACC,uBAED,WACC,mBAED,WACC,iBAED,WACC,iBAED,WACC,mBAED,WACC,0BAED,WACC,qBAED,WACC,iBAED,WACC,oBAED,WACC,yBAED,WACC,kBAED,WACC,yBAED,WACC,0BAED,WACC,wBAED,WACC,yBAED,WACC,yBAED,WACC,yBAED,WACC,4BAED,WACC,wBAED,WACC,uBAED,WACC,gBAED,WACC,uBAED,WACC,wBAED,WACC,qBAED,WACC,uBAED,WACC,kBAED,WACC,mBAED,WACC,qBAED,WACC,iBAED,WACC,kBAED,WACC,qBAED,WACC,+BAED,WACC,iBAED,WACC,iBAED,WACC,iBAED,WACC,gBAED,WACC,sBAED,WACC,iCAED,WACC,kBAED,WACC,yBAED,WACC,mBAED,WACC,oBAED,WACC,mBAED,WACC,uBAED,WACC,yBAED,WACC,0BAED,WACC,mBAED,WACC,wBAED,WACC,qBAED,WACC,qBAED,WACC,sBAED,WACC,yBAED,WACC,gBAED,WACC,iBAED,WACC,qBAED,WACC,qBAED,WACC,sBAED,WACC,0BAED,WACC,mBAED,WACC,kBAED,WACC,yBAED,WACC,wBAED,WACC,gBAED,WACC,gBAED,WACC,qBAED,WACC,wBAED,WACC,6BAED,WACC,4BAED,WACC,0BAED,WACC,4BAED,WACC,8BAED,WACC,+BAED,WACC,4BAED,WACC,8BAED,WACC,kBAED,WACC,mBAED,WACC,kBAED,WACC,mBAED,WACC,sBAED,WACC,uBAED,WACC,kBAED,WACC,iBAED,WACC,kBAED,WACC,kBAED,WACC,gBAED,WACC,iBAED,WACC,sBAED,WACC,iBAED,WACC,mBAED,WACC,iBAED,WACC,oBAED,WACC,oBAED,WACC,kBAED,WACC,kBAED,WACC,kBAED,WACC,uBAED,WACC,uBAED,WACC,qBAED,WACC,uBAED,WACC,wBAED,WACC,oBAED,WACC,sBAED,WACC,qBAED,WACC,iBAED,WACC,kBAED,WACC,uBAED,WACC,iBAED,WACC,oBAED,WACC,qBAED,WACC,kBAED,WACC,sBAED,WACC,qBAED,WACC,2BAED,WACC,yBAED,WACC,oBAED,WACC,wBAED,WACC,iBAED,WACC,mBAED,WACC,qBAED,WACC,sBAED,WACC,mBAED,WACC,wBAED,WACC,iBAED,WACC,qBAED,WACC,wBAED,WACC,8BAED,WACC,+BAED,WACC,8BAED,WACC,uBAED,WACC,wBAED,WACC,qBAED,WACC,uBAED,WACC,oBAED,WACC,mBAED,WACC,mBAED,WACC,uBAED,WACC,wBAED,WACC,oBAED,WACC,mBAED,WACC,kBAED,WACC,kBAED,WACC,gBAED,WACC,oBAED,WACC,qBAED,WACC,2BAED,WACC,qBAED,WACC,iBAED,WACC,2BAED,WACC,iBAED,WACC,wBAED,WACC,iBAED,WACC,wBAED,WACC,mBAED,WACC,yBAED,WACC,uBAED,WACC,6BAED,WACC,mBAED,WACC,qBAED,WACC,8BAED,WACC,mBAED,WACC,gCAED,WACC,iCAED,WACC,8BAED,WACC,gCAED,WACC,iBAED,WACC,mBAED,WACC,uBAED,WACC,qBAED,WACC,uBAED,WACC,uBAED,WACC,wBAED,WACC,mBAED,WACC,yBAED,WACC,qBAED,WACC,uBAED,WACC,yBAED,WACC,iCAED,WACC,yBAED,WACC,oBAED,WACC,8BAED,WACC,4BAED,WACC,+BAED,WACC,sBAED,WACC,uBAED,WACC,wBAED,WACC,uBAED,WACC,qBAED,WACC,uBAED,WACC,iBAED,WACC,qBAED,WACC,8BAED,WACC,sBAED,WACC,wBAED,WACC,gBAED,WACC,sBAED,WACC,4BAED,WACC,0BAED,WACC,4BAED,WACC,6BAED,WACC,oBAED,WACC,mBAED,WACC,iBAED,WACC,gBAED,WACC,iBAED,WACC,oBAED,WACC,gBAED,WACC,sBAED,WACC,8BAED,WACC,uBAED,WACC,uBAED,WACC,sBAED,WACC,0BAED,WACC,4BAED,WACC,mBAED,WACC,uBAED,WACC,2BAED,WACC,mBAED,WACC,wBAED,WACC,mBAED,WACC,qBAED,WACC,gBAED,WACC,qBAED,WACC,kBAED,WACC,gBAED,WACC,iBAED,WACC,kBAED,WACC,oBAED,WACC,2BAED,WACC,oBAED,WACC,gBAED,WACC,iBAED,WACC,iBAED,WACC,uBAED,WACC,qBAED,WACC,qBAED,WACC,sBAED,WACC,uBAED,WACC,4BAED,WACC,uBAED,WACC,yBAED,WACC,uBAED,WACC,uBAED,WACC,sBAED,WACC,iBAED,WACC,oBAED,WACC,sBAED,WACC,yBAED,WACC,kBAED,WACC,eAED,WACC,wBAED,WACC,oBAED,WACC,oBAED,WACC,sBAED,WACC,sBAED,WACC,iBAED,WACC,mBAED,WACC,uBAED,WACC,iBAED,WACC,uBAED,WACC,iBAED,WACC,uBAED,WACC,oBAED,WACC,0BAED,WACC,wBAED,WACC,oBAED,WACC,sBAED,WACC,sBAED,WACC,uBAED,WACC,kBAED,WACC,sBAED,WACC,eAED,WACC,wBAED,WACC,wBAED,WACC,0BAED,WACC,uBAED,WACC,sBAED,WACC,uBAED,WACC,uBAED,WACC,sBAED,WACC,oBAED,WACC,gBAED,WACC,sBAED,WACC,8BAED,WACC,uBAED,WACC,2BAED,WACC,qBAED,WACC,sBAED,WACC,4BAED,WACC,oBAED,WACC,iBAED,WACC,uBAED,WACC,sBAED,WACC,iBAED,WACC,oBAED,WACC,wBAED,WACC,wBAED,WACC,0BAED,WACC,0BAED,WACC,uBAED,WACC,qBAED,WACC,mBAED,WACC,sBAED,WACC,uBAED,WACC,gBAED,WACC,kBAED,WACC,yBAED,WACC,mCAED,WACC,yBAED,WACC,4BAED,WACC,0BAED,WACC,0BAED,WACC,qBAED,WACC,yBAED,WACC,wBAED,WACC,mBAED,WACC,2BAED,WACC,kBAED,WACC,0BAED,WACC,4BAED,WACC,2BAED,WACC,0BAED,WACC,sBAED,WACC,sBAED,WACC,uBAED,WACC,0BAED,WACC,wBAED,WACC,uBAED,WACC,yBAED,WACC,uBAED,WACC,sBAED,WACC,uBAED,WACC,6BAED,WACC,sBAED,WACC,mBAED,WACC,eAED,WACC,mBAED,WACC,kBAED,WACC,mBAED,WACC,0BAED,WACC,2BAED,WACC,2BAED,WACC,2BAED,WACC,qBAED,WACC,oBAED,WACC,sBAED,WACC,wBAED,WACC,sBAED,WACC,qBAED,WACC,yBAED,WACC,wBAED,WACC,oBAED,WACC,sBAED,WACC,wBAED,WACC,6BAED,WACC,kBAED,WACC,8BAED,WACC,oBAED,WACC,wCAED,WACC,gDAED,WACC,iBAED,WACC,uBAED,WACC,sBAED,WACC,0BAED,WACC,yBAED,WACC,yBAED,WACC,wBAED,WACC,qBAED,WACC,oBAED,WACC,6BAED,WACC,mBAED,WACC,oBAED,WACC,2BAED,WACC,sBAED,WACC,sBAED,WACC,kBAED,WACC,yBAED,WACC,mBAED,WACC,qBAED,WACC,sBAED,WACC,qBAED,WACC,sBAED,WACC,kBAED,WACC,yBAED,WACC,wBAED,WACC,oBAED,WACC,oBAED,WACC,gCAED,WACC,+BAED,WACC,gBAED,WACC,mBAED,WACC,6BAED,WACC,oBAED,WACC,0BAED,WACC,0BAED,WACC,gCAED,WACC,gCAED,WACC,iCAED,WACC,8BAED,WACC,qBAED,WACC,2BAED,WACC,wBAED,WACC,qBAED,WACC,sBAED,WACC,oBAED,WACC,eAED,WACC,eAED,WACC,eAED,WACC,uBAED,WACC,uBAED,WACC,uBAED,WACC,sBAED,WACC,6BAED,WACC,sBAED,WACC,0BAED,WACC,8BAED,WACC,wBAED,WACC,+BAED,WACC,gCAED,WACC,gCAED,WACC,iCAED,WACC,8BAED,WACC,0BAED,WACC,iBAED,WACC,sBAED,WACC,gCAED,WACC,gCAED,WACC,iCAED,WACC,8BAED,WACC,8BAED,WACC,8BAED,WACC,+BAED,WACC,4BAED,WACC,uBAED,WACC,iCAED,WACC,kBAED,WACC,wBAED,WACC,yBAED,WACC,yBAED,WACC,8BAED,WACC,8BAED,WACC,+BAED,WACC,4BAED,WACC,4BAED,WACC,0BAED,WACC,2BAED,WACC,yBAED,WACC,8BAED,WACC,4BAED,WACC,6BAED,WACC,2BAED,WACC,kCAED,WACC,gCAED,WACC,iCAED,WACC,+BAED,WACC,gCAED,WACC,8BAED,WACC,+BAED,WACC,6BAED,WACC,wBAED,WACC,kCAED,WACC,kCAED,WACC,mCAED,WACC,iCAED,WACC,2BAED,WACC,2BAED,WACC,4BAED,WACC,yBAED,WACC,kCAED,WACC,kCAED,WACC,mCAED,WACC,gCAED,WACC,8BAED,WACC,qCAED,WACC,yBAED,WACC,mBAED,WACC,4BAED,WACC,qBAED,WACC,2BAED,WACC,2BAED,WACC,oBAED,WACC,mBAED,WACC,sBAED,WACC,0BAED,WACC,uBAED,WACC,oBAED,WACC,gBAED,WACC,yBAED,WACC,0BAED,WACC,oBAED,WACC,iBAED,WACC,uBAED,WACC,oBAED,WACC,sBAED,WACC,oBAED,WACC,6BAED,WACC,uBAED,WACC,+BAED,WACC,6BAED,WACC,uBAED,WACC,uBAED,WACC,yBAED,WACC,wBAED,WACC,uBAED,WACC,mBAED,WACC,8BAED,WACC,8BAED,WACC,qBAED,WACC,uBAED,WACC,oBAED,WACC,oBAED,WACC,wBAED,WACC,2BAED,WACC,qBAED,WACC,gBAED,WACC,yBAED,WACC,2BAED,WACC,yBAED,WACC,sBAED,WACC,0BAED,WACC,2BAED,WACC,2BAED,WACC,uBAED,WACC,2BAED,WACC,+BAED,WACC,2BAED,WACC,qBAED,WACC,kBAED,WACC,sBAED,WACC,6BAED,WACC,qBAED,WACC,uBAED,WACC,sBAED,WACC,yBAED,WACC,yBAED,WACC,oBAED,WACC,yBAED,WACC,uBAED,WACC,uBAED,WACC,0BAED,WACC,qBAED,WACC,0BAED,WACC,4BAED,WACC,4BAED,WACC,yBAED,WACC,yBAED,WACC,yBAED,WACC,kBAED,WACC,yBAED,WACC,6BAED,WACC,wBAED,WACC,wBAED,WACC,4BAED,WACC,uBAED,WACC,2BAED,WACC,yBAED,WACC,6BAED,WACC,uBAED,WACC,2BAED,WACC,wBAED,WACC,4BAED,WACC,uBAED,WACC,2BAED,WACC,oBAED,WACC,oBAED,WACC,qBAED,WACC,yBAED,WACC,0BAED,WACC,4BAED,WACC,sBAED,WACC,sBAED,WACC,wBAED,WACC,0BAED,WACC,qBAED,WACC,oBAED,WACC,wBAED,WACC,wBAED,WACC,yBAED,WACC,wBAED,WACC,oBAED,WACC,sBAED,WACC,qBAED,WACC,wBAED,WACC,yBAED,WACC,yBAED,WACC,gBAED,WACC,sBAED,WACC,kBAED,WACC,8BAED,WACC,iBAED,WACC,qBAED,WACC,4BAED,WACC,2BAED,WACC,0BAED,WACC,8BAED,WACC,sBAED,WACC,gBAED,WACC,kBAED,WACC,wBAED,WACC,0BAED,WACC,8BAED,WACC,gCAED,WACC,yBAED,WACC,6BAED,WACC,sBAED,WACC,qBAED,WACC,6BAED,WACC,2BAED,WACC,yBAED,WACC,4BAED,WACC,wBAED,WACC,sBAED,WACC,kBAED,WACC,oBAED,WACC,oBAED,WACC,oBAED,WACC,sBAED,WACC,mBAED,WACC,kBAED,WACC,oBAED,WACC,uBAED,WACC,oBAED,WACC,wBAED,WACC,qBAED,WACC,sBAED,WACC,qBAED,WACC,mBAED,WACC,oBAED,WACC,sBAED,WACC,8BAED,WACC,6BAED,WACC,6BAED,WACC,oCAED,WACC,8BAED,WACC,8BAED,WACC,6BAED,WACC,8BAED,WACC,8BAED,WACC,0BAED,WACC,yBAED,WACC,yBAED,WACC,gCAED,WACC,0BAED,WACC,0BAED,WACC,yBAED,WACC,0BAED,WACC,0BAED,WACC,0BAED,WACC,yBAED,WACC,8BAED,WACC,kBAED,WACC,mBAED,WACC,iBAED,WACC,oBAED,WACC,uBAED,WACC,yBAED,WACC,+BAED,WACC,kCAED,WACC,6BAED,WACC,+BAED,WACC,kBAED,WACC,wBAED,WACC,0BAED,WACC,sBAED,WACC,0BAED,WACC,yBAED,WACC,yBAED,WACC,uBAED,WACC,iBAED,WACC,uBAED,WACC,0BAED,WACC,uBAED,WACC,mBAED,WACC,kBAED,WACC,qBAED,WACC,gCAED,WACC,gCAED,WACC,mCAED,WACC,gCAED,WACC,gCAED,WACC,oCAED,WACC,mCAED,WACC,sCAED,WACC,2CAED,WACC,mCAED,WACC,qBAED,WACC,yBAED,WACC,uBAED,WACC,qBAED,WACC,+BAED,WACC,gCAED,WACC,oBAED,WACC,sBAED,WACC,4BAED,WACC,kBAED,WACC,uBAED,WACC,+BAED,WACC,mBAED,WACC,kBAED,WACC,yBAED,WACC,iBAED,WACC,kBAED,WACC,iBAED,WACC,sBAED,WACC,sBAED,WACC,qBAED,WACC,qBAED,WACC,uBAED,WACC,qBAED,WACC,mBAED,WACC,wBAED,WACC,sBAED,WACC,mBAED,WACC,oBAED,WACC,iBAED,WACC,qBAED,WACC,oBAED,WACC,yBAED,WACC,+BAED,WACC,uBAED,WACC,qBAED,WACC,sBAED,WACC,sBAED,WACC,4BAED,WACC,mBAED,WACC,iCAED,WACC,4BAED,WACC,gCAED,WACC,wBAED,WACC,4BAED,WACC,sBAED,WACC,oBAED,WACC,uBAED,WACC,4BAED,WACC,qBAED,WACC,kBAED,WACC,kBAED,WACC,mBAED,WACC,wBAED,WACC,wBAED,WACC,kBAED,WACC,kBAED,WACC,oBAED,WACC,mBAED,WACC,mBAED,WACC,kBAED,WACC,oBAED,WACC,kBAED,WACC,kBAED,WACC,sBAED,WACC,yBAED,WACC,iBAED,WACC,kBAED,WACC,qBAED,WACC,yBAED,WACC,qBAED,WACC,2BAED,WACC,mBAED,WACC,wBAED,WACC,qBAED,WACC,+BAED,WACC,kBAED,WACC,6BAED,WACC,iBAED,WACC,0BAED,WACC,wBAED,WACC,0BAED,WACC,0BAED,WACC,wBAED,WACC,wBAED,WACC,yBAED,WACC,gCAED,WACC,8BAED,WACC,6BAED,WACC,wBAED,WACC,iBAED,WACC,sBAED,WACC,wBAED,WACC,iBAED,WACC,oBAED,WACC,uBAED,WACC,8BAED,WACC,yBAED,WACC,2BAED,WACC,uBAED,WACC,oBAED,WACC,iBAED,WACC,qBAED,WACC,sBAED,WACC,4BAED,WACC,wBAED,WACC,wBAED,WACC,8BAED,WACC,uBAED,WACC,uBAED,WACC,wBAED,WACC,+BAED,WACC,6BAED,WACC,sBAED,WACC,4BAED,WACC,0BAED,WACC,2BAED,WACC,wBAED,WACC,oBAED,WACC,kBAED,WACC,kBAED,WACC,iBAED,WACC,sBAED,WACC,4BAED,WACC,kBAED,WACC,uBAED,WACC,yBAED,WACC,uBAED,WACC,yBAED,WACC,uBAED,WACC,2BAED,WACC,mBAED,WACC,kBAED,WACC,sBAED,WACC,6BAED,WACC,qBAED,WACC,0BAED,WACC,yBAED,WACC,qBAED,WACC,yBAED,WACC,wBAED,WACC,sBAED,WACC,wBAED,WACC,sBAED,WACC,kBAED,WACC,oBAED,WACC,kBAED,WACC,2BAED,WACC,0BAED,WACC,sBAED,WACC,iBAED,WACC,qBAED,WACC,qBAED,WACC,wBAED,WACC,8BAED,WACC,iBAED,WACC,uBAED,WACC,qBAED,WACC,wBAED,WACC,kBAED,WACC,kBAED,WACC,uBAED,WACC,oBAED,WACC,wBAED,WACC,qBAED,WACC,sBAED,WACC,uBAED,WACC,yBAED,WACC,qBAED,WACC,qBAED,WACC,qBAED,WACC,6BAED,WACC,mCAED,WACC,0BAED,WACC,oBAED,WACC,uBAED,WACC,yBAED,WACC,wBAED,WACC,yBAED,WACC,wBAED,WACC,gBAED,WACC,2BAED,WACC,2BAED,WACC,wBAED,WACC,wBAED,WACC,uBAED,WACC,oBAED,WACC,oBAED,WACC,wBAED,WACC,wBAED,WACC,wBAED,WACC,uBAED,WACC,iBAED,WACC,qBAED,WACC,2BAED,WACC,kBAED,WACC,iCAED,WACC,6BAED,WACC,4BAED,WACC,mCAED,WACC,6BAED,WACC,gCAED,WACC,6BAED,WACC,6BAED,WACC,6BAED,WACC,uBAED,WACC,+BAED,WACC,oBAED,WACC,2BAED,WACC,0BAED,WACC,mBAED,WACC,2BAED,WACC,oBAED,WACC,uBAED,WACC,iBAED,WACC,yBAED,WACC,0BAED,WACC,wBAED,WACC,mBAED,WACC,eAED,WACC,mBAED,WACC,sBAED,WACC,iBAED,WACC,yBAED,WACC,0BAED,WACC,kBAED,WACC,2BAED,WACC,6BAED,WACC,0BAED,WACC,2BAED,WACC,2BAED,WACC,4BAED,WACC,0BAED,WACC,iBAED,WACC,2BAED,WACC,gCAED,WACC,4BAED,WACC,kBAED,WACC,yBAED,WACC,sBAED,WACC,iCAED,WACC,+BAED,WACC,4BAED,WACC,2BAED,WACC,6BAED,WACC,sBAED,WACC,yBAED,WACC,wBAED,WACC,yBAED,WACC,oBAED,WACC,qBAED,WACC,0BAED,WACC,sBAED,WACC,oBAED,WACC,kBAED,WACC,kBAED,WACC,qBAED,WACC,yBAED,WACC,iBAED,WACC,2BAED,WACC,kBAED,WACC,oBAED,WACC,mBAED,WACC,mBAED,WACC,qBAED,WACC,6BAED,WACC,kCAED,WACC,yBAED,WACC,4BAED,WACC,sBAED,WACC,oBAED,WACC,uBAED,WACC,mBAED,WACC,eAED,WACC,kBAED,WACC,2BAED,WACC,kBAED,WACC,eAED,WACC,0BAED,WACC,yBAED,WACC,kBAED,WACC,kBAED,WACC,yBAED,WACC,kBAED,WACC,wBAED,WACC,uBAED,WACC,mBAED,WACC,uBAED,WACC,qBAED,WACC,kBAED,WACC,sCAED,WACC,oBAED,WACC,wBAED,WACC,gBAED,WACC,uBAED,WACC,yBAED,WACC,gBAED,WACC,0BAED,WACC,sBAED,WACC,wBAED,WACC,sBAED,WACC,qBAED,WACC,uBAED,WACC,0BAED,WACC,uBAED,WACC,gBAED,WACC,qBAED,WACC,kBAED,WACC,yBAED,WACC,uBAED,WACC,uBAED,WACC,sBAED,WACC,wBAED,WACC,mBAED,WACC,iBAED,WACC,gBAED,WACC,0BAED,WACC,2BAED,WACC,mBAED,WACC,qBAED,WACC,qBAED,WACC,qBAED,WACC,oBAED,WACC,oBAED,WACC,oBAED,WACC,gBAED,WACC,wBAED,WACC,mBAED,WACC,sBAED,WACC,2BAED,WACC,iBAED,WACC,oBAED,WACC,qBAED,WACC,qBAED,WACC,qBAED,WACC,wBAED,WACC,kBAED,WACC,yBAED,WACC,yBAED,WACC,kBAED,WACC,mBAED,WACC,uBAED,WACC,+BAED,WACC,qBAED,WACC,sBAED,WACC,uBAED,WACC,sBAED,WACC,oBAED,WACC,0BAED,WACC,mBAED,WACC,kBAED,WACC,wBAED,WACC,uBAED,WACC,kBAED,WACC,yBAED,WACC,oBAED,WACC,yBAED,WACC,iBAED,WACC,0BAED,WACC,uBAED,WACC,qBAED,WACC,iBAED,WACC,qBAED,WACC,iBAED,WACC,mBAED,WACC,qBAED,WACC,sBAED,WACC,0BAED,WACC,kBAED,WACC,gBAED,WACC,oBAED,WACC,mBAED,WACC,gBAED,WACC,oBAED,WACC,sBAED,WACC,mBAED,WACC,mBAED,WACC,kBAED,WACC,kBAED,WACC,mBAED,WACC,uBAED,WACC,qBAED,WACC,kBAED,WACC,kBAED,WACC,yBAED,WACC,sBAED,WACC,mBAED,WACC,yBAED,WACC,kBAED,WACC,kBAED,WACC,mBAED,WACC,yBAED,WACC,0BAED,WACC,uBAED,WACC,0BAED,WACC,iCAED,WACC,0BAED,WACC,uBAED,WACC,6BAED,WACC,2BAED,WACC,4BAED,WACC,uBAED,WACC,0BAED,WACC,0BAED,WACC,gCAED,WACC,wBAED,WACC,uBAED,WACC,2BAED,WACC,mBAED,WACC,wBAED,WACC,uBAED,WACC,qBAED,WACC,qBAED,WACC,oBAED,WACC,wBAED,WACC,uBAED,WACC,qBAED,WACC,qBAED,WACC,gBAED,WACC,wBAED,WACC,qBAED,WACC,sBAED,WACC,yBAED,WACC,mBAED,WACC,uBAED,WACC,uBAED,WACC,wBAED,WACC,sBAED,WACC,iBAED,WACC,yBAED,WACC,oBAED,WACC,kBAED,WACC,kBAED,WACC,uBAED,WACC,sBAED,WACC,iBAED,WACC,0BAED,WACC,sBAED,WACC,kBAED,WACC,sBAED,WACC,iBAED,WACC,iBAED,WACC,yBAED,WACC,0BAED,WACC,gCAED,WACC,uBAED,WACC,mBAED,WACC,0BAED,WACC,mBAED,WACC,0BAED,WACC,oBAED,WACC,gCAED,WACC,uBAED,WACC,iBAED,WACC,yBAED,WACC,qBAED,WACC,oBAED,WACC,qBAED,WACC,0BAED,WACC,sBAED,WACC,qBAED,WACC,sBAED,WACC,wBAED,WACC,kBAED,WACC,4BAED,WACC,4BAED,WACC,yBAED,WACC,0BAED,WACC,gCAED,WACC,yBAED,WACC,uBAED,WACC,uBAED,WACC,gCAED,WACC,mBAED,WACC,sBAED,WACC,uBAED,WACC,yBAED,WACC,wBAED,WACC,uBAED,WACC,sBAED,WACC,oBAED,WACC,kBAED,WACC,2BAED,WACC,sBAED,WACC,mBAED,WACC,oBAED,WACC,6BAED,WACC,qBAED,WACC,0BAED,WACC,qBAED,WACC,sBAED,WACC,kBAED,WACC,oBAED,WACC,qBAED,WACC,mBAED,WACC,gBAED,WACC,oBAED,WACC,mBAED,WACC,kBAED,WACC,qBAED,WACC,kBAED,WACC,yBAED,WACC,sBAED,WACC,0BAED,WACC,qBAED,WACC,2BAED,WACC,uBAED,WACC,wBAED,WACC,oBAED,WACC,0BAED,WACC,mBAED,WACC,yBAED,WACC,yBAED,WACC,2BAED,WACC,2BAED,WACC,gCAED,WACC,4BAED,WACC,sBAED,WACC,mBAED,WACC,qBAED,WACC,kBAED,WACC,oBAED,WACC,gBAED,WACC,sBAED,WACC,0BAED,WACC,wBAED,WACC,mBAED,WACC,4BAED,WACC,yBAED,WACC,wBAED,WACC,yBAED,WACC,kBAED,WACC,sBAED,WACC,+BAED,WACC,qBAED,WACC,4BAED,WACC,8BAED,WACC,qBAED,WACC,oBAED,WACC,wBAED,WACC,0BAED,WACC,sBAED,WACC,mBAED,WACC,sBAED,WACC,4BAED,WACC,2BAED,WACC,oBAED,WACC,iBAED,WACC,kBAED,WACC,uBAED,WACC,wBAED,WACC,oBAED,WACC,0BAED,WACC,wBAED,WACC,kBAED,WACC,qBAED,WACC,oBAED,WACC,iBAED,WACC,kBAED,WACC,sBAED,WACC,yBAED,WACC,0BAED,WACC,mBAED,WACC,mBAED,WACC,mBAED,WACC,uBAED,WACC,sBAED,WACC,uBAED,WACC,8BAED,WACC,uBAED,WACC,yBAED,WACC,wBAED,WACC,0BAED,WACC,6BAED,WACC,sBAED,WACC,mBAED,WACC,4BAED,WACC,uBAED,WACC,0BAED,WACC,yBAED,WACC,wBAED,WACC,wBAED,WACC,yBAED,WACC,yBAED,WACC,yBAED,WACC,6BAED,WACC,uBAED,WACC,0BAED,WACC,wBAED,WACC,wBAED,WACC,yBAED,WACC,oBAED,WACC,iBAED,WACC,iBAED,WACC,yBAED,WACC,oBAED,WACC,kBAED,WACC,sBAED,WACC,kBAED,WACC,sBAED,WACC,gBAED,WACC,iBAED,WACC,wBAED,WACC,sBAED,WACC,uBAED,WACC,2BAED,WACC,yBAED,WACC,eAED,WACC,qBAED,WACC,4BAED,WACC,kBAED,WACC,oBAED,WACC,yBAED,WACC,wBAED,WACC,6BAED,WACC,iCAED,WACC,gBAED,WACC,sBAED,WACC,mBAED,WACC,qBAED,WACC,iCAED,WACC,2BAED,WACC,qBAED,WACC,iBAED,WACC,sBAED,WACC,sBAED,WACC,mBAED,WACC,yBAED,WACC,4BAED,WACC,4BAED,WACC,0BAED,WACC,kBAED,WACC,6BAED,WACC,uBAED,WACC,0BAED,WACC,kBAED,WACC,oBAED,WACC,oBAED,WACC,wBAED,WACC,mBAED,WACC,sBAED,WACC,yBAED,WACC,4BAED,WACC,0BAED,WACC,iBAED,WACC,qBAED,WACC,wBAED,WACC,8BAED,WACC,0BAED,WACC,6BAED,WACC,6BAED,WACC,oBAED,WACC,yBAED,WACC,uBAED,WACC,uBAED,WACC,0BAED,WACC,uBAED,WACC,kBAED,WACC,2BAED,WACC,kCAED,WACC,wBAED,WACC,uBAED,WACC,4BAED,WACC,oBAED,WACC,uBAED,WACC,4BAED,WACC,iCAED,WACC,yBAED,WACC,2BAED,WACC,qBAED,WACC,gBAED,WACC,kBAED,WACC,kBAED,WACC,uBAED,WACC,6BAED,WACC,kBAED,WACC,sBAED,WACC,uBAED,WACC,wBAED,WACC,2BAED,WACC,sBAED,WACC,sBAED,WACC,mBAED,WACC,sBAED,WACC,qBAED,WACC,oBAED,WACC,sBAED,WACC,0BAED,WACC,qBAED,WACC,0BAED,WACC,4BAED,WACC,kBAED,WACC,0BAED,WACC,oBAED,WACC,qBAED,WACC,sBAED,WACC,oBAED,WACC,mBAED,WACC,0BAED,WACC,wBAED,WACC,oBAED,WACC,gBCjlKA,sBADD,oBAEC,kBAOA,eACA,CADA,6BACA,CAHA,iBACA,CAEA,oFACG,CALH,eACA,CAFA,YACA,CAEA,iBACA,CAEG,WAPJ,WAQC,yBACA,gCACC,qCAGA,kBACA,CACA,WAHD,YACC,CACA,cAEA,0CACA,gBACC,qBAkBF,kBACA,wBAHA,YACA,CAFA,UACA,CACA,sBACA,CALA,UACA,CAHA,iBACA,UACA,CAJD,8BACC,CAIA,UACA,CALA,SAUA,4BAGA,YADD,UAEC,yBAED,WACC,qBAGD,wBACC,CADD,uCACC,YACA,mBACA,WACA,CADA,wBACA,eACA,eACA,CACA,YADA,YAEA,uBAGC,yBADD,iBAEC,uCAID,gEACC,oCAID,qBACC,mBACA,mBAOD,gBACA,CAEA,wBAEA,wBAHA,kBACA,CAFA,UACA,CADA,wBACA,CAJA,cACA,eACA,CAHD,iBASC,kCACA,gEACC,gCAID,eACA,CADA,6BACA,CACA,wBACA,CAFA,aACA,CACA,iBAGA,yDAPD,iBACC,CAIA,OACA,SAMA,6BAMA,wBACA,YACA,kBACA,WACA,CADA,wBACA,eACA,eACA,iBACA,CAXD,iBACC,YACA,CACA,QACA,CAOA,UATA,UAUA,2BAGA,YACA,UAFD,gBAGC,2BAKA,wBACA,YACA,mBACA,WACA,CADA,wBACA,eACA,eACA,CAGA,WACA,mBAbD,iBACC,WACA,CAQA,SACA,YACA,CAVA,UAYA,0BAaA,yBACA,CADA,uCACA,CACA,kBADA,yBACA,CAHA,uBACA,CANA,WACA,CAEA,cACA,yBACA,CAHA,WACA,CAFA,SACA,CAHA,qBACA,CAJD,WAaC,yBAKA,YADA,iBACA,CAFD,SAGC,wBAGA,iBACA,aAFD,OAGC,iCAGA,kBADD,UAEC,yBAGD,oBACC,CADD,qCACC,kBA0BA,oDAxBA,yDACC,wDAIA,WADD,eAEC,CAIA,4FAGD,8EAEC,4DAIA,kBACC,SASF,oBACA,CAHD,wBACC,CAEA,kBACA,CAHA,aACA,CAMA,oBACA,iBAFA,kBACA,CAFA,YACA,CAHA,WACA,gBAeA,CAXA,QAIA,oBACA,CAHD,wBACC,CAEA,kBACA,CAHA,aACA,CAMA,oBACA,SAIA,oBACA,CAHD,wBACC,CAEA,kBACA,CAHA,aACA,CAMA,oBACA,iBAFA,kBACA,CAFA,YACA,CAHA,WACA,gBAeA,CAXA,QAIA,kBACA,CAHD,wBACC,CADD,uCACC,CAEA,kBACA,CAHA,aACA,CADA,4BACA,CAMA,oBACA,2BAED,kBACC,CADD,sCACC,CACA,8BACA,CADA,+CACA,CACA,6BADA,qBACA,CAHA,wBAIA,wDAED,8EAGC,8GACA,4CACC,+IAED,oCACC,sRAED,sBACC,wMAGA,mBACC,mBACA,CAID,0TAED,eACC,sIAGD,kBACC,CADD,sCACC,CACA,8BACA,CADA,+CACA,CAFA,UACA,CACA,qBACA,8GAGD,8EACC,iKAKA,kBACC,8MAED,wEACC,yNAkBD,wBADA,uBACA,CALD,mBAMC,6GAMA,wBADA,uBACA,CAHD,mBAIC,yNAOA,wBADA,uBACA,CALD,mBAMC,qCAID,eACC,CADD,6BACC,CACA,iBACA,+BAFA,cAGA,iDAKC,kBACA,CAFA,YACA,CACA,8BALD,cAEC,aAIC,+EAMC,cACA,CAFD,eACC,CACA,gBACA,qFAED,cACC,iBACA,cACA,mEAOD,mBAFA,YACA,SACA,CAJD,gBACK,WAIJ,0HAIE,mBADF,YAEE,gJAOE,kBACA,CANF,kBACE,CACA,iBACA,eACA,aACA,CACA,uBALA,iBAMA,oJAGE,YADF,UAEE,qJAKF,uCACA,iBAFF,iBAGE,iKAQE,kBACA,CACA,kBACA,CACA,6BACA,CAFA,0BACA,CAPA,QACA,CAOA,eANA,YACA,CACA,sBACA,CAPA,MACA,CAFF,iBACE,CACA,KACA,CACA,UAQA,qKAGE,YADF,UAEE,wKAMF,qBACA,kBACA,CACA,eADA,YACA,CAJA,2BACA,CAFF,UAME,iJASJ,yCACE,wJAGF,kCACE,oIAQF,6BADF,kCAEE,wBAKN,GACE,OACE,IAEF,WACE,yDAiBD,mBADD,mBAEC,uEAGC,mBAFD,YACC,kBAEA,yFAEC,UACA,WAFD,WAGC,iHACA,aACC,gHAIA,kBACA,aAFD,eAGC,6JAEA,qBACC,0HAIF,WACC,6GAED,UACC,yBACA,gGAED,mBACC,wHAKD,sCACC,gFAMF,wBACA,CADA,oCACA,mBACA,CACA,YAJA,aACA,CAFD,iBACC,CAGA,SAEA,wEAIA,YACA,CAFA,WACA,CAGA,mBADA,sBACA,CALD,iBACC,CAEA,UAGA,qFAEC,gBACA,qBAFD,WAGC,iGACA,mBACC,6GAED,WAEC,sHAED,4BACC,sBACA,6GAGA,YADD,kBAEC,gHAED,qBACC,qGAED,QACC,kIAED,iBACC,gGAGF,kBACC,mBAOL,iBACC,QACA,SACA,6CAcA,yBACA,CADA,uCACA,CAXD,kBAEC,CAUA,kBADA,yBACA,CAHA,uBACA,CARA,kBACA,CACA,gBACA,iBACA,CAEA,wBACA,CAFA,WACA,CAFA,QACA,CAJA,qBAUA,+EAEC,4EADD,YAEC,yGAEC,cACA,CAFD,eACC,CACA,gBACA,qHAED,cACC,iBACA,mFAMD,cACA,cAHA,8BACA,qBACA,CAHD,oBAKC,iHACA,YACC,yHACA,SACC,qMAGA,WACC,mJAGF,gBACC,+GAGF,eACC,iFAID,mBADD,UAEC,6IAGC,0DACC,mHAGF,WACC,WACA,uFAID,OACA,CAFD,WACC,CACA,aACA,uHAGC,kCACA,CADA,iDACA,CAEA,kBACA,CALA,aACA,CADA,4BACA,CACA,cACA,CAJD,eACC,CAKA,0BAFA,kBAGA,CAGC,4OAIF,wBACC,CADD,uCACC,oCACA,qFAMD,0CAHD,YACC,CACA,cACA,CAFA,UAGA,mGAIC,wBACA,CADA,uCACA,CAFA,wBACA,CADA,uCACA,CAFA,iBACA,CAFD,UACC,CADD,wBACC,CAKA,yEADA,4BACA,CADA,uCACA,CAFA,yBAGA,gBC/pBF,qBACA,CADA,mCACA,kBACA,2BAHD,gBAIC,4BAEA,iBACC,kCAIA,YADA,iBACA,CAFD,OAGC,qCACA,iBACC,WACA,mDAKA,gBADA,UACA,CAFA,iBACA,CAFD,SAIC,oDAKA,gBADA,SACA,CAFA,iBACA,CAFD,SAIC,CAkBD,qCAIA,WACA,CACA,4BAHD,UACC,CACA,WAEA,4CAEA,iBACC,8DAID,aACC,oEAGA,sCACC,6DAKD,mBADD,SAEC,0DAGD,UACC,eACA,oCAKF,kBADD,UAEC,mDAWA,yBACA,CADA,uCACA,CACA,kBADA,yBACA,CANA,gBACA,CAEA,wBACA,CAFA,SACA,CAFA,SACA,CAHA,qBACA,CAHD,UAUC,qFAEC,4EADD,YAEC,+GACA,cACC,gBACA,uGAKD,cACA,gBACA,oBAHD,iBAIC,+HACA,sCACC,yIAED,UACC,yLAEA,aACC,CADD,wBACC,iBACA,2NAED,2BACC,6DAIH,yBACC,iGAGA,4BACA,CADA,wCACA,CAFD,YACC,CACA,cACA,iIACA,wBACC,CADD,uCACC,WACA,CADA,wBACA,CAEA,yEADA,4BACA,CADA,uCACA,CAFA,yBAGA,iJACA,wBACC,oBACA,6CAWH,yBACA,CADA,uCACA,CACA,kBADA,yBACA,CAJA,wBAEA,CAHA,qBACA,CAHD,SAQC,qFAEA,gBACC,CACA,YADA,SAEA,yFAIA,UACA,CAEA,aADA,SACA,CAFA,SACA,CAHD,qBAKC,+EAGA,4EADD,YAEC,yGACA,cACC,gBACA,aACA,6EAIF,cACC,2BACA,2EAGD,wBACC,cACA,oBACA,iHACA,sBACC,iMAED,iBACC,qFAQD,sEAHA,QACA,CACA,YACA,CAJD,iBACC,CACA,gBAGA,mGAIC,wBACA,CADA,uCACA,CAFA,iBACA,CACA,aACA,CADA,4BACA,CAEA,yEAND,gBACC,CAIA,4BACA,CADA,uCACA,CAFA,yBAGA,iIAGD,kCACC,CADD,iDACC,wDACA,+JAEA,kCACC,YACA,sBAWH,0BACA,CAFA,WACA,CAHA,MACA,CAIA,oBAPD,cACC,MACA,CACA,UACA,CAEA,SAEA,+BAKA,cACA,CAHD,iBACC,CAEA,UACA,CAHA,QACA,CAEA,UACA,gCAED,YACC,UACA,CAEA,UAFA,iBACA,UAEA,oCAED,iBACC,aACA,SACA,WACA,4BAOA,wBACA,CADA,uCACA,YACA,mBACA,WACA,CADA,wBACA,eACA,eACA,CACA,WACA,CAFA,iBACA,CAXD,iBACC,WACA,CACA,QACA,CAQA,YAVA,UAWA,oBAED,UACC,kBAOA,YACA,uBAND,0BACC,CACA,cACA,CACA,iBACA,CAFA,SACA,CAHA,uBAMA,8BAEA,gBACC,gCAGD,eACC,2BAID,WACA,CAFD,iBACC,CACA,WACA,0BAGA,WACA,CAFD,iBACC,CACA,UACA,mCAGA,WACA,aACA,WAHD,iBAIC,iCAOA,WACA,kBACA,CAGA,iBATA,WAUA,+DAPA,wBACA,CAEA,UACA,CADA,wBACA,eACA,eACA,CAVD,iBACC,CAEA,KACA,CAFA,UAsBA,CAbA,8BAOA,WACA,mBACA,CAGA,kBATA,UAUA,qBAIA,kBACA,CAHD,kBACC,YACA,CACA,wBACA,kDACA,UACC,gBAIF,wBACC,iCACA,sBAEA,uBACC,mBC3VF,+BACC,gBAGD,YACC,gBAGA,cACA,CAFD,eACC,CACA,gBACA,eAIA,QACA,CAHD,iBACC,QACA,CACA,8BACA,UACA,sBAGA,YADD,UAEC,sBAMA,UACA,CAFA,UACA,CAFD,QACC,CAEA,WACA,0BAIA,UACA,CAFA,UACA,CAFD,QACC,CAEA,WACA,oBAMA,UACA,2CAFA,iBACA,CAHA,WACA,CAFD,UACC,CACA,QAUA,CAPA,uBAMA,UACA,SAGD,wBACC,kBAGD,YACC,yBACA,mBACA,gBAGD,eACC,WACA,yBAIA,iBADA,cACA,CAFD,SAGC,kCAGD,iBACC,YACA,yBAGD,kBACC,8CACA,eACC,4BAIF,WACC,oBAMA,kBAFA,WACA,CAFD,UACC,CACA,QAEA,qCAEA,gBACC,oBAIF,YACC,CACA,gBADA,UAEA,4BAEA,wBACC,mBAMD,kBADA,iBACA,CAFD,QAGC,sCAWC,aACA,CAHD,cACC,gBACA,CACA,wBACA,uCASA,uBADA,iBACA,CAFA,iBACA,CAFD,gBAIC,8CAEA,kCACC,qBACA,kEACA,oBACC,6CAIF,0BACC,qCASD,cAND,cACC,gBACA,CACA,mBACA,CAFA,gBACA,CAEA,gBACA,CAFA,eAGA,4CAEA,oBACC,gBASF,kBACA,yBACA,CAGG,kBACA,CARJ,sBACC,sBACA,CAMG,0BAJH,WACA,CACG,qCACA,CAFH,SAIG,kCAEH,WACC,wDAEC,YADD,WAEC,2BAID,gBADD,iBAEC,2CACA,UACC,CACA,cACA,CAFA,eACA,CACA,kBACA,4CAED,cACC,kBACA,4CAIA,kBACA,CAFA,YACA,CAFD,cACC,CAEA,oBACA,gDACA,aACC,8CAED,aACC,CADD,4BACC,kBACA,iCAKF,gBADD,eAEC,qCACA,wBACC,0CAMS,qBACA,CAFA,iBACA,CAFH,cACG,CAEA,kBACH,CACG,aANT,YACM,CAIA,WACG,CAPV,4BAQU,2BAIV,wBACA,CAEA,WACA,mBACA,CAHA,UACA,CAGA,cACA,CAFA,cACA,CAPD,iBACC,CAOA,qCANA,UAOA,6BAGA,gBADC,iBAEC,2CAGA,kBACA,CAHF,YACC,sBACC,CAEA,gBADA,oBAEA,2DAEF,eADD,eAEC,4DAED,cACC,CAEA,eADA,iBACA,CAFA,wBAGA,mBClQF,yDACC,CACA,UACA,CADA,wBACA,CAFA,cACA,CACA,WACA,CAKA,4BALA,gBACA,CACA,iBACA,YAGA,gBAUA,yBACA,CADA,uCACA,2BAFA,uBACA,CANA,WACA,CAEA,cACA,yBACA,CAHA,WACA,CAFA,SACA,CAHA,qBACA,CAFD,aAUC,SAGA,eACA,CACA,wBACA,CAOA,iBAEA,CAXA,aACA,CACA,kBACA,YACA,CAEA,UACA,CAHA,QACA,CAGA,SAMA,qBAHA,cACA,eACA,kBAfD,iBACC,CAMA,iBACA,CACA,UAuBA,CAhBA,YASA,wBACA,YACA,kBACA,WACA,CADA,wBACA,CAVA,YACA,CAEA,UACA,CAHA,QACA,CAGA,QAQA,aAWA,iBACA,aACA,SACA,eAOA,wBACA,YACA,kBACA,WACA,CADA,wBACA,eACA,eACA,kBAVD,iBACC,WACA,CACA,QACA,CAFA,UASA,2DAUC,gBACA,gFAEA,QACC,kBACA,CAKA,gLAED,yBACC,iDAIF,aACC,4BAUA,mBAJD,+BACC,CADD,2CACC,CAEA,YACA,CAHA,cACA,uBAGA,6CACA,wBACC,0DAEC,cACA,CAFD,eACC,CACA,gBACA,8CAGF,gBACC,YACA,2FACA,iBACC,oEAED,WACC,gBAKJ,8BACC,CADD,+CACC,CAEA,qCADA,2BACA,CAFA,QAGA,6CACA,oCACC,qIAED,4BACC,QCnJD,cAND,qBACC,eACA,gBACA,CACA,4BACA,CAFA,mBACA,CACA,iBAEA,cAaA,WATA,cACA,gBACA,CAIA,WACA,CAJA,4BACA,CAFA,gBACA,CAEA,WAKA,qBAXD,mBACC,CAOA,KACA,UACA,CALA,iBAmBA,CAbA,OAYA,WARA,cACA,gBACA,CAGA,WACA,CAJA,gBACA,CACA,UAKA,6CAMA,kBACA,yBACA,CALD,sBAEC,sBACA,CAIA,YADA,wBACA,CAFA,YAGA,iFAEA,WACC,6HAEC,YADD,WAEC,2FAKD,kBACA,CAFD,eACC,CACA,iBACA,mJAGC,UACA,CAFD,4BACC,CAEA,eADA,eAEA,iFAOD,gBAFA,eACA,kBACA,CAHD,UAIC,qHAGC,UACA,CACA,eADA,eACA,CAHD,eAIC,+HACA,cACC,yHAQD,0DAHA,qBACA,CAFD,2BACC,CACA,cACA,WAGA,uHAED,aACC,gBACA,uHAkBD,4BACC,eACA,gBACA,oBACA,mJAED,kBACC,yHAKA,wBACA,CAFA,iBACA,CAFA,WACA,CAEA,gBAJD,WAKC,2KAIC,eADA,gBACA,CAFD,eAGC,qLAIA,kBACA,CAFD,YACC,CAEA,SADA,gBAEA,6LACA,wBACC,uKAIF,aACC,eACA,2KAGD,cACC,iBACA,qFAMF,WACA,CACA,cACA,CAFA,QACA,CAHD,iBACC,CAGA,iBACA,2HAEA,aACC,iKAEA,aACC,CACA,cADA,oBAEA,6KAEA,yBACC,yCAQH,eADD,iBAEC,uCAGD,wBACC,0BACA,yCAIA,8BADD,WAEC,sBC7LD,2BACA,CAFA,0BACA,CAIA,0BADA,yBACA,CAFA,YACA,CAFA,2BACA,CAJD,yBAOC,6CAGC,aADD,kBAEC,mEAEA,cACC,gBACA,4FAIC,aACA,CAHD,cACC,gBACA,CACA,cACA,kBACA,+DAIF,2BACC,WACA,mEAEA,WACC,WACA,qCAKH,0BACC,cACA,sDAEC,cACA,iBAFD,cAGC,mEAEA,eACC,mDAIF,YACC,uBACA,gBACA,gBACA,uFAGC,cACC,CAEA,wBADA,cACA,CAFA,eAGA,4FAGD,UACC,CAEA,UAFA,cACA,gBAEA,6FAGD,aACC,4FAGD,UACC,qDAKH,eACC,8DAEA,YACC,6DAMD,qBACA,kBACA,CAJD,eACC,CAGA,YAHA,iBAIA,kFAEA,+BACC,CACA,yBACA,CAFA,oBACA,CAMA,yBADA,yBACA,CAJA,WACA,CACA,mBACA,CAFA,mCACA,CAHA,UAMA,uFAKA,cACA,CAFA,sBACA,CACA,0BAHD,kBAIC,uEAOA,4BACA,CAJD,qBACC,CAKA,kBAJA,sBACA,CACA,uCACA,eACA,CALA,qBAMA,uEAIA,eADD,UAEC,0EAIA,mBADD,YAEC,8EACA,WACC,WACA,uEAIF,aACC,CADD,wBACC,eACA,CACA,iBADA,gBAEA,2CAKF,WACC,yDAGD,kCACC,CADD,mDACC,CACA,+BACA,CADA,wCACA,CAFA,oBACA,CADA,kCACA,CAGA,sDAFA,QACA,4BACA,CADA,iCAEA,+DAEA,kCACC,CADD,mDACC,yFACA,+EAGD,kCACC,CADD,gDACC,8DACA,wBAOH,iBACA,CAEA,aAJD,YACC,CAEA,iBACA,CAFA,WAGA,6CAMC,mBAFA,YACA,sBACA,CAJD,iBACC,QAIA,4DAGC,eADD,eAEC,iEAIA,kBACA,CAFD,YACC,CACA,oBACA,wEAEA,kBACC,CADD,mCACC,CACA,qBACA,CADA,8BACA,CAFA,oBACA,CADA,kCACA,CAEA,sDADA,2BACA,CADA,iCAEA,8EAEA,kBACC,CADD,mCACC,yFACA,oBC9MJ,uBACC,yCAEC,SADD,oBAEC,yEAQA,wEADA,cACA,CAJD,aACC,YACA,QAGA,gXAGF,oBAGC,kDAIA,gBACC,YACA,qBAID,UADD,UAEC,oCAED,SAEC,YACA,oJAIE,iBADD,UAEC,kNAED,yBACC,4BACG,sPAKJ,wBADA,iBAEA,kPAIC,yBADD,kBAEC,8QAGD,yBADA,yBAEA,gLAEA,0BACA,yBACA,oRAID,2BADD,wBAEG,+DAMH,gBACC,uEAGA,kCACC,mDAGF,eACC,0CAGA,SACC,WACA,6DAID,OADD,UAEC,2CAGA,UADD,UAEC,4CAGA,yBACC,iFAGA,YACC,0EAGF,0BACC,oEAED,SACC,qBACA,uBAGF,OACC,wCAIC,WACA,CACA,2BADA,2BACA,CAHD,0BAIC,iFAID,0BACC,yFAIC,SADD,oBAEC,uBAIH,oBACC,wCAED,iBACC,uKAIA,0BACE,6LAGE,UADF,UAEC,mYACF,oBACG,wEAIL,yBACC,8FAKA,UADD,oBAEC,mGAKA,6BACC,kLAOD,gBAEC,yBACA,wHAGA,QACO,WACN,oSAID,0BACC,qUAGE,UADF,UAEC,oYACH,oBACC,sGAKD,gBACC,yBACA,sBAMM,SADD,UAEC,uBAGT,SACI,qBACH,mEAGA,wBADD,gBAEC,qDAIA,yBACC,yBACG,gFAIA,wBADL,iBAEE,oGAGA,0BACE,+GAGE,UADF,UAEC,oIACF,mBACG,+IAIL,oBACC,0EAGD,QAEE,qBACA,uFAEF,yBACC,yBACG,wCAIJ,oBACC,OR1PF,qJAEC,oBACA,oBACA,sBACA,oBACA,oBACA,qBACA,qBACA,yBACA,yBACA,2BACA,uBACA,yBACA,yBACA,2BACA,mBAGA,wBACA,yBACA,yBACA,yBACA,yBACA,yBACA,yBACA,yBACA,yBACA,yBACA,2BAGA,4BACA,4BACA,4BACA,4BACA,2BAGA,4BACA,4BACA,yBACA,0BACA,0BACA,2BACA,4BACA,4BACA,gDAGA,iCACA,qCACA,2CACA,0CACA,4CACA,oCACA,0CACA,qCACA,oBAGA,mBACA,oBACA,iBACA,oBACA,mBACA,iBACA,oBACA,kBACA,kBACA,kBACA,oBAGA,qBACA,mBACA,oBACA,kBACA,kCAGA,8DACA,gEACA,iEACA,qCAGA,oCACA,mDAGA,8CACA,iCACA,oCAGA,sCACA,oCACA,CAOA,YADD,sLAOC,CANA,KAIA,kCACA,mCAHD,QAIC,0BAWA,4BACA,CARD,kCAKC,kBACA,CACA,mBACA,CAFA,eACA,CAEA,cADA,mBAEA,GAED,wBACC,YAGD,sBACC,kBACA,YAKD,wBACC,kBACA,yNAOA,YAgBA,mBAhBA,wBACA,CAcD,iBACC,CADD,gBAdC,oYAeA,wDACA,eAYC,kBACA,CAZD,qBAEA,oCAGA,gCACA,8CACA,CAKC,gCAGA,4BACA,CANA,YACA,CAJD,YACA,cACC,MACA,CAHD,oBASC,0BAQA,kBACA,CARA,cAED,CAKC,aALD,WACC,CAGA,sBACA,CACA,oBACA,CANA,UAMA,mCAKC,kBACA,CAFD,YACC,CAHA,UAED,CAFC,YADD,aACC,CAIA,oBACA,CALA,iBAKA,qCAID,cACA,+CAIA,WACA,iBACA,CAHD,WAGC,oCAGA,YACA,CAFA,iBACA,CACA,qCAID,UACC,CAHA,eAGA,qBAEA,+BACA,CACA,oBADA,mBACA,CAPA,gBAED,CAKC,eACA,2CACC,CADD,aACC,iBACA,CADA,qBADD,WAEC,qCAKA,8CALA,wBACA,CACA,4FACA,2CAEA,4BACC,kCAGD,+DACC,qFACA,0DAGA,6BACA,sGAGA,kCACA,2DASH,sBACA,kEAKC,yBAEA,kFAGE,iEACA,oEAEA,6CAGG,CAHH,8FAGG,6IAIA,0BACC,qHAIA,yJAWA,iCAXA,mIASA,+BAEA,iCAGH,gBACC,CAJE,iBAIF,iBAEA,CAFA,QAJE,WAGH,CAGC,SACA,qGAQA,qCAEC,eAFD,kCAHA,gBACA,eACA,CACA,0CACA,mBACC,CAHD,WAGC,2GACA,oBAGD,+BACC,4GACA,wBAKD,WACC,yBACA,8GAEA,qCAGD,CAHC,YAGD,6BAEC,oHAEC,cADD,cACC,wHAGA,sIACA,yIAgBN,2EACA,6CAGC,sCADA,UACA,oEAIA,yGAIA,wGAEC,sGAEC,+GAEC,uBAIH,uCACC,gGAEC,sHAGD,wBACC,gHACA,mBACC,6HACA,+CAEC,2IACA,uCACA,wJACC,wLAMF,2JAIC,iFAED,CAFC,WAED,8IAIC,uFACA,CADA,WACA,qJAOC,0EAED,CAFC,oCAED,CACC,iJAGF,WACC,iIAKA,8CAMH,yBANG,oBAKJ,sBACC,iGAGA,gHAEC,WAQL,qGAKE,mBAMF,2BAIC,kBAEA,CAHD,YACC,CAEA,mEAED,CAFC,aAED,MAMC,kBACA,uCACA,CADA,UACA,uEAUA,sBAIA,CANA,uCAEA,CAHA,iBACA,CAFA,iBACA,CAOA,SACA,YAQA,6BAGD,CANC,YAEA,CANA,YACA,iCAGA,CAEA,WACA,CAGD,SACC,YAEA,WACA,CAED,WACC,CAHA,SAED,CAJC,UAKA,cACA,YACA,CAGD,WACC,CAHA,SAED,CAHC,WACA,CAGA,SACA,aACA,YACA,CAGA,WADD,UAFC,WAGA,aAID,YACC,WADD,SACC,CAJA,UAIA,YAGA,WACA,CAHA,WAEA,WAFA,UAGA,kCACA,YACA,cASA,4BAJA,2BAGA,qBACA,CATA,0BAID,CAKC,YACA,iCAGA,CATD,gBASC,yBACA,cACA,cACA,gBAED,2BACC,mBACA,CAEA,6BAGA,CAJA,gBACA,CAIA,cANA,eACA,CAIA,UACA,eACA,wBAEA,sCACA,CADA,YAEA,gBAID,wBACC,CADD,6BACC,CACA,mCACA,eAEA,eAED,iBACC,CAXA,iBACA,cAUA,eAOA,sBACA,CAFA,+BACA,CAEA,QARA,yBAGA,kBAKA,eAEA,kBAEA,CASA,qBAGA,CAJA,wBACA,CAHA,kBACA,CAHA,qBAEA,CAMA,aACA,CAbA,WAED,CAJC,aAEA,QAHA,WACA,CASA,eACA,CAND,iBAEC,CAPA,WAgBA,eAEA,UADA,YACA,CAEA,cAEA,CACA,gBAHA,YAEA,gBACA,CALA,UAKA,0BAMA,kBACA,CAOA,qBACA,CAOA,wBAEA,uCACA,CARA,kBAEA,CAlBA,qBACA,CAaA,cAbA,cACA,aACA,mBACA,CAOA,eACA,eACA,CAKA,QAZA,0BAEA,mBACA,CALA,sBACA,CAMA,gBACA,CAHA,eACA,CAKA,eACA,CASA,aAMD,CAjBC,iBACA,CACA,WAgBA,6BARA,kCAcA,CANA,8BAEA,CAEA,sBACA,yBACA,+BAMA,qBAGD,CAIA,WACC,CAGA,iBACA,CATD,UACC,yBAGD,CAEC,cACA,CAVA,YAQA,YACA,CAdA,SACA,eACA,QACA,2BACA,CAaA,uBACA,CAdA,UACA,CAUA,gBAGA,eAGD,uBACC,aACA,oCACA,CADA,eADA,YACA,CAGA,WACA,CAHA,eAEA,CAFA,gBAGA,UACA,qBACA,oCAGA,CALA,2BACA,CAQD,UACC,yBAEA,CAZA,aAKA,qBACA,CACA,WAED,CAQC,OACA,cACA,CAJD,eACC,CACA,eALA,yBAGD,CARC,QAUA,gBAEA,cAED,YACC,CACA,iBACA,CADA,MADA,UAEA,aAIA,wBAEA,qBAEA,CALA,yBAKA,gBAEA,UACA,CAHA,iBAEA,CACA,mCAcA,wBACA,CAMA,sBAGA,kBACA,qBAEA,CAVA,kBACA,CAjBA,qBACA,CAaA,WAbA,cACA,CASA,eACA,eACA,CAIA,OACA,CAZA,0BAEA,mBACA,CALA,sBACA,CAMA,gBACA,CAHA,eACA,CAKA,gBAWA,eAEA,CAED,aACC,CAjBA,kBAiBA,iBAEA,WACA,CANA,kBAED,CAdC,WAkBA,8BA7BA,kBACA,CAJA,YACA,mBAuCA,CARA,YAOA,YAFA,gBAGA,CAIA,oBAHA,kBAGA,CALA,YACA,mBAgBA,CAZA,QAYA,eARA,iCAMA,kBAEA,aAKD,cACC,iBACA,WACA,2BALA,kBAED,CAHC,YAaA,CAPA,cAEA,kBACA,QAEA,iBAEA,gBACA,UAMA,kBACA,CAIA,sBACA,CASA,mBACA,CADA,oBACA,kBACA,qBACA,CAXA,kBADA,cAJA,cACA,CACA,gBAGA,QAHA,WACA,CAPA,sBAEA,CAIA,iBAHA,eACA,CAgBA,gBACA,CAXA,UAFA,iBACA,CAYA,mBAdA,UAcA,6BAzBA,qBAEA,eACA,aACA,mBAwCA,CAnBA,aAGA,eACA,yBAEA,CAOA,kBACA,CALD,8BAGC,CANA,gBAMA,YACA,CAXA,iBAiBA,2BAFA,6BAEA,CAnBA,UA2BA,8BAPA,YAIA,mBAQA,CALA,gBAIA,sBACA,CAFA,QAEA,kCAKA,cALA,cACA,iBAGA,CAGA,eACA,uBAEA,0BANA,eACA,CAIA,yCACA,CALA,kBAeA,CAVA,WAMA,cAHA,cACA,iBACA,CAHD,cAQC,+BACA,CACA,eAGD,CAOC,SAFA,0BACA,oBAVA,gBACA,CASA,eACA,0BANA,kBAEA,sBAEA,CALD,+BA0BC,CAnBA,SAWA,wBAEA,CAMA,YAlBA,kBAEA,CAUA,WAGD,gBACC,yBAEA,CATA,kBACA,CALA,yBAGA,kBAUA,CACA,WAKA,kBAED,CALC,kBAEA,cACA,CAJA,WACA,CAFA,SAOD,gBASC,kBACA,CATA,YACA,CAMA,cANA,kBACA,CAGA,WAEA,CALA,wBAEA,CAGA,OACA,CAHA,SAIA,aACA,UAMA,kBACA,CAIA,sBACA,CASA,mBACA,CADA,oBACA,kBAEA,qBAEA,CAbA,kBAbA,qBAEA,CAUA,cAVA,cACA,aACA,oBAIA,cACA,CACA,gBAGA,QAHA,WACA,CAPA,sBAEA,CAIA,iBAHA,eACA,CAkBA,gBACA,CAbA,UAFA,iBACA,CAcA,kBACA,CAjBA,UAiBA,WACA,UAMA,kBACA,CAIA,sBACA,CAQA,mBAEA,CAFA,oBAEA,kBACA,qBACA,CAXA,kBAbA,qBAEA,CAUA,cAVA,cACA,aACA,oBAMA,gBAGA,QAHA,WACA,CAPA,sBAEA,CACA,eACA,CAgBA,gBACA,CAXA,UAFA,iBACA,CAFA,UAeA,yBAlBA,cACA,CACA,iBAeA,kBAeA,CAdA,cAKA,eACA,aACA,CAHD,eACC,CAJA,gBASD,eACC,uBAEA,0BACA,iBAED,WAEC,kBAEA,CAHA,+BAGA,0BAEA,sBAEA,8BACA,0BACA,CAIA,kBACA,CAOA,sBACA,CASA,mBACA,CADA,oBACA,kBAEA,qBAEA,CAbA,iBACA,CAfA,sBAaA,cAbA,2BACA,mBACA,CASA,8BACA,CAEA,QAPA,0BACA,mBAEA,CARA,sBACA,CAQA,iBADA,eACA,CAgBA,eACA,CAdA,iBADA,iBACA,CAcA,kBACA,CAxBA,2CAwBA,aACA,UAMA,kBACA,CAIA,sBACA,CAUA,mBACA,CADA,oBACA,kBACA,qBAED,CAbC,kBAbA,qBAEA,CAUA,cAVA,cACA,aACA,oBAIA,cACA,CACA,gBAGA,QAHA,WACA,CAPA,sBAEA,CAIA,iBAHA,eACA,CAkBD,gBACC,CAbA,UAFA,iBACA,CAcA,kBACA,CAjBA,UAiBA,SAEA,kBAED,CACC,aACA,kBAEA,CARA,iBAEA,CAED,UAIC,sBACA,kBACA,CAED,eAFC,qBAED,gBACC,cAID,SAHC,QACA,kBAED,uCAEC,CANA,UAMA,kBAQD,eACC,WACA,mCAPD,UACC,yBAUA,CAJA,iBAGA,cACA,WACA,iCACA,aAGA,wBAIA,YACA,kBAEA,WACA,yBAGD,8BACC,iBAGD,CAlBC,iBAEA,YACA,UADA,UAgBD,+CACC,CADD,0BACC,SACA,CAFD,QAEC,oCACA,oBACA,yDAMA,CACA,UACA,yBAEA,CAJA,cACA,CAMD,WAEC,CAeD,mBACC,CAhBA,qBAeD,CApBC,eAqBA,SAEA,oBACA,CACA,YAHA,4BACA,CACA,UACA,eAIA,QAED,CAHA,iBAGA,YAEC,kBACA,CAFA,+BACA,CAEA,gBAED,CAHC,eAGD,gCAEC,cACA,aAKA,4BA+BA,0BAhCA,kBACA,CAHD,+BA0CC,aAGA,UACA,CAHA,cAEA,CAHA,eAIA,iCACA,sBACA,8BAGA,iEAEC,0BACA,CAHD,wBAFA,YACA,wBACA,CADA,QADA,QACA,CAFA,qBACA,CADA,aAMC,2BACC,yDAMD,aAEA,gCACA,UACA,yBAEA,CAJA,cACA,CAGA,iCAKA,gDACA,CACC,YAJD,kBACA,WADA,QACA,CAGC,UACA,4BACA,yBACA,gBAEA,CAHA,YAGA,0CAGA,oDAEC,CAHF,cACC,CAGC,SADA,cACA,CAJF,UAIE,0CACA,yEAEC,mBADD,YACC,8BAIH,iFAKD,kBACC,4CAIA,wCACA,4DAEC,CACA,2CAED,6CAKC,8BAHA,UACA,yBAEA,wCAED,iCACC,CACA,kBAQF,CARE,eADA,gBASF,uCAGA,wBAEA,wCACA,iBACA,oBAMA,yBAIA,wCAGA,0BAEA,CAVD,uBACC,CANA,WACA,eACA,yBAGD,CAJC,iBADA,iCAeA,8DACA,YACA,CAWA,kBACA,CAXA,cAGD,CAJC,SACA,CAFA,WAaA,iBACA,YACA,iBACA,+CAgBA,QACA,CAFA,eACA,gBALA,eAGD,kBACC,CAfA,gBAiBA,yBACA,eAlBA,UAkBA,2CAlBA,0KASA,+BAEA,CAOA,+BAlBA,iBAsCA,CApBA,aAUA,SAGA,2CAIA,CATA,iBACA,eACA,CAUA,iBAVA,yBAGA,CAIA,gBACA,CAlBA,SAoBA,sEAWD,sDAGC,CANA,YAGD,CANA,eACC,uBACA,mBAOA,eAGA,qBAED,CAJC,oCAID,kBASC,aACA,CARA,kBAIA,gBACA,CANA,oBACA,CAKA,sBAEA,CAHA,mBAIA,iCAEA,kBACA,cAiBA,gBACA,CAEA,YACA,CAPA,WAEA,oBACA,CAFA,mBAZD,iBACC,CASA,eACA,CAVA,sBASA,CAKA,YACA,CAEA,aAOD,wBACC,CAJD,kBACC,CAGA,UACA,eACA,CALA,WAED,CAGC,iBACA,CAVA,SACA,CAOA,YAEA,WACA,yCACA,kBAQA,sBAGD,CAVC,aAED,CAIC,WACA,CALD,mBAEC,sBACA,sBACA,CAID,oBACC,eAKA,aAHA,cADA,kBACA,CAGA,eACA,CAFA,gBAFA,qBAIA,eAUA,WACA,CADA,+BACA,8BACA,CAZA,cAcA,6BAFA,kBACA,aAIA,aAGD,qCAGA,oBACC,CAKA,YALA,mBACA,CAGD,eACC,CAXA,aAED,CAKC,qBAGD,CACC,UACA,gBACA,mBACA,WAED,kBACC,CADD,aACC,cACA,YAEA,wBAGD,qBACC,mBAKA,oBAGD,qBACC,CAJA,oBAIA,UACA,eACA,mDACA,CADA,gBACA,aAFA,YAEA,UAGA,qBACA,oCAGD,CAPC,oBACA,CAMD,2CAEC,cACA,CATA,cACA,CASA,wBAGD,0BACC,CALA,0BAKA,WACA,4BACA,uBAEC,0BAEA,CAND,UAMC,aACA,2BACA,6BAEA,uBAMA,wBAIF,kBACC,kBACA,CADA,iBAVC,cACA,CAWD,YACA,CAZC,cACA,CAED,eACC,CAHA,WAED,CAOA,eACA,CAVC,UAWD,wBAGA,oBAGA,2BAIA,mBACA,CACA,WAQA,mBAMD,wBACC,CARA,oBACA,CAGA,mBAGD,CAbC,kCAKD,qBACC,CAEA,wBACA,iBAKA,UAOA,cAHD,eACC,0BACA,CALA,iBAGD,CAGC,mBACA,CACA,aACA,SACA,mBACA,CAFA,eAEA,YAWD,aARC,iBAED,CAHA,eACC,CAED,mBACC,CAKD,kBACC,gBACA,CAPA,sBAGA,WAIA,kBACA,kBACA,YACA,oBACA,sBACA,cACA,mBAIA,qBACA,CAMA,wBACA,CAJA,+BACA,6BAEA,CAVA,aAYA,mBAED,CAHC,gBACA,CAZA,oBACA,kBACA,CAHA,oBAeD,iBAEC,wBACA,qBACA,CAHA,sCAGA,gBAUA,qBACA,CAKD,gCAEC,CALD,6BAGA,CAMA,yBAJC,6BAEA,CAdA,yBACA,CAFA,mBACA,CANA,UACA,CAQA,kBACA,CATA,2CAEA,oCAmBD,+BACC,yFAwBA,4BACA,CATA,sBAcD,wBACC,CAXA,qBAED,CArBC,wCAIA,CAIA,cAgBA,wBAEA,CALD,sBAEC,CAGA,eACA,CAbA,oBACA,CAJA,gBAPA,2CAEA,oCAEA,CACA,qBACA,CAiBA,UAGA,+BAtBA,iBACA,CAOA,mBAqBA,CAPA,mBACA,aACA,gBACA,CAED,mBACC,aACA,cAEA,aAED,CAJC,mBACA,eACA,CAFA,mBAID,cAEC,cACA,CAEA,mBACA,CAHA,0BACA,mBACA,CALD,wBACC,iBAKA,kBAEA,wBAEA,CAGD,wBACC,kCAEA,+BACA,CAEA,aAbA,oBACA,sBAYA,iCAFA,kBACA,cAqBA,CApBA,eAMA,+BAMA,mCAEA,CAZA,yBACA,wBACA,+BACA,6BACA,CAQA,+BAGD,CAEC,mBACA,CAnBA,0CAmBA,eAOA,eACA,6BAJA,kBAGA,CAJA,YACA,CAHA,gBAaA,CANA,cAKA,cACA,CAFA,eACA,CAJA,eAKA,aAUA,wBAEA,CALD,qBACC,CALA,iBACA,CASA,eACA,CAFA,cARA,cAGD,CANC,gBACA,CAQA,gBAEA,YACA,CAXA,gBACA,CAKA,gBACA,UAKA,sBAKA,cAHA,gBAYA,8CAXA,gBAEA,CAMA,wBACA,CALD,wBACC,mBACA,CARA,cACA,CAWA,YACA,CAZA,eAUA,eACA,CAHA,gBACA,CAFA,eAsBD,CAjBC,wBAKA,aACA,CAOA,cAZA,eAgBD,wBAOC,aACA,CALA,oBACA,CACA,WACA,CAHA,WAGA,eACA,CALA,iBACA,CACA,UAIA,gBASA,gBAEA,yBACA,CAVA,kBACA,CASA,WAZA,kBACA,CAEA,gBAUA,6BADA,sBACA,CAND,cACC,eACA,CAIA,gBAZA,gBACA,CAEA,iBACA,cA4BA,CApBA,aAIA,gBACA,sBAEA,CAYA,kBACA,CAbA,UAED,yBACC,CAGA,kBAKD,CAEC,gCAEA,aACA,uBACA,eAEA,CALA,gBAKA,gBAQA,wBACA,qBACA,cARA,kBAGD,CAKC,gBAED,gBACC,CAZA,mBACA,CAQA,SAGA,4BAED,eACC,iBACA,eAEA,qBACA,iCAEA,mDAMA,sBAGA,CAHA,wBAGA,CATA,UACA,CAED,yBAEC,CAJA,0BAQA,2BAGA,wBAEA,CAQA,kBARA,UACA,yBAMD,CAKC,cACA,CAJA,kBACA,iBACA,CAHA,WACA,CAEA,iBACA,CALD,gBAXC,iBACA,YACA,CAcA,SACA,6BAQA,wBACA,qBACA,cANA,mBAMA,iCARA,mBAEA,CAMA,qFAUA,iDAVA,mIASA,+BACA,eAWC,CAXD,0BAGA,+EAHA,qBACA,cACA,cACA,6DAQC,oBAIA,iBAFD,8BAEC,uDAEA,wCAID,wBACC,qCAGA,mBAJD,mBAIC,gBAPA,gBAUD,wCAOG,aACA,CAFF,YACE,CADF,sBAJA,4BAME,uCAEH,eACC,sCAOD,kBACA,CANC,YACA,8BAKD,oCAKA,uCAFD,iBAEC,sBACA,oBACA,CADA,iBACA,uBAGA,wBACA,CACA,mCAEA,CAIA,kBAJA,UACA,yBAED,CASC,eALD,iBACC,iBACA,CALA,YAGD,CAGC,iBACA,CARD,eACC,CAVA,iBACA,YACA,CAeA,SACA,CACA,4BACA,+CAQA,sBAMA,CANA,wBAMA,CAdA,UACA,CAGD,QACC,kBAJA,iBAGD,SAUC,CAEA,gDAEA,kBACA,iBACA,mBAGD,0BACC,CAIA,kBACA,cACA,iCACA,0BAPA,wBAGA,iBAkBA,CAdA,kBACA,qBAEA,CAOA,mBACA,kBACA,CARA,WACA,CAOA,mBACA,UATA,UASA,iBAVA,YACA,CACA,mBACA,kBAIA,CANA,SAUA,iBAGA,iBACA,CAQA,kBACA,CATA,sDAMA,CACA,qBAEA,oBAZA,WACA,CASA,6BACA,CACA,kBAXA,wBACA,CAFA,UAYA,iBAMA,iBACA,CAQA,qBAGD,CAJC,kBACA,CARA,sDAIA,sBACA,CAMD,YACC,CAjBA,YAUA,sBAEA,CAZA,UAoBA,eACA,CAJA,QAjBA,oDAEA,mDAEA,CALA,UACA,CAiBA,UAIA,mCAJA,sCACA,yBACA,iBAeA,CAbA,mMAQD,+BACC,CATA,cASA,oBAIA,CACA,mCAdA,4DA4BA,CAdA,iBASD,qBACC,CATA,2CAEA,CAOA,mBAJA,aACA,CAMA,mBACA,CAPA,iBAGA,oBAGA,gBAHA,wBACA,iBAGA,kBACA,mBAEA,CAGA,iBACA,CAKA,iBACA,aAGA,CANA,sBAPA,6CAGA,CAOA,cAGA,8BACA,CAPA,WACA,CAMA,WACA,kBAXA,eAEA,CASA,gCAKD,qCAdC,qBACA,CACA,UAYD,gBACC,+DADD,UACC,wBAuBA,4BAGA,CAVA,sBAaA,qBAGA,CAZA,qBAEA,CArBA,wCAIA,CAIA,cAiBA,wBAEA,CAnBA,iBACA,CAuBA,YAXA,sBACA,CANA,mBACA,CAcA,kBACA,iBACA,CANA,eACA,CAdA,2BACA,CALA,eACA,CAPA,2CAEA,oCAEA,CACA,qBACA,CAkBA,SAKA,WAyCA,cACA,CACA,kBACA,CAFA,kBACA,CAHD,gBAIC,mBAEA,mCACA,CAFA,SAEA,iBAGC,kBACA,CAHA,kCAEA,CACA,qBAJD,0BAIC,iGAEA,sGAQA,CARA,aAQA,oDAEC,eADD,eACC,8CAGA,YACA,uDAGA,uEAGF,uBACC,4CACA,cACA,CACA,8BAEA,wBAIF,wCAEC,CAKA,6BACA,CANA,UACA,yBAGA,YACA,CADA,UAEA,mBAOA,yBAKD,wCAKA,2BAXA,uBACC,CANA,WACA,CADA,kBACA,cAID,0BAJC,kBADA,mCAgBD,8CACC,eAGD,eACC,aACA,kBACA,oBAED,qBACC,iCAID,6BAGC,gCACA,mBACA,kBAKA,kBAEA,CAHD,iBAGC,cAOA,WAJA,SACA,CAEA,mBACA,CALD,iBACC,CACA,QACA,+BAEA,QAID,WAHC,cAGD,CAHC,SAGD,cAOC,qBAEA,CAHA,cACA,CALA,WAGD,aACC,CAGA,8BAIA,CAZA,WAYA,oBACA,oBACA,aACA,cAMD,WACC,CALD,iBACC,iBACA,YAGA,eAaA,YAZA,iBAWA,iBACA,CAZA,WAYA,aAGD,iBACC,CAHA,gBAGA,CACA,aACA,cACA,CADA,gBACA,yBAEA,0BACA,eACA,eAKD,wBACC,6CACA,CAPA,cACA,CAMA,cAID,CAVC,iBAUD,yCAEC,wBACA,mCAGD,yBACC,yBAID,wBACC,kCAGD,yEAKC,uBAUA,sGAED,YACC,8BAIA,sBACC,CAED,mCACC,iBAGF,sBAIC,WACA,aACA,0BAKA,yBACA,2BAEA,kBAOC,oBAGD,CAHC,gBAGD,4BACC,kBACA,CADA,aACA,kBACA,wCACA,mCACA,wBAEA,0BAGA,CACA,gCAED,CAHC,0BACA,CAGA,4BADD,eACC,uCACA,cACA,CADA,SACA,iCAIA,gCAGD,6BACC,CANA,qBACA,kCACA,CAHA,wBACA,CAEA,0BAIA,0BACA,8BAGD,0CACC,kCAIF,6BAEC,yCAEA,kCAEA,mBAMA,YACA,CANA,aAED,yBAEC,CAEA,QACA,CAHA,iBACA,CAMA,uBAJA,0BAGD,mBACC,kBAIA,cAFA,WACA,YACA,CAHA,kBACA,UACA,UACA,eAEA,6CAGA,kBACC,CACA,gDAIA,CAVD,gBACA,CASC,4BAGD,CAHC,gBALA,iBAQD,sBACC,yBACA,kCAGD,iDACC,0BAGD,mBACC,8CAGD,wBAEC,wCACA,sBAEA,6BAIA,eAED,8BAEC,CAMA,cACA,CAFA,eACA,CAJA,iBAEA,mBACA,CALA,iBAEA,CATA,uBAEA,gBACA,oBAWA,gCAOC,kBAGD,CANC,kBACA,kBACA,aACA,CAND,YAMC,uBAND,iBAGC,SAHD,UASA,8CAKC,kBADA,WACA,kBAFA,UAEA,8CAMA,iBAEA,CAFA,YAEA,4BAHA,UAGA,gDAGA,wBAEA,CAFA,YAEA,8BAGC,YAKH,CARE,YAFA,8BAKC,UAKH,gEAIC,UACA,yBAEA,YACA,CADA,UACA,8BAGC,UACA,CACA,kCAEA,CAFA,eAHD,eACC,CAIA,eACA,CAJA,cAHD,4BAOC,+CAGC,kCACC,CAJF,iBAGC,CAHD,YAIE,0BAJF,cAIE,CAQA,4PAIA,+OAYH,+BACA,gDACA,CAdG,kBAcH,+CAWF,kCACC,CATC,4BAKF,CAIC,aACA,2BAXC,yBACA,CAWD,mBACC,cACA,yBACA,gGAIC,4BADA,UACA,wTAEC,wQAUA,aADD,2BACC,CAHA,SAGA,8GAKA,0HAEC,kBACA,iHACA,2PASD,2CAGA,qXAEC,kLAUF,wBAKD,0BACA,oDAEA,4DAGC,YACA,kCACA,6CAKD,kBAGA,CAHA,YAGA,gBACC,CAJD,4CAIC,+HAOH,iBACC,qEACA,sBACA,6BAED,wBAEC,sCAJA,qBAED,CAFC,sBAIA,2FAID,wBACC,qCAGA,CAJD,eAIC,4DAED,CACC,oCACA,6BAEA,sEAIA,kBACA,CADA,aADA,0BACA,CADA,kBAID,sCAGC,+BACA,6CAEC,gWAOC,0UAWF,2BACA,oBAIA,4BACC,yBACA,0BAID,CAPA,sBACA,CAMA,mCAGA,CAZA,UAYA,6CAZA,kCACA,qBAiBA,kBAEC,eAFD,iBACA,CACC,UACA,CAFD,QACC,CACA,0BACA,qDAED,kCACC,aAED,0BACC,sBAED,CACC,iGAKD,oBAIA,6BAEA,UACA,+DAEA,uCAIC,yBACA,wCACA,kBAED,CANA,yCAEC,CAFD,sBADA,WAOA,qDAIC,mBADA,YACA,qBAGA,yCAGA,kBACA,CAFA,YACA,CACA,sBACA,kDAGC,mBADA,8BACA,CAFD,WACC,CACA,sBAIF,CANC,UAMD,uCAGC,sCACA,yBAGD,4CAOC,8CADA,iBACA,wCAQD,iBACC,CAJC,2BAGF,CACC,kFAIA,eACA,wCACA,qEACA,CADA,SADA,0CACA,yDACA,0HAUC,gDAJA,0BAGD,+CANC,4BAEA,wCAKA,6DAIA,eACA,4FAMA,YADA,UACA,2EAIA,wBACA,4FAGA,iBADD,iBACC,4EAGA,wBACA,+FAKA,8BACA,4EAGA,0BAEA,iGAGA,iBADD,iCACC,6DAED,gEACC,4DACA,wBAID,wCACC,2CAED,8FACC,6DAGD,+BACC,CACA,kEAED,yDAOD,qBANE,WAMF,yDACC,sBAMA,wDACC,mGAKA,iBACA,uEAIC,mBAJD,YAEA,8BAEC,uGAGC,YACA,qIACA,CADA,eACA,0JAQD,iBACA,2FAID,YACC,yBACA,YACA,iGACA,+RAGC,+QAKC,uSAOF,0IACC,sDAEA,mJAKC,yJAKD,0JACC,sLAWF,2HAIA,aAEC,wBAFD,uCAEC,0XAMC,+SAMC,+BAIH,gBAJG,sBAIH,CAJG,kBAIH,0HACC,sDAQJ,gJAQC,mBAED,uDACC,gBACA,CADA,yBAEA,0CAGD,+BACC,4CAED,CALC,wBAED,CAIC,eACA,CAFD,YAEC,kEAGA,yCAEA,4FAGC,6DACA,qEACC,sFAGF,mBACC,qGACA,kDACA,CAKD,yBAEA,gDAPC,oEAOD,0CAGC,2HAGA,eAGF,WACC,uFAEC,iBAGF,iGACC,wDACA,uDACA,CACA,wEACC,CAFD,oEAEC,2DAGD,kCACC,kDAID,4FACA,4CACA,qEACC,CADD,YACC,6DACA,oFACA,+DACA,CAMF,yBACA,+CAGA,CAVE,4BAKH,wCAKC,YACA,kBACA,2CACA,CAIC,WADA,cACA,mBAHA,iBAEA,CACA,SAJD,wBAIC,2CAKA,eAED,qFAFC,sCAFA,mBAWA,2CAIA,qCACA,CAFA,mBAEA,8BACC,yCAED,yBACC,CAHA,cAGA,0CAEA,gFAEC,uEAED,2DAEC,2FAGA,WADD,eACC,uEAID,wBACA,iGAGC,YACA,mGAEC,kBACA,mBACA,CAHD,YAGC,iGAEA,mBAIA,4BACA,CALA,qCAED,CACC,mBAEA,8FAEA,gCAIA,cACA,CALA,wBAED,mCAGC,kGAGD,kBACC,CADD,aACC,sHAIC,wGAQH,iIACA,sJACC,4CAED,gIACC,0MAKD,2BACC,0HAKA,iCADA,qBACA,qCADA,0BACA,8NAEC,wBAQD,sIACC,oUAWD,eADD,eACC,gXAmBC,4BAGF,0IAMC,kYAGC,QAOA,8XAKA,yPAeA,sCADA,mBACA,6EAOD,cAGF,CAHE,WAGF,oEAGC,cACA,wEAGC,6DACA,CAFA,kBAEA,4DAIC,mBADA,YACA,+DACA,aAGF,CAGC,6BAHD,eACC,CACA,sBACA,CAFA,kBAEA,8DAKC,oBACC,oCACA,CALF,6DAGC,CAHD,mCAKE,+EAIF,uIAEC,oFAEA,2IAQF,kCACA,8CAMC,6CAGA,0FAGA,aAED,CAFC,+BAED,kBACC,6DACA,uEAGC,2GAIA,gBAED,CAFC,gBAED,uEACC,aAID,kJAGE,qHAEA,oJAGA,6BAGC,qHAKA,uCAKF,eACC,CANC,aAMD,6BACA,CAPC,uBAOD,oGAGA,2BADD,iBACC,oGAIA,wBACA,4FAKF,2GAGC,2HACA,gCACA,8BAGA,gQAKA,aACC,6BACA,yGAGA,mBACA,4BACC,CAFD,8DAEC,+HACA,YAGD,sBACC,+HAKA,qFAJA,4BACA,+DAGA,0BAEC,CAFD,6BAEC,oIAOF,gBACA,CAFD,gBAEC,mGACA,gDAEA,aACC,uTAGC,sNAIC,uOAMC,8RAIC,gMAKF,sMAIA,qMAGA,sLAgBF,wBACA,mHAMA,mDAEC,QAFD,6CACA,CAEC,SAFD,UAEC,qIAED,eACC,sIAMA,mBAHA,qBAED,YACC,CAHA,UAGA,4HAQF,yGAGA,cACC,CAJD,kBAIC,0IAQD,wBADD,6BACC,4HAEC,CAKA,iRAMA,SACC,oJACA,kJAMD,6IAcL,mEACA,oEAEC,+BAEA,4CAGA,CALA,YAKA,oDAID,aACC,CAHA,+BAGA,eAEA,CALA,mBAED,CAGC,qDAEA,oDACC,2BAKH,CACC,UACA,CAFD,QAEC,mHAEC,iBADD,sBACC,qGAKD,8DACA,qEAEC,0EACA,kFACA,CASF,wEAIA,CAbE,4BAOF,wCAMA,oBAGA,yBACC,yCACA,kBADA,yBACA,CAHD,iCACA,CAFA,WAIC,qCAGC,2EAGA,CAJA,YAIA,kDACA,8BA2BF,CACC,qDACA,cACC,CACA,wDAMD,sEACA,oDACA,4BACC,yCACA,CAFD,2BAEC,2CACA,gEACA,qCAQD,yBACA,+CAGD,CAXE,4BAKF,wCAEC,CARC,yBAYF,oCAEC,oBAID,CAJC,gBAID,wBACC,qBACA,gEACA,2CACC,2GAEC,0BACA,+FAWJ,qBACC,CANA,uBACC,uBAIF,CACC,oBACA,kDAEA,wBAIA,sWASE,utBAMC,svBAGC,4OASF,8vBAMC,wwBAYH,6DAIC,aACA,aAIF,8FAIC,kBACA,cACA,4BAKE,kBAGF,CAHE,aAKD,qBACA,CATD,0BAMA,sBAEC,CAGD,UACC,CAHA,iBAED,CAXA,wBAYC,iCACA,WAED,0BAGC,eACA,CAHA,iBAEA,CACA,qCAEA,aACA,2CAIA,+CADA,eACA,CAMC,QAND,mCAMC,8BAMF,8BAEA,4EAQA,YAIC,gBAGC,oBAEA,CACA,YALA,iBAEA,CAGA,UACA,sBAEA,SADA,iBACA,wBAQA,qBAGA,mBACA,CAJA,SAHD,cACC,OACA,CAKA,kBACA,CAZA,iBAID,CAEC,OACA,CAFA,MAKA,cAEA,CACA,8BAKD,sBACC,kBADD,WACC,8BAID,CATC,uBAID,OALC,iBACA,CAID,cACC,CALA,UASD,sCACC,wBAID,qFACC,0BAKA,oCACA,0BAGD,0CACD,uCACA,mBADA,UACA,2CACC,kCACA,wDAGC,+FAID,qGAMD,6BACI,0DAIH,kBACA,CAFA,YACA,CAEA,4BADA,iBACA,yDACA,yISz0HF,IAGE,uBAAwB,CAFxB,yBAA2B,CAC3B,oBAAqB,CAErB,iBAAkB,CAClB,qBCCF,CDKA,YAOE,QAAW,CAFX,WCKF,CDEA,wBAXE,YAAa,CACb,SAAU,CAOV,iBAAkB,CANlB,yDAA2D,CAC3D,iECkBF,CDVA,YAOE,OAAQ,CAFR,UCKF,CDEA,oDAGE,wBAA6B,CAD7B,aCEF,CDEA,oJAME,UCCF,CDEA,kJAME,qBAAsB,CACtB,UCCF,CDKA,aAOE,UAAW,CAFX,UAAW,CAFX,6DAA+D,CAC/D,qECMF,CDEA,0BAXE,qBAAsB,CACtB,iBAAkB,CAOlB,iBCaF,CDVA,aAOE,SAAU,CAJV,4DAA8D,CAC9D,oEAAsE,CACtE,SCKF,CDEA,oGAGE,qBAAsB,CACtB,WCCF,CDEA,oGAGE,qBAAsB,CACtB,UCCF,CDGA,oCACE,IACE,uBCCF,CACF,CDEA,sEACE,IACE,uBCCF,CACF,CCjHA,qBAEE,WAAY,CADZ,iBACY,CPFd,mCACC,gCAGD,gCACC,aAED,gCAEC,eADA,gBAEA,iBAED,+BAGC,SAFA,kBACA,QAEA,+BACA,UAED,sCAEC,YADA,UACA,CAID,sCAGC,WADA,WADA,SAGA,YAED,4CAGC,WADA,WADA,SAGA,YAED,oCAIC,kBAFA,YAGA,WAJA,WAEA,QAEA,CAED,wCAIC,kBAFA,YAGA,WAJA,WAEA,QAEA,CAGD,yBACC,yBAGD,kCACC,aACA,yBACA,mBAGD,gCACC,gBACA,WAED,yCAGC,iBADA,eADA,SAEA,CAGD,kDACC,kBACA,YAGD,yCACC,mBACA,+EACC,gBAIF,4CACC,YAGD,oCAIC,kBAFA,YADA,WAEA,QACA,CAEA,sEACC,iBAIF,oCACC,aAEA,gBADA,UACA,CAEA,6DACC,yBAIF,mCAGC,kBADA,kBADA,QAEA,CASA,uEAGC,cAFA,eACA,gBAEA,yBAMD,wEAIC,uBADA,kBADA,kBADA,gBAGA,CAEA,iGACC,mCACA,qBACA,sIACC,qBAIF,8EACC,2BAGF,uEAOC,cANA,eACA,gBAEA,oBADA,iBAGA,iBADA,eAEA,CAEA,gGACC,qBAOH,gCAGC,mBACA,yBAIG,mBAPH,uBACA,sBAOG,0BAJH,YAEG,sCADA,SAGA,CAEH,mEACC,YACA,0GAEC,YADA,WACA,CAGF,4DAEC,gBADA,iBACA,CACA,4EACC,WAEA,eADA,gBAEA,mBAED,6EACC,eACA,kBAED,6EAGC,mBADA,aADA,eAGA,qBACA,iFACC,cAED,+EACC,6BACA,kBAIH,kEAEC,gBADA,eACA,CACA,sEACC,yBAED,2EAKU,sBADA,kBADA,eAGH,mBAEG,aANH,aAKG,YANT,4BAOS,CAGX,4DAEC,yBAGA,YACA,mBAFA,WAIA,eADA,eANA,kBAQA,qCANA,UAMA,CAEC,+DAEC,gBADF,iBACE,CACF,8FAGE,mBAFD,aACC,sBAGA,gBADA,oBACA,CACH,8GAEC,eADA,eACA,CAED,+GACC,eAGA,eADA,kBADA,wBAEA,CFlQF,iCAEC,sBADA,oBACA,CAED,mCAMC,8BAFA,kBAGG,qFAJH,gBADA,aAGA,kBAGA,WAPA,WAOA,CACA,0CACC,iCAED,wEAEC,mBAEA,WAHA,aAEA,cACA,CACA,6EACC,iBASH,sCAUC,mBACA,uBAHA,aADA,WAEA,uBAJA,WAFA,kBACA,UAHA,+BAKA,WAJA,SASA,CAED,6CAEC,YADA,UACA,CAED,0CACC,YAGD,sCACC,wCACA,YACA,mBACA,yBACA,eACA,eAEA,YADA,YACA,CAEA,wCAEC,yBADA,iBACA,CAID,2EACC,wCAID,uEACC,sBACA,mBAIF,oCAIC,iBAIA,yBACA,uBAHA,mBADA,yBAHA,eACA,eAFA,iBAQA,CACA,qEACC,wCAGF,iDAEC,8BAEA,yBADA,cAEA,kBAJA,kBAKA,QACA,SAED,2CACC,kBACA,QACA,SAED,8CAKC,yBACA,YACA,kBACA,yBACA,eACA,eACA,iBAVA,kBACA,YAEA,SAQA,UATA,UASA,CAED,6CAEC,aACA,SAFA,gBAEA,CAED,4CAIC,yBACA,YACA,mBACA,yBACA,eACA,eAIA,YACA,kBAbA,kBACA,WASA,UACA,YATA,UAWA,CAGD,2CAWC,wCAEA,kBADA,0BAFA,wBALA,YAGA,eACA,yBAFA,YADA,UAFA,sBADA,WAUA,CAGD,0CAGC,YADA,kBADA,SAEA,CAED,0CAEC,kBACA,YAFA,OAEA,CAED,kDAEC,kBADA,UACA,CAGD,0CACC,sCA0BA,kBAxBA,uFACC,uCAGD,2FAEC,WADA,eACA,CAOD,oKAEC,gDAIA,iHACC,mBAOH,0BAGC,qBAFA,yBAGA,mBAFA,cAOA,qBAED,oDAHC,mBADA,aAFA,YACA,eAcA,CATD,0BAGC,qBAFA,yBAGA,mBAFA,cAOA,qBAED,0BAGC,qBAFA,yBAGA,mBAFA,cAOA,qBAED,oDAHC,mBADA,aAFA,YACA,eAcA,CATD,0BAGC,mBAFA,wCAGA,mBAFA,6BAOA,qBAED,4CACC,uCAEA,gDAEA,6BADA,sBAFA,wBAGA,CAED,8GAGC,gDACA,0NACC,4CAA8C,CAE/C,2PACC,qCAED,8eACC,uBAGA,0WACC,oBACA,mBAMF,0kBACC,gBAGD,kPACC,uCAEA,gDADA,WAEA,sBAGD,0NACC,gDAKA,mUACC,mBAED,saACC,4CAaF,wWAMC,wBADA,wBADA,mBAEA,CAGD,oLAIC,wBADA,wBADA,mBAEA,CAED,wWAMC,wBADA,wBADA,mBAEA,CAID,wEACC,8BAEA,kBACA,8BAFA,cAEA,CACA,sGAKC,mBADA,aAEC,8BAJD,eACA,YAGC,CAKA,wKAEC,eADA,gBAEA,iBAED,8KACC,eACA,iBACA,cAGF,0IAKC,mBAFA,aACA,SAHI,iBACJ,UAGA,CAGA,uPAEE,mBADA,YACA,CAEA,+RAME,mBALA,mBAEA,kBACA,eACA,aAEA,uBALA,iBAKA,CAEA,mSAEE,YADA,UACA,CAIJ,oSAEE,0DACA,gBAFA,iBAEA,CAEA,kUAOE,mBAEA,mBAEA,8BADA,2BANA,SAQA,eANA,aAEA,uBANA,OADA,kBAEA,MAEA,UAOA,CAEA,sUAEE,YADA,UACA,CAIJ,0UAGE,sBACA,kBAEA,eADA,aAHA,4BADA,UAKA,CASJ,gSACE,0CAGF,wSACE,mCAOJ,iQAEE,6BADA,kCACA,CAKN,yCACE,GACE,QAEF,GACE,aAgBF,6GAEC,mBADA,mBACA,CACA,8IAGC,mBAFA,aACA,iBACA,CACA,kLAEC,WACA,UAFA,WAEA,CACA,4NACC,cAGD,2NAEC,mBACA,YAFA,eAEA,CAEA,4SACC,sBAIF,qOACC,YAED,wNACC,WACA,yBAED,yLACC,oBAKD,mOACC,yBAIH,uJAGC,qCACA,mBAEA,YAJA,cADA,kBAIA,SACA,CAED,+IAGC,aADA,YAIA,mBADA,uBAJA,kBAGA,UAEA,CACA,8KAEC,iBACA,oBAFA,WAEA,CACA,4MACC,oBAED,wNAEC,YAED,iOACC,6BACA,sBAED,yNAEC,YADA,kBACA,CAED,4NACC,sBAED,gNACC,SAED,gQACC,kBAGF,yLACC,mBAOL,oCACC,kBACA,QACA,SAID,iFAWC,wCATA,mBAWA,kBADA,0BAFA,wBAPA,mBAEA,iBACA,iBAGA,yBADA,YADA,SAHA,qBASA,CACA,uJAEC,4CADA,YACA,CACA,qNAEC,eADA,gBAEA,iBAED,iOACC,eACA,iBAGF,2JAIC,eACA,aAHA,+BACA,qBAFA,oBAIA,CACA,6NACC,aACA,qOACC,UAGA,yXACC,YAGF,+PACC,iBAGF,6NACC,gBAGF,yJAEC,mBADA,UACA,CAGC,6RACC,mCAGF,+NACC,YACA,WAGF,+JAEC,QADA,YAEA,cACA,mOAGC,kDAGA,mBAJA,6BAEA,eAHA,gBAMA,0BAFA,kBAEA,CAOD,scACC,wCACA,yBAGF,6JAIC,0CAHA,aAEA,eADA,UAEA,CACA,2KAIC,wCADA,wCADA,kBADA,yBAMA,+CADA,wCADA,yBAEA,CChqBH,kCAEC,oCACA,kBACA,0BAHA,gBAGA,CAEA,iEACC,kBAED,wEAGC,YADA,kBADA,OAEA,CACA,2EACC,kBACA,WAED,4GAIC,gBADA,WADA,kBADA,SAGA,CAED,6GAIC,gBADA,UADA,kBADA,SAGA,CAqBF,2EAEC,YAEA,4BAHA,WAEA,WACA,CAEA,iFACC,kBAID,sHACC,cAGA,+IACC,yBAIF,qHAEC,mBADA,SACA,CAGD,kHACC,WACA,eAIH,sDAEC,kBADA,UACA,CAID,wFAQC,wCAEA,kBADA,0BALA,iBAGA,yBADA,UADA,UAFA,sBADA,UAQA,CACA,gKAEC,4CADA,YACA,CACA,kOACC,eACA,gBAIF,kLAEC,eACA,gBACA,mBAHA,iBAGA,CACA,gPACC,yBAED,0PACC,WAEA,gVACC,yBACA,iBAED,kXACC,4BAIH,kGACC,0BAED,4KAEC,yCADA,aAEA,eACA,kPACC,wCACA,yBAGA,+CADA,wCADA,yBAEA,CACA,wSACC,yBACA,oBAMJ,kFAMC,wCAEA,kBADA,0BAFA,yBAFA,sBADA,SAMA,CAEA,gKACC,iBAEA,YADA,SACA,CAGD,oKAEC,WAGA,aADA,UADA,UAFA,qBAIA,CAED,0JAEC,4CADA,YACA,CACA,4NACC,eACA,gBACA,aAIF,wJACC,eACA,2BAGD,wJACC,yBACA,cACA,oBACA,oOACC,uBAED,gYACC,kBAIF,gKAKC,yCAHA,SAEA,aAHA,kBAEA,gBAEA,CAEA,8KAGC,wCADA,kBAEA,6BAGA,+CANA,iBAKA,wCADA,yBAEA,CAGD,kPACC,kDACA,mCAEA,sTACC,mCACA,YAMJ,wCAMC,2BADA,YAFA,OAKA,oBAPA,eACA,MAEA,WAGA,SACA,CAGD,iDAGC,eAFA,kBAGA,WAFA,SAGA,WAED,kDACC,aACA,UAGA,UAFA,kBACA,SACA,CAED,uDACC,kBACA,aACA,SACA,WAGD,8CAKC,wCACA,YACA,mBACA,yBACA,eACA,eAEA,YADA,kBAVA,kBACA,WAEA,SASA,YAVA,UAUA,CAED,sCACC,WAED,oCAMC,aACA,sBANA,2BAEA,eAEA,kBADA,UAFA,uBAKA,CAEA,mEACC,iBAGD,qEACC,gBAGF,6CAEC,YADA,kBAEA,YAED,4CAEC,YADA,kBAEA,WAED,qDAEC,YACA,aACA,UAHA,iBAGA,CAED,mDAMC,YACA,kBAIA,iBATA,WASA,CAED,oGARC,yBAGA,yBACA,eACA,eATA,kBAGA,MADA,UAqBA,CAXD,iDAMC,YACA,mBAIA,kBATA,UASA,CAED,uCAGC,mBAFA,mBACA,YAEA,yBACA,0GACC,WAIF,mCACC,yBACA,iCAEA,yCACC", "sources": ["styles/global.scss", "assets/icons.scss", "components/user/UserStyles.module.scss", "components/account/AccountStle.module.scss", "components/settings/UserRoles.module.scss", "components/organization/OrganizationStyles.module.scss", "components/login/welcomeback.module.scss", "components/adminMenu/Adminmenu.scss", "styles/rtl-styles.scss", "../../../../../node_modules/_perfect-scrollbar@1.5.0@perfect-scrollbar/css/perfect-scrollbar.css", "../node_modules/react-perfect-scrollbar/dist/css/styles.css", "../../../../../src/styles.scss"], "sourcesContent": [".common-icon{text-align:center;width:60px;height:60px;padding-top:10px;margin:auto;background:#ccc;border-radius:100%}.common-icon:before{font-family:\"qadapt-icons\" !important;font-size:36px;font-weight:100;color:#444}.fa-analysis:before{content:\"ꀃ\"}.fa-attendance-machine:before{content:\"ꀄ\"}.fa-bike-insurance:before{content:\"ꀅ\"}.fa-bill-receipt:before{content:\"ꀆ\"}.fa-business-communication:before{content:\"ꀇ\"}.fa-business-investment:before{content:\"ꀈ\"}.fa-business-management:before{content:\"ꀉ\"}.fa-businessman-with-briefcase:before{content:\"ꀐ\"}.fa-business-presentation:before{content:\"ꀑ\"}.fa-business-professional:before{content:\"ꀒ\"}.fa-business-profit:before{content:\"ꀓ\"}.fa-business-relationship:before{content:\"ꀔ\"}.fa-buyer:before{content:\"ꀕ\"}.fa-career:before{content:\"ꀖ\"}.fa-car-insurance:before{content:\"ꀗ\"}.fa-car-repair-mechanic:before{content:\"ꀘ\"}.fa-cashier:before{content:\"ꀙ\"}.fa-ceo:before{content:\"ꀠ\"}.fa-client:before{content:\"ꀡ\"}.fa-clients:before{content:\"ꀢ\"}.fa-closed:before{content:\"ꀣ\"}.fa-contract:before{content:\"ꀤ\"}.fa-core-values:before{content:\"ꀥ\"}.fa-corporate:before{content:\"ꀦ\"}.fa-credit-card-swipe:before{content:\"ꀧ\"}.fa-crm-browser:before{content:\"ꀨ\"}.fa-customer-experience:before{content:\"ꀩ\"}.fa-customer-journey:before{content:\"ꀰ\"}.fa-data-analytics:before{content:\"ꀱ\"}.fa-data-science:before{content:\"ꀲ\"}.fa-document-application:before{content:\"ꀳ\"}.fa-document-application-woman:before{content:\"ꀴ\"}.fa-erp:before{content:\"ꀵ\"}.fa-factory-pollution:before{content:\"ꀶ\"}.fa-family-insurance:before{content:\"ꀷ\"}.fa-female-reporter-journalist:before{content:\"ꀸ\"}.fa-fire-insurance:before{content:\"ꀹ\"}.fa-food-industry:before{content:\"ꁀ\"}.fa-general-insurance:before{content:\"ꁁ\"}.fa-growing-market-analysis:before{content:\"ꁂ\"}.fa-gst:before{content:\"ꁃ\"}.fa-headquarter:before{content:\"ꁄ\"}.fa-health-insurance:before{content:\"ꁅ\"}.fa-hierarchy-management:before{content:\"ꁆ\"}.fa-hierarchy-management-task:before{content:\"ꁇ\"}.fa-home-insurance:before{content:\"ꁈ\"}.fa-import-product:before{content:\"ꁉ\"}.fa-improvement-performance:before{content:\"ꁐ\"}.fa-income-taxes:before{content:\"ꁑ\"}.fa-influencer:before{content:\"ꁒ\"}.fa-insight:before{content:\"ꁓ\"}.fa-inspection:before{content:\"ꁔ\"}.fa-insurance-protection:before{content:\"ꁕ\"}.fa-integration:before{content:\"ꁖ\"}.fa-interview:before{content:\"ꁗ\"}.fa-investor:before{content:\"ꁘ\"}.fa-invoice:before{content:\"ꁙ\"}.fa-job:before{content:\"ꁠ\"}.fa-job-search:before{content:\"ꁡ\"}.fa-male-reporter-journalist:before{content:\"ꁢ\"}.fa-management:before{content:\"ꁣ\"}.fa-manufacturing-production:before{content:\"ꁤ\"}.fa-market-research:before{content:\"ꁥ\"}.fa-market-share:before{content:\"ꁦ\"}.fa-mechanic:before{content:\"ꁧ\"}.fa-meeting:before{content:\"ꁨ\"}.fa-meeting-table:before{content:\"ꁩ\"}.fa-mind-map:before{content:\"ꁰ\"}.fa-money-transfer:before{content:\"ꁱ\"}.fa-new-product:before{content:\"ꁲ\"}.fa-newspaper:before{content:\"ꁳ\"}.fa-newspaper-jobs:before{content:\"ꁴ\"}.fa-office:before{content:\"ꁵ\"}.fa-online-survey:before{content:\"ꁶ\"}.fa-online-work:before{content:\"ꁷ\"}.fa-pending-work:before{content:\"ꁸ\"}.fa-person-insurance:before{content:\"ꁹ\"}.fa-pilot:before{content:\"ꂀ\"}.fa-planning:before{content:\"ꂁ\"}.fa-plumbing:before{content:\"ꂂ\"}.fa-power-plant:before{content:\"ꂃ\"}.fa-product-development:before{content:\"ꂄ\"}.fa-productivity:before{content:\"ꂅ\"}.fa-product-launch-release:before{content:\"ꂆ\"}.fa-project:before{content:\"ꂇ\"}.fa-project-management:before{content:\"ꂈ\"}.fa-project-management-timeline:before{content:\"ꂉ\"}.fa-project-manager:before{content:\"ꂐ\"}.fa-project-work:before{content:\"ꂑ\"}.fa-quality-control:before{content:\"ꂒ\"}.fa-receipt:before{content:\"ꂓ\"}.fa-remote-work:before{content:\"ꂔ\"}.fa-repairing:before{content:\"ꂕ\"}.fa-retail-shop:before{content:\"ꂖ\"}.fa-satisfaction:before{content:\"ꂗ\"}.fa-seller:before{content:\"ꂘ\"}.fa-service-desk:before{content:\"ꂙ\"}.fa-services:before{content:\"ꄀ\"}.fa-solution:before{content:\"ꄁ\"}.fa-strategist:before{content:\"ꄂ\"}.fa-successful-businessman:before{content:\"ꄃ\"}.fa-supervisor:before{content:\"ꄄ\"}.fa-supply-chain:before{content:\"ꄅ\"}.fa-tax-calculator:before{content:\"ꄆ\"}.fa-tax-cut:before{content:\"ꄇ\"}.fa-tax-return:before{content:\"ꄈ\"}.fa-team:before{content:\"ꄉ\"}.fa-team-meeting:before{content:\"ꄐ\"}.fa-technician:before{content:\"ꄑ\"}.fa-trade:before{content:\"ꄒ\"}.fa-user-network:before{content:\"ꄓ\"}.fa-value:before{content:\"ꄔ\"}.fa-vat:before{content:\"ꄕ\"}.fa-video-conference:before{content:\"ꄖ\"}.fa-virtual-meeting:before{content:\"ꄗ\"}.fa-meeting-room:before{content:\"ꄘ\"}.fa-workflow:before{content:\"ꄙ\"}.fa-working-hours:before{content:\"ꄠ\"}.fa-working-on-office:before{content:\"ꄡ\"}.fa-working-time:before{content:\"ꄢ\"}.fa-workplace:before{content:\"ꄣ\"}.fa-workshop:before{content:\"ꄤ\"}.fa-waiting-room-area:before{content:\"ꄥ\"}.fa-user-tie-solid:before{content:\"ꄧ\"}.fa-caret-up-solid:before{content:\"ꄨ\"}.fa-check-circle-solid:before{content:\"ꄩ\"}.fa-times-circle-solid:before{content:\"ꄰ\"}.fa-password-reset:before{content:\"ꄱ\"}.fa-password:before{content:\"ꄲ\"}.fa-reset-password:before{content:\"ꄳ\"}.fa-men-gear-circle:before{content:\"ꄴ\"}.fa-men-gear:before{content:\"ꄵ\"}.fa-light-ceilinglight:before{content:\"ꅇ\"}.fa-exclamation-circle-solid:before{content:\"ꅈ\"}.fa-add-multi-dtrecords:before{content:\"ꅉ\"}.fa-branch:before{content:\"ꅐ\"}.fa-deploy-log-download:before{content:\"ꅑ\"}.fa-deployment-log:before{content:\"ꅒ\"}.fa-deploy-rollback:before{content:\"ꅓ\"}.fa-dt-download:before{content:\"ꅔ\"}.fa-ds-filter:before{content:\"ꅕ\"}.fa-dt-functions:before{content:\"ꅖ\"}.fa-import-dtrecords:before{content:\"ꅗ\"}.fa-dt-index:before{content:\"ꅘ\"}.fa-ds-join:before{content:\"ꅙ\"}.fa-manage-stages:before{content:\"ꅠ\"}.fa-records:before{content:\"ꅡ\"}.fa-regular-view:before{content:\"ꅢ\"}.fa-dt-sync:before{content:\"ꅣ\"}.fa-timeline-view:before{content:\"ꅤ\"}.fa-manage-apps:before{content:\"ꅥ\"}.fa-box-key:before{content:\"ꅦ\"}.fa-deploy-branch:before{content:\"ꅧ\"}.fa-version-manage:before{content:\"ꅨ\"}.fa-add-ds:before{content:\"ꅩ\"}.fa-add-dttable:before{content:\"ꅰ\"}.fa-admenu:before{content:\"ꅱ\"}.fa-apps:before{content:\"ꅲ\"}.fa-appstore:before{content:\"ꅳ\"}.fa-crreport:before{content:\"ꅴ\"}.fa-crview:before{content:\"ꅵ\"}.fa-datasource:before{content:\"ꅶ\"}.fa-dsref:before{content:\"ꅷ\"}.fa-dttable:before{content:\"ꅸ\"}.fa-imp-user:before{content:\"ꅹ\"}.fa-ip-white:before{content:\"ꆀ\"}.fa-more:before{content:\"ꆁ\"}.fa-multi-user:before{content:\"ꆂ\"}.fa-pref:before{content:\"ꆃ\"}.fa-config-sms:before{content:\"ꆄ\"}.fa-ham-menu:before{content:\"ꆅ\"}.fa-myroles:before{content:\"ꆆ\"}.fa-dots:before{content:\"ꆇ\"}.fa-add-field:before{content:\"ꆈ\"}.fa-add-plus:before{content:\"ꆉ\"}.fa-avatar:before{content:\"ꆐ\"}.fa-back-arrow:before{content:\"ꆑ\"}.fa-close:before{content:\"ꆒ\"}.fa-close-1:before{content:\"ꆓ\"}.fa-copy-icon:before{content:\"ꆔ\"}.fa-drag:before{content:\"ꆕ\"}.fa-editprofile:before{content:\"ꆖ\"}.fa-group-by:before{content:\"ꆗ\"}.fa-integrations:before{content:\"ꆘ\"}.fa-logout:before{content:\"ꆙ\"}.fa-caret-down-solid:before{content:\"ꈀ\"}.fa-sort-down-solid:before{content:\"ꈁ\"}.fa-mpin:before{content:\"ꈂ\"}.fa-no-notifications:before{content:\"ꈃ\"}.fa-notask:before{content:\"ꈄ\"}.fa-password-lock:before{content:\"ꈅ\"}.fa-preferences:before{content:\"ꈆ\"}.fa-process:before{content:\"ꈇ\"}.fa-profile-notifications:before{content:\"ꈈ\"}.fa-profile-user:before{content:\"ꈉ\"}.fa-reassign:before{content:\"ꈐ\"}.fa-reportproblem:before{content:\"ꈑ\"}.fa-right-arrow:before{content:\"ꈒ\"}.fa-sort:before{content:\"ꈓ\"}.fa-validation:before{content:\"ꈔ\"}.fa-add-record:before{content:\"ꈕ\"}.fa-dafts:before{content:\"ꈖ\"}.fa-dashboard:before{content:\"ꈗ\"}.fa-initiated:before{content:\"ꈘ\"}.fa-manage-app:before{content:\"ꈙ\"}.fa-menu:before{content:\"ꈠ\"}.fa-participated:before{content:\"ꈡ\"}.fa-reports:before{content:\"ꈢ\"}.fa-requests:before{content:\"ꈣ\"}.fa-tasks-circle:before{content:\"ꈤ\"}.fa-tasks_old:before{content:\"ꈥ\"}.fa-solutionview:before{content:\"ꈦ\"}.fa-meeting-room-light:before{content:\"ꈧ\"}.fa-external-rdbms:before{content:\"ꈨ\"}.fa-pin-inclined:before{content:\"ꈩ\"}.fa-generate-data:before{content:\"ꈰ\"}.fa-dt-filter:before{content:\"ꈱ\"}.fa-export-settings:before{content:\"ꐀ\"}.fa-caravan-alt:before{content:\"\"}.fa-cat-space:before{content:\"\"}.fa-coffee-pot:before{content:\"\"}.fa-comet:before{content:\"\"}.fa-fan-table:before{content:\"\"}.fa-faucet:before{content:\"\"}.fa-faucet-drip:before{content:\"\"}.fa-galaxy:before{content:\"\"}.fa-garage:before{content:\"\"}.fa-garage-car:before{content:\"\"}.fa-garage-open:before{content:\"\"}.fa-heat:before{content:\"\"}.fa-house-day:before{content:\"\"}.fa-house-leave:before{content:\"\"}.fa-house-night:before{content:\"\"}.fa-house-return:before{content:\"\"}.fa-house-signal:before{content:\"\"}.fa-lamp-desk:before{content:\"\"}.fa-lamp-floor:before{content:\"\"}.fa-light-ceiling:before{content:\"\"}.fa-light-switch:before{content:\"\"}.fa-light-switch-off:before{content:\"\"}.fa-microwave:before{content:\"\"}.fa-raygun:before{content:\"\"}.fa-rocket-launch:before{content:\"\"}.fa-coffin-cross:before{content:\"\"}.fa-folder-download:before{content:\"\"}.fa-folder-upload:before{content:\"\"}.fa-bacteria:before{content:\"\"}.fa-bacterium:before{content:\"\"}.fa-box-tissue:before{content:\"\"}.fa-hand-holding-medical:before{content:\"\"}.fa-hand-sparkles:before{content:\"\"}.fa-hands-wash:before{content:\"\"}.fa-handshake-alt-slash:before{content:\"\"}.fa-handshake-slash:before{content:\"\"}.fa-head-side-cough:before{content:\"\"}.fa-head-side-cough-slash:before{content:\"\"}.fa-head-side-mask:before{content:\"\"}.fa-head-side-virus:before{content:\"\"}.fa-house-user:before{content:\"\"}.fa-laptop-house:before{content:\"\"}.fa-lungs-virus:before{content:\"\"}.fa-angle-double-up:before{content:\"\"}.fa-drum-light:before{content:\"\"}.fa-file-signature:before{content:\"\"}.fa-horse-head-light:before{content:\"\"}.fa-image-light:before{content:\"\"}.fa-inventory-light:before{content:\"\"}.fa-line-columns-light:before{content:\"\"}.fa-location-arrow-light:before{content:\"\"}.fa-location-circle:before{content:\"\"}.fa-mailbox-light:before{content:\"\"}.fa-map-marker-light:before{content:\"\"}.fa-mug-tea:before{content:\"\"}.fa-music-alt-slash-light:before{content:\"\"}.fa-network-wired-light:before{content:\"\"}.fa-neuter-light:before{content:\"\"}.fa-notes-medical-light:before{content:\"\"}.fa-object-ungroup-light:before{content:\"\"}.fa-oil-temp-light:before{content:\"\"}.fa-otter-light:before{content:\"\"}.fa-outdent-light:before{content:\"\"}.fa-outlet-light:before{content:\"\"}.fa-oven-light:before{content:\"\"}.fa-overline-light:before{content:\"\"}.fa-page-break-light:before{content:\"\"}.fa-chevron-left-light:before{content:\"\"}.fa-mobile-android-light:before{content:\"\"}.fa-comments-alt-dollar-light:before{content:\"\"}.fa-bus-alt:before{content:\"\"}.fa-bars-light---f0c9:before{content:\"\"}.fa-bath:before{content:\"\"}.fa-user-tag:before{content:\"\"}.fa-trophy-alt:before{content:\"\"}.fa-file-light---f15b:before{content:\"\"}.fa-grip-horizontal-light---f58d:before{content:\"\"}.fa-blinds-open:before{content:\"\"}.fa-mailbox-light---f813:before{content:\"\"}.fa-glass-martini:before{content:\"\"}.fa-music:before{content:\"\"}.fa-search:before{content:\"\"}.fa-heart:before{content:\"\"}.fa-star:before{content:\"\"}.fa-user:before{content:\"\"}.fa-film:before{content:\"\"}.fa-th:before{content:\"\"}.fa-check:before{content:\"\"}.fa-times:before{content:\"\"}.fa-search-plus:before{content:\"\"}.fa-search-minus:before{content:\"\"}.fa-power-off:before{content:\"\"}.fa-signal:before{content:\"\"}.fa-cog:before{content:\"\"}.fa-home:before{content:\"\"}.fa-clock:before{content:\"\"}.fa-road:before{content:\"\"}.fa-download:before{content:\"\"}.fa-inbox:before{content:\"\"}.fa-redo:before{content:\"\"}.fa-sync:before{content:\"\"}.fa-list-alt:before{content:\"\"}.fa-lock:before{content:\"\"}.fa-flag:before{content:\"\"}.fa-headphones:before{content:\"\"}.fa-volume-up:before{content:\"\"}.fa-qrcode:before{content:\"\"}.fa-barcode:before{content:\"\"}.fa-tag:before{content:\"\"}.fa-book:before{content:\"\"}.fa-bookmark:before{content:\"\"}.fa-print:before{content:\"\"}.fa-camera:before{content:\"\"}.fa-font:before{content:\"\"}.fa-bold:before{content:\"\"}.fa-italic:before{content:\"\"}.fa-text-width:before{content:\"\"}.fa-align-left:before{content:\"\"}.fa-align-center:before{content:\"\"}.fa-align-right:before{content:\"\"}.fa-align-justify:before{content:\"\"}.fa-list:before{content:\"\"}.fa-indent:before{content:\"\"}.fa-video:before{content:\"\"}.fa-image:before{content:\"\"}.fa-pencil:before{content:\"\"}.fa-map-marker:before{content:\"\"}.fa-adjust:before{content:\"\"}.fa-tint:before{content:\"\"}.fa-edit:before{content:\"\"}.fa-arrows:before{content:\"\"}.fa-fast-backward:before{content:\"\"}.fa-backward:before{content:\"\"}.fa-stop:before{content:\"\"}.fa-forward:before{content:\"\"}.fa-fast-forward:before{content:\"\"}.fa-eject:before{content:\"\"}.fa-chevron-left:before{content:\"\"}.fa-chevron-right:before{content:\"\"}.fa-plus-circle:before{content:\"\"}.fa-minus-circle:before{content:\"\"}.fa-times-circle:before{content:\"\"}.fa-check-circle:before{content:\"\"}.fa-question-circle:before{content:\"\"}.fa-info-circle:before{content:\"\"}.fa-crosshairs:before{content:\"\"}.fa-ban:before{content:\"\"}.fa-arrow-left:before{content:\"\"}.fa-arrow-right:before{content:\"\"}.fa-arrow-up:before{content:\"\"}.fa-arrow-down:before{content:\"\"}.fa-share:before{content:\"\"}.fa-expand:before{content:\"\"}.fa-compress:before{content:\"\"}.fa-plus:before{content:\"\"}.fa-minus:before{content:\"\"}.fa-asterisk:before{content:\"\"}.fa-exclamation-circle:before{content:\"\"}.fa-gift:before{content:\"\"}.fa-leaf:before{content:\"\"}.fa-fire:before{content:\"\"}.fa-eye:before{content:\"\"}.fa-eye-slash:before{content:\"\"}.fa-exclamation-triangle:before{content:\"\"}.fa-plane:before{content:\"\"}.fa-calendar-alt:before{content:\"\"}.fa-random:before{content:\"\"}.fa-comment:before{content:\"\"}.fa-magnet:before{content:\"\"}.fa-chevron-up:before{content:\"\"}.fa-chevron-down:before{content:\"\"}.fa-shopping-cart:before{content:\"\"}.fa-folder:before{content:\"\"}.fa-folder-open:before{content:\"\"}.fa-arrows-v:before{content:\"\"}.fa-arrows-h:before{content:\"\"}.fa-chart-bar:before{content:\"\"}.fa-camera-retro:before{content:\"\"}.fa-key:before{content:\"\"}.fa-cogs:before{content:\"\"}.fa-comments:before{content:\"\"}.fa-sign-out:before{content:\"\"}.fa-thumbtack:before{content:\"\"}.fa-external-link:before{content:\"\"}.fa-upload:before{content:\"\"}.fa-lemon:before{content:\"\"}.fa-phone-square:before{content:\"\"}.fa-credit-card:before{content:\"\"}.fa-rss:before{content:\"\"}.fa-hdd:before{content:\"\"}.fa-bullhorn:before{content:\"\"}.fa-certificate:before{content:\"\"}.fa-hand-point-right:before{content:\"\"}.fa-hand-point-left:before{content:\"\"}.fa-hand-point-up:before{content:\"\"}.fa-hand-point-down:before{content:\"\"}.fa-arrow-circle-left:before{content:\"\"}.fa-arrow-circle-right:before{content:\"\"}.fa-arrow-circle-up:before{content:\"\"}.fa-arrow-circle-down:before{content:\"\"}.fa-globe:before{content:\"\"}.fa-wrench:before{content:\"\"}.fa-tasks:before{content:\"\"}.fa-filter:before{content:\"\"}.fa-briefcase:before{content:\"\"}.fa-arrows-alt:before{content:\"\"}.fa-users:before{content:\"\"}.fa-link:before{content:\"\"}.fa-cloud:before{content:\"\"}.fa-flask:before{content:\"\"}.fa-cut:before{content:\"\"}.fa-copy:before{content:\"\"}.fa-paperclip:before{content:\"\"}.fa-save:before{content:\"\"}.fa-square:before{content:\"\"}.fa-bars:before{content:\"\"}.fa-list-ul:before{content:\"\"}.fa-list-ol:before{content:\"\"}.fa-table:before{content:\"\"}.fa-magic:before{content:\"\"}.fa-truck:before{content:\"\"}.fa-money-bill:before{content:\"\"}.fa-caret-down:before{content:\"\"}.fa-caret-up:before{content:\"\"}.fa-caret-left:before{content:\"\"}.fa-caret-right:before{content:\"\"}.fa-columns:before{content:\"\"}.fa-sort-down:before{content:\"\"}.fa-envelope:before{content:\"\"}.fa-undo:before{content:\"\"}.fa-gavel:before{content:\"\"}.fa-tachometer:before{content:\"\"}.fa-bolt:before{content:\"\"}.fa-sitemap:before{content:\"\"}.fa-umbrella:before{content:\"\"}.fa-paste:before{content:\"\"}.fa-lightbulb:before{content:\"\"}.fa-exchange:before{content:\"\"}.fa-cloud-download:before{content:\"\"}.fa-cloud-upload:before{content:\"\"}.fa-user-md:before{content:\"\"}.fa-stethoscope:before{content:\"\"}.fa-bell:before{content:\"\"}.fa-coffee:before{content:\"\"}.fa-hospital:before{content:\"\"}.fa-ambulance:before{content:\"\"}.fa-medkit:before{content:\"\"}.fa-fighter-jet:before{content:\"\"}.fa-beer:before{content:\"\"}.fa-h-square:before{content:\"\"}.fa-plus-square:before{content:\"\"}.fa-angle-double-left:before{content:\"\"}.fa-angle-double-right:before{content:\"\"}.fa-angle-double-down:before{content:\"\"}.fa-angle-left:before{content:\"\"}.fa-angle-right:before{content:\"\"}.fa-angle-up:before{content:\"\"}.fa-angle-down:before{content:\"\"}.fa-desktop:before{content:\"\"}.fa-laptop:before{content:\"\"}.fa-mobile:before{content:\"\"}.fa-quote-left:before{content:\"\"}.fa-quote-right:before{content:\"\"}.fa-spinner:before{content:\"\"}.fa-circle:before{content:\"\"}.fa-smile:before{content:\"\"}.fa-frown:before{content:\"\"}.fa-meh:before{content:\"\"}.fa-gamepad:before{content:\"\"}.fa-keyboard:before{content:\"\"}.fa-flag-checkered:before{content:\"\"}.fa-terminal:before{content:\"\"}.fa-code:before{content:\"\"}.fa-location-arrow:before{content:\"\"}.fa-crop:before{content:\"\"}.fa-code-branch:before{content:\"\"}.fa-info:before{content:\"\"}.fa-exclamation:before{content:\"\"}.fa-eraser:before{content:\"\"}.fa-puzzle-piece:before{content:\"\"}.fa-microphone:before{content:\"\"}.fa-microphone-slash:before{content:\"\"}.fa-shield:before{content:\"\"}.fa-calendar:before{content:\"\"}.fa-fire-extinguisher:before{content:\"\"}.fa-rocket:before{content:\"\"}.fa-chevron-circle-left:before{content:\"\"}.fa-chevron-circle-right:before{content:\"\"}.fa-chevron-circle-up:before{content:\"\"}.fa-chevron-circle-down:before{content:\"\"}.fa-css3:before{content:\"\"}.fa-anchor:before{content:\"\"}.fa-unlock-alt:before{content:\"\"}.fa-bullseye:before{content:\"\"}.fa-ellipsis-h:before{content:\"\"}.fa-ellipsis-v:before{content:\"\"}.fa-play-circle:before{content:\"\"}.fa-ticket:before{content:\"\"}.fa-minus-square:before{content:\"\"}.fa-level-up:before{content:\"\"}.fa-level-down:before{content:\"\"}.fa-check-square:before{content:\"\"}.fa-external-link-square:before{content:\"\"}.fa-share-square:before{content:\"\"}.fa-compass:before{content:\"\"}.fa-caret-square-down:before{content:\"\"}.fa-caret-square-up:before{content:\"\"}.fa-caret-square-right:before{content:\"\"}.fa-euro-sign:before{content:\"\"}.fa-pound-sign:before{content:\"\"}.fa-dollar-sign:before{content:\"\"}.fa-rupee-sign:before{content:\"\"}.fa-yen-sign:before{content:\"\"}.fa-ruble-sign:before{content:\"\"}.fa-file:before{content:\"\"}.fa-file-alt:before{content:\"\"}.fa-sort-numeric-down:before{content:\"\"}.fa-thumbs-up:before{content:\"\"}.fa-thumbs-down:before{content:\"\"}.fa-adn:before{content:\"\"}.fa-bitbucket:before{content:\"\"}.fa-long-arrow-down:before{content:\"\"}.fa-long-arrow-up:before{content:\"\"}.fa-long-arrow-left:before{content:\"\"}.fa-long-arrow-right:before{content:\"\"}.fa-android:before{content:\"\"}.fa-female:before{content:\"\"}.fa-male:before{content:\"\"}.fa-sun:before{content:\"\"}.fa-moon:before{content:\"\"}.fa-archive:before{content:\"\"}.fa-bug:before{content:\"\"}.fa-pagelines:before{content:\"\"}.fa-caret-square-left:before{content:\"\"}.fa-dot-circle:before{content:\"\"}.fa-wheelchair:before{content:\"\"}.fa-lira-sign:before{content:\"\"}.fa-space-shuttle:before{content:\"\"}.fa-envelope-square:before{content:\"\"}.fa-openid:before{content:\"\"}.fa-university:before{content:\"\"}.fa-graduation-cap:before{content:\"\"}.fa-google:before{content:\"\"}.fa-stumbleupon:before{content:\"\"}.fa-drupal:before{content:\"\"}.fa-language:before{content:\"\"}.fa-fax:before{content:\"\"}.fa-building:before{content:\"\"}.fa-child:before{content:\"\"}.fa-paw:before{content:\"\"}.fa-cube:before{content:\"\"}.fa-cubes:before{content:\"\"}.fa-behance:before{content:\"\"}.fa-behance-square:before{content:\"\"}.fa-recycle:before{content:\"\"}.fa-car:before{content:\"\"}.fa-taxi:before{content:\"\"}.fa-tree:before{content:\"\"}.fa-deviantart:before{content:\"\"}.fa-database:before{content:\"\"}.fa-file-pdf:before{content:\"\"}.fa-file-word:before{content:\"\"}.fa-file-excel:before{content:\"\"}.fa-file-powerpoint:before{content:\"\"}.fa-file-image:before{content:\"\"}.fa-file-archive:before{content:\"\"}.fa-file-audio:before{content:\"\"}.fa-file-video:before{content:\"\"}.fa-file-code:before{content:\"\"}.fa-vine:before{content:\"\"}.fa-codepen:before{content:\"\"}.fa-life-ring:before{content:\"\"}.fa-circle-notch:before{content:\"\"}.fa-rebel:before{content:\"\"}.fa-qq:before{content:\"\"}.fa-paper-plane:before{content:\"\"}.fa-history:before{content:\"\"}.fa-heading:before{content:\"\"}.fa-paragraph:before{content:\"\"}.fa-share-alt:before{content:\"\"}.fa-bomb:before{content:\"\"}.fa-futbol:before{content:\"\"}.fa-binoculars:before{content:\"\"}.fa-plug:before{content:\"\"}.fa-newspapers:before{content:\"\"}.fa-wifi:before{content:\"\"}.fa-calculator:before{content:\"\"}.fa-cc-visa:before{content:\"\"}.fa-cc-mastercard:before{content:\"\"}.fa-cc-discover:before{content:\"\"}.fa-cc-amex:before{content:\"\"}.fa-cc-paypal:before{content:\"\"}.fa-cc-stripe:before{content:\"\"}.fa-bell-slash:before{content:\"\"}.fa-trash:before{content:\"\"}.fa-copyright:before{content:\"\"}.fa-at:before{content:\"\"}.fa-eye-dropper:before{content:\"\"}.fa-paint-brush:before{content:\"\"}.fa-birthday-cake:before{content:\"\"}.fa-chart-area:before{content:\"\"}.fa-chart-pie:before{content:\"\"}.fa-chart-line:before{content:\"\"}.fa-toggle-off:before{content:\"\"}.fa-toggle-on:before{content:\"\"}.fa-bicycle:before{content:\"\"}.fa-bus:before{content:\"\"}.fa-angellist:before{content:\"\"}.fa-closed-captioning:before{content:\"\"}.fa-buysellads:before{content:\"\"}.fa-connectdevelop:before{content:\"\"}.fa-dashcube:before{content:\"\"}.fa-cart-plus:before{content:\"\"}.fa-cart-arrow-down:before{content:\"\"}.fa-diamond:before{content:\"\"}.fa-ship:before{content:\"\"}.fa-motorcycle:before{content:\"\"}.fa-heartbeat:before{content:\"\"}.fa-mars:before{content:\"\"}.fa-mercury:before{content:\"\"}.fa-mars-double:before{content:\"\"}.fa-mars-stroke:before{content:\"\"}.fa-mars-stroke-v:before{content:\"\"}.fa-mars-stroke-h:before{content:\"\"}.fa-genderless:before{content:\"\"}.fa-whatsapp:before{content:\"\"}.fa-server:before{content:\"\"}.fa-user-plus:before{content:\"\"}.fa-user-times:before{content:\"\"}.fa-bed:before{content:\"\"}.fa-train:before{content:\"\"}.fa-battery-full:before{content:\"\"}.fa-battery-three-quarters:before{content:\"\"}.fa-battery-half:before{content:\"\"}.fa-battery-quarter:before{content:\"\"}.fa-battery-empty:before{content:\"\"}.fa-mouse-pointer:before{content:\"\"}.fa-i-cursor:before{content:\"\"}.fa-object-group:before{content:\"\"}.fa-sticky-note:before{content:\"\"}.fa-cc-jcb:before{content:\"\"}.fa-cc-diners-club:before{content:\"\"}.fa-clone:before{content:\"\"}.fa-balance-scale:before{content:\"\"}.fa-hourglass-start:before{content:\"\"}.fa-hourglass-half:before{content:\"\"}.fa-hourglass-end:before{content:\"\"}.fa-hourglass:before{content:\"\"}.fa-hand-rock:before{content:\"\"}.fa-hand-paper:before{content:\"\"}.fa-hand-scissors:before{content:\"\"}.fa-hand-lizard:before{content:\"\"}.fa-hand-spock:before{content:\"\"}.fa-hand-pointer:before{content:\"\"}.fa-hand-peace:before{content:\"\"}.fa-trademark:before{content:\"\"}.fa-registered:before{content:\"\"}.fa-creative-commons:before{content:\"\"}.fa-gg-circle:before{content:\"\"}.fa-chrome:before{content:\"\"}.fa-tv:before{content:\"\"}.fa-contao:before{content:\"\"}.fa-500px:before{content:\"\"}.fa-amazon:before{content:\"\"}.fa-calendar-plus:before{content:\"\"}.fa-calendar-minus:before{content:\"\"}.fa-calendar-times:before{content:\"\"}.fa-calendar-check:before{content:\"\"}.fa-industry:before{content:\"\"}.fa-map-pin:before{content:\"\"}.fa-map-signs:before{content:\"\"}.fa-comment-alt:before{content:\"\"}.fa-black-tie:before{content:\"\"}.fa-codiepie:before{content:\"\"}.fa-pause-circle:before{content:\"\"}.fa-stop-circle:before{content:\"\"}.fa-hashtag:before{content:\"\"}.fa-bluetooth:before{content:\"\"}.fa-bluetooth-b:before{content:\"\"}.fa-universal-access:before{content:\"\"}.fa-blind:before{content:\"\"}.fa-audio-description:before{content:\"\"}.fa-braille:before{content:\"\"}.fa-assistive-listening-systems:before{content:\"\"}.fa-american-sign-language-interpreting:before{content:\"\"}.fa-deaf:before{content:\"\"}.fa-low-vision:before{content:\"\"}.fa-handshake:before{content:\"\"}.fa-envelope-open:before{content:\"\"}.fa-address-book:before{content:\"\"}.fa-address-card:before{content:\"\"}.fa-user-circle:before{content:\"\"}.fa-id-badge:before{content:\"\"}.fa-id-card:before{content:\"\"}.fa-thermometer-full:before{content:\"\"}.fa-shower:before{content:\"\"}.fa-podcast:before{content:\"\"}.fa-window-restore:before{content:\"\"}.fa-microchip:before{content:\"\"}.fa-snowflake:before{content:\"\"}.fa-watch:before{content:\"\"}.fa-utensils-alt:before{content:\"\"}.fa-trophy:before{content:\"\"}.fa-triangle:before{content:\"\"}.fa-trash-alt:before{content:\"\"}.fa-sync-alt:before{content:\"\"}.fa-stopwatch:before{content:\"\"}.fa-spade:before{content:\"\"}.fa-sign-out-alt:before{content:\"\"}.fa-sign-in-alt:before{content:\"\"}.fa-uniF2F7:before{content:\"\"}.fa-uniF2F8:before{content:\"\"}.fa-rectangle-landscape:before{content:\"\"}.fa-rectangle-portrait:before{content:\"\"}.fa-poo:before{content:\"\"}.fa-images:before{content:\"\"}.fa-pencil-alt-light:before{content:\"\"}.fa-octagon:before{content:\"\"}.fa-minus-hexagon:before{content:\"\"}.fa-minus-octagon:before{content:\"\"}.fa-long-arrow-alt-down:before{content:\"\"}.fa-long-arrow-alt-left:before{content:\"\"}.fa-long-arrow-alt-right:before{content:\"\"}.fa-long-arrow-alt-up:before{content:\"\"}.fa-lock-alt:before{content:\"\"}.fa-jack-o-lantern:before{content:\"\"}.fa-info-square:before{content:\"\"}.fa-inbox-in:before{content:\"\"}.fa-inbox-out:before{content:\"\"}.fa-hexagon:before{content:\"\"}.fa-h1:before{content:\"\"}.fa-h2:before{content:\"\"}.fa-h3:before{content:\"\"}.fa-file-check:before{content:\"\"}.fa-file-times:before{content:\"\"}.fa-file-minus:before{content:\"\"}.fa-file-plus:before{content:\"\"}.fa-file-exclamation:before{content:\"\"}.fa-file-edit:before{content:\"\"}.fa-expand-arrows:before{content:\"\"}.fa-expand-arrows-alt:before{content:\"\"}.fa-expand-wide:before{content:\"\"}.fa-exclamation-square:before{content:\"\"}.fa-chevron-double-down:before{content:\"\"}.fa-chevron-double-left:before{content:\"\"}.fa-chevron-double-right:before{content:\"\"}.fa-chevron-double-up:before{content:\"\"}.fa-compress-wide:before{content:\"\"}.fa-club:before{content:\"\"}.fa-clipboard:before{content:\"\"}.fa-chevron-square-down:before{content:\"\"}.fa-chevron-square-left:before{content:\"\"}.fa-chevron-square-right:before{content:\"\"}.fa-chevron-square-up:before{content:\"\"}.fa-caret-circle-down:before{content:\"\"}.fa-caret-circle-left:before{content:\"\"}.fa-caret-circle-right:before{content:\"\"}.fa-caret-circle-up:before{content:\"\"}.fa-camera-alt:before{content:\"\"}.fa-calendar-exclamation:before{content:\"\"}.fa-badge:before{content:\"\"}.fa-badge-check:before{content:\"\"}.fa-arrows-alt-h:before{content:\"\"}.fa-arrows-alt-v:before{content:\"\"}.fa-arrow-square-down:before{content:\"\"}.fa-arrow-square-left:before{content:\"\"}.fa-arrow-square-right:before{content:\"\"}.fa-arrow-square-up:before{content:\"\"}.fa-arrow-to-bottom:before{content:\"\"}.fa-arrow-to-left:before{content:\"\"}.fa-arrow-to-right:before{content:\"\"}.fa-arrow-to-top:before{content:\"\"}.fa-arrow-from-bottom:before{content:\"\"}.fa-arrow-from-left:before{content:\"\"}.fa-arrow-from-right:before{content:\"\"}.fa-arrow-from-top:before{content:\"\"}.fa-arrow-alt-from-bottom:before{content:\"\"}.fa-arrow-alt-from-left:before{content:\"\"}.fa-arrow-alt-from-right:before{content:\"\"}.fa-arrow-alt-from-top:before{content:\"\"}.fa-arrow-alt-to-bottom:before{content:\"\"}.fa-arrow-alt-to-left:before{content:\"\"}.fa-arrow-alt-to-right:before{content:\"\"}.fa-arrow-alt-to-top:before{content:\"\"}.fa-alarm-clock:before{content:\"\"}.fa-arrow-alt-square-down:before{content:\"\"}.fa-arrow-alt-square-left:before{content:\"\"}.fa-arrow-alt-square-right:before{content:\"\"}.fa-arrow-alt-square-up-:before{content:\"\"}.fa-arrow-alt-down:before{content:\"\"}.fa-arrow-alt-left:before{content:\"\"}.fa-arrow-alt-right:before{content:\"\"}.fa-arrow-alt-up:before{content:\"\"}.fa-arrow-alt-circle-down:before{content:\"\"}.fa-arrow-alt-circle-left:before{content:\"\"}.fa-arrow-alt-circle-right:before{content:\"\"}.fa-arrow-alt-circle-up:before{content:\"\"}.fa-external-link-alt:before{content:\"\"}.fa-external-link-square-alt:before{content:\"\"}.fa-exchange-alt:before{content:\"\"}.fa-repeat:before{content:\"\"}.fa-accessible-icon:before{content:\"\"}.fa-accusoft:before{content:\"\"}.fa-adversalbrands:before{content:\"\"}.fa-affiliatetheme:before{content:\"\"}.fa-algolia:before{content:\"\"}.fa-amilia:before{content:\"\"}.fa-app-store:before{content:\"\"}.fa-app-store-ios:before{content:\"\"}.fa-asymmetrik:before{content:\"\"}.fa-avianex:before{content:\"\"}.fa-aws:before{content:\"\"}.fa-battery-bolt:before{content:\"\"}.fa-battery-slash:before{content:\"\"}.fa-bitcoin:before{content:\"\"}.fa-bity:before{content:\"\"}.fa-blackberry:before{content:\"\"}.fa-blogger:before{content:\"\"}.fa-blogger-b:before{content:\"\"}.fa-browser:before{content:\"\"}.fa-buromobelexperte:before{content:\"\"}.fa-centercode:before{content:\"\"}.fa-cloud-download-alt:before{content:\"\"}.fa-cloud-upload-alt:before{content:\"\"}.fa-cloudscale:before{content:\"\"}.fa-cloudsmith:before{content:\"\"}.fa-cloudversify:before{content:\"\"}.fa-code-commit:before{content:\"\"}.fa-code-merge:before{content:\"\"}.fa-cpanel:before{content:\"\"}.fa-credit-card-blank:before{content:\"\"}.fa-credit-card-front:before{content:\"\"}.fa-css3-alt:before{content:\"\"}.fa-cuttlefish:before{content:\"\"}.fa-d-and-d:before{content:\"\"}.fa-deskpro:before{content:\"\"}.fa-desktop-alt:before{content:\"\"}.fa-ellipsis-v-alt:before{content:\"\"}.fa-film-alt:before{content:\"\"}.fa-gem:before{content:\"\"}.fa-industry-alt:before{content:\"\"}.fa-level-down-alt:before{content:\"\"}.fa-level-up-alt:before{content:\"\"}.fa-lock-open:before{content:\"\"}.fa-lock-open-alt:before{content:\"\"}.fa-map-marker-alt:before{content:\"\"}.fa-microphone-alt:before{content:\"\"}.fa-mobile-alt:before{content:\"\"}.fa-mobile-android:before{content:\"\"}.fa-mobile-android-alt:before{content:\"\"}.fa-money-bill-alt:before{content:\"\"}.fa-portrait:before{content:\"\"}.fa-reply:before{content:\"\"}.fa-sliders-v:before{content:\"\"}.fa-sliders-v-square:before{content:\"\"}.fa-user-alt:before{content:\"\"}.fa-window-alt:before{content:\"\"}.fa-apple-pay:before{content:\"\"}.fa-cc-apple-pay:before{content:\"\"}.fa-autoprefixer:before{content:\"\"}.fa-angular:before{content:\"\"}.fa-compress-alt:before{content:\"\"}.fa-expand-alt:before{content:\"\"}.fa-amazon-pay:before{content:\"\"}.fa-cc-amazon-pay:before{content:\"\"}.fa-baseball:before{content:\"\"}.fa-baseball-ball:before{content:\"\"}.fa-basketball-ball:before{content:\"\"}.fa-basketball-hoop:before{content:\"\"}.fa-bowling-ball:before{content:\"\"}.fa-bowling-pins:before{content:\"\"}.fa-boxing-glove:before{content:\"\"}.fa-chess:before{content:\"\"}.fa-chess-bishop:before{content:\"\"}.fa-chess-bishop-alt:before{content:\"\"}.fa-chess-board:before{content:\"\"}.fa-chess-clock:before{content:\"\"}.fa-chess-clock-alt:before{content:\"\"}.fa-chess-king:before{content:\"\"}.fa-chess-king-alt:before{content:\"\"}.fa-chess-knight:before{content:\"\"}.fa-chess-knight-alt:before{content:\"\"}.fa-chess-pawn:before{content:\"\"}.fa-chess-pawn-alt:before{content:\"\"}.fa-chess-queen:before{content:\"\"}.fa-chess-queen-alt:before{content:\"\"}.fa-chess-rook:before{content:\"\"}.fa-chess-rook-alt:before{content:\"\"}.fa-cricket:before{content:\"\"}.fa-curling:before{content:\"\"}.fa-dumbbell:before{content:\"\"}.fa-field-hockey:before{content:\"\"}.fa-football-ball:before{content:\"\"}.fa-football-helmet:before{content:\"\"}.fa-golf-ball:before{content:\"\"}.fa-golf-club:before{content:\"\"}.fa-hockey-puck:before{content:\"\"}.fa-hockey-sticks:before{content:\"\"}.fa-luchador:before{content:\"\"}.fa-racquet:before{content:\"\"}.fa-shuttlecock:before{content:\"\"}.fa-square-full:before{content:\"\"}.fa-table-tennis:before{content:\"\"}.fa-tennis-ball:before{content:\"\"}.fa-whistle:before{content:\"\"}.fa-allergies:before{content:\"\"}.fa-band-aid:before{content:\"\"}.fa-barcode-alt:before{content:\"\"}.fa-barcode-read:before{content:\"\"}.fa-barcode-scan:before{content:\"\"}.fa-box:before{content:\"\"}.fa-box-check:before{content:\"\"}.fa-boxes:before{content:\"\"}.fa-briefcase-medical:before{content:\"\"}.fa-burn:before{content:\"\"}.fa-capsules:before{content:\"\"}.fa-clipboard-check:before{content:\"\"}.fa-clipboard-list:before{content:\"\"}.fa-conveyor-belt:before{content:\"\"}.fa-conveyor-belt-alt:before{content:\"\"}.fa-diagnoses:before{content:\"\"}.fa-dna:before{content:\"\"}.fa-dolly:before{content:\"\"}.fa-dolly-empty:before{content:\"\"}.fa-dolly-flatbed:before{content:\"\"}.fa-dolly-flatbed-alt:before{content:\"\"}.fa-dolly-flatbed-empty:before{content:\"\"}.fa-file-medical:before{content:\"\"}.fa-file-medical-alt:before{content:\"\"}.fa-first-aid:before{content:\"\"}.fa-forklift:before{content:\"\"}.fa-hand-holding-box:before{content:\"\"}.fa-hand-receiving:before{content:\"\"}.fa-hospital-alt:before{content:\"\"}.fa-hospital-symbol:before{content:\"\"}.fa-id-card-alt:before{content:\"\"}.fa-inventory:before{content:\"\"}.fa-pills:before{content:\"\"}.fa-smoking:before{content:\"\"}.fa-syringe:before{content:\"\"}.fa-tablets:before{content:\"\"}.fa-warehouse:before{content:\"\"}.fa-weight:before{content:\"\"}.fa-x-ray:before{content:\"\"}.fa-blanket:before{content:\"\"}.fa-book-heart:before{content:\"\"}.fa-box-alt:before{content:\"\"}.fa-box-fragile:before{content:\"\"}.fa-box-full:before{content:\"\"}.fa-box-heart:before{content:\"\"}.fa-box-open:before{content:\"\"}.fa-box-up:before{content:\"\"}.fa-box-usd:before{content:\"\"}.fa-boxes-alt:before{content:\"\"}.fa-comment-alt-check:before{content:\"\"}.fa-comment-alt-dots:before{content:\"\"}.fa-comment-alt-edit:before{content:\"\"}.fa-comment-alt-exclamation:before{content:\"\"}.fa-comment-alt-lines:before{content:\"\"}.fa-comment-alt-minus:before{content:\"\"}.fa-comment-alt-plus:before{content:\"\"}.fa-comment-alt-smile:before{content:\"\"}.fa-comment-alt-times:before{content:\"\"}.fa-comment-check:before{content:\"\"}.fa-comment-dots:before{content:\"\"}.fa-comment-edit:before{content:\"\"}.fa-comment-exclamation:before{content:\"\"}.fa-comment-lines:before{content:\"\"}.fa-comment-minus:before{content:\"\"}.fa-comment-plus:before{content:\"\"}.fa-comment-slash:before{content:\"\"}.fa-comment-smile:before{content:\"\"}.fa-comment-times:before{content:\"\"}.fa-comments-alt:before{content:\"\"}.fa-container-storage:before{content:\"\"}.fa-couch:before{content:\"\"}.fa-donate:before{content:\"\"}.fa-dove:before{content:\"\"}.fa-fragile:before{content:\"\"}.fa-hand-heart:before{content:\"\"}.fa-hand-holding:before{content:\"\"}.fa-hand-holding-heart:before{content:\"\"}.fa-hand-holding-seedling:before{content:\"\"}.fa-hand-holding-usd:before{content:\"\"}.fa-hand-holding-water:before{content:\"\"}.fa-hands:before{content:\"\"}.fa-hands-heart:before{content:\"\"}.fa-hands-helping:before{content:\"\"}.fa-hands-usd:before{content:\"\"}.fa-handshake-alt:before{content:\"\"}.fa-heart-circle:before{content:\"\"}.fa-heart-square:before{content:\"\"}.fa-home-heart:before{content:\"\"}.fa-lamp:before{content:\"\"}.fa-leaf-heart:before{content:\"\"}.fa-parachute-box:before{content:\"\"}.fa-piggy-bank:before{content:\"\"}.fa-ribbon:before{content:\"\"}.fa-route:before{content:\"\"}.fa-seedling:before{content:\"\"}.fa-creative-commons-by:before{content:\"\"}.fa-creative-commons-nc:before{content:\"\"}.fa-creative-commons-nc-jp:before{content:\"\"}.fa-creative-commons-nd:before{content:\"\"}.fa-creative-commons-pd:before{content:\"\"}.fa-creative-commons-pd-alt:before{content:\"\"}.fa-creative-commons-remix:before{content:\"\"}.fa-creative-commons-sampling:before{content:\"\"}.fa-creative-commons-sampling-plus:before{content:\"\"}.fa-creative-commons-share:before{content:\"\"}.fa-user-cog:before{content:\"\"}.fa-user-friends:before{content:\"\"}.fa-user-slash:before{content:\"\"}.fa-user-tie:before{content:\"\"}.fa-balance-scale-left:before{content:\"\"}.fa-balance-scale-right:before{content:\"\"}.fa-blender:before{content:\"\"}.fa-book-open:before{content:\"\"}.fa-broadcast-tower:before{content:\"\"}.fa-broom:before{content:\"\"}.fa-chalkboard:before{content:\"\"}.fa-chalkboard-teacher:before{content:\"\"}.fa-church:before{content:\"\"}.fa-coins:before{content:\"\"}.fa-compact-disc:before{content:\"\"}.fa-crow:before{content:\"\"}.fa-crown:before{content:\"\"}.fa-dice:before{content:\"\"}.fa-dice-five:before{content:\"\"}.fa-dice-four:before{content:\"\"}.fa-dice-one:before{content:\"\"}.fa-dice-six:before{content:\"\"}.fa-dice-three:before{content:\"\"}.fa-dice-two:before{content:\"\"}.fa-divide:before{content:\"\"}.fa-door-closed:before{content:\"\"}.fa-door-open:before{content:\"\"}.fa-equals:before{content:\"\"}.fa-feather:before{content:\"\"}.fa-frog:before{content:\"\"}.fa-gas-pump:before{content:\"\"}.fa-glasses:before{content:\"\"}.fa-greater-than:before{content:\"\"}.fa-greater-than-equal:before{content:\"\"}.fa-helicopter:before{content:\"\"}.fa-infinity:before{content:\"\"}.fa-kiwi-bird:before{content:\"\"}.fa-less-than:before{content:\"\"}.fa-less-than-equal:before{content:\"\"}.fa-memory:before{content:\"\"}.fa-microphone-alt-slash:before{content:\"\"}.fa-money-bill-wave:before{content:\"\"}.fa-money-bill-wave-alt:before{content:\"\"}.fa-money-check:before{content:\"\"}.fa-money-check-alt:before{content:\"\"}.fa-not-equal:before{content:\"\"}.fa-palette:before{content:\"\"}.fa-percentage:before{content:\"\"}.fa-project-diagram:before{content:\"\"}.fa-receipts:before{content:\"\"}.fa-robot:before{content:\"\"}.fa-ruler:before{content:\"\"}.fa-school:before{content:\"\"}.fa-screwdriver:before{content:\"\"}.fa-shoe-prints:before{content:\"\"}.fa-skull:before{content:\"\"}.fa-store:before{content:\"\"}.fa-toolbox:before{content:\"\"}.fa-tshirt:before{content:\"\"}.fa-wallet:before{content:\"\"}.fa-angry:before{content:\"\"}.fa-archway:before{content:\"\"}.fa-atlas:before{content:\"\"}.fa-award:before{content:\"\"}.fa-backspace:before{content:\"\"}.fa-bezier-curve:before{content:\"\"}.fa-bong:before{content:\"\"}.fa-brush:before{content:\"\"}.fa-cannabis:before{content:\"\"}.fa-check-double:before{content:\"\"}.fa-cocktail:before{content:\"\"}.fa-concierge-bell:before{content:\"\"}.fa-cookie:before{content:\"\"}.fa-cookie-bite:before{content:\"\"}.fa-crop-alt:before{content:\"\"}.fa-digital-tachograph:before{content:\"\"}.fa-dizzy:before{content:\"\"}.fa-drafting-compass:before{content:\"\"}.fa-drum:before{content:\"\"}.fa-drum-steelpan:before{content:\"\"}.fa-feather-alt:before{content:\"\"}.fa-file-contract:before{content:\"\"}.fa-file-download:before{content:\"\"}.fa-file-export:before{content:\"\"}.fa-file-import:before{content:\"\"}.fa-file-invoice:before{content:\"\"}.fa-file-invoice-dollar:before{content:\"\"}.fa-file-prescription:before{content:\"\"}.fa-file-certificate:before{content:\"\"}.fa-file-upload:before{content:\"\"}.fa-fill:before{content:\"\"}.fa-fill-drip:before{content:\"\"}.fa-fingerprint:before{content:\"\"}.fa-fish:before{content:\"\"}.fa-flushed:before{content:\"\"}.fa-frown-open:before{content:\"\"}.fa-glass-martini-alt:before{content:\"\"}.fa-globe-africa:before{content:\"\"}.fa-globe-americas:before{content:\"\"}.fa-globe-asia:before{content:\"\"}.fa-grimace:before{content:\"\"}.fa-grin:before{content:\"\"}.fa-grin-alt:before{content:\"\"}.fa-grin-beam:before{content:\"\"}.fa-grin-beam-sweat:before{content:\"\"}.fa-grin-hearts:before{content:\"\"}.fa-grin-squint:before{content:\"\"}.fa-grin-squint-tears:before{content:\"\"}.fa-grin-stars:before{content:\"\"}.fa-grin-tears:before{content:\"\"}.fa-grin-tongue:before{content:\"\"}.fa-grin-tongue-squint:before{content:\"\"}.fa-grin-tongue-wink:before{content:\"\"}.fa-grin-wink:before{content:\"\"}.fa-grip-horizontal:before{content:\"\"}.fa-grip-vertical:before{content:\"\"}.fa-headphones-alt:before{content:\"\"}.fa-highlighter:before{content:\"\"}.fa-hot-tub:before{content:\"\"}.fa-hotel:before{content:\"\"}.fa-joint:before{content:\"\"}.fa-kiss:before{content:\"\"}.fa-kiss-beam:before{content:\"\"}.fa-kiss-wink-heart:before{content:\"\"}.fa-laugh:before{content:\"\"}.fa-laugh-beam:before{content:\"\"}.fa-laugh-squint:before{content:\"\"}.fa-laugh-wink:before{content:\"\"}.fa-luggage-cart:before{content:\"\"}.fa-map-marked:before{content:\"\"}.fa-map-marked-alt:before{content:\"\"}.fa-marker:before{content:\"\"}.fa-medal:before{content:\"\"}.fa-meh-blank:before{content:\"\"}.fa-meh-rolling-eyes:before{content:\"\"}.fa-monument:before{content:\"\"}.fa-mortar-pestle:before{content:\"\"}.fa-paint-roller:before{content:\"\"}.fa-passport:before{content:\"\"}.fa-prescription:before{content:\"\"}.fa-shuttle-van:before{content:\"\"}.fa-signature:before{content:\"\"}.fa-solar-panel:before{content:\"\"}.fa-spray-can:before{content:\"\"}.fa-stamp:before{content:\"\"}.fa-swimmer:before{content:\"\"}.fa-tooth:before{content:\"\"}.fa-weight-hanging:before{content:\"\"}.fa-air-freshener:before{content:\"\"}.fa-apple-alt:before{content:\"\"}.fa-atom:before{content:\"\"}.fa-atom-alt:before{content:\"\"}.fa-backpack:before{content:\"\"}.fa-bell-school:before{content:\"\"}.fa-bell-school-slash:before{content:\"\"}.fa-bone:before{content:\"\"}.fa-bone-break:before{content:\"\"}.fa-book-alt:before{content:\"\"}.fa-book-reader:before{content:\"\"}.fa-books:before{content:\"\"}.fa-brain:before{content:\"\"}.fa-bus-school:before{content:\"\"}.fa-car-alt:before{content:\"\"}.fa-car-battery:before{content:\"\"}.fa-car-bump:before{content:\"\"}.fa-car-crash:before{content:\"\"}.fa-car-garage:before{content:\"\"}.fa-car-mechanic:before{content:\"\"}.fa-car-side:before{content:\"\"}.fa-car-tilt:before{content:\"\"}.fa-car-wash:before{content:\"\"}.fa-charging-station:before{content:\"\"}.fa-clipboard-prescription:before{content:\"\"}.fa-compass-slash:before{content:\"\"}.fa-diploma:before{content:\"\"}.fa-directions:before{content:\"\"}.fa-do-not-enter:before{content:\"\"}.fa-draw-circle:before{content:\"\"}.fa-draw-polygon:before{content:\"\"}.fa-draw-square:before{content:\"\"}.fa-ear:before{content:\"\"}.fa-engine-warning:before{content:\"\"}.fa-gas-pump-slash:before{content:\"\"}.fa-glasses-alt:before{content:\"\"}.fa-globe-stand:before{content:\"\"}.fa-heart-rate:before{content:\"\"}.fa-inhaler:before{content:\"\"}.fa-kidneys:before{content:\"\"}.fa-laptop-code:before{content:\"\"}.fa-layer-group:before{content:\"\"}.fa-layer-minus:before{content:\"\"}.fa-layer-plus:before{content:\"\"}.fa-lips:before{content:\"\"}.fa-location:before{content:\"\"}.fa-location-slash:before{content:\"\"}.fa-lungs:before{content:\"\"}.fa-map-marker-alt-slash:before{content:\"\"}.fa-map-marker-check:before{content:\"\"}.fa-map-marker-edit:before{content:\"\"}.fa-map-marker-exclamation:before{content:\"\"}.fa-map-marker-minus:before{content:\"\"}.fa-map-marker-question:before{content:\"\"}.fa-map-marker-slash:before{content:\"\"}.fa-map-marker-smile:before{content:\"\"}.fa-map-marker-times:before{content:\"\"}.fa-microscope:before{content:\"\"}.fa-monitor-heart-rate:before{content:\"\"}.fa-oil-can:before{content:\"\"}.fa-parking-circle:before{content:\"\"}.fa-route-highway:before{content:\"\"}.fa-shapes:before{content:\"\"}.fa-steering-wheel:before{content:\"\"}.fa-stomach:before{content:\"\"}.fa-teeth-open:before{content:\"\"}.fa-tire:before{content:\"\"}.fa-traffic-cone:before{content:\"\"}.fa-traffic-light:before{content:\"\"}.fa-users-class:before{content:\"\"}.fa-abacus:before{content:\"\"}.fa-ad:before{content:\"\"}.fa-alipay:before{content:\"\"}.fa-analytics:before{content:\"\"}.fa-ankh:before{content:\"\"}.fa-badge-dollar:before{content:\"\"}.fa-badge-percent:before{content:\"\"}.fa-bible:before{content:\"\"}.fa-bullseye-arrow:before{content:\"\"}.fa-bullseye-pointer:before{content:\"\"}.fa-business-time:before{content:\"\"}.fa-cabinet-filing:before{content:\"\"}.fa-calculator-alt:before{content:\"\"}.fa-chart-line-down:before{content:\"\"}.fa-chart-pie-alt:before{content:\"\"}.fa-city:before{content:\"\"}.fa-comment-dollar:before{content:\"\"}.fa-comments-alt-dollar:before{content:\"\"}.fa-comments-dollar:before{content:\"\"}.fa-cross:before{content:\"\"}.fa-dharmachakra:before{content:\"\"}.fa-empty-set:before{content:\"\"}.fa-envelope-open-dollar:before{content:\"\"}.fa-envelope-open-text:before{content:\"\"}.fa-file-chart-line:before{content:\"\"}.fa-file-chart-pie:before{content:\"\"}.fa-file-spreadsheet:before{content:\"\"}.fa-file-user:before{content:\"\"}.fa-folder-minus:before{content:\"\"}.fa-folder-plus:before{content:\"\"}.fa-folder-times:before{content:\"\"}.fa-folders:before{content:\"\"}.fa-function:before{content:\"\"}.fa-funnel-dollar:before{content:\"\"}.fa-gift-card:before{content:\"\"}.fa-gopuram:before{content:\"\"}.fa-hamsa:before{content:\"\"}.fa-bahai:before{content:\"\"}.fa-integral:before{content:\"\"}.fa-intersection:before{content:\"\"}.fa-jedi:before{content:\"\"}.fa-journal-whills:before{content:\"\"}.fa-kaaba:before{content:\"\"}.fa-keynote:before{content:\"\"}.fa-khanda:before{content:\"\"}.fa-lambda:before{content:\"\"}.fa-landmark:before{content:\"\"}.fa-lightbulb-dollar:before{content:\"\"}.fa-lightbulb-exclamation:before{content:\"\"}.fa-lightbulb-on:before{content:\"\"}.fa-lightbulb-slash:before{content:\"\"}.fa-megaphone:before{content:\"\"}.fa-menorah:before{content:\"\"}.fa-mind-share:before{content:\"\"}.fa-mosque:before{content:\"\"}.fa-om:before{content:\"\"}.fa-omega:before{content:\"\"}.fa-pastafarianism:before{content:\"\"}.fa-peace:before{content:\"\"}.fa-pi:before{content:\"\"}.fa-praying-hands:before{content:\"\"}.fa-presentation:before{content:\"\"}.fa-quran:before{content:\"\"}.fa-sigma:before{content:\"\"}.fa-signal-alt-2:before{content:\"\"}.fa-socks:before{content:\"\"}.fa-square-root:before{content:\"\"}.fa-user-chart:before{content:\"\"}.fa-volume:before{content:\"\"}.fa-wifi-slash:before{content:\"\"}.fa-yin-yang:before{content:\"\"}.fa-acorn:before{content:\"\"}.fa-acquisitions-incorporated:before{content:\"\"}.fa-alicorn:before{content:\"\"}.fa-apple-crate:before{content:\"\"}.fa-axe:before{content:\"\"}.fa-axe-battle:before{content:\"\"}.fa-badger-honey:before{content:\"\"}.fa-bat:before{content:\"\"}.fa-blender-phone:before{content:\"\"}.fa-book-dead:before{content:\"\"}.fa-book-spells:before{content:\"\"}.fa-bow-arrow:before{content:\"\"}.fa-campfire:before{content:\"\"}.fa-campground:before{content:\"\"}.fa-candle-holder:before{content:\"\"}.fa-candy-corn:before{content:\"\"}.fa-cat:before{content:\"\"}.fa-cauldron:before{content:\"\"}.fa-chair:before{content:\"\"}.fa-chair-office:before{content:\"\"}.fa-claw-marks:before{content:\"\"}.fa-cloud-moon:before{content:\"\"}.fa-cloud-sun:before{content:\"\"}.fa-coffee-togo:before{content:\"\"}.fa-coffin:before{content:\"\"}.fa-corn:before{content:\"\"}.fa-cow:before{content:\"\"}.fa-critical-role:before{content:\"\"}.fa-d-and-d-beyond:before{content:\"\"}.fa-dagger:before{content:\"\"}.fa-dice-d10:before{content:\"\"}.fa-dice-d12:before{content:\"\"}.fa-dice-d20:before{content:\"\"}.fa-dice-d4:before{content:\"\"}.fa-dice-d6:before{content:\"\"}.fa-dice-d8:before{content:\"\"}.fa-dog:before{content:\"\"}.fa-dog-leashed:before{content:\"\"}.fa-dragon:before{content:\"\"}.fa-drumstick:before{content:\"\"}.fa-drumstick-bite:before{content:\"\"}.fa-duck:before{content:\"\"}.fa-dungeon:before{content:\"\"}.fa-elephant:before{content:\"\"}.fa-eye-evil:before{content:\"\"}.fa-file-csv:before{content:\"\"}.fa-fist-raised:before{content:\"\"}.fa-flame:before{content:\"\"}.fa-flask-poison:before{content:\"\"}.fa-flask-potion:before{content:\"\"}.fa-ghost:before{content:\"\"}.fa-hammer:before{content:\"\"}.fa-hammer-war:before{content:\"\"}.fa-hand-holding-magic:before{content:\"\"}.fa-hanukiah:before{content:\"\"}.fa-hat-witch:before{content:\"\"}.fa-hat-wizard:before{content:\"\"}.fa-head-side:before{content:\"\"}.fa-head-vr:before{content:\"\"}.fa-helmet-battle:before{content:\"\"}.fa-hiking:before{content:\"\"}.fa-hippo:before{content:\"\"}.fa-hockey-mask:before{content:\"\"}.fa-hood-cloak:before{content:\"\"}.fa-horse:before{content:\"\"}.fa-house-damage:before{content:\"\"}.fa-hryvnia:before{content:\"\"}.fa-key-skeleton:before{content:\"\"}.fa-kite:before{content:\"\"}.fa-knife-kitchen:before{content:\"\"}.fa-leaf-maple:before{content:\"\"}.fa-leaf-oak:before{content:\"\"}.fa-mace:before{content:\"\"}.fa-mandolin:before{content:\"\"}.fa-mask:before{content:\"\"}.fa-monkey:before{content:\"\"}.fa-mountain:before{content:\"\"}.fa-mountains:before{content:\"\"}.fa-network-wired:before{content:\"\"}.fa-otter:before{content:\"\"}.fa-pie:before{content:\"\"}.fa-pumpkin:before{content:\"\"}.fa-rabbit:before{content:\"\"}.fa-ram:before{content:\"\"}.fa-running:before{content:\"\"}.fa-scarecrow:before{content:\"\"}.fa-scroll:before{content:\"\"}.fa-shovel:before{content:\"\"}.fa-slash:before{content:\"\"}.fa-snake:before{content:\"\"}.fa-spider:before{content:\"\"}.fa-spider-web:before{content:\"\"}.fa-squirrel:before{content:\"\"}.fa-staff:before{content:\"\"}.fa-sword:before{content:\"\"}.fa-toilet-paper:before{content:\"\"}.fa-tombstone:before{content:\"\"}.fa-turtle:before{content:\"\"}.fa-vr-cardboard:before{content:\"\"}.fa-whale:before{content:\"\"}.fa-wheat:before{content:\"\"}.fa-ballot:before{content:\"\"}.fa-ballot-check:before{content:\"\"}.fa-booth-curtain:before{content:\"\"}.fa-box-ballot:before{content:\"\"}.fa-calendar-star:before{content:\"\"}.fa-clipboard-list-check:before{content:\"\"}.fa-cloud-drizzle:before{content:\"\"}.fa-cloud-hail:before{content:\"\"}.fa-cloud-hail-mixed:before{content:\"\"}.fa-cloud-meatball:before{content:\"\"}.fa-cloud-moon-rain:before{content:\"\"}.fa-cloud-rain:before{content:\"\"}.fa-cloud-rainbow:before{content:\"\"}.fa-cloud-showers:before{content:\"\"}.fa-cloud-showers-heavy:before{content:\"\"}.fa-cloud-sleet:before{content:\"\"}.fa-cloud-snow:before{content:\"\"}.fa-cloud-sun-rain:before{content:\"\"}.fa-clouds:before{content:\"\"}.fa-clouds-moon:before{content:\"\"}.fa-clouds-sun:before{content:\"\"}.fa-democrat:before{content:\"\"}.fa-dewpoint:before{content:\"\"}.fa-eclipse:before{content:\"\"}.fa-eclipse-alt:before{content:\"\"}.fa-fire-smoke:before{content:\"\"}.fa-flag-alt:before{content:\"\"}.fa-flag-usa:before{content:\"\"}.fa-fog:before{content:\"\"}.fa-house-flood:before{content:\"\"}.fa-humidity:before{content:\"\"}.fa-hurricane:before{content:\"\"}.fa-landmark-alt:before{content:\"\"}.fa-meteor:before{content:\"\"}.fa-moon-cloud:before{content:\"\"}.fa-moon-stars:before{content:\"\"}.fa-podium-star:before{content:\"\"}.fa-raindrops:before{content:\"\"}.fa-smog:before{content:\"\"}.fa-thunderstorm:before{content:\"\"}.fa-volcano:before{content:\"\"}.fa-water:before{content:\"\"}.fa-angel:before{content:\"\"}.fa-artstation:before{content:\"\"}.fa-atlassian:before{content:\"\"}.fa-baby:before{content:\"\"}.fa-baby-carriage:before{content:\"\"}.fa-ball-pile:before{content:\"\"}.fa-bells:before{content:\"\"}.fa-biohazard:before{content:\"\"}.fa-blog:before{content:\"\"}.fa-boot:before{content:\"\"}.fa-calendar-day:before{content:\"\"}.fa-calendar-week:before{content:\"\"}.fa-canadian-maple-leaf:before{content:\"\"}.fa-candy-cane:before{content:\"\"}.fa-carrot:before{content:\"\"}.fa-cash-register:before{content:\"\"}.fa-centos:before{content:\"\"}.fa-chart-network:before{content:\"\"}.fa-chimney:before{content:\"\"}.fa-compress-arrows-alt:before{content:\"\"}.fa-confluence:before{content:\"\"}.fa-deer:before{content:\"\"}.fa-deer-rudolph:before{content:\"\"}.fa-diaspora:before{content:\"\"}.fa-dreidel:before{content:\"\"}.fa-dumpster:before{content:\"\"}.fa-dumpster-fire:before{content:\"\"}.fa-ear-muffs:before{content:\"\"}.fa-ethernet:before{content:\"\"}.fa-fireplace:before{content:\"\"}.fa-frosty-head:before{content:\"\"}.fa-gifts:before{content:\"\"}.fa-gingerbread-man:before{content:\"\"}.fa-glass-champagne:before{content:\"\"}.fa-glass-cheers:before{content:\"\"}.fa-glass-whiskey:before{content:\"\"}.fa-glass-whiskey-rocks:before{content:\"\"}.fa-globe-europe:before{content:\"\"}.fa-globe-snow:before{content:\"\"}.fa-grip-lines:before{content:\"\"}.fa-grip-lines-vertical:before{content:\"\"}.fa-guitar:before{content:\"\"}.fa-hat-santa:before{content:\"\"}.fa-hat-winter:before{content:\"\"}.fa-heart-broken:before{content:\"\"}.fa-holly-berry:before{content:\"\"}.fa-horse-head:before{content:\"\"}.fa-ice-skate:before{content:\"\"}.fa-icicles:before{content:\"\"}.fa-igloo:before{content:\"\"}.fa-lights-holiday:before{content:\"\"}.fa-mistletoe:before{content:\"\"}.fa-mitten:before{content:\"\"}.fa-mug-hot:before{content:\"\"}.fa-mug-marshmallows:before{content:\"\"}.fa-ornament:before{content:\"\"}.fa-radiation-alt:before{content:\"\"}.fa-restroom:before{content:\"\"}.fa-satellite:before{content:\"\"}.fa-scarf:before{content:\"\"}.fa-sd-card:before{content:\"\"}.fa-sim-card:before{content:\"\"}.fa-sleigh:before{content:\"\"}.fa-sms:before{content:\"\"}.fa-snowman:before{content:\"\"}.fa-toilet:before{content:\"\"}.fa-tools:before{content:\"\"}.fa-fire-alt:before{content:\"\"}.fa-bacon:before{content:\"\"}.fa-book-medical:before{content:\"\"}.fa-book-user:before{content:\"\"}.fa-books-medical:before{content:\"\"}.fa-brackets:before{content:\"\"}.fa-brackets-curly:before{content:\"\"}.fa-bread-loaf:before{content:\"\"}.fa-bread-slice:before{content:\"\"}.fa-burrito:before{content:\"\"}.fa-chart-scatter:before{content:\"\"}.fa-cheese:before{content:\"\"}.fa-cheese-swiss:before{content:\"\"}.fa-cheeseburger:before{content:\"\"}.fa-clinic-medical:before{content:\"\"}.fa-clipboard-user:before{content:\"\"}.fa-comment-alt-medical:before{content:\"\"}.fa-comment-medical:before{content:\"\"}.fa-croissant:before{content:\"\"}.fa-crutch:before{content:\"\"}.fa-crutches:before{content:\"\"}.fa-debug:before{content:\"\"}.fa-disease:before{content:\"\"}.fa-egg:before{content:\"\"}.fa-egg-fried:before{content:\"\"}.fa-files-medical:before{content:\"\"}.fa-fish-cooked:before{content:\"\"}.fa-flower:before{content:\"\"}.fa-flower-daffodil:before{content:\"\"}.fa-flower-tulip:before{content:\"\"}.fa-folder-tree:before{content:\"\"}.fa-french-fries:before{content:\"\"}.fa-glass:before{content:\"\"}.fa-hamburger:before{content:\"\"}.fa-hand-middle-finger:before{content:\"\"}.fa-hard-hat:before{content:\"\"}.fa-head-side-brain:before{content:\"\"}.fa-head-side-medical:before{content:\"\"}.fa-home-alt:before{content:\"\"}.fa-home-lg:before{content:\"\"}.fa-home-lg-alt:before{content:\"\"}.fa-hospital-user:before{content:\"\"}.fa-hospitals:before{content:\"\"}.fa-hotdog:before{content:\"\"}.fa-ice-cream:before{content:\"\"}.fa-island-tropical:before{content:\"\"}.fa-laptop-medical:before{content:\"\"}.fa-mailbox:before{content:\"\"}.fa-meat:before{content:\"\"}.fa-pager:before{content:\"\"}.fa-pepper-hot:before{content:\"\"}.fa-pizza-slice:before{content:\"\"}.fa-popcorn:before{content:\"\"}.fa-rings-wedding:before{content:\"\"}.fa-sack-dollar:before{content:\"\"}.fa-salad:before{content:\"\"}.fa-sandwich:before{content:\"\"}.fa-sausage:before{content:\"\"}.fa-soup:before{content:\"\"}.fa-steak:before{content:\"\"}.fa-stretcher:before{content:\"\"}.fa-user-headset:before{content:\"\"}.fa-users-medical:before{content:\"\"}.fa-walker:before{content:\"\"}.fa-webcam:before{content:\"\"}.fa-airbnb:before{content:\"\"}.fa-battle-net:before{content:\"\"}.fa-bootstrap:before{content:\"\"}.fa-chromecast:before{content:\"\"}.fa-alarm-exclamation:before{content:\"\"}.fa-alarm-plus:before{content:\"\"}.fa-alarm-snooze:before{content:\"\"}.fa-align-slash:before{content:\"\"}.fa-bags-shopping:before{content:\"\"}.fa-bell-exclamation:before{content:\"\"}.fa-bell-plus:before{content:\"\"}.fa-biking:before{content:\"\"}.fa-biking-mountain:before{content:\"\"}.fa-border-all:before{content:\"\"}.fa-border-bottom:before{content:\"\"}.fa-border-inner:before{content:\"\"}.fa-border-left:before{content:\"\"}.fa-border-none:before{content:\"\"}.fa-border-outer:before{content:\"\"}.fa-border-right:before{content:\"\"}.fa-border-style:before{content:\"\"}.fa-border-style-alt:before{content:\"\"}.fa-border-top:before{content:\"\"}.fa-bring-forward:before{content:\"\"}.fa-bring-front:before{content:\"\"}.fa-burger-soda:before{content:\"\"}.fa-car-building:before{content:\"\"}.fa-car-bus:before{content:\"\"}.fa-cars:before{content:\"\"}.fa-coin:before{content:\"\"}.fa-construction:before{content:\"\"}.fa-digging:before{content:\"\"}.fa-drone:before{content:\"\"}.fa-drone-alt:before{content:\"\"}.fa-dryer:before{content:\"\"}.fa-dryer-alt:before{content:\"\"}.fa-fan:before{content:\"\"}.fa-farm:before{content:\"\"}.fa-file-search:before{content:\"\"}.fa-font-case:before{content:\"\"}.fa-game-board:before{content:\"\"}.fa-game-board-alt:before{content:\"\"}.fa-glass-citrus:before{content:\"\"}.fa-h4:before{content:\"\"}.fa-hat-chef:before{content:\"\"}.fa-horizontal-rule:before{content:\"\"}.fa-icons:before{content:\"\"}.fa-kerning:before{content:\"\"}.fa-line-columns:before{content:\"\"}.fa-line-height:before{content:\"\"}.fa-money-check-edit:before{content:\"\"}.fa-money-check-edit-alt:before{content:\"\"}.fa-mug:before{content:\"\"}.fa-phone-alt:before{content:\"\"}.fa-snooze:before{content:\"\"}.fa-sort-alt:before{content:\"\"}.fa-sort-amount-down-alt:before{content:\"\"}.fa-sort-size-down:before{content:\"\"}.fa-sparkles:before{content:\"\"}.fa-text:before{content:\"\"}.fa-text-size:before{content:\"\"}.fa-voicemail:before{content:\"\"}.fa-washer:before{content:\"\"}.fa-wind-turbine:before{content:\"\"}.fa-border-center-h:before{content:\"\"}.fa-border-center-v:before{content:\"\"}.fa-cotton-bureau:before{content:\"\"}.fa-album:before{content:\"\"}.fa-album-collection:before{content:\"\"}.fa-amp-guitar:before{content:\"\"}.fa-badge-sheriff:before{content:\"\"}.fa-banjo:before{content:\"\"}.fa-betamax:before{content:\"\"}.fa-boombox:before{content:\"\"}.fa-buy-n-large:before{content:\"\"}.fa-cactus:before{content:\"\"}.fa-camcorder:before{content:\"\"}.fa-camera-movie:before{content:\"\"}.fa-camera-polaroid:before{content:\"\"}.fa-cassette-tape:before{content:\"\"}.fa-cctv:before{content:\"\"}.fa-clarinet:before{content:\"\"}.fa-cloud-music:before{content:\"\"}.fa-comment-alt-music:before{content:\"\"}.fa-comment-music:before{content:\"\"}.fa-computer-classic:before{content:\"\"}.fa-computer-speaker:before{content:\"\"}.fa-cowbell:before{content:\"\"}.fa-cowbell-more:before{content:\"\"}.fa-disc-drive:before{content:\"\"}.fa-file-music:before{content:\"\"}.fa-film-canister:before{content:\"\"}.fa-flashlight:before{content:\"\"}.fa-flute:before{content:\"\"}.fa-flux-capacitor:before{content:\"\"}.fa-game-console-handheld:before{content:\"\"}.fa-gamepad-alt:before{content:\"\"}.fa-gramophone:before{content:\"\"}.fa-guitar-electric:before{content:\"\"}.fa-guitars:before{content:\"\"}.fa-hat-cowboy:before{content:\"\"}.fa-hat-cowboy-side:before{content:\"\"}.fa-head-side-headphones:before{content:\"\"}.fa-horse-saddle:before{content:\"\"}.fa-image-polaroid:before{content:\"\"}.fa-joystick:before{content:\"\"}.fa-jug:before{content:\"\"}.fa-kazoo:before{content:\"\"}.fa-lasso:before{content:\"\"}.fa-list-music:before{content:\"\"}.fa-microphone-stand:before{content:\"\"}.fa-mouse:before{content:\"\"}.fa-mouse-alt:before{content:\"\"}.fa-mp3-player:before{content:\"\"}.fa-music-slash:before{content:\"\"}.fa-piano-keyboard:before{content:\"\"}.fa-projector:before{content:\"\"}.fa-radio-alt:before{content:\"\"}.fa-router:before{content:\"\"}.fa-saxophone:before{content:\"\"}.fa-speakers:before{content:\"\"}.fa-trumpet:before{content:\"\"}.fa-usb-drive:before{content:\"\"}.fa-walkie-talkie:before{content:\"\"}.fa-waveform:before{content:\"\"}.fa-scanner-image:before{content:\"\"}.fa-air-conditioner:before{content:\"\"}.fa-alien:before{content:\"\"}.fa-alien-monster:before{content:\"\"}.fa-bed-alt:before{content:\"\"}.fa-bed-bunk:before{content:\"\"}.fa-bed-empty:before{content:\"\"}.fa-bell-on:before{content:\"\"}.fa-blinds:before{content:\"\"}.fa-blinds-raised:before{content:\"\"}.fa-camera-home:before{content:\"\"}.fa-caravan:before{content:\"\"}.qadpt-grdicon{width:30px !important;padding:4px !important}.qadpt-menupopup{z-index:999;padding:10px;min-width:170px;border-radius:8px;position:absolute;background:var(--white-color);box-shadow:-5px 5px 5px -3px rgba(0,0,0,.2),0px 8px 10px 1px rgba(0,0,0,.14),0px 3px 14px 2px rgba(0,0,0,.12);right:20px}.qadpt-menupopup button{background-color:rgba(0,0,0,0) !important}.qadpt-menupopup .qadpt-actionpopup{display:flex;align-items:center;font-size:14px;color:#000}.qadpt-menupopup .qadpt-actionpopup span{margin-left:10px}.qadpt-loaderstyles{transform:translate(-50%, -50%);z-index:1;position:absolute;top:225px;left:475px;width:100%;height:77%;display:flex;justify-content:center;align-items:center;background-color:hsla(0,0%,100%,.8)}.qadpt-loaderSpinnerStyles{width:45px;height:45px}.qadpt-deletepopupwidth{width:200px}.qadpt-memberButton{background-color:var(--button-bg-color);border:none;border-radius:15px;color:var(--white-color);cursor:pointer;font-size:14px;padding:10px;height:40px}.qadpt-memberButton i{margin-right:10px;font-size:14px !important}.qadpt-memberButton.qadpt-mem-updated{background-color:var(--button-bg-color)}.qadpt-memberButton.qadpt-disabled{background-color:#ccc;cursor:not-allowed}.qadpt-membersave{position:absolute;cursor:pointer;font-size:14px;background:rgba(0,0,0,0);color:var(--white-color);border-radius:15px;background-color:#91a7d9;border:0px solid #91a7d9}.qadpt-membersave.isLabelUpdated{background-color:var(--button-bg-color)}.qadpt-deletepopupCancelbutton{position:relative;background:var(--white-color);color:#007bff;border:1px solid #007bff;margin-right:27px;right:0px;top:10px}.qadpt-deletepopupButton{position:relative;right:0px;top:10px}.qadpt-usereditcancelButton{position:absolute;right:220px;z-index:99;top:67px;background-color:#007bff;border:none;border-radius:5px;color:var(--white-color);cursor:pointer;font-size:14px;padding:7px 21px;top:440px}.qadpt-usereditbuttonsdiv{margin-left:40px;display:flex;gap:20px}.qadpt-usereditsaveButton{position:absolute;right:37px;z-index:99;background-color:#a5c3c5;border:none;border-radius:25px;color:var(--white-color);cursor:pointer;font-size:14px;padding:7px 21px;top:460px;width:110px;height:44px;padding:10px 12px 10px 12px}.qadpt-userpasswordpopup{z-index:100;width:350px !important;height:37vh;top:200px;right:400px;padding:0 20px;position:fixed !important;display:block !important;background:var(--white-color) !important;box-shadow:0 3px 8px #000;border-radius:8px}.qadpt-usrhdrpossideoff{top:-38px;position:relative;left:-440px}.qadpt-usrhdrpossideon{top:2px;position:relative;right:560px}.qadpt-userCreateButtonPosition{top:-130px;position:relative}.qadpt-custom-data-grid{border-color:var(--grid-border-color);position:relative}.qadpt-custom-data-grid .MuiDataGrid-columnHeaders{background:var(--grid-head-background)}.qadpt-custom-data-grid .MuiDataGrid-columnHeaderTitle{font-weight:500;color:#000}.qadpt-custom-data-grid .MuiDataGrid-cell{border-right:1px solid var(--grid-border-color)}.qadpt-custom-data-grid .MuiDataGrid-columnHeader,.qadpt-custom-data-grid .MuiDataGrid-cell{border-right:1px solid var(--grid-border-color)}.qadpt-custom-data-grid .MuiDataGrid-row .MuiDataGrid-cell{border-bottom:none}.st-ina{border:1px solid #9a9a9a;color:#9a9a9a;background:rgba(154,154,154,.2);border-radius:20px;height:32px;margin:10px 5px;display:flex;align-items:center;place-content:center}.st-blo{border:1px solid #e77a7a;color:#f57676;background:rgba(250,152,152,.2);border-radius:20px;height:32px;margin:10px 5px;display:flex;align-items:center;place-content:center}.st-pen{border:1px solid #fc8d33;color:#f77722;background:rgba(250,169,19,.2);border-radius:20px;height:32px;margin:10px 5px;display:flex;align-items:center;place-content:center}.st-act{border:1px solid var(--button-bg-color);color:var(--button-bg-color);background:#dae7e7;border-radius:20px;height:32px;margin:10px 5px;display:flex;align-items:center;place-content:center}.MuiDataGrid-columnHeader{background:var(--grid-head-background);padding:0 15px !important;border-right:1px solid var(--grid-border-color);height:40px !important;border-color:rgba(0,0,0,0) !important}.qadpt-setting-grd,.qadpt-account-grd,.qadpt-audit-grd{border-color:var(--grid-border-color) !important}.qadpt-setting-grd .MuiDataGrid-main,.qadpt-account-grd .MuiDataGrid-main,.qadpt-audit-grd .MuiDataGrid-main{--DataGrid-topContainerHeight: 40px !important}.qadpt-setting-grd .MuiDataGrid-virtualScroller,.qadpt-account-grd .MuiDataGrid-virtualScroller,.qadpt-audit-grd .MuiDataGrid-virtualScroller{height:calc(100vh - 295px) !important}.qadpt-setting-grd .MuiDataGrid-footerContainer .MuiTablePagination-root .MuiInputBase-input,.qadpt-account-grd .MuiDataGrid-footerContainer .MuiTablePagination-root .MuiInputBase-input,.qadpt-audit-grd .MuiDataGrid-footerContainer .MuiTablePagination-root .MuiInputBase-input{padding:20px !important}.qadpt-setting-grd .MuiDataGrid-columnHeader .MuiDataGrid-sortIcon,.qadpt-account-grd .MuiDataGrid-columnHeader .MuiDataGrid-sortIcon,.qadpt-audit-grd .MuiDataGrid-columnHeader .MuiDataGrid-sortIcon{opacity:1 !important;visibility:visible}.qadpt-setting-grd .MuiDataGrid-row .MuiDataGrid-cell i,.qadpt-account-grd .MuiDataGrid-row .MuiDataGrid-cell i,.qadpt-audit-grd .MuiDataGrid-row .MuiDataGrid-cell i{font-weight:600}.qadpt-setting-grd .MuiDataGrid-columnHeaderTitle,.qadpt-account-grd .MuiDataGrid-columnHeaderTitle,.qadpt-audit-grd .MuiDataGrid-columnHeaderTitle{font-weight:600}.qadpt-setting-grd .MuiDataGrid-columnHeader,.qadpt-account-grd .MuiDataGrid-columnHeader,.qadpt-audit-grd .MuiDataGrid-columnHeader{background:var(--grid-head-background);color:#000;border-right:1px solid var(--grid-border-color);height:40px !important}.qadpt-setting-grd .MuiDataGrid-cell,.qadpt-account-grd .MuiDataGrid-cell,.qadpt-audit-grd .MuiDataGrid-cell{border-right:1px solid var(--grid-border-color)}.qadpt-setting-grd .MuiDataGrid-row .MuiDataGrid-cell,.qadpt-account-grd .MuiDataGrid-row .MuiDataGrid-cell,.qadpt-audit-grd .MuiDataGrid-row .MuiDataGrid-cell{border-bottom:none}.qadpt-setting-grd .MuiDataGrid-row .MuiButtonBase-root.MuiChip-root,.qadpt-account-grd .MuiDataGrid-row .MuiButtonBase-root.MuiChip-root,.qadpt-audit-grd .MuiDataGrid-row .MuiButtonBase-root.MuiChip-root{background:var(--button-bg-color) !important}.qadpt-setting-grd .MuiDataGrid-columnHeader:nth-child(2),.qadpt-setting-grd .MuiDataGrid-cell:nth-child(2),.qadpt-setting-grd .MuiDataGrid-columnHeader:nth-child(3),.qadpt-setting-grd .MuiDataGrid-cell:nth-child(3){width:25% !important;min-width:25% !important;max-width:25% !important}.qadpt-setting-grd .MuiDataGrid-columnHeader:nth-child(5),.qadpt-setting-grd .MuiDataGrid-cell:nth-child(5){width:14% !important;min-width:14% !important;max-width:14% !important}.qadpt-setting-grd .MuiDataGrid-columnHeader:nth-child(6),.qadpt-setting-grd .MuiDataGrid-cell:nth-child(6),.qadpt-setting-grd .MuiDataGrid-columnHeader:nth-child(4),.qadpt-setting-grd .MuiDataGrid-cell:nth-child(4){width:20% !important;min-width:20% !important;max-width:20% !important}.qadpt-midpart .qadpt-content-block{background:var(--white-color);padding:0 20px;border-radius:6px;min-height:calc(100vh - 80px)}.qadpt-midpart .qadpt-content-block .qadpt-head{margin:0 -20px;padding:20px;display:flex;align-items:center;justify-content:space-between}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-title-sec .qadpt-title{font-weight:600;font-size:22px;line-height:20px}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-title-sec .qadpt-description{font-size:14px;line-height:18px;padding:5px 0}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part{text-align:right;width:auto;display:flex;gap:10px;align-items:center}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper .open-ai-key-out .open-ai-key-box{display:flex;align-items:center}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper .open-ai-key-out .open-ai-key-box .open-ai-key-box-icon{background:#f0f0f0;padding:10px 12px;border-radius:8px;cursor:pointer;display:flex;align-items:center;justify-content:center}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper .open-ai-key-out .open-ai-key-box .open-ai-key-box-icon img{width:16px;height:16px}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper .open-ai-key-out .open-ai-key-box .open-ai-key-input-wrapper{position:relative;animation:expandWidth .5s ease forwards;overflow:hidden}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper .open-ai-key-out .open-ai-key-box .open-ai-key-input-wrapper .input-icon{position:absolute;left:0;top:0;bottom:0;width:40px;display:flex;align-items:center;justify-content:center;background:#f0f0f0;border-top-left-radius:8px;border-bottom-left-radius:8px;cursor:pointer}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper .open-ai-key-out .open-ai-key-box .open-ai-key-input-wrapper .input-icon img{width:16px;height:16px}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper .open-ai-key-out .open-ai-key-box .open-ai-key-input-wrapper .open-ai-key-input{width:100%;padding:10px 12px 10px 48px;border:1px solid #ccc;border-radius:8px;outline:none;font-size:14px}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper.open-ai-key-error .open-ai-key-input-wrapper .input-icon{background:rgba(255,97,97,.2784313725) !important}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper.open-ai-key-error .open-ai-key-input-wrapper .open-ai-key-input{border:1px solid #ff4a4a !important}.qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part .open-ai-key-wrapper.open-ai-key-submitted .open-ai-key-box-icon{border:1px solid #5de86b !important;background:#e5f8e7 !important}@keyframes expandWidth{from{width:0}to{width:320px}}.qadpt-midpart .qadpt-content-block .qadpt-content-box{padding-bottom:20px;margin:20px 0 10px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt{display:flex;place-content:end;align-items:center}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-teamsearch{width:210px;right:10px;top:-14px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-teamsearch .MuiFormHelperText-root{color:inherit}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-teamsearch .MuiOutlinedInput-root{padding-top:6px;border-radius:12px;height:40px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-teamsearch .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline{border:1px solid #ccc}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-teamsearch .MuiOutlinedInput-notchedOutline{height:43px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-teamsearch .MuiInputBase-input{height:1em;padding-left:0px !important}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-teamsearch button{padding:0px !important}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-input-field-error .MuiFormHelperText-root{color:var(--error-color)}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-usrfilter button{position:relative;padding:0 8px;border:1px solid var(--border-color);border-radius:10px;top:-10px;height:40px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head{position:relative;float:right;display:flex;width:100%;place-content:flex-end;margin-bottom:10px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .slt-acc-drp{width:210px;margin:-7px 10px;padding:0 !important}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .slt-acc-drp.MuiBox-root{padding:0 !important}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .slt-acc-drp .MuiOutlinedInput-input{width:210px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .slt-acc-drp .MuiOutlinedInput-notchedOutline{border-radius:10px !important;height:42px !important}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .slt-acc-drp .MuiInputLabel-outlined{top:-9px !important;padding:6px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .slt-acc-drp .MuiAutocomplete-inputRoot{height:42px !important}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .slt-acc-drp .MuiSelect-icon{top:10px}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .slt-acc-drp .MuiInputLabel-outlined.MuiInputLabel-shrink{top:2px !important}.qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-grd-head .qadpt-usrfilter button{top:-4px !important}.qadpt-userButton{position:relative;right:0;top:10px}.qadpt-usercreatepopup,.qadpt-userEditpopup{border:0 !important;margin:0 !important;width:400px !important;max-height:580px;min-height:400px;top:70px;right:460px;position:fixed !important;display:block !important;background:var(--white-color) !important;box-shadow:0 3px 8px #000;border-radius:4px}.qadpt-usercreatepopup .qadpt-title-sec,.qadpt-userEditpopup .qadpt-title-sec{padding:15px;border-bottom:1px solid var(--border-color)}.qadpt-usercreatepopup .qadpt-title-sec .qadpt-title,.qadpt-userEditpopup .qadpt-title-sec .qadpt-title{font-weight:600;font-size:18px;line-height:20px}.qadpt-usercreatepopup .qadpt-title-sec .qadpt-description,.qadpt-userEditpopup .qadpt-title-sec .qadpt-description{font-size:12px;line-height:18px}.qadpt-usercreatepopup .qadpt-formcontent,.qadpt-userEditpopup .qadpt-formcontent{width:100% !important;max-height:calc(100vh - 200px);overflow:hidden auto;font-size:14px;height:353px}.qadpt-usercreatepopup .qadpt-formcontent .qadpt-usrname,.qadpt-userEditpopup .qadpt-formcontent .qadpt-usrname{display:flex}.qadpt-usercreatepopup .qadpt-formcontent .qadpt-usrname>div,.qadpt-userEditpopup .qadpt-formcontent .qadpt-usrname>div{width:50%}.qadpt-usercreatepopup .qadpt-formcontent .qadpt-usrname .qadpt-userfields .MuiFormHelperText-root,.qadpt-userEditpopup .qadpt-formcontent .qadpt-usrname .qadpt-userfields .MuiFormHelperText-root{width:150px}.qadpt-usercreatepopup .qadpt-formcontent .qadpt-usrname div:nth-child(2),.qadpt-userEditpopup .qadpt-formcontent .qadpt-usrname div:nth-child(2){margin-left:10px}.qadpt-usercreatepopup .qadpt-formcontent .MuiGrid-root,.qadpt-userEditpopup .qadpt-formcontent .MuiGrid-root{padding-top:4px}.qadpt-usercreatepopup .qadpt-userfields,.qadpt-userEditpopup .qadpt-userfields{width:100%;margin-bottom:-2px}.qadpt-usercreatepopup .qadpt-userfields .MuiFormHelperText-root.error,.qadpt-userEditpopup .qadpt-userfields .MuiFormHelperText-root.error{color:var(--error-color) !important}.qadpt-usercreatepopup .qadpt-userfields .MuiSvgIcon-root,.qadpt-userEditpopup .qadpt-userfields .MuiSvgIcon-root{height:16px;width:16px}.qadpt-usercreatepopup .qadpt-genderbuttons,.qadpt-userEditpopup .qadpt-genderbuttons{height:40px;gap:7px;margin:0 -7px}.qadpt-usercreatepopup .qadpt-genderbuttons .MuiButton-root,.qadpt-userEditpopup .qadpt-genderbuttons .MuiButton-root{min-width:100px;color:var(--button-bg-color);border:1px solid var(--button-bg-color) !important;font-size:12px;white-space:nowrap;border-radius:14px;text-transform:capitalize}.qadpt-usercreatepopup .qadpt-genderbuttons .MuiButton-root:hover,.qadpt-userEditpopup .qadpt-genderbuttons .MuiButton-root:hover{background-color:var(--button-bg-color);color:var(--white-color)}.qadpt-usercreatepopup .qadpt-genderbuttons .selected,.qadpt-userEditpopup .qadpt-genderbuttons .selected{background-color:var(--button-bg-color);color:var(--white-color)}.qadpt-usercreatepopup .qadpt-submitbutton,.qadpt-userEditpopup .qadpt-submitbutton{padding:15px;width:100%;text-align:end;border-top:1px solid var(--bordaer-color)}.qadpt-usercreatepopup .qadpt-submitbutton button,.qadpt-userEditpopup .qadpt-submitbutton button{color:var(--white-color);border-radius:4px;border:1px solid var(--button-bg-color);background-color:var(--button-bg-color);text-transform:capitalize;padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important}.qadpt-account{margin-right:6px;background-color:var(--white-color);border-radius:6px;height:calc(100vh - 90px)}.qadpt-account.sidebaropen{margin-left:190px}.qadpt-account .qadpt-accttitles{top:1px;position:relative;left:-420px}.qadpt-account .qadpt-accttitles h1{position:relative;right:28px}.qadpt-account .qadpt-accttitles .qadpt-hdrsideon{top:-15px;position:relative;left:440px;font-weight:bold}.qadpt-account .qadpt-accttitles .qadpt-hdrsideoff{top:-15px;position:relative;left:50px;font-weight:bold}.accountlistGrid{height:calc(100vh - 195px);width:calc(100% - 20px);margin-top:2px;top:-17px;position:relative;display:flex;flex-direction:column}.accountlistGrid.sidebarOpen{margin-left:12px}.accountlistGrid.sidebarClosed{margin-left:1px}.accountlistGrid .qadpt-searchfilter{right:20px;float:right;width:200px;margin-bottom:8px !important}.accountlistGrid .qadpt-searchfilter input{padding:10px 14px}.accountlistGrid .qadpt-searchfilter .MuiFormHelperText-root{color:inherit}.accountlistGrid .qadpt-searchfilter .MuiFormHelperText-root.error{color:var(--error-color)}.accountlistGrid .qadpt-searchfilter .MuiOutlinedInput-root{padding:0px;border-radius:20px}.accountlistGrid .qadpt-searchfilter .MuiInputBase-input{height:1em;padding-left:0px}.qadpt-accountCreateButtonPosition{top:-163px;position:relative}.qadpt-accountcreatepopup,.qadpt-accounteditpopup{z-index:99;width:400px !important;max-height:350px;top:200px;right:30%;position:fixed !important;background:var(--white-color) !important;box-shadow:0 3px 8px #000;border-radius:4px}.qadpt-accountcreatepopup .qadpt-title-sec,.qadpt-accounteditpopup .qadpt-title-sec{padding:15px;border-bottom:1px solid var(--border-color)}.qadpt-accountcreatepopup .qadpt-title-sec .qadpt-title,.qadpt-accounteditpopup .qadpt-title-sec .qadpt-title{font-size:18px;font-weight:600}.qadpt-accountcreatepopup .qadpt-accountcreatefield,.qadpt-accounteditpopup .qadpt-accountcreatefield{position:relative;font-size:14px;font-weight:600;margin:16px 16px 0}.qadpt-accountcreatepopup .qadpt-accountcreatefield.qadpt-error,.qadpt-accounteditpopup .qadpt-accountcreatefield.qadpt-error{color:var(--error-color)}.qadpt-accountcreatepopup .qadpt-accountcreatefield .qadpt-acctfield,.qadpt-accounteditpopup .qadpt-accountcreatefield .qadpt-acctfield{width:100%}.qadpt-accountcreatepopup .qadpt-accountcreatefield .qadpt-acctfield .MuiFormHelperText-root,.qadpt-accounteditpopup .qadpt-accountcreatefield .qadpt-acctfield .MuiFormHelperText-root{color:var(--error-color);line-height:12px}.qadpt-accountcreatepopup .qadpt-accountcreatefield .qadpt-acctfield .MuiInputBase-root-MuiOutlinedInput-root,.qadpt-accounteditpopup .qadpt-accountcreatefield .qadpt-acctfield .MuiInputBase-root-MuiOutlinedInput-root{border-radius:4px !important}.qadpt-accountcreatepopup form,.qadpt-accounteditpopup form{height:calc(100% - 100px)}.qadpt-accountcreatepopup .qadpt-account-buttons,.qadpt-accounteditpopup .qadpt-account-buttons{padding:15px;border-top:1px solid var(--border-color);text-align:end}.qadpt-accountcreatepopup .qadpt-account-buttons .qadpt-save-btn,.qadpt-accounteditpopup .qadpt-account-buttons .qadpt-save-btn{background-color:var(--button-bg-color);color:var(--white-color);text-transform:capitalize;padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important}.qadpt-accountcreatepopup .qadpt-account-buttons .qadpt-save-btn.invalid,.qadpt-accounteditpopup .qadpt-account-buttons .qadpt-save-btn.invalid{background-color:#a5c3c5;pointer-events:none}.qadpt-roleeditpopup,.qadpt-roledeletepopup{z-index:9;width:500px !important;position:fixed !important;background:var(--white-color) !important;box-shadow:0 3px 8px #000;border-radius:4px}.qadpt-roleeditpopup.qadpt-roleeditpopup,.qadpt-roledeletepopup.qadpt-roleeditpopup{max-height:500px;top:140px;right:420px}.qadpt-roleeditpopup.qadpt-roledeletepopup,.qadpt-roledeletepopup.qadpt-roledeletepopup{width:395px !important;height:23%;top:252px;right:30%;padding:10px}.qadpt-roleeditpopup .qadpt-title-sec,.qadpt-roledeletepopup .qadpt-title-sec{padding:15px;border-bottom:1px solid var(--border-color)}.qadpt-roleeditpopup .qadpt-title-sec .qadpt-title,.qadpt-roledeletepopup .qadpt-title-sec .qadpt-title{font-size:18px;font-weight:600;margin-top:0}.qadpt-roleeditpopup .qadpt-subtitle,.qadpt-roledeletepopup .qadpt-subtitle{font-size:14px;padding:10px 20px 20px 5px}.qadpt-roleeditpopup .qadpt-addrole,.qadpt-roledeletepopup .qadpt-addrole{height:calc(100% - 75px);overflow:auto;padding:0 15px 15px 15px}.qadpt-roleeditpopup .qadpt-addrole .MuiGrid-container,.qadpt-roledeletepopup .qadpt-addrole .MuiGrid-container{margin-top:0 !important}.qadpt-roleeditpopup .qadpt-addrole .MuiGrid-item .MuiInputLabel-outlined.MuiInputLabel-shrink,.qadpt-roledeletepopup .qadpt-addrole .MuiGrid-item .MuiInputLabel-outlined.MuiInputLabel-shrink{top:7px !important}.qadpt-roleeditpopup .qadpt-role-buttons,.qadpt-roledeletepopup .qadpt-role-buttons{position:relative;bottom:0;text-align:right;padding:15px;border-top:1px solid var(--border-color)}.qadpt-roleeditpopup .qadpt-role-buttons button,.qadpt-roledeletepopup .qadpt-role-buttons button{margin-left:16px;border-radius:4px;border:1px solid var(--button-bg-color);color:var(--button-bg-color);text-transform:capitalize;padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important}.qadpt-roleeditpopup .qadpt-role-buttons .qadpt-conform-button,.qadpt-roledeletepopup .qadpt-role-buttons .qadpt-conform-button{background-color:var(--button-bg-color) !important;color:var(--white-color) !important}.qadpt-roleeditpopup .qadpt-role-buttons .qadpt-conform-button.qadpt-disabled,.qadpt-roledeletepopup .qadpt-role-buttons .qadpt-conform-button.qadpt-disabled{background-color:#a5c3c5 !important;border:none}.qadpt-modal-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.5);z-index:9;pointer-events:auto}.qadpt-iconcloseaccountcreate{position:absolute;top:20px;cursor:pointer;right:15px;width:15px}.qadpt-accountCreateButtonsdiv{display:flex;gap:150px;position:relative;top:-25px;left:60px}.qadpt-accountCreatesaveButtonsdiv{position:relative;right:-280px;top:95px;width:80px}.qadpt-accountcreateButton{position:absolute;right:20px;z-index:99;top:55px;background-color:var(--button-bg-color);border:none;border-radius:15px;color:var(--white-color);cursor:pointer;font-size:14px;padding:10px 14px 10px 14px;height:44px;width:183px}.qadpt-buttoncolor{color:#000}.accountlistGrid{height:calc(100vh - 195px);width:calc(100% - 20px);margin-top:2px;top:-17px;position:relative;display:flex;flex-direction:column}.accountlistGrid.sidebarOpen{margin-left:12px}.accountlistGrid.sidebarClosed{margin-left:1px}.qadpt-editaccounttexthdr{position:relative;bottom:60px;right:100px}.qadpt-editaccountfields{position:relative;bottom:70px;right:10px}.qadpt-editaccountbuttonsposition{position:relative;bottom:70px;display:flex;gap:120px}.qadpt-editaccountcancelbuttons{position:absolute;right:300px;z-index:99;top:0px;background-color:#007bff;border:none;border-radius:5px;color:var(--white-color);cursor:pointer;font-size:14px;padding:7px 21px}.qadpt-editaccountsavebuttons{position:absolute;right:60px;z-index:99;top:0px;background-color:#007bff;border:none;border-radius:10px;color:var(--white-color);cursor:pointer;font-size:14px;padding:10px 25px}.qadpt-accountalert{top:40px !important;width:400px;left:55% !important;z-index:999999 !important}.qadpt-accountalert .MuiPaper-root.MuiAlert-root{width:100%}.Addiconingrid{cursor:pointer !important;transition:color .2s ease-in-out}.Addiconingrid:hover{color:#5f9ea0 !important}.smoothtransition{transition:margin-left .3s ease}.team-settings{padding:20px}.UserRoleTitle{font-weight:700;font-size:23px;margin-top:-14px}.Loaderstyles{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);z-index:1}.LoaderSpinnerStyles{width:50px;height:50px}.selectaccountGlobal{top:25px;right:51px;left:567px;width:200px}.selectaccountGloballeft{top:25px;right:51px;left:581px;width:200px}.addUserroleglobal{right:35px;height:34px;top:27px;font-size:x-small;left:586px}.addUserrolegloballeft{right:35px;height:34px;top:27px;font-size:x-small;left:611px}.header{background-color:#a3c2c2}.searchContainer{display:flex;justify-content:flex-end;margin-bottom:16px}.selectAccount{max-width:200px;right:51px}.MuiOutlinedInput-input{padding:0;margin-top:5px;margin-left:31px}.MuiOutlinedInput-notchedOutline{border-radius:5px;height:35px}.MuiInputLabel-outlined{top:-6px !important}.MuiInputLabel-outlined.MuiInputLabel-shrink{top:0 !important}.MuiAutocomplete-inputRoot{height:35px}.addUserRoleButton{right:35px;height:34px;top:13px;font-size:x-small}.addUserRoleButton .MuiSvgIcon-root{margin-right:8px}.dataGridContainer{height:344px;width:100%;margin-top:16px}.dataGridContainer .header{background-color:#a3c2c2}.qadpt-setsidebar{top:20px;position:relative;padding:10px 20px}.qadpt-setsidebar .qadpt-sidebarlist{font-size:12px;font-weight:600;color:#8a8a8a;text-transform:uppercase}.qadpt-setsidebar .qadpt-sidebarinput{padding:3px 22px;margin-bottom:4px;border-radius:6px;background-color:rgba(0,0,0,0)}.qadpt-setsidebar .qadpt-sidebarinput.active{background-color:#5f9ea0 !important;color:#fff !important}.qadpt-setsidebar .qadpt-sidebarinput.active .MuiTypography-root{color:#fff !important}.qadpt-setsidebar .qadpt-sidebarinput:hover{background-color:rgba(0,0,0,.08)}.qadpt-setsidebar .qadpt-sidebarval{font-size:14px;font-weight:400;line-height:21px;letter-spacing:.3px;text-align:left;margin-left:-2px;color:#202224}.qadpt-setsidebar .qadpt-sidebarval.active{color:#fff !important}.qadpt-unistpg{display:flex !important;flex-direction:column;align-items:center;background-color:#f9f1f0;margin:20px;padding:0;max-width:calc(100% - 40px) !important;border-radius:20px;height:calc(100vh - 40px)}.qadpt-unistpg .qadpt-brand-logo{margin:40px}.qadpt-unistpg .qadpt-brand-logo .qadpt-brand-logo-img{width:300px;height:auto}.qadpt-unistpg .qadpt-mgs{text-align:center;margin-top:50px}.qadpt-unistpg .qadpt-mgs>div:first-child{color:#222;font-weight:600;font-size:20px;margin-bottom:10px}.qadpt-unistpg .qadpt-mgs>div:nth-child(2){font-size:16px;margin-bottom:3px}.qadpt-unistpg .qadpt-mgs>div:nth-child(3){font-size:16px;display:flex;align-items:center;place-content:center}.qadpt-unistpg .qadpt-mgs>div:nth-child(3) img{padding:0 4px}.qadpt-unistpg .qadpt-mgs>div:nth-child(3) a{color:var(--button-bg-color);padding-right:4px}.qadpt-unistpg .qadpt-feedbkfrm{max-width:500px;margin-top:20px}.qadpt-unistpg .qadpt-feedbkfrm div{margin:0 0 5px 0 !important}.qadpt-unistpg .qadpt-feedbkfrm textarea{width:-webkit-fill-available;padding:10px;font-size:16px;border-radius:8px;border:1px solid #ddd;margin-bottom:20px;resize:none;outline:none}.qadpt-unistpg .qadpt-btn{padding:10px 20px;background-color:#5f9ea0;width:100%;color:#fff;border:none;border-radius:15px;font-size:16px;cursor:pointer;transition:background-color .3s ease}.qadpt-unistpg .qadpt-thkpg{text-align:center;margin-top:40px}.qadpt-unistpg .qadpt-thkpg .qadpt-thkmsg{display:flex;flex-direction:column;align-items:center;place-content:center;margin-top:20px}.qadpt-unistpg .qadpt-thkpg .qadpt-thkmsg>div:nth-child(1){font-weight:600;font-size:20px}.qadpt-unistpg .qadpt-thkpg .qadpt-thkmsg>div:nth-child(2){font-size:16px;width:calc(100% - 180px);text-align:center;margin-top:8px}.userEditpopup h1{background-image:linear-gradient(to right, rgb(37, 81, 181), rgba(0, 0, 0, 0.7));font-size:17px;color:var(--white-color);height:44px;padding:5px 18px;margin:6px -194px 17px;text-align:center;width:340px;margin-right:101px;margin-left:-21px}.userEditpopup{z-index:16000;width:334px !important;height:35vh;top:223px;right:683px;padding:0px 20px;position:fixed !important;display:block !important;background:var(--white-color) !important;box-shadow:0 3px 4px #000}.cancel{position:relative;background:#fff;color:#007bff;border:1px solid #007bff;margin-right:173px;right:-71px;top:64px;position:absolute;right:37px;z-index:99;top:142px;border-radius:5px;cursor:pointer;font-size:14px;padding:7px 21px}.deactivate{position:relative;right:-147px;top:65px;position:absolute;right:37px;z-index:99;top:67px;background-color:#007bff;border:none;border-radius:5px;color:var(--white-color);cursor:pointer;font-size:14px;padding:7px 21px}.userButton{position:absolute;z-index:99;background-color:#007bff;border:none;border-radius:5px;color:var(--white-color);cursor:pointer;font-size:14px;padding:7px 21px;position:relative;right:-147px;top:66px}.createButton{position:absolute;right:37px;z-index:99;top:67px;background-color:#5f9ea0;border:none;border-radius:5px;color:var(--white-color);cursor:pointer;font-size:14px;padding:7px 21px}.qadpt-org-filter .MuiFormControl-root .MuiFormLabel-root{padding-left:5px}.qadpt-org-filter .MuiFormControl-root .MuiFormLabel-root.MuiInputLabel-shrink{left:6px;top:9px !important}.qadpt-org-filter .MuiFormControl-root .MuiInputBase-root.Mui-focused input{padding-top:5px !important}.qadpt-org-filter .MuiFormControl-root .MuiInputBase-root.Mui-focused .MuiAutocomplete-endAdornment{padding-top:5px !important}.qadpt-org-filter .MuiFormControl-root fieldset{margin:0 10px}.qadpt-orgcont .qadpt-head{border-bottom:1px solid var(--border-color);margin:0 -20px;padding:20px 20px 10px 20px;display:flex;align-items:center}.qadpt-orgcont .qadpt-head .qadpt-title-sec{width:calc(100% - 200px)}.qadpt-orgcont .qadpt-head .qadpt-title-sec .qadpt-title{font-weight:600;font-size:22px;line-height:20px}.qadpt-orgcont .qadpt-head .qadpt-right-part{text-align:right;width:210px}.qadpt-orgcont .qadpt-head .qadpt-right-part .MuiInputLabel-outlined.MuiInputLabel-shrink{top:7px !important}.qadpt-orgcont .qadpt-head .qadpt-right-part .MuiAutocomplete-root{padding:5px}.qadpt-org-grd{border-color:var(--grid-border-color) !important;top:10px;position:relative !important;height:calc(100vh - 127px) !important}.qadpt-org-grd .MuiDataGrid-virtualScroller{height:calc(100vh - 237px) !important}.qadpt-org-grd .MuiDataGrid-footerContainer .MuiTablePagination-root .MuiInputBase-root .MuiSelect-select.MuiTablePagination-select{padding-right:24px !important}.title{font-family:Syncopate;font-size:19px;font-weight:700;line-height:19.78px;letter-spacing:.3000000119px;text-align:center;color:#5f9ea0}.welcomeback{font-family:Poppins;font-size:24px;font-weight:700;line-height:36px;letter-spacing:.3000000119px;text-align:center;width:188px;height:36px;gap:0px;opacity:0px;color:#222}.email{font-family:Poppins;font-size:16px;font-weight:400;line-height:24px;text-align:center;width:44px;height:24px;gap:0px;opacity:0px;color:#444}.qadpt-superadminlogin,.qadpt-resetpassword{display:flex !important;flex-direction:column;align-items:center;background-color:#f9f1f0;padding:20px;max-width:100% !important;height:100%}.qadpt-superadminlogin .qadpt-brand-logo,.qadpt-resetpassword .qadpt-brand-logo{margin:40px}.qadpt-superadminlogin .qadpt-brand-logo .qadpt-brand-logo-img,.qadpt-resetpassword .qadpt-brand-logo .qadpt-brand-logo-img{width:300px;height:auto}.qadpt-superadminlogin .qadpt-welcome-message,.qadpt-resetpassword .qadpt-welcome-message{margin-top:60px;margin-bottom:20px;text-align:center}.qadpt-superadminlogin .qadpt-welcome-message .qadpt-welcome-message-text,.qadpt-resetpassword .qadpt-welcome-message .qadpt-welcome-message-text{font-family:\"Arial\",sans-serif;color:#333;font-weight:bold;font-size:22px}.qadpt-superadminlogin .qadpt-login-form,.qadpt-resetpassword .qadpt-login-form{width:100%;max-width:300px;text-align:center;margin-top:30px}.qadpt-superadminlogin .qadpt-login-form .qadpt-form-label,.qadpt-resetpassword .qadpt-login-form .qadpt-form-label{text-align:left;color:#444;margin-top:10px;font-size:14px}.qadpt-superadminlogin .qadpt-login-form .qadpt-form-label span,.qadpt-resetpassword .qadpt-login-form .qadpt-form-label span{cursor:pointer}.qadpt-superadminlogin .qadpt-login-form .qadpt-custom-input,.qadpt-resetpassword .qadpt-login-form .qadpt-custom-input{border-radius:8px !important;border:1px solid #ccc;margin-top:8px;width:100%;background-color:var(--white-color)}.qadpt-superadminlogin .qadpt-login-form .qadpt-text-danger,.qadpt-resetpassword .qadpt-login-form .qadpt-text-danger{color:#d9534f;font-size:.9rem}.qadpt-superadminlogin .qadpt-login-form .qadpt-button-text,.qadpt-resetpassword .qadpt-login-form .qadpt-button-text{font-family:\"Arial\",sans-serif;font-size:1rem;font-weight:500;text-transform:none}.qadpt-superadminlogin .qadpt-login-form .MuiOutlinedInput-notchedOutline,.qadpt-resetpassword .qadpt-login-form .MuiOutlinedInput-notchedOutline{border:0 !important}.qadpt-superadminlogin .qadpt-login-form .qadpt-passwordhint,.qadpt-resetpassword .qadpt-login-form .qadpt-passwordhint{width:300px;height:50px;border-radius:6px;border:1px solid #dcdde1;margin-top:10px}.qadpt-superadminlogin .qadpt-login-form .qadpt-passwordhint .qadpt-passwordhint-text,.qadpt-resetpassword .qadpt-login-form .qadpt-passwordhint .qadpt-passwordhint-text{text-align:left;margin-left:13px;font-size:14px}.qadpt-superadminlogin .qadpt-login-form .qadpt-passwordhint .qadpt-passwordhint-container,.qadpt-resetpassword .qadpt-login-form .qadpt-passwordhint .qadpt-passwordhint-container{display:flex;align-items:center;margin-left:13px;gap:10px}.qadpt-superadminlogin .qadpt-login-form .qadpt-passwordhint .qadpt-passwordhint-container svg,.qadpt-resetpassword .qadpt-login-form .qadpt-passwordhint .qadpt-passwordhint-container svg{font-size:18px !important}.qadpt-superadminlogin .qadpt-login-form .qadpt-passwordhint .qadpt-checkicon-valid,.qadpt-resetpassword .qadpt-login-form .qadpt-passwordhint .qadpt-checkicon-valid{color:#5f9ea0;font-size:18px}.qadpt-superadminlogin .qadpt-login-form .qadpt-passwordhint .qadpt-passwordhint-item,.qadpt-resetpassword .qadpt-login-form .qadpt-passwordhint .qadpt-passwordhint-item{font-size:12px;line-height:21px}.qadpt-superadminlogin .qadpt-login-footer,.qadpt-resetpassword .qadpt-login-footer{position:absolute;bottom:10px;left:42%;font-size:14px;text-align:center}.qadpt-superadminlogin .qadpt-login-footer .qadpt-footer-text,.qadpt-resetpassword .qadpt-login-footer .qadpt-footer-text{color:#5f9ea0}.qadpt-superadminlogin .qadpt-login-footer .qadpt-footer-text .qadpt-footer-link,.qadpt-resetpassword .qadpt-login-footer .qadpt-footer-text .qadpt-footer-link{color:#5f9ea0;text-decoration:none;margin:0 10px}.qadpt-superadminlogin .qadpt-login-footer .qadpt-footer-text .qadpt-footer-link:hover,.qadpt-resetpassword .qadpt-login-footer .qadpt-footer-text .qadpt-footer-link:hover{text-decoration:underline}.qadpt-resetpassword .qadpt-pwd-changed{text-align:center;margin-top:50%}.qadpt-resetpassword .qadpt-pwd-title{font-size:20px !important;font-weight:600 !important}.qadpt-resetpassword .qadpt-changed-msg{width:300px;margin-bottom:-10px !important}.qadpt-feedbackpopup{width:calc(100vh - 270px);height:calc(100vh - 190px);border-radius:8px !important;position:relative !important;padding:20px;overflow:hidden !important;margin-top:76px !important}.qadpt-feedbackpopup .qadpt-feedback-header{margin-bottom:10px;display:flex}.qadpt-feedbackpopup .qadpt-feedback-header .qadpt-feedback-title{font-size:18px;font-weight:600}.qadpt-feedbackpopup .qadpt-feedback-header .qadpt-feedback-title .qadpt-feedback-subtitle{font-size:12px;font-weight:400;color:#c3c3c3;margin-top:8px;text-align:center}.qadpt-feedbackpopup .qadpt-feedback-header .qadpt-close-icon{position:absolute !important;right:15px}.qadpt-feedbackpopup .qadpt-feedback-header .qadpt-close-icon svg{height:16px;width:16px}.qadpt-feedbackpopup .qadpt-content{height:calc(100vh - 240px);overflow:auto}.qadpt-feedbackpopup .qadpt-content .qadpt-container{margin-top:6px;font-size:12px;font-weight:400}.qadpt-feedbackpopup .qadpt-content .qadpt-container .qadpt-label{font-weight:600}.qadpt-feedbackpopup .qadpt-content .qadpt-rating{display:flex;justify-content:center;margin-top:10px;overflow:hidden}.qadpt-feedbackpopup .qadpt-content .qadpt-rating .qadpt-rating-stars .MuiRating-icon{font-size:25px;transition:none;padding:0 10px;margin:0 10px !important}.qadpt-feedbackpopup .qadpt-content .qadpt-rating .qadpt-rating-stars .MuiRating-iconHover{color:#fff;transform:none;transition:none;opacity:1}.qadpt-feedbackpopup .qadpt-content .qadpt-rating .qadpt-rating-stars .MuiRating-iconFilled{color:#5f9ea0}.qadpt-feedbackpopup .qadpt-content .qadpt-rating .qadpt-rating-stars .MuiRating-iconEmpty{color:#222}.qadpt-feedbackpopup .qadpt-content .qadpt-textarea{margin-top:10px}.qadpt-feedbackpopup .qadpt-content .qadpt-textarea textarea{height:100px}.qadpt-feedbackpopup .qadpt-content .qadpt-upload-container{margin-top:10px;text-align:center;border:1px solid #ccc;border-radius:4px;padding:5px}.qadpt-feedbackpopup .qadpt-content .qadpt-upload-container .qadpt-upload-button{background-color:#fff !important;color:#000 !important;box-shadow:none !important;width:100%;height:20px;text-transform:capitalize !important;padding:0 !important;font-weight:600 !important;font-size:12px !important}.qadpt-feedbackpopup .qadpt-content .qadpt-upload-container .qadpt-upload-description{white-space:nowrap;text-overflow:ellipsis;font-size:11px;text-transform:capitalize}.qadpt-feedbackpopup .qadpt-content .qadpt-file-list .qadpt-list-item{border:1px solid #ccc;padding:2px !important;display:flex !important;align-items:center !important;justify-content:space-between !important;margin-top:5px;border-radius:5px}.qadpt-feedbackpopup .qadpt-content .qadpt-file-list .qadpt-file-name{margin:7px;font-size:14px}.qadpt-feedbackpopup .qadpt-content .qadpt-file-list .qadpt-file-actions{display:flex;align-items:center}.qadpt-feedbackpopup .qadpt-content .qadpt-file-list .qadpt-file-actions svg{height:21px;width:21px}.qadpt-feedbackpopup .qadpt-content .qadpt-file-list .qadpt-fileerror{color:var(--error-color);font-size:12px;margin-top:-10px;margin-left:61px}.qadpt-feedbackpopup .qadpt-footer button{float:right}.qadpt-feedbackpopup .qadpt-footer .qadpt-submit-button{background-color:var(--color-primary-600) !important;color:var(--color-white) !important;border-radius:var(--radius-md) !important;top:10px;transition:var(--transition-fast);font-weight:var(--font-weight-medium)}.qadpt-feedbackpopup .qadpt-footer .qadpt-submit-button:hover{background-color:var(--color-primary-700) !important;box-shadow:var(--shadow-md)}.qadpt-feedbackpopup .qadpt-footer .qadpt-submit-button.qadpt-submit-disabled{background-color:var(--color-gray-300) !important;color:var(--color-gray-500) !important}.qadpt-feedback-dialog{padding:20px;border-radius:8px;width:320px;text-align:center;height:423px}.qadpt-feedback-dialog .qadpt-dialogcontent{position:relative;top:40%;display:flex;flex-direction:column;align-items:center}.qadpt-feedback-dialog .qadpt-dialogcontent .qadpt-message{font-weight:bold;font-size:15px}.qadpt-feedback-dialog .qadpt-dialogcontent .qadpt-actionbutton{display:flex;align-items:center;place-content:center}.qadpt-feedback-dialog .qadpt-dialogcontent .qadpt-actionbutton button{background:var(--color-primary-600);color:var(--color-white) !important;border-radius:var(--radius-md);transition:var(--transition-fast);font-weight:var(--font-weight-medium)}.qadpt-feedback-dialog .qadpt-dialogcontent .qadpt-actionbutton button:hover{background:var(--color-primary-700);box-shadow:var(--shadow-md)}.rtl .qadpt-banner{direction:rtl !important}.rtl .qadpt-banner .adapat-banner-right{right:auto !important;left:9px}.rtl .qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper{direction:rtl;float:right;right:0px;border-right:0;border-left:1px solid var(--border-color)}.rtl .qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-icon img[alt=Banners],.rtl .qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-icon img[alt=Tooltips],.rtl .qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-icon img[alt=Surveys]{transform:scaleX(-1)}.rtl .qadpt-page-content .qadpt-settings-content{left:0 !important;right:240px}.rtl .qadpt-orgcont{right:20px;left:auto}.rtl .qadpt-homepg,.rtl .qadpt-web{left:auto;right:220px}.rtl .qadpt-homepg .qadpt-webcontent .qadpt-setting-title .qadpt-back-text,.rtl .qadpt-web .qadpt-webcontent .qadpt-setting-title .qadpt-back-text{right:10px;left:0 !important}.rtl .qadpt-homepg .qadpt-webcontent .qadpt-setting-title .qadpt-titsec .qadpt-action-btn .MuiButton-icon,.rtl .qadpt-web .qadpt-webcontent .qadpt-setting-title .qadpt-titsec .qadpt-action-btn .MuiButton-icon{margin-left:8px !important;margin-right:-4px !important}.rtl .qadpt-homepg .qadpt-webcontent .qadpt-set-right .qadpt-page-target .qadpt-header-container .qadpt-add-target-btn.save,.rtl .qadpt-web .qadpt-webcontent .qadpt-set-right .qadpt-page-target .qadpt-header-container .qadpt-add-target-btn.save{margin-right:10px;margin-left:0 !important}.rtl .qadpt-homepg .qadpt-webcontent .qadpt-set-right .qadpt-rev-publish .MuiCardContent-root .MuiGrid-root .MuiGrid-item,.rtl .qadpt-web .qadpt-webcontent .qadpt-set-right .qadpt-rev-publish .MuiCardContent-root .MuiGrid-root .MuiGrid-item{padding-right:16px;padding-left:0 !important}.rtl .qadpt-homepg .qadpt-webcontent .qadpt-set-right .qadpt-rev-publish .MuiCardContent-root .MuiGrid-root .MuiGrid-item:first-of-type,.rtl .qadpt-web .qadpt-webcontent .qadpt-set-right .qadpt-rev-publish .MuiCardContent-root .MuiGrid-root .MuiGrid-item:first-of-type{padding-right:0 !important;padding-left:0 !important}.rtl .qadpt-homepg .qadpt-webcontent .qadpt-set-right .qadpt-rev-publish .qadpt-gridleft,.rtl .qadpt-web .qadpt-webcontent .qadpt-set-right .qadpt-rev-publish .qadpt-gridleft{border-left:1px solid #ccc;border-right:0 !important}.rtl .qadpt-homepg .qadpt-webcontent .qadpt-set-right .qadpt-frequency .qadpt-card fieldset .MuiGrid-root .MuiGrid-item:nth-child(4) label,.rtl .qadpt-web .qadpt-webcontent .qadpt-set-right .qadpt-frequency .qadpt-card fieldset .MuiGrid-root .MuiGrid-item:nth-child(4) label{margin-right:0px !important;margin-left:16px !important}.rtl .css-5kysay-MuiDataGrid-root .MuiDataGrid-cell--textLeft{text-align:right}.rtl .MuiTablePagination-actions .MuiButtonBase-root .MuiSvgIcon-root{transform:rotate(180deg) !important}.rtl .qadpt-orgcont .qadpt-head .qadpt-right-part{text-align:left}.rtl .qadpt-createpopup .qadpt-closeicon{left:15px;right:auto}.rtl .MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--vertical{right:auto;left:0}.rtl .qadpt-page-content .qadpt-side-menu{right:10px;left:auto}.rtl .qadpt-feedbackpopup .character-count{text-align:left !important}.rtl .qadpt-feedbackpopup .qadpt-upload-button .css-1d6wzja-MuiButton-startIcon{margin:0 8px}.rtl .qadpt-feedbackpopup .qadpt-rating-stars.css-1qqgbpl-MuiRating-root{text-align:right !important}.rtl .qadpt-feedbackpopup .qadpt-feedback-header .qadpt-close-icon{left:15px;right:auto !important}.rtl .confirm-actions{gap:5px}.rtl .qadpt-prfsidebar .qadpt-prftitle{text-align:right !important;float:right;margin-right:10px !important;margin-left:auto !important}.rtl .qadpt-prfbox .qadpt-txtctrl .qadpt-prffldval .MuiOutlinedInput-root input{text-align:right !important}.rtl .qadpt-prfbox .qadpt-txtctrl.gender-fld .css-hfutr2-MuiSvgIcon-root-MuiSelect-icon{right:auto !important;left:8px}.rtl .qadpt-crossIcon{float:left !important}.rtl .css-nswfot-MuiDialogActions-root{gap:5px !important}.rtl .qadpt-midpart .qadpt-content-block .slt-acc-drp fieldset,.rtl .qadpt-midpart .qadpt-content-block .qadpt-filter-left .qadpt-select-form:nth-of-type(2) fieldset{text-align:right !important}.rtl .qadpt-midpart .qadpt-content-block .slt-acc-drp .MuiInputLabel-root,.rtl .qadpt-midpart .qadpt-content-block .qadpt-filter-left .qadpt-select-form:nth-of-type(2) .MuiInputLabel-root{right:21px;left:auto}.rtl .qadpt-midpart .qadpt-content-block .slt-acc-drp .MuiInputLabel-root:not(.qadpt-filter-left .qadpt-select-form:nth-of-type(2) .MuiInputLabel-root).MuiInputLabel-shrink,.rtl .qadpt-midpart .qadpt-content-block .qadpt-filter-left .qadpt-select-form:nth-of-type(2) .MuiInputLabel-root:not(.qadpt-filter-left .qadpt-select-form:nth-of-type(2) .MuiInputLabel-root).MuiInputLabel-shrink{right:-4px !important}.rtl .qadpt-midpart .qadpt-content-block .qadpt-head .qadpt-right-part{text-align:left !important}.rtl .qadpt-midpart .qadpt-content-block .qadpt-content-box .qadpt-src-flt .qadpt-teamsearch{right:auto !important;left:10px}.rtl .qadpt-midpart .qadpt-content-block .qadpt-account-grd .MuiDataGrid-root .MuiChip-deleteIcon{margin:0 -6px 0 5px !important}.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .drp-fields,.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .dt-fields{margin-left:15px;margin-right:0 !important}.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .drp-fields .MuiAutocomplete-endAdornment{left:9px;right:auto}.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .drp-fields fieldset,.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .dt-fields fieldset,.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .name-fld fieldset{text-align:right !important}.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .drp-fields .MuiInputLabel-root,.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .dt-fields .MuiInputLabel-root,.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .name-fld .MuiInputLabel-root{right:21px;left:auto}.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .drp-fields .MuiInputLabel-root.MuiInputLabel-shrink,.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .dt-fields .MuiInputLabel-root.MuiInputLabel-shrink,.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .name-fld .MuiInputLabel-root.MuiInputLabel-shrink{right:10px !important}.rtl .qadpt-midpart .qadpt-content-block .grid-toolbar-options .left-options .qadpt-DateTime.dt-fld2{margin-left:15px;margin-right:0 !important}.rtl .MuiSelect-icon{right:auto;left:7px}.rtl .qadpt-closeicon{left:15px;right:auto !important}.rtl .qadpt-mngpwd-popup .MuiDialogActions-root button:last-child{margin-right:8px;margin-left:0 !important}.rtl .qadpt-trainpop .qadpt-uplddoc .MuiButton-icon{margin-left:8px !important;margin-right:0px !important}.rtl .qadpt-usercreatepopup .qadpt-formcontent .qadpt-usrname div:nth-child(2){margin-right:10px;margin-left:0 !important}.rtl .qadpt-roleeditpopup .qadpt-addrole .MuiGrid-root .MuiGrid-item .MuiFormControl-root fieldset{text-align:right !important}.rtl .qadpt-roleeditpopup .qadpt-addrole .MuiGrid-root .MuiGrid-item .MuiFormControl-root .MuiInputLabel-root{right:21px;left:auto}.rtl .qadpt-roleeditpopup .qadpt-addrole .MuiGrid-root .MuiGrid-item .MuiFormControl-root .MuiInputLabel-root.MuiInputLabel-shrink{right:4px !important}.rtl .qadpt-roleeditpopup .qadpt-addrole .MuiGrid-root .MuiGrid-item:last-child .MuiFormControl-root .MuiInputLabel-root.MuiInputLabel-shrink{right:18px !important}.rtl .qadpt-rolesfltpopup .MuiDialog-container .qadpt-title .qadpt-close{left:8px;right:auto !important}.rtl .qadpt-rolesfltpopup .MuiDialog-container .MuiDialogContent-root .MuiButton-icon{margin-left:8px !important;margin-right:0px !important}.rtl .qadpt-prfpopup .qadpt-logout img{transform:scaleX(-1)}:root{--font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", sans-serif;--font-size-xs: 12px;--font-size-sm: 14px;--font-size-base: 16px;--font-size-lg: 18px;--font-size-xl: 20px;--font-size-2xl: 24px;--font-size-3xl: 30px;--font-weight-normal: 400;--font-weight-medium: 500;--font-weight-semibold: 600;--font-weight-bold: 700;--line-height-tight: 1.25;--line-height-normal: 1.5;--line-height-relaxed: 1.75;--color-white: #ffffff;--color-gray-50: #f9fafb;--color-gray-100: #f3f4f6;--color-gray-200: #e5e7eb;--color-gray-300: #d1d5db;--color-gray-400: #9ca3af;--color-gray-500: #6b7280;--color-gray-600: #4b5563;--color-gray-700: #374151;--color-gray-800: #1f2937;--color-gray-900: #111827;--color-primary-50: #eff6ff;--color-primary-100: #dbeafe;--color-primary-500: #3b82f6;--color-primary-600: #2563eb;--color-primary-700: #1d4ed8;--color-success-50: #f0fdf4;--color-success-500: #22c55e;--color-success-600: #16a34a;--color-error-50: #fef2f2;--color-error-500: #ef4444;--color-error-600: #dc2626;--color-warning-50: #fffbeb;--color-warning-500: #f59e0b;--color-warning-600: #d97706;--content-background-color: var(--color-gray-50);--white-color: var(--color-white);--border-color: var(--color-gray-200);--button-bg-color: var(--color-primary-600);--grid-border-color: var(--color-gray-200);--grid-head-background: var(--color-gray-50);--tool-tip-bg: var(--color-gray-800);--font-size: var(--font-size-sm) !important;--error-color: var(--color-error-500);--spacing-1: 0.25rem;--spacing-2: 0.5rem;--spacing-3: 0.75rem;--spacing-4: 1rem;--spacing-5: 1.25rem;--spacing-6: 1.5rem;--spacing-8: 2rem;--spacing-10: 2.5rem;--spacing-12: 3rem;--spacing-16: 4rem;--spacing-20: 5rem;--radius-sm: 0.25rem;--radius-md: 0.375rem;--radius-lg: 0.5rem;--radius-xl: 0.75rem;--radius-2xl: 1rem;--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);--box-bottom-shadow: var(--shadow-md);--box-right-shadow: var(--shadow-lg);--button-padding: var(--spacing-2) var(--spacing-4);--button-lineheight: var(--line-height-normal);--button-radius: var(--radius-md);--transition-fast: 150ms ease-in-out;--transition-normal: 250ms ease-in-out;--transition-slow: 350ms ease-in-out}code{font-family:var(--font-family) !important}*{font-family:var(--font-family) !important}body{margin:0;font-family:var(--font-family) !important;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fal,.far,.fad,.fas,.fab{font-family:\"qadapt-icons\" !important;font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;line-height:1}i{font-size:18px !important}@font-face{font-family:\"Gotham Pro\";font-style:normal}@font-face{font-family:\"Proxima Nova\";font-style:normal;src:local(\"Proxima Nova\"),local(\"ProximaNova-Regular\"),url(\"../assets/fonts/ProximaNova-Regular.woff2\") format(\"woff2\"),url(\"../assets/fonts/ProximaNova-Regular.woff\") format(\"woff\")}@font-face{font-family:\"qadapt-icons\";src:url(\"../assets/fonts/qadapt-icons.eot?qmcsfb\");src:url(\"../assets/fonts/qadapt-icons.eot?qmcsfb#iefix\") format(\"embedded-opentype\"),url(\"../assets/fonts/qadapt-icons.ttf?qmcsfb\") format(\"truetype\"),url(\"../assets/fonts/qadapt-icons.woff?qmcsfb\") format(\"woff\"),url(\"../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons\") format(\"svg\");font-weight:normal;font-style:normal;font-display:block}.scrollbar-container{height:calc(100% - 50px) !important}.qadpt-banner{background-color:var(--color-white);border-bottom:1px solid var(--color-gray-200);width:100%;height:45px;z-index:9;position:fixed;top:0;display:flex;align-items:center;box-shadow:var(--shadow-sm)}.qadpt-banner .menu-icon{cursor:pointer;height:27px;width:27px;margin:5px;margin-right:9px;margin-top:7px;display:flex;align-items:center;place-content:center}.qadpt-banner .adapat-banner-left{margin:0 10px;height:45px;position:relative;float:left;display:flex;align-items:center;place-content:center}.qadpt-banner .adapat-banner-left i{cursor:pointer}.qadpt-banner .adapat-banner-left .qadpt-logo{width:200px;height:auto;padding-top:10px}.qadpt-banner .adapat-banner-right{position:absolute;display:flex;right:9px}.qadpt-banner .qadapt-logo{cursor:pointer;margin-left:15px;color:#222;font-family:\"Syncopate\";font-size:14px;font-weight:700;line-height:14.57px;letter-spacing:.3px;text-align:left}.qadpt-banner .qxy-lang{width:140px;display:flex;align-items:center;place-content:center;margin-right:12px}.qadpt-banner .qxy-lang .lang-input{border:1px solid var(--color-gray-300);border-radius:var(--radius-md);padding:0 var(--spacing-3);background:var(--color-white);transition:var(--transition-fast)}.qadpt-banner .qxy-lang .lang-input:hover{border-color:var(--color-gray-400)}.qadpt-banner .qxy-lang .lang-input:focus-within{border-color:var(--color-primary-500);box-shadow:0 0 0 3px var(--color-primary-50)}.qadpt-banner .qxy-lang .lang-input .MuiInputBase-input{padding:5px !important;padding-right:24px !important}.qadpt-banner .qxy-lang .lang-input label+.lang-input-box{margin-top:0 !important}.qadpt-banner .qxy-lang .lang-input label+.lang-input-box::before{border-bottom:0 !important}.qadpt-page-content{display:flex;width:100%}.qadpt-page-content .qadpt-side-menu{width:240px;height:100%;display:block;position:absolute;z-index:9}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper{width:240px;border-right:1px solid var(--border-color);top:45px;background:var(--white-color)}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-cmgitems span:nth-of-type(1)>span>.qadpt-sm-item{padding-top:18px !important}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-cmgitems .qadpt-sm-item{margin-top:0 !important}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-cmgitems .cmg-soon{background:#d2e6ff;font-family:var(--font-family);font-size:11px;line-height:16px;color:#5c80ac;border-radius:20px;padding-left:8px;width:100px;margin-left:18px;top:9px;position:relative;z-index:9}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-item{margin-left:10px;margin-top:6px;width:220px;height:40px;padding:12px 23px;gap:-5px;border-radius:6px;display:flex;align-items:center;place-content:flex-start;white-space:nowrap;cursor:pointer}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-item:hover{background:rgba(95,157,159,.15);transition:background .3s ease}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-item.active{background-color:#5f9ea0;color:var(--white-color)}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-item.disabled{height:auto;background:#f9f9f9;cursor:not-allowed;pointer-events:auto !important}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-item .qadpt-sm-icon{min-width:40px;color:#202224}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-item .qadpt-sm-icon img{filter:none}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-item .qadpt-sm-icon.active{color:#fff}.qadpt-page-content .qadpt-side-menu .qadpt-smenu .MuiDrawer-paper .qadpt-smenu-list .qadpt-sm-item .qadpt-sm-icon.active img{filter:invert(30%) sepia(15%) saturate(75%) hue-rotate(45deg) contrast(100)}.qadpt-page-content .qadpt-settings-content{width:100%;left:240px;top:45px;position:relative}.qadpt-page-content .qadpt-settings-content.qadpt-viewprofile-page{top:auto !important;width:calc(100% - 240px)}.qadpt-page-content .qadpt-settings-content.qadpt-hide-smenu{top:auto !important;left:auto !important}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page{display:flex;width:100%}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .MuiBox-root{width:100%}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .MuiBox-root .qadpt-grd-action svg{color:var(--button-bg-color) !important;cursor:pointer}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page{width:calc(100% - 410px)}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .MuiBox-root{padding:20px 0 !important}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root{padding:0 !important}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst{height:calc(100vh - 200px);overflow:hidden auto}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst .qadpt-stdata{font-size:14px;width:calc(100% - 310px)}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst .qadpt-stdata .qadpt-steps{margin:15px 0}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst .qadpt-stdata .qadpt-steps .qadpt-subdata div{font-weight:600}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst .qadpt-codehdr{background-color:#f5f6fa;border-top-left-radius:15px;padding:9px;border-top-right-radius:15px}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst .qadpt-codehdrbt{background-color:#f5f6fa;border-bottom-left-radius:15px;padding:9px;border-bottom-right-radius:15px}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst .qadpt-codehdrbt button{border:1px solid #ccc;display:flex;align-items:center;padding:9px 5px;border-radius:15px;gap:8px;cursor:pointer}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst .qadpt-codehdrbt img{height:20px}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-content-box.MuiBox-root.qadpt-cdinst pre{margin:0 !important;background:#000;color:#fff;white-space:pre-wrap;word-break:break-word;overflow-wrap:break-word}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .css-1oqqzyl-MuiContainer-root{max-width:100% !important}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .qadpt-settings-smenu{width:180px}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .qadpt-settings-smenu .MuiBox-root{padding:0 !important}.qadpt-settings-container{display:flex;align-items:center;margin:0 10px}.qadpt-settings-container .qadapt-link{margin:0 10px;cursor:pointer}.App{text-align:center}.bg-circles{width:100%;height:100%;overflow:hidden}.circleOne,.circleTwo,.circleThree,.circleFour,.circleFive,.circleSix{position:absolute;border-radius:50%;border:2px solid rgba(226,225,231,.877);background-color:rgba(0,0,0,0);z-index:1}.circleOne{right:-133px;top:28%;transform:rotate(-90deg);height:100px;width:200px;border-radius:150px 150px 0 0;z-index:0}.circleTwo{width:40px;height:40px;top:160px;right:209px}.circleThree{height:150px;width:150px;top:-61px;right:529px;z-index:1}.circleFour{height:150px;width:150px;top:-67px;left:200px}.circleFive{width:50px;height:50px;top:168px;left:135px}.circleSix{width:40px;height:40px;left:82px;bottom:76px}.from-DateTime{border-radius:15px;height:40px}.lside-bgimg{height:calc(117vh - 193px);min-height:300px;background-repeat:no-repeat;background-size:auto;background-position:-6% 85%;margin-top:0px;max-width:-webkit-fill-available}.guides{margin:27px 36px}.guides-text{font-size:20px;font-weight:600;height:fit-content;min-height:36px;line-height:36px;-webkit-box-pack:justify;justify-content:space-between;width:100%;margin:16px 0px}.search-panel{background-color:#f4f7fd;padding:20px;border-radius:12px;margin-bottom:24px}.search-button{position:absolute;z-index:99999;background-color:#007bff;border:none;border-radius:5px;color:var(--white-color);cursor:pointer;font-size:14px;padding:7px 21px}.search-block{width:fit-content;display:flex;flex-direction:row;align-items:flex-start;gap:8px}.search-label{width:220px;height:36px;display:flex;-webkit-box-align:center;align-items:center;gap:8px;cursor:text;padding:10px 16px;box-sizing:border-box;border-radius:12px;overflow:hidden;border:1px solid #ecf0f5;background-color:#fff;color:#2e410a}.search-input{display:none;all:unset;width:100%;height:100%;font-size:12px;line-height:16px;font-weight:500}.filter-button{all:unset;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:row;-webkit-box-pack:center;justify-content:center;-webkit-box-align:center;align-items:center;height:fit-content;max-height:36px;font-size:14px;line-height:24px;font-weight:600;background-color:#fff;color:#2e4168;position:relative;max-width:100px;width:100px;border-radius:12px;gap:6px;border-width:1px;border-style:solid;border-color:#ecf0f5;border-image:initial;overflow:hidden;white-space:nowrap;padding:8px 0px}.filter-span{text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;user-select:none}.open-button{padding:0px;position:fixed;top:50%;transform:translateY(-50%);width:23px;height:50px;background-color:#999;color:var(--white-color);border:none;outline:none;cursor:pointer;z-index:99999999;border-radius:4px;transition:all .3s ease}.sidebar.open{width:14%;z-index:99999}.menu-items{display:flex;align-items:center;cursor:pointer;border-radius:4px;padding:9px 18px;min-width:150px;height:21px}.sidebar{display:flex;border-right:5px solid #999;background-color:var(--white-color);flex-direction:column;width:0;height:100%;color:var(--white-color);transition:width .3s ease;overflow:hidden;position:fixed;left:0px;z-index:99999999;margin-top:-1%}.popup-title{display:flex;width:100%;top:0px;position:absolute}.popup-desc{position:absolute;top:30%;font-size:16px !important;margin:9px !important}.popup-buttons{position:absolute;bottom:9px;right:0px}.newguide-button{all:unset;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:row;-webkit-box-pack:center;justify-content:center;-webkit-box-align:center;align-items:center;height:fit-content;max-height:36px;font-size:14px;line-height:24px;font-weight:600;background-color:#5478f0;color:#fff;position:relative;max-width:160px;width:160px;border-radius:12px;gap:6px;border-width:2px;border-style:solid;border-color:rgba(0,0,0,0);border-image:initial;overflow:hidden;white-space:nowrap;padding:8px 0px;position:absolute;right:36px}.select-box{display:flex;flex-direction:row;margin-left:24px;-webkit-box-align:center;align-items:center;height:32px}.select-svg{display:flex;flex-direction:row;-webkit-box-align:center;align-items:center}.jvijPe{display:flex;flex-direction:row;-webkit-box-align:center;align-items:center;gap:4px;width:fit-content;cursor:pointer}.logout-div{display:flex;align-items:center;cursor:pointer;padding:18px 9px;width:100%}.select-panel{display:flex;flex-direction:row;gap:8px;margin-left:24px;-webkit-box-align:center;align-items:center}.select-button{all:unset;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:row;-webkit-box-pack:center;justify-content:center;-webkit-box-align:center;align-items:center;max-height:32px;font-size:12px;line-height:16px;font-weight:600;height:32px;width:32px;background-color:rgba(0,0,0,0);color:#74819b;position:relative;gap:6px;border-radius:8px;padding:0px;border-width:initial;border-style:none;border-color:initial;border-image:initial;overflow:visible;white-space:nowrap}.guide-table{width:100%;cursor:pointer;padding:16px 24px;background:#fff;border:1px solid #ecf0f5;margin-top:16px;box-sizing:border-box;box-shadow:rgba(55,68,115,.04) 0px 2px 2px;outline:none;border-radius:16px;display:flex;flex-direction:row;-webkit-box-pack:justify;justify-content:space-between}.guide-block{display:flex;flex-direction:row;-webkit-box-pack:justify;justify-content:space-between;width:100%}.checkbox-block{display:flex;flex-direction:row;gap:12px;align-items:flex-start}.updated-date{-webkit-box-align:center;align-items:center;font-size:16px;line-height:24px;font-weight:500;color:#2e4168;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;user-select:none}.date-text{margin-top:8px;font-size:12px;line-height:16px;font-weight:500;color:#919db5;white-space:nowrap;user-select:none}.inactive-block{font-size:12px;line-height:16px;font-weight:500;display:flex;flex-direction:row;-webkit-box-align:center;align-items:center;box-sizing:border-box;height:fit-content;margin-top:16px;gap:16px}.grey-bg{box-sizing:border-box;border-radius:20px;display:flex;flex-direction:row;-webkit-box-align:center;align-items:center;width:fit-content;white-space:nowrap;background-color:#ecf0f5;height:18px;padding-block:0px;padding-inline:12px 14px;border:none}.vert-line{width:1px;height:18px;background:#dae1ed;flex:0 0 auto;-webkit-box-flex:0}.right-buttons{display:flex;flex-direction:row;-webkit-box-pack:end;justify-content:flex-end;padding:0px;height:32px;flex:0 0 auto;order:0;-webkit-box-flex:0}.right-icon{all:unset;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:row;-webkit-box-pack:center;justify-content:center;-webkit-box-align:center;align-items:center;max-height:32px;font-size:12px;line-height:16px;font-weight:600;height:32px;width:32px;background-color:rgba(0,0,0,0);color:#74819b;position:relative;gap:6px;border-radius:8px;padding:0px;border-width:initial;border-style:none;border-color:initial;border-image:initial;overflow:visible;white-space:nowrap}.del-icon{all:unset;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:row;-webkit-box-pack:center;justify-content:center;-webkit-box-align:center;align-items:center;max-height:32px;font-size:12px;line-height:16px;font-weight:600;height:32px;width:32px;background-color:rgba(0,0,0,0);color:#2e4168;position:relative;gap:6px;border-radius:8px;padding:0px;border-width:initial;border-style:none;border-color:initial;border-image:initial;overflow:visible;white-space:nowrap}.default-text{max-width:128px;font-size:12px;line-height:16px;font-weight:500;align-self:end;color:#919db5;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;user-select:none}.svg-block{display:flex;flex-direction:row;-webkit-box-align:center;align-items:center}.link-button{display:flex;flex-direction:column;-webkit-box-pack:justify;justify-content:space-between}.edittag-button{all:unset;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:row;-webkit-box-pack:center;justify-content:center;-webkit-box-align:center;align-items:center;width:fit-content;height:fit-content;max-height:32px;font-size:12px;line-height:16px;font-weight:600;background-color:rgba(0,0,0,0);color:#2e4168;position:relative;padding:8px 32px;gap:6px;border-radius:8px;border-width:initial;border-style:none;border-color:initial;border-image:initial;overflow:hidden;white-space:nowrap}.del-button{all:unset;box-sizing:border-box;cursor:pointer;display:flex;flex-direction:row;-webkit-box-pack:center;justify-content:center;-webkit-box-align:center;align-items:center;max-height:32px;font-size:12px;line-height:16px;font-weight:600;height:32px;width:32px;background-color:rgba(0,0,0,0);color:#2e4168;position:relative;gap:6px;border-radius:8px;padding:0px;border-width:initial;border-style:none;border-color:initial;border-image:initial;overflow:visible;white-space:nowrap}.layout{text-align:center;background:#5c6ac4;width:100%;height:92.3vh;position:absolute}.child-layout button{background:#30373b;height:41px !important;margin-top:1px;font-size:15px}.child-layout{width:100%;margin:0;position:absolute;top:40%;left:50%;transform:translate(-50%, -50%)}.child-layout h2{color:var(--white-color);font-size:27px;padding:9px}.child-layout h1{color:var(--white-color);font-size:40px}.App-logo{height:40vmin;pointer-events:none}.userButton{position:absolute;right:47px;z-index:99;top:519px;background-color:#007bff;border:none;border-radius:5px;color:var(--white-color);cursor:pointer;font-size:14px;padding:7px 21px}.closeusercreate-icon{position:absolute;top:13px;right:0px;fill:#fff;cursor:pointer;right:5px}.css-1rtad1{display:block !important}.userEdit-popup h1{background-image:linear-gradient(to right, rgb(37, 81, 181), rgba(0, 0, 0, 0.7));font-size:17px;color:var(--white-color);text-align:left;height:44px;padding:10px 0 0 20px;margin:0 -20px 14px}.switch{position:absolute;right:17px;display:inline-block;width:54px;height:27px}.switch input{opacity:0;width:0;height:0}.container{flex:1;justify-content:center;align-items:center;margin-top:11px;margin-left:-3px}.radioButton input[type=radio]{margin-top:7px}.radioGroup{display:flex;flex-direction:row;align-items:center;justify-content:space-around}.radioButton{display:flex;flex-direction:row;align-items:center}.radioLabel{margin-left:8px;font-size:17px;color:#333}.slider.round{border-radius:34px}.slider.round:before{border-radius:50%}.user-popup{z-index:99999;width:400px !important;height:100%;top:46px;right:0;position:fixed !important;display:block !important;background:var(--white-color) !important;box-shadow:0 3px 8px #000}.user-popup .qadpt-header{background-image:linear-gradient(to right, rgb(37, 81, 181), rgba(0, 0, 0, 0.7));padding:10px}.user-popup .qadpt-header span{font-size:16px;color:var(--white-color);margin:0}.user-popup .close-icon{position:absolute;top:11px;right:10px;fill:var(--white-color);cursor:pointer;height:16px;width:16px}.user-popup .qadpt-usrform{padding:20px;height:calc(100% - 175px);overflow-y:auto}.user-popup .qadpt-usrform .qadpt-txtinp{width:100%;font-size:14px;border:0;border-bottom:1px solid rgba(0,0,0,.42) !important;padding:10px 0;margin:0}.user-popup .qadpt-usrform .qadpt-txtfld{margin-bottom:10px}.user-popup .qadpt-usrform .qadpt-txtfld.qadpt-switch{display:flex;align-items:center;justify-content:space-between}.user-popup .qadpt-usrform .qadpt-txtfld.qadpt-switch .MuiFormControlLabel-root{margin:0 !important}.user-popup .qadpt-usrform .qadpt-txtlabel{font-size:14px}.user-popup .error-input{border:1px solid var(--error-color)}.user-popup .qadpt-button{text-align:center;width:100%}.user-popup .qadpt-button button{color:var(--white-color);border-radius:4px;border:none}.user-popup .qadpt-button .qadpt-disab{background-color:#ccc;border:none;padding:6px 14px;font-size:16px;cursor:not-allowed}.user-popup .qadpt-button .qadpt-enab{background-color:var(--button-bg-color);padding:6px 14px}.userAccount-popup{z-index:100;width:300px !important;height:91vh;top:51px;right:0;padding:0 20px;position:fixed !important;display:block !important;background:var(--white-color) !important;box-shadow:0 3px 8px #000}.MuiPickersPopper-container{z-index:100000}.userDelete-popup{z-index:99999;width:214px;height:217px;top:235px;margin-top:9px;right:0;padding:0 20px;background:var(--white-color) !important;box-shadow:0 3px 8px #000;margin-right:500px}.filter-options{display:flex;margin-left:27px}.filter-options input,select{margin-top:16px;background:rgba(0,0,0,0);color:rgba(0,0,0,.**********);box-shadow:none;position:relative;width:100%;font-family:var(--font-family);line-height:1.4;margin-bottom:4px;border-radius:0;font-size:14px;border:0;min-height:22px !important;min-width:0;padding:4px 0;line-height:19.6px}.searchInput{color:rgba(0,0,0,.**********);box-shadow:none;position:relative;width:30%;font-family:var(--font-family);border-radius:5px;font-size:16px;border:0;min-height:36px !important;border-bottom:1px solid rgba(0,0,0,.42) !important;min-width:0;padding:4px 10px;line-height:19.6px;margin-right:4px}.searchInput:focus{outline:none}.user-popup input:focus,select:focus{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;outline:none;border-bottom:2px solid rgba(189,28,28,.527) !important}.settings-div{padding-left:2rem;padding-right:2rem;padding-bottom:2.5rem}.settings-header{justify-content:left;font-size:1.875rem;line-height:2.25rem;font-weight:500;letter-spacing:-0.025em;color:#0f172a}.settings-nav{margin-top:1.25rem;position:relative}.settings-ul{margin-left:-30px;padding-bottom:1.25rem;overflow-x:auto;gap:.75rem;margin-bottom:1rem;list-style-type:none;display:contents;width:1109px}.settings-ul{display:flex}.settings-li{opacity:1;border-color:#e2e8f0;border-width:1px;border-radius:10px;height:25px;border-style:solid;float:left;padding:4px;font-size:13px;margin-right:12px;width:115px}.settings-li:hover{border:1px solid #000}.settings-anchor{color:#64748b;line-height:.625rem;padding-left:-0.375rem;padding-right:.625rem;height:28px;background-color:rgba(0,0,0,0);text-decoration:none}.settings-svg{margin-right:.5rem;color:#6b7280;vertical-align:middle;overflow:hidden;fill:#6b7280;margin-top:-3px}.invite-block{padding:1.5rem;border-color:#e2e8f0;border-style:solid;border-bottom-width:1px;border-left:none;border-right:none;border-top:none;justify-content:space-between;align-items:center;display:flex}.invite-items{display:flex;align-items:center}.invite-svg{padding:.5rem;fill:#1e293b;background-color:#f1f5f9;border-radius:.5rem;margin-right:.75rem;vertical-align:middle;overflow:hidden;height:25px;width:25px}.team-settings{padding:2rem 2rem 0rem 2rem}.qx-align{display:flex;align-items:center;padding:1.5rem}.qx-border{border-color:#e2e8f0;border-style:solid;border-width:1px;border-radius:.75rem}.qx-border-bottom{border-color:#e2e8f0;border-style:solid;border-width:0px;border-bottom-width:1px;border-radius:.75rem}.team-ul{padding:1rem;border-radius:0;display:flex;flex-direction:column;margin-bottom:0;list-style:none;margin-top:0}.team-li{border-width:0 0 1px;padding:1rem 0rem;padding-right:0 !important;background-color:var(--white-color);border-bottom:1px solid rgba(11,24,21,.125);display:block;padding-top:1rem !important;padding-left:0 !important;padding-right:0 !important}.team-div{width:100%;align-items:center !important;display:flex !important;margin-bottom:0 !important}.qadpt-prof{border-radius:50% !important;margin-right:.5rem !important}.qadpt-prof .prof-div{cursor:pointer;font-size:16px;width:34px;height:34px;font-weight:700;background-color:#c9befd;border-color:#fff;border-width:2px;border-radius:50%;overflow:hidden;display:flex}.qadpt-prof .prof-span{display:inline-block;margin:auto}.team-involve{padding-left:1.5rem;padding-right:1.5rem;padding-bottom:1rem;padding-top:1rem}.qx-flex{display:flex}.text-span{font-size:.75rem;line-height:1rem;padding-bottom:.125rem;padding-top:.125rem;padding-left:.5rem;padding-right:.5rem;border-radius:.75rem;align-items:center;user-select:none;display:inline-flex;background-color:#f1f5f9}.mail-h6{line-height:.9rem;font-size:.8rem;font-weight:400 !important;color:#0f172a;margin-bottom:.5rem;margin-top:0}.qx-role{min-width:150px;margin-right:.75rem}.role-span{font-weight:400;font-size:.875rem;line-height:1.25rem;text-overflow:ellipsis;width:100%;display:flex;margin-bottom:auto;margin-top:auto}.qx-items-center{align-items:center}.qx-w-full{width:100% !important}.qx-flex{display:flex}.email-input{align-items:center;width:100% !important;display:flex;padding-bottom:.5rem;padding-top:.5rem;background-color:#fff;border-color:#cbd5e1;border-width:1px;border-bottom-left-radius:.5rem;border-top-left-radius:.5rem;border-style:solid;overflow:visible;line-height:1.25rem}.complete-block{padding-bottom:1rem;margin-bottom:1rem;border-color:#e2e8f0;border-style:solid;border-width:1px;border-radius:.75rem}.invite-button{opacity:.7;text-decoration-line:none !important;line-height:1.25rem;font-weight:600 !important;padding-left:1rem;padding-right:1rem;padding-bottom:.5rem;padding-top:.5rem;background-color:#fff;border-width:1px;border-left-width:0 !important;border-bottom-right-radius:.5rem;border-top-right-radius:.5rem;border-color:#cbd5e1;border-style:solid}.qx-relative{position:relative}.role-div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1 1}.qx-button{box-shadow:0 0 rgba(0,0,0,0),0 0 rgba(0,0,0,0),0 0 rgba(0,0,0,0);text-decoration-line:none !important;color:#1e293b;font-size:.875rem;vertical-align:middle;text-align:left;padding-left:.75rem;padding-right:.75rem;padding-bottom:.5rem;padding-top:.5rem;background-color:#fff;line-height:1.25rem;border-style:solid;border-width:1px;border-radius:.375rem;justify-content:center;align-items:center !important;cursor:pointer !important;max-width:280px;width:100%;border-color:#e2e8f0}.team-involve-text{color:#334155;font-weight:600;font-size:.875rem;line-height:1.25rem;margin-bottom:.5rem;margin-top:0}.invite-text{font-size:1.125rem;line-height:1.75rem;font-weight:700;color:#0f172a}.role-access{user-select:none;cursor:default;height:fit-content;display:inline-flex}.unlimited-seats{padding-left:.625rem;padding-right:.625rem;background-color:#f1f5f9;border-color:#e2e8f0;border-style:solid;border-width:1px;border-bottom-left-radius:.375rem;border-top-left-radius:.375rem;align-items:center;height:2.5rem;display:flex}.unlimited-div{padding-left:.875rem;padding-right:.875rem;border-color:#e2e8f0;border-style:solid;border-right-width:1px;border-top-width:1px;border-bottom-width:1px;border-bottom-right-radius:.375rem;border-top-right-radius:.375rem;align-items:center;height:2.5rem;display:inline-flex}.profile-text{margin-left:36px;display:flex;align-items:center;margin-top:13px}.class-labels{margin-left:36px;margin-top:27px;display:flex;align-items:center;font-weight:500;font-size:15px}.text-field{margin-left:36px;padding:8px 10px;border-radius:9px;font-size:14px;border:1px solid #ccc;text-align:start;width:23%;margin-top:10px;background-color:#f4f7fd;color:#595d85;outline:none;box-shadow:none}.email-change-button{cursor:pointer;font-size:13px;padding:7px 21px;background:rgba(0,0,0,0);color:#5d748c;border:2px solid #e5e5e5;border-radius:10px;margin-top:18px;margin-left:36px;background-color:#f4f7fd;font-weight:bold;display:flex}.password-change-button{padding:7px 9px;cursor:pointer;font-size:13px;background:rgba(0,0,0,0);color:#0f172b;border:2px solid #e5e5e5;border-radius:10px;background-color:#f4f7fd;font-weight:bold;color:#595d85;margin-left:36px;margin-top:18px;display:flex}.authentication-switch{position:absolute;left:119px;display:inline-block;width:54px;height:27px;margin-top:10px;color:#595d85}.cancel-button{margin-left:1036px;margin-top:-42px;border-radius:10px;padding:7px 38px;position:absolute;z-index:99999;cursor:pointer;font-size:14px;background:rgba(0,0,0,0);background-color:#f4f7fd;color:#000;border:0px solid #e5e5e5;font-weight:bold}.save-button{position:absolute;z-index:99999;cursor:pointer;font-size:14px;background:rgba(0,0,0,0);background-color:blue;color:var(--white-color);font-weight:bold;border:0px solid #e5e5e5;margin-left:1185px;margin-top:-42px;border-radius:10px;padding:7px 42px}.disabled-text{margin-left:98px;color:#595d85;font-family:sans-serif;font-size:14px}.account-block{padding-bottom:1rem;margin-bottom:1rem;border-color:#e2e8f0;border-style:solid;border-width:1px;border-radius:.75rem;height:120px;width:90%;margin-left:35px;margin-top:27px}.receive-text{color:#64748b;font-size:13px;margin-left:19px;margin-top:5px;text-decoration:none}.tooltip:hover .editbtn-tooltip{visibility:visible}.tooltip .editbtn-tooltip::after{content:\"\";position:absolute;top:100%;left:50%;margin-left:-5px;border-width:5px;border-style:solid;border-color:#334155 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.tooltip .editbtn-tooltip{visibility:hidden;width:203px;background-color:#334155;color:var(--white-color);text-align:left;border-radius:4px;padding:9px;margin-left:-196px;margin-top:-54px;position:absolute;z-index:1;font-size:13px}.right-authentication-block{padding-bottom:1rem;margin-bottom:1rem;border-color:#e2e8f0;border-style:solid;border-width:1px;border-radius:.75rem;height:129px;width:958px;margin-left:49px;margin-top:27px}.right-setting-header{margin-bottom:18px;margin-left:33px;font-size:14px;color:\"#0F172B\";font-family:var(--font-family);font-size:14px}.user-authentication-text{box-sizing:border-box;color:#334155;display:block;font-size:14px;height:60px;line-height:1.25rem;text-size-adjust:100%;width:947px;margin-left:18px;font-family:var(--font-family)}.qxy-content-block{max-height:calc(100vh - 220px);overflow-y:scroll;margin-top:-30px}.qxy-content-block .qadpt-sub-title{padding:0}.qxy-content-block .qadpt-box{padding:8px 12px;margin-bottom:1rem;border:1px solid var(--border-color);border-radius:6px;margin-top:15px}.qxy-content-block .right-setting-text{justify-content:space-around;flex-direction:column;display:flex;color:#0f172b}.qxy-content-block .qadpt-swtch-label{font-weight:600}.qxy-content-block .qadpt-box-switch{display:flex;justify-content:space-between;align-items:center}.qxy-content-block .right-relative{position:relative;--tw-border-opacity: 1;right:-650%;margin-top:-47px}.tooltip{position:relative;display:inline-block}.tooltip .tooltiptext{visibility:hidden;width:203px;background-color:var(--tool-tip-bg);color:var(--white-color);text-align:left;border-radius:4px;padding:10px;margin-left:822px;margin-top:-80px;position:absolute;z-index:1;font-size:12px}.tooltip:hover .tooltiptext{visibility:visible}.tooltip .tooltiptext::after{content:\"\";position:absolute;top:100%;left:50%;margin-left:-5px;border-width:5px;border-style:solid;border-color:#334155 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.cursor-not-allowed{cursor:not-allowed}.editbtn-cursor-not-allowed{cursor:not-allowed}.pointer-events{pointer-events:none}.label{align-items:center;-webkit-user-select:none;user-select:none;cursor:not-allowed;display:flex;margin-bottom:0;position:relative}.switch-textfield{box-sizing:border-box;padding:11px;opacity:0;width:86%;height:100%;pointer-events:none;position:absolute;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible;-webkit-user-select:none;user-select:none}.switch-spanone{width:42px;height:24px;transition-duration:.25s;--tw-bg-opacity: 1;background-color:rgb(203, 213, 225, var(--tw-bg-opacity));border-radius:.75rem;justify-content:space-between;align-items:center;display:inline-flex;position:relative}.switch-spantwo{width:15px;height:15px;left:10px;transition-duration:.25s;transition-property:inherit;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);--tw-bg-opacity: 1;background-color:rgb(255, 255, 255, var(--tw-bg-opacity));border-radius:10156px;justify-content:center;align-items:center;--tw-translate-y: -61%;display:flex;z-index:21;top:50%;cursor:not-allowed;pointer-events:none;-webkit-user-select:none;user-select:none;margin-left:4px}.switch-spanthree{--tw-text-opacity: 1;color:rgb(220, 225, 224, var(--tw-text-opacity));text-transform:uppercase;font-weight:700;font-size:7px;font-family:var(--font-family);margin-left:-0.625rem;cursor:not-allowed;pointer-events:none;-webkit-user-select:none;user-select:none}.switch-spanfour{--tw-text-opacity: 1;color:rgb(71, 85, 105, var(--tw-text-opacity));text-transform:uppercase;font-weight:700;font-size:8px;margin-right:5px;box-sizing:border-box;cursor:not-allowed;pointer-events:none;-webkit-user-select:none;user-select:none;text-align:left;line-height:7.25rem}.switch-spanfive{--tw-text-opacity: 1;color:rgb(148, 163, 184, var(--tw-text-opacity));fill:currentColor;overflow:hidden;vertical-align:middle;box-sizing:border-box;height:27px;width:10px;--tw-bg-opacity: 1;color:#94a3b8;fill:#94a3b8;font-size:14px;font-weight:800;height:17px;line-height:20px;overflow-clip-margin:content-box;overflow-x:hidden;overflow-y:hidden}.right-tooltip{width:100%;display:inline-block;box-sizing:border-box;--tw-text-opacity: 1}.organization-dropdown{box-shadow:0 0 rgba(0,0,0,0),0 0 rgba(0,0,0,0),0 0 rgba(0,0,0,0);text-decoration-line:none !important;color:#1e293b;font-size:.875rem;vertical-align:middle;text-align:left;padding-left:.75rem;padding-right:.75rem;padding-bottom:.5rem;padding-top:-0.5rem;background-color:#fff;line-height:1.25rem;border-style:solid;border-width:1px;border-radius:.375rem;justify-content:center;align-items:center !important;cursor:pointer !important;max-width:280px;width:10%;border-color:#000;margin-left:1198px;height:27px;margin-top:-37px}.org-text{margin-top:-30px;font-size:14px;margin-left:1183px;margin-bottom:20px}.dropdown-options{width:11%;margin-left:1171px;margin-top:-34px}.qadpt-addadmin{position:relative;top:50px;display:flex;flex-direction:column;align-items:center;place-content:center}.qadpt-addadmin .qadpt-block{background:var(--white-color);border:1px solid #ccc;padding:20px;border-radius:4px;box-shadow:0 1px 10px 1px rgba(0,0,0,.2),0 0 1px rgba(0,0,0,.1411764706),0 1px 3px rgba(0,0,0,.1215686275);text-align:center}.qadpt-addadmin .addadmin-heading{font-weight:600;font-size:21px;padding:10px}.qadpt-addadmin .qadpt-adminfld{padding:10px}.qadpt-addadmin .qadpt-adminfld .MuiInputLabel-shrink{top:7px !important}.qadpt-addadmin .qadpt-adminfld .MuiFormControl-root{height:45px;width:450px}.qadpt-addadmin .qadpt-adminfld .Mui-error{margin:3px 0 0 0}.qadpt-addadmin .qadpt-subbtn{background-color:var(--button-bg-color);color:var(--white-color);width:70px;height:31px;border:none;border-radius:4px}.userDelete-popup{overflow-y:scroll;z-index:99999;width:200px !important;height:20vh;top:243px;right:0;padding:0 20px;position:fixed !important;display:table !important;background:var(--white-color) !important;box-shadow:0 3px 8px #000}.error{color:var(--error-color);font-size:.8em;margin-top:5px}.form-group{margin-bottom:20px}.error-input:focus{outline-color:var(--error-color)}.userButton.button-disabled{background-color:#ccc;color:#888;cursor:not-allowed}.input-container{position:relative;margin-bottom:20px}.input-label{position:absolute;left:10px;top:10px;transition:all .3s ease-in-out;pointer-events:none;color:#666}.float{top:-10px;font-size:12px;color:#333}.admin-input{width:449px;height:40px;padding:10px;font-size:16px;border:1px solid #ccc;transition:all .3s ease-in-out}.admin-input:focus{border-color:#007bff;outline:none}.multiselect{margin-left:302px;margin-top:-49px;width:204px;height:65px}.add-dropdown{width:192px;margin-left:242px;margin-top:-10px;height:37px}.add-button{margin-top:-40px;margin-left:462px}.bold-header{font-weight:1050;font-size:20px}.updatebutton-container{float:left;margin-left:-2%;margin-top:29%}.updateButton{font-size:14px;padding:7px 21px;border:none;background-color:#007bff;border:none;border-radius:5px;cursor:pointer;font-size:16px;transition:background-color .3s,color .3s}.updateButton--enabled{background-color:#007bff;color:var(--white-color)}.updateButton--disabled{background-color:#e0e0e0;color:#b0b0b0;cursor:not-allowed}.goog-te-banner-frame.skiptranslate{display:none !important}.skiptranslate .goog-te-banner-frame{display:none !important}.goog-te-gadget-simple .goog-te-gadget-icon,.goog-te-gadget-simple .goog-te-gadget-simple{display:none}#goog-gt-tt{display:none}.google-translate-powered-by{display:none !important}.goog-te-gadget-simple{display:none}.goog-logo-link{display:none !important}.App-link{color:#61dafb}@keyframes App-logo-spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}body,html,#root{overflow:hidden;height:100%;margin:0;padding:0}.qadpt-prfsidebar{display:flex;align-items:center;margin:55px 20px 0}.qadpt-prfsidebar.closed{margin-left:3%}.qadpt-prfsidebar .qadpt-prftitle{font-size:20px !important;font-weight:600 !important;line-height:48px !important;letter-spacing:-0.114px !important;text-align:left;margin-right:auto !important;width:50%}.qadpt-prfsidebar .save-btn{width:50%;text-align:end}.qadpt-prfsidebar .qadpt-prfbtn{cursor:pointer !important;color:var(--white-color) !important;font-weight:500 !important;border:0px solid #e5e5e5 !important;border-radius:15px !important;padding:6px 18px !important;text-transform:none !important}.qadpt-prfsidebar .qadpt-prfbtn-disabled{background-color:#a5c3c5 !important;cursor:not-allowed !important}.qadpt-prfsidebar .qadpt-prfbtn-enabled{background-color:#5f9ea0 !important}.qadpt-file-error{color:var(--error-color);position:absolute;bottom:-20px;left:81%;transform:translateX(-50%);white-space:nowrap;text-overflow:ellipsis}.qadpt-crossIcon{position:absolute;top:-34px;float:right;width:16px;height:16px;color:#afafaf}.qadpt-prfbox{margin:15px 20px;background:var(--white-color);border-radius:8px;padding:10px 20px;box-sizing:border-box;height:calc(100vh - 139px);overflow-y:auto;min-height:calc(90vh - 90px)}.qadpt-prfbox.closed{margin-left:3%;width:calc(100% - 0px)}.qadpt-prfbox::-webkit-scrollbar{width:6px}.qadpt-prfbox::-webkit-scrollbar-thumb{background-color:rgba(95,158,160,.8);border-radius:10px}.qadpt-prfbox::-webkit-scrollbar-thumb:hover{background-color:#5f9ea0}.qadpt-prfbox::-webkit-scrollbar-track{background-color:rgba(0,0,0,0)}.qadpt-prfbox .qadpt-subtle{position:sticky;top:-20px;z-index:1;background:var(--white-color);padding:14px 18px;margin-left:-10px;margin-right:-20px;font-weight:600;font-size:16px}.qadpt-prfbox .qadpt-prfimgcon{width:90px;height:90px;position:relative;top:8px;background:#f6eeee;border-radius:50%;display:flex;justify-content:center;align-items:center}.qadpt-prfbox .qadpt-prfimgcon .qadpt-sltimg{width:100%;height:100%;border-radius:50%;object-fit:cover}.qadpt-prfbox .qadpt-prfimgcon .qadpt-prfimg{width:50px;height:50px;border-radius:50%;object-fit:cover;opacity:.5}.qadpt-prfbox .qadpt-prfimgcon .qadpt-editcont{position:absolute;bottom:10px;right:-10px;background-color:#1814f3;padding:5px;box-shadow:0px 4px 8px rgba(0,0,0,.1);width:25px;height:25px}.qadpt-prfbox .qadpt-prfimgcon .qadpt-editcont .qadpt-editicon{color:var(--white-color);width:15px;height:15px}.qadpt-prfbox .qadpt-txtctrl{position:relative;width:100%;font-weight:500;color:#222;padding:0 4px;font-size:14px;display:flex;flex-direction:column;font-weight:600}.qadpt-prfbox .qadpt-txtctrl .qadpt-prffldval{width:365.67px;height:45px;border-radius:6px;border:1px solid #dcdde1 !important;opacity:.9;margin-top:5px}.qadpt-prfbox .qadpt-txtctrl .qadpt-prffldval .MuiOutlinedInput-root fieldset{border-color:rgba(220,221,225,.1) !important}.qadpt-prfbox .qadpt-txtctrl .qadpt-prffldval .MuiOutlinedInput-root:hover fieldset{border-color:rgba(220,221,225,.1) !important}.qadpt-prfbox .qadpt-txtctrl .qadpt-prffldval .MuiOutlinedInput-root.Mui-focused fieldset{border-color:rgba(220,221,225,.1) !important}.qadpt-prfbox .qadpt-txtctrl .qadpt-prffldval .MuiOutlinedInput-root input{padding:11px 12px;font-family:var(--font-family);font-size:16px;font-weight:400;line-height:28px;text-align:left}.qadpt-prfbox .qadpt-prfchgpwd{margin-top:10px !important;border-radius:15px !important;border:1px solid rgba(0,0,0,0) !important;border-color:#5f9ea0 !important;color:#5f9ea0;font-weight:600 !important;text-transform:none}.css-1gcrhl3{box-shadow:none !important}.grid-toolbar-options{width:100% !important;margin:20px 0 0}.grid-toolbar-options .left-options{width:100%;max-width:100%;display:flex}.grid-toolbar-options .left-options .drp-fields,.grid-toolbar-options .left-options .dt-fields{display:flex;margin-right:15px}.grid-toolbar-options .left-options .drp-fields .MuiInputLabel-outlined.MuiInputLabel-shrink,.grid-toolbar-options .left-options .dt-fields .MuiInputLabel-outlined.MuiInputLabel-shrink{top:7px !important}.grid-toolbar-options .left-options .drp-fields .auto-filed,.grid-toolbar-options .left-options .drp-fields .qadpt-DateTime,.grid-toolbar-options .left-options .dt-fields .auto-filed,.grid-toolbar-options .left-options .dt-fields .qadpt-DateTime{width:50%;max-width:50%;min-width:50%;margin:0 5px}.grid-toolbar-options .left-options .drp-fields button,.grid-toolbar-options .left-options .dt-fields button{text-transform:unset !important}.grid-toolbar-options .left-options .drp-fields .MuiInputBase-root.MuiOutlinedInput-root{padding-right:35px}.grid-toolbar-options .left-options .drp-fields .MuiInputBase-root.MuiOutlinedInput-root .MuiAutocomplete-input{width:inherit !important;min-width:30px;max-width:100%}.grid-toolbar-options .left-options .auto-filed .MuiSvgIcon-root,.grid-toolbar-options .left-options .name-fld .MuiSvgIcon-root,.grid-toolbar-options .left-options .qadpt-DateTime .MuiSvgIcon-root{height:16px !important;width:16px !important}.grid-toolbar-options .left-options .auto-filed.dt-fld2,.grid-toolbar-options .left-options .name-fld.dt-fld2,.grid-toolbar-options .left-options .qadpt-DateTime.dt-fld2{margin-left:0px}.grid-toolbar-options .left-options .auto-filed.dt-fld2.hide-close,.grid-toolbar-options .left-options .name-fld.dt-fld2.hide-close,.grid-toolbar-options .left-options .qadpt-DateTime.dt-fld2.hide-close{margin-left:0}.grid-toolbar-options .left-options .auto-filed input,.grid-toolbar-options .left-options .name-fld input,.grid-toolbar-options .left-options .qadpt-DateTime input{font-size:12px !important;padding-right:0 !important}.grid-toolbar-options .left-options .dt-close-icon{right:55px;height:40px}.grid-toolbar-options .right-options{display:flex;margin:5px;place-content:flex-end}.grid-toolbar-options .right-options button{text-transform:unset !important;padding:5px;height:32px;border-radius:20px;margin:0 0 0 5px}.grid-toolbar-options svg{height:16px;width:16px}.grid-toolbar-options .name-fld .MuiInputLabel-outlined.MuiInputLabel-shrink{top:7px !important}[class*=MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input]{padding:10px !important}[class*=MuiInputBase-input]{padding:10px !important;height:20px !important;font-size:var(--font-size) !important}[class*=MuiOutlinedInput-notchedOutline],[class*=MuiInputBase-root-MuiOutlinedInput-root]{top:0 !important;font-size:var(--font-size) !important}[class*=MuiInputLabel-formControl]{font-size:var(--font-size) !important}[class*=MuiTooltip-tooltip]{margin-bottom:0 !important}[class*=\"MuiFormHelperText-root Mui-error\"]{margin:0 !important;line-height:12px !important;display:flex;align-items:center}.MuiInputBase-input:-webkit-autofill{background-color:#fff !important;box-shadow:0 0 0px 1000px #fff inset !important;color:#000 !important}.qadpt-orgcont{position:relative;left:20px;top:35px;width:calc(100% - 40px)}.qadpt-orgcont .MuiContainer-root{padding:0 !important}.qadpt-page-content .qadpt-settings-content .usr-list-page .qadpt-usercreatepopup .qadpt-formcontent,.qadpt-page-content .qadpt-settings-content .usr-list-page .qadpt-userEditpopup .qadpt-formcontent{padding:10px 15px !important}.qadpt-page-content .qadpt-settings-content .usr-list-page .qadpt-usercreatepopup .qadpt-formcontent .qadpt-userfields .MuiOutlinedInput-notchedOutline,.qadpt-page-content .qadpt-settings-content .usr-list-page .qadpt-userEditpopup .qadpt-formcontent .qadpt-userfields .MuiOutlinedInput-notchedOutline{border-radius:5px !important}.qadpt-btn-default{width:100%;background-color:#5f9ea0 !important;color:#fff !important;padding:10px !important;border-radius:15px !important;font-size:14px !important;margin-top:30px !important;text-transform:capitalize !important}.qadpt-btn-default:hover{background-color:#5f9ea0 !important;color:#fff !important}.qadpt-closeicon{position:absolute;top:10px;cursor:pointer;right:15px;width:15px}.qadpt-toaster{top:30px !important;width:40%;z-index:999999 !important}.qadpt-toaster .MuiAlert-message{display:block;overflow:hidden !important;word-break:break-word}.qadpt-toaster.qadpt-toaster-success{border:1px solid #2e7d32}.qadpt-toaster.qadpt-toaster-error{border:1px solid red}.qadpt-toaster .qadpt-alert{width:150%}.MuiPopper-root.MuiAutocomplete-popper .MuiAutocomplete-paper{font-size:14px}.qadpt-usrconfirm-popup{width:400px;max-height:300px;top:186px;right:480px;position:fixed !important;background:var(--white-color) !important;border-radius:4px}.qadpt-usrconfirm-popup .qadpt-usrconfirm-popup div{display:flex;align-items:center;place-content:center}.qadpt-usrconfirm-popup>div:first-child{display:flex;align-items:center;justify-content:center;padding:10px}.qadpt-usrconfirm-popup .qadpt-icon{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center}.qadpt-usrconfirm-popup .qadpt-icon i{color:var(--error-color);font-size:20px !important}.qadpt-usrconfirm-popup .qadpt-popup-title{text-align:center;font-size:16px;font-weight:600;padding:0 10px}.qadpt-usrconfirm-popup .qadpt-warning{font-size:14px;padding:20px;border-radius:4px;text-align:center}.qadpt-usrconfirm-popup .qadpt-warning .qadpt-delete-popup-bold{font-weight:600}.qadpt-usrconfirm-popup .qadpt-buttons{display:flex;justify-content:space-between;min-height:40px;padding:10px !important;position:relative;bottom:0;border-top:1px solid var(--border-color)}.qadpt-usrconfirm-popup .qadpt-buttons .qadpt-cancel-button,.qadpt-usrconfirm-popup .qadpt-buttons .qadpt-conform-button{padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important;font-size:14px;border-radius:4px;cursor:pointer}.qadpt-usrconfirm-popup .qadpt-buttons .qadpt-cancel-button{background:var(--white-color)}.qadpt-usrconfirm-popup .qadpt-buttons .qadpt-conform-button{color:var(--white-color);border:none}.qadpt-usrconfirm-popup.qadpt-danger .qadpt-icon{background-color:#e4b6b0;color:var(--error-color)}.qadpt-usrconfirm-popup.qadpt-danger .qadpt-warning{overflow-x:hidden;overflow-y:auto;max-height:135px}.qadpt-usrconfirm-popup.qadpt-danger .qadpt-conform-button{background-color:var(--error-color)}.qadpt-usrconfirm-popup.qadpt-danger .qadpt-cancel-button{border:1px solid;color:var(--error-color)}.qadpt-usrconfirm-popup.qadpt-success .qadpt-icon{background-color:rgba(95,158,168,.2);color:var(--button-bg-color)}.qadpt-usrconfirm-popup.qadpt-success .qadpt-warning{overflow-x:hidden;overflow-y:auto;max-height:145px}.qadpt-usrconfirm-popup.qadpt-success .qadpt-conform-button{background-color:var(--button-bg-color)}.qadpt-usrconfirm-popup.qadpt-success .qadpt-cancel-button{border:1px solid var(--button-bg-color);color:var(--button-bg-color)}.qadpt-usrconfirm-popup.qadpt-deltrigger>div:first-child{justify-content:flex-start !important}.qadpt-usrconfirm-popup.qadpt-deltrigger .qadpt-popup-title{border-bottom:1px solid var(--border-color);padding:15px !important}.qadpt-usrconfirm-popup.qadpt-deltrigger .qadpt-warning{height:90px;align-content:center}.qadpt-usrconfirm-popup.qadpt-deltrigger .qadpt-buttons{padding:15px !important}.qadpt-midpart .qadpt-content-block.qadpt-multilingual{width:calc(100% - 22px)}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-memberButton{margin-right:10px}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-filters{display:flex;justify-content:space-between;align-items:center;padding:10px}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-filters .qadpt-filter-left{display:flex;gap:20px}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-filters .qadpt-filter-left .qadpt-select-form{min-width:170px;max-width:200px}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-filters .qadpt-filter-left .qadpt-select-form .MuiInputLabel-outlined.MuiInputLabel-shrink{top:7px !important}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-filters .qadpt-filter-right{display:flex;justify-content:flex-end;width:180px}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers{display:flex;align-items:center;place-content:center;gap:20px}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-left,.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-rgt{width:50%}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-left svg:first-of-type,.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-rgt svg:first-of-type{width:16px;height:16px;color:#000}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-left .MuiSelect-select,.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-rgt .MuiSelect-select{padding-left:0 !important}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-left{background:var(--grid-border-color)}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-left .MuiInputBase-root.MuiInput-root{margin-top:0 !important}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-left ul{padding-left:0}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-rgt .MuiInputBase-root.MuiOutlinedInput-root{padding-left:0 !important}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-headers .qadpt-lang-rgt .MuiInputLabel-outlined.MuiInputLabel-shrink{top:7px !important}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-language-section{display:flex;max-height:300px;overflow:hidden scroll;margin-top:5px;gap:20px}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-language-section .MuiBox-root{width:50%}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-language-section .qadpt-sec-left ul,.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-language-section .qadpt-sec-right ul{padding-left:0 !important;margin-top:0 !important}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-language-section .qadpt-sec-left ul li,.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-language-section .qadpt-sec-right ul li{display:block;white-space:nowrap;line-height:16px;text-overflow:ellipsis;overflow:hidden}.qadpt-midpart .qadpt-content-block.qadpt-multilingual .qadpt-language-selection .qadpt-language-section .qadpt-sec-left{background:var(--grid-border-color)}.qadpt-page-content .qadpt-settings-content .qadpt-settings-page .usr-list-page .qadpt-language-selection .qadpt-language-section .MuiBox-root{padding:0px !important}.qadpt-mngpwd-popup .MuiPaper-root.MuiPaper-elevation{width:calc(100vh - 200px);min-height:300px}.qadpt-mngpwd-popup .MuiDialogTitle-root{font-size:18px !important;border-bottom:1px solid var(--border-color);padding:15px;font-weight:600}.qadpt-mngpwd-popup .MuiDialogContent-root{padding:15px !important}.qadpt-mngpwd-popup .qadpt-lang-subhead{font-size:16px !important;margin-bottom:10px}.qadpt-mngpwd-popup .qadpt-primary-language-row{display:flex;align-items:center;justify-content:space-between}.qadpt-mngpwd-popup .qadpt-primary-language-row .qadpt-primary-lang{width:260px}.qadpt-mngpwd-popup .qadpt-primary-language-row .qadpt-primary-lang input{padding:0 !important}.qadpt-mngpwd-popup .qadpt-primary-language-row .qadpt-add-button{background-color:var(--button-bg-color) !important;padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important}.qadpt-mngpwd-popup .qadpt-lang-dropdown{display:flex;align-items:center;place-content:end;width:100%}.qadpt-mngpwd-popup .qadpt-lang-dropdown .MuiFormControl-root{margin-right:0px;width:200px}.qadpt-mngpwd-popup .qadpt-lang-dropdown .MuiInputLabel-outlined.MuiInputLabel-shrink{top:7px !important}.qadpt-mngpwd-popup .qadpt-second-lang{width:400px}.qadpt-mngpwd-popup .qadpt-second-lang input{padding:0 !important}.qadpt-mngpwd-popup .qadpt-langsave{color:var(--white-color) !important;padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important}.qadpt-mngpwd-popup .qadpt-langsave.qadpt-langsave-active{background-color:var(--button-bg-color) !important}.qadpt-mngpwd-popup .qadpt-langsave.qadpt-langsave-inactive{background-color:#ccc !important}.qadpt-mngpwd-popup .MuiDialogActions-root{padding:15px;border-top:1px solid var(--border-color)}.qadpt-mngpwd-popup .MuiDialogActions-root .qadpt-langclose{border:1px solid var(--button-bg-color) !important;color:var(--button-bg-color) !important;padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important}.qadpt-web{background:var(--content-background-color);width:calc(100% - 200px);position:relative;max-width:100%;overflow-x:hidden;left:220px;top:10px}.qadpt-web .MuiDataGrid-cell:nth-child(2){width:40% !important;max-width:40% !important;min-width:40%;font-weight:600}.qadpt-web .MuiDataGrid-cell:nth-child(3){width:40% !important;max-width:40% !important;min-width:40%}.qadpt-web .MuiDataGrid-cell:nth-child(4){width:20% !important;max-width:20% !important;min-width:20%}.qadpt-web .MuiDataGrid-root{border:none}.qadpt-web .qadpt-webcontent{padding:0 20px;height:calc(100vh - 37px)}.qadpt-web .qadpt-webcontent .qadpt-head{border-bottom:none !important}.qadpt-web .qadpt-webcontent .qadpt-tabs-container{border-bottom:1px solid #ccc;display:flex;justify-content:space-between}.qadpt-web .qadpt-webcontent .qadpt-tabs-container button{text-transform:capitalize}.qadpt-web .qadpt-webcontent .qadpt-tabs-container .Mui-selected{font-weight:600;color:#000}.qadpt-web .qadpt-webcontent .qadpt-tabs-container .MuiTabs-indicator{background-color:var(--button-bg-color)}.qadpt-web .qadpt-webcontent .qadpt-websearch-container{display:flex;justify-content:flex-end}.qadpt-web .qadpt-webcontent .qadpt-websearch-container .qadpt-websearch{display:flex;align-items:center;border-radius:10px}.qadpt-web .qadpt-webcontent .qadpt-websearch-container .qadpt-websearch .MuiOutlinedInput-root{display:flex;align-items:center;justify-content:flex-end;padding:0 !important;border-radius:10px !important}.qadpt-web .qadpt-webcontent .qadpt-websearch-container .qadpt-websearch .MuiInputBase-input{display:flex;align-items:center;justify-content:flex-end;padding:10px 15px 10px 0 !important;font-size:16px}.qadpt-web .qadpt-webcontent .qadpt-websearch-container .qadpt-websearch .MuiInputAdornment-root{display:flex;align-items:center;justify-content:flex-end;margin:0 8px}.qadpt-web .qadpt-webcontent .qadpt-websearch-container .qadpt-websearch button{padding:0 !important}.qadpt-web .qadpt-webcontent .qadpt-websearch-container .qadpt-websearch button svg{margin:0 !important;height:20px;width:20px}.qadpt-web .qadpt-webcontent .qadpt-webgird,.qadpt-web .qadpt-webcontent .qadpt-webgrd{height:calc(100vh - 170px)}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-main,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-main{--DataGrid-topContainerHeight: 40px !important}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-filler,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-filler{--rowBorderColor: transparent !important}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-columnHeaders div[role=row],.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-columnHeaders div[role=row]{max-width:calc(100% - 30px)}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-row,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-row{border:.5px solid #ececec;background-color:var(--white-color);border-radius:8px;margin-bottom:10px;max-width:calc(100% - 30px);--rowBorderColor: transparent !important}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-row .MuiDataGrid-cell,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-row .MuiDataGrid-cell{padding:0 15px !important}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-row:hover,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-row:hover{background-color:rgba(0,0,0,.04)}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-columnHeaders .MuiDataGrid-columnHeaderTitleContainerContent .MuiDataGrid-columnHeaderTitle,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-columnHeaders .MuiDataGrid-columnHeaderTitleContainerContent .MuiDataGrid-columnHeaderTitle{font-weight:600;font-size:14px}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-cell,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-cell{border-bottom:none}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiTablePagination-root .MuiSelect-select.MuiTablePagination-select,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiTablePagination-root .MuiSelect-select.MuiTablePagination-select{padding-right:24px !important}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-cell button,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-cell button{border:1px solid #ccc;border-radius:4px;height:26px;width:26px;margin-right:5px;margin-top:-5px}.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-cell button img,.qadpt-web .qadpt-webcontent .qadpt-webgird .MuiDataGrid-cell button svg,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-cell button img,.qadpt-web .qadpt-webcontent .qadpt-webgrd .MuiDataGrid-cell button svg{zoom:.7}.qadpt-web .qadpt-webcontent .qadpt-webgird.qadpt-anngrd .MuiDataGrid-columnHeader:nth-child(2),.qadpt-web .qadpt-webcontent .qadpt-webgrd.qadpt-anngrd .MuiDataGrid-columnHeader:nth-child(2){width:40% !important;max-width:40% !important;min-width:40%}.qadpt-web .qadpt-webcontent .qadpt-webgird.qadpt-anngrd .MuiDataGrid-columnHeader:nth-child(3),.qadpt-web .qadpt-webcontent .qadpt-webgrd.qadpt-anngrd .MuiDataGrid-columnHeader:nth-child(3){width:40% !important;max-width:40% !important;min-width:40%}.qadpt-web .qadpt-webcontent .qadpt-webgird.qadpt-anngrd .MuiDataGrid-columnHeader:nth-child(4),.qadpt-web .qadpt-webcontent .qadpt-webgrd.qadpt-anngrd .MuiDataGrid-columnHeader:nth-child(4){width:20% !important;max-width:20% !important;min-width:20%}.qadpt-web .qadpt-webcontent .qadpt-setting-title .qadpt-titsec-grid button{padding:5px;margin:0 -10px}.qadpt-web .qadpt-webcontent .qadpt-setting-title .qadpt-back-text{cursor:pointer;position:relative;left:10px}.qadpt-web .qadpt-webcontent .qadpt-titsec{margin-bottom:16px;display:flex;align-items:center;justify-content:space-between}.qadpt-web .qadpt-webcontent .qadpt-titsec .qadpt-name-box{display:flex;align-items:center}.qadpt-web .qadpt-webcontent .qadpt-titsec .qadpt-name-box h5{display:block;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;max-width:calc(100vh - 90px)}.qadpt-web .qadpt-webcontent .qadpt-titsec .qadpt-action-btn{border-radius:15px;text-transform:none;font-weight:600;color:var(--button-bg-color);border-color:var(--button-bg-color)}.qadpt-web .qadpt-webcontent .qadpt-titsec .qadpt-action-btn.qadpt-share span{margin-right:0 !important;margin-left:0 !important}.qadpt-web .qadpt-webcontent .qadpt-titsec .qadpt-action-btn.qadpt-action-btn-primary{background-color:var(--button-bg-color) !important;color:var(--white-color)}.qadpt-web .qadpt-webcontent .qadpt-titsec .qadpt-action-btn.qadpt-action-btn-primary.qadpt-unpublish{background-color:var(--error-color) !important}.qadpt-web .qadpt-webcontent .qadpt-content{display:flex;width:100%;gap:10px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-label{font-size:14px;font-weight:600;display:block;margin-bottom:10px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-sublabel{font-size:14px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-card{border-radius:15px;margin-top:10px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-card.qadpt-buildcard{min-height:420px;max-height:420px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-card.qadpt-trigger{line-height:0}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-card.qadpt-rev{margin-bottom:20px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-left{width:40%}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-left .qadpt-description .MuiPaper-root.MuiCard-root{padding:0 !important}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-left .qadpt-description .MuiPaper-root.MuiCard-root .MuiCardContent-root{padding-bottom:15px !important}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-left .qadpt-description .MuiPaper-root.MuiCard-root textarea{width:100%;height:100px;padding:10px;border:1px solid #ccc;border-radius:8px;font-size:14px;line-height:1.5;overflow:auto}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-left .qadpt-buildsteps .qadpt-build-content{overflow-x:hidden;height:calc(100vh - 320px)}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-left .qadpt-buildsteps .MuiFormControl-root{background-color:#eef5f5}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-left .qadpt-buildsteps .qadpt-steps{margin-top:8px;opacity:.5;pointer-events:none}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right{width:60%}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-auto-trigger .MuiGrid-root.MuiGrid-container{display:flex;align-items:center;justify-content:space-between}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-auto-trigger .MuiGrid-root.MuiGrid-container .Mui-checked{color:var(--button-bg-color)}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-auto-trigger .MuiGrid-root.MuiGrid-container .MuiSwitch-thumb{color:var(--button-bg-color)}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-header-container{display:flex;justify-content:space-between;align-items:center;padding-bottom:10px;border-bottom:1px solid #ccc}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-header-container .qadpt-label-sublabel{display:flex;flex-direction:column}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-header-container .qadpt-add-target-btn{border-radius:15px !important;color:var(--button-bg-color) !important;border:1px solid var(--button-bg-color) !important;text-transform:none !important;font-weight:600 !important}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-header-container .qadpt-add-target-btn.save{padding:8px 12px;margin-left:10px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions{max-height:calc(110vh - 470px);overflow-x:hidden;padding:10px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container{margin-top:0 !important}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container .MuiGrid-root.MuiGrid-item{padding-top:0px !important;padding-bottom:5px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container .MuiGrid-root.MuiGrid-item.qadpt-btngrid{display:flex;align-items:center;place-content:center}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container .MuiGrid-root.MuiGrid-item.qadpt-btngrid button{border:1px solid #f68e8e;padding:5px !important;border-radius:10px !important;margin-top:27px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container .MuiGrid-root.MuiGrid-item.qadpt-btngrid.qadpt-error-btn button{margin-top:10px !important}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container .MuiGrid-root.MuiGrid-item fieldset{height:40px !important}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container .MuiGrid-root.MuiGrid-item .qadpt-field-label{font-size:13px;color:#afafaf}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container .MuiGrid-root.MuiGrid-item .Mui-error{color:#e6a957}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .MuiGrid-root.MuiGrid-container .MuiGrid-root.MuiGrid-item:last-child{padding-left:0 !important}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .qadpt-operator{display:flex;align-items:center;place-content:center;flex-direction:row;justify-content:flex-end;width:100%;gap:8px;top:10px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .qadpt-operator span{color:#7b7b7b;font-size:14px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .qadpt-operator .MuiInputBase-root{width:80px;border:1px solid #ccc;height:30px;background:#ebfeff}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-page-target .qadpt-conditions .qadpt-operator fieldset{border:0}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-frequency .qadpt-freqselect{white-space:nowrap;font-size:12px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-frequency .MuiGrid-root.MuiGrid-item:last-child .MuiFormControl-root{max-width:90px;min-width:90px;margin-left:0 !important}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-rev-publish .qadpt-gridleft{border-right:1px solid #ccc}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-rev-publish .qadpt-gridleft .MuiFormControl-root .MuiInputBase-root{width:80%}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-rev-publish .qadpt-gridright .MuiFormControl-root .MuiInputBase-root{width:80%}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-rev-publish .MuiGrid-container .MuiGrid-item .MuiTypography-root{font-size:14px}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-rev-publish .MuiGrid-container .MuiGrid-item .MuiTypography-root:first-child{font-weight:600}.qadpt-web .qadpt-webcontent .qadpt-content .qadpt-set-right .qadpt-rev-publish .MuiGrid-container .MuiFormControl-root span{font-size:14px}.qadpt-webclonepopup .MuiPaper-root.MuiDialog-paper{border-radius:4px;width:400px}.qadpt-webclonepopup .qadpt-title-sec{padding:15px;border-bottom:1px solid var(--border-color)}.qadpt-webclonepopup .qadpt-title-sec .qadpt-title{font-size:18px;font-weight:600;padding:0px !important;display:block;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;width:290px}.qadpt-webclonepopup .qadpt-title-sec .qadpt-close{position:absolute !important;top:10px;right:20px}.qadpt-webclonepopup .qadpt-title-sec .qadpt-close svg{font-size:18px}.qadpt-webclonepopup .MuiDialogContent-root{padding:15px !important;min-height:100px}.qadpt-webclonepopup .MuiDialogContent-root .clone-fld .MuiInputLabel-outlined.MuiInputLabel-shrink{top:7px !important}.qadpt-webclonepopup .MuiDialogActions-root{border-top:1px solid var(--border-color);padding:15px}.qadpt-webclonepopup .MuiDialogActions-root .MuiButton-root{background-color:var(--button-bg-color);border-radius:4px;padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important}.qadpt-createpopup{width:450px;max-height:400px;min-height:310px;background:var(--white-color) !important;box-shadow:0 3px 8px #000;border-radius:8px}.qadpt-createpopup .qadpt-title-sec{padding:15px;border-bottom:1px solid var(--border-color)}.qadpt-createpopup .qadpt-title-sec .qadpt-title{font-size:18px;font-weight:600}.qadpt-createpopup .qadpt-title-sec .qadpt-sub-title{font-size:12px;line-height:initial}.qadpt-createpopup .qadpt-createflds{padding:15px}.qadpt-createpopup .qadpt-createflds label{font-size:14px;font-weight:600}.qadpt-createpopup .qadpt-save-btn{text-align:end;padding:15px;border-top:1px solid var(--border-color)}.qadpt-createpopup .qadpt-save-btn button{background-color:var(--button-bg-color);color:var(--white-color);text-transform:capitalize;padding:var(--button-padding) !important;line-height:var(--button-lineheight) !important}.qadpt-multi-drpdwn .MuiPaper-root{max-width:500px;left:640px !important;min-width:auto !important;width:auto !important}.qadpt-multi-drpdwn li{white-space:normal;word-break:break-word}.qadpt-datepicker .MuiCalendarPicker-root{max-height:283px !important;overflow-y:hidden}.qadpt-datepicker .MuiCalendarPicker-root .MuiYearPicker-root{max-height:234px !important}.qadpt-datepicker .MuiCalendarPicker-root .MuiYearPicker-root .PrivatePickersYear-root button{line-height:1 !important;margin:4px 0 !important;height:32px !important;width:69px !important}.qadpt-datepicker .MuiPickersCalendarHeader-root{margin-top:8px !important}.qadpt-rolemenu{max-height:270px !important;margin-top:5px !important;overflow-y:auto !important;max-width:460px !important}.qadpt-svg{padding:0 !important}.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-act,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-act{padding:10px !important;display:flex;align-items:center;gap:2px}.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-act svg,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-act i,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-act .MuiSvgIcon-root,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-act svg,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-act i,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-act .MuiSvgIcon-root{cursor:pointer !important;transition:color .2s ease-in-out}.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-act svg:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-act i:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-act .MuiSvgIcon-root:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-act svg:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-act i:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-act .MuiSvgIcon-root:hover{color:#5f9ea0 !important}.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-action,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-action{padding:15px !important;display:flex;align-items:center;gap:2px;place-content:center}.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-action svg,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-action i,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-action .MuiSvgIcon-root,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-action svg,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-action i,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-action .MuiSvgIcon-root{cursor:pointer !important;transition:color .2s ease-in-out}.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-action svg:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-action i:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-setting-grd .qadpt-grd-action .MuiSvgIcon-root:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-action svg:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-action i:hover,.qadpt-page-content .usr-list-page .qadpt-midpart .qadpt-content-box .qadpt-account-grd .qadpt-grd-action .MuiSvgIcon-root:hover{color:#5f9ea0 !important}.MuiButtonBase-root.MuiMenuItem-root{margin:0 10px;padding:10px}.MuiButtonBase-root.MuiMenuItem-root.Mui-selected,.MuiButtonBase-root.MuiMenuItem-root:hover{border-radius:12px;margin:0 10px;padding:10px}.qadpt-homepg{height:calc(100vh - 37px);width:calc(100% - 200px);display:flex;align-items:center;justify-content:center;flex-direction:column;position:relative;left:220px}.qadpt-homepg .qadpt-imgsec img{height:44vh}.qadpt-homepg .qadpt-des{text-align:center;margin-top:50px;width:450px}.qadpt-homepg .qadpt-msg{color:#8d8d8d;font-size:14px}.qadpt-homepg .qadpt-usrnm{font-weight:600;font-size:19px;margin-top:5px;display:flex;align-items:center;place-content:center;gap:5px}.qadpt-homepg .qadpt-statmsg{font-size:14px;margin-top:10px}.qadpt-feedbackpopup .qadpt-upload-button .css-1d6wzja-MuiButton-startIcon{margin:0 8px}.toggle-switch{position:relative;display:inline-block;height:20px;width:36px}.toggle-switch input{opacity:0;width:0;height:0}.toggle-switch .slider{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:#ccc;transition:.4s;border-radius:24px;margin:0 !important}.toggle-switch .slider:before{position:absolute;content:\"\";height:15px;width:15px;left:0px;bottom:3px;background-color:#fff;transition:.4s;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.1)}.toggle-switch input:checked+.slider{background-color:var(--button-bg-color)}.toggle-switch input:checked+.slider:before{transform:translateX(20px)}.toggle-switch input:focus+.slider{box-shadow:0 0 1px var(--button-bg-color)}.toggle-switch input:disabled+.slider{opacity:.5;cursor:not-allowed}.qadpt-rolesfltpopup .MuiDialog-container{justify-content:flex-end !important}.qadpt-rolesfltpopup .MuiDialog-container .qadpt-title{padding:8px 12px !important}.qadpt-rolesfltpopup .MuiDialog-container .qadpt-title .qadpt-close{position:absolute;top:8px;right:8px}.qadpt-rolesfltpopup .MuiDialog-container .MuiDialogContent-root{padding:0 16px 16px 16px !important}.qadpt-rolesfltpopup .MuiDialog-container .qadpt-actions{display:flex;align-items:center;place-content:end;gap:10px;margin-bottom:10px}.qadpt-rolesfltpopup .MuiDialog-container .qadpt-footer{display:flex;align-items:center;place-content:end;padding:8px 12px;border-top:1px solid var(--border-color)}", ".common-icon {\r\n\ttext-align: center;\r\n\twidth: 60px;\r\n\theight: 60px;\r\n\tpadding-top: 10px;\r\n\tmargin: auto;\r\n\tbackground: #ccc;\r\n\tborder-radius: 100%;\r\n}\r\n\r\n.common-icon:before {\r\n\tfont-family: \"qadapt-icons\" !important;\r\n\tfont-size: 36px;\r\n\tfont-weight: 100;\r\n\tcolor: #444;\r\n}\r\n\r\n.fa-analysis:before {\r\n\tcontent: \"\\a003\";\r\n}\r\n.fa-attendance-machine:before {\r\n\tcontent: \"\\a004\";\r\n}\r\n.fa-bike-insurance:before {\r\n\tcontent: \"\\a005\";\r\n}\r\n.fa-bill-receipt:before {\r\n\tcontent: \"\\a006\";\r\n}\r\n.fa-business-communication:before {\r\n\tcontent: \"\\a007\";\r\n}\r\n.fa-business-investment:before {\r\n\tcontent: \"\\a008\";\r\n}\r\n.fa-business-management:before {\r\n\tcontent: \"\\a009\";\r\n}\r\n.fa-businessman-with-briefcase:before {\r\n\tcontent: \"\\a010\";\r\n}\r\n.fa-business-presentation:before {\r\n\tcontent: \"\\a011\";\r\n}\r\n.fa-business-professional:before {\r\n\tcontent: \"\\a012\";\r\n}\r\n.fa-business-profit:before {\r\n\tcontent: \"\\a013\";\r\n}\r\n.fa-business-relationship:before {\r\n\tcontent: \"\\a014\";\r\n}\r\n.fa-buyer:before {\r\n\tcontent: \"\\a015\";\r\n}\r\n.fa-career:before {\r\n\tcontent: \"\\a016\";\r\n}\r\n.fa-car-insurance:before {\r\n\tcontent: \"\\a017\";\r\n}\r\n.fa-car-repair-mechanic:before {\r\n\tcontent: \"\\a018\";\r\n}\r\n.fa-cashier:before {\r\n\tcontent: \"\\a019\";\r\n}\r\n.fa-ceo:before {\r\n\tcontent: \"\\a020\";\r\n}\r\n.fa-client:before {\r\n\tcontent: \"\\a021\";\r\n}\r\n.fa-clients:before {\r\n\tcontent: \"\\a022\";\r\n}\r\n.fa-closed:before {\r\n\tcontent: \"\\a023\";\r\n}\r\n.fa-contract:before {\r\n\tcontent: \"\\a024\";\r\n}\r\n.fa-core-values:before {\r\n\tcontent: \"\\a025\";\r\n}\r\n.fa-corporate:before {\r\n\tcontent: \"\\a026\";\r\n}\r\n.fa-credit-card-swipe:before {\r\n\tcontent: \"\\a027\";\r\n}\r\n.fa-crm-browser:before {\r\n\tcontent: \"\\a028\";\r\n}\r\n.fa-customer-experience:before {\r\n\tcontent: \"\\a029\";\r\n}\r\n.fa-customer-journey:before {\r\n\tcontent: \"\\a030\";\r\n}\r\n.fa-data-analytics:before {\r\n\tcontent: \"\\a031\";\r\n}\r\n.fa-data-science:before {\r\n\tcontent: \"\\a032\";\r\n}\r\n.fa-document-application:before {\r\n\tcontent: \"\\a033\";\r\n}\r\n.fa-document-application-woman:before {\r\n\tcontent: \"\\a034\";\r\n}\r\n.fa-erp:before {\r\n\tcontent: \"\\a035\";\r\n}\r\n.fa-factory-pollution:before {\r\n\tcontent: \"\\a036\";\r\n}\r\n.fa-family-insurance:before {\r\n\tcontent: \"\\a037\";\r\n}\r\n.fa-female-reporter-journalist:before {\r\n\tcontent: \"\\a038\";\r\n}\r\n.fa-fire-insurance:before {\r\n\tcontent: \"\\a039\";\r\n}\r\n.fa-food-industry:before {\r\n\tcontent: \"\\a040\";\r\n}\r\n.fa-general-insurance:before {\r\n\tcontent: \"\\a041\";\r\n}\r\n.fa-growing-market-analysis:before {\r\n\tcontent: \"\\a042\";\r\n}\r\n.fa-gst:before {\r\n\tcontent: \"\\a043\";\r\n}\r\n.fa-headquarter:before {\r\n\tcontent: \"\\a044\";\r\n}\r\n.fa-health-insurance:before {\r\n\tcontent: \"\\a045\";\r\n}\r\n.fa-hierarchy-management:before {\r\n\tcontent: \"\\a046\";\r\n}\r\n.fa-hierarchy-management-task:before {\r\n\tcontent: \"\\a047\";\r\n}\r\n.fa-home-insurance:before {\r\n\tcontent: \"\\a048\";\r\n}\r\n.fa-import-product:before {\r\n\tcontent: \"\\a049\";\r\n}\r\n.fa-improvement-performance:before {\r\n\tcontent: \"\\a050\";\r\n}\r\n.fa-income-taxes:before {\r\n\tcontent: \"\\a051\";\r\n}\r\n.fa-influencer:before {\r\n\tcontent: \"\\a052\";\r\n}\r\n.fa-insight:before {\r\n\tcontent: \"\\a053\";\r\n}\r\n.fa-inspection:before {\r\n\tcontent: \"\\a054\";\r\n}\r\n.fa-insurance-protection:before {\r\n\tcontent: \"\\a055\";\r\n}\r\n.fa-integration:before {\r\n\tcontent: \"\\a056\";\r\n}\r\n.fa-interview:before {\r\n\tcontent: \"\\a057\";\r\n}\r\n.fa-investor:before {\r\n\tcontent: \"\\a058\";\r\n}\r\n.fa-invoice:before {\r\n\tcontent: \"\\a059\";\r\n}\r\n.fa-job:before {\r\n\tcontent: \"\\a060\";\r\n}\r\n.fa-job-search:before {\r\n\tcontent: \"\\a061\";\r\n}\r\n.fa-male-reporter-journalist:before {\r\n\tcontent: \"\\a062\";\r\n}\r\n.fa-management:before {\r\n\tcontent: \"\\a063\";\r\n}\r\n.fa-manufacturing-production:before {\r\n\tcontent: \"\\a064\";\r\n}\r\n.fa-market-research:before {\r\n\tcontent: \"\\a065\";\r\n}\r\n.fa-market-share:before {\r\n\tcontent: \"\\a066\";\r\n}\r\n.fa-mechanic:before {\r\n\tcontent: \"\\a067\";\r\n}\r\n.fa-meeting:before {\r\n\tcontent: \"\\a068\";\r\n}\r\n.fa-meeting-table:before {\r\n\tcontent: \"\\a069\";\r\n}\r\n.fa-mind-map:before {\r\n\tcontent: \"\\a070\";\r\n}\r\n.fa-money-transfer:before {\r\n\tcontent: \"\\a071\";\r\n}\r\n.fa-new-product:before {\r\n\tcontent: \"\\a072\";\r\n}\r\n.fa-newspaper:before {\r\n\tcontent: \"\\a073\";\r\n}\r\n.fa-newspaper-jobs:before {\r\n\tcontent: \"\\a074\";\r\n}\r\n.fa-office:before {\r\n\tcontent: \"\\a075\";\r\n}\r\n.fa-online-survey:before {\r\n\tcontent: \"\\a076\";\r\n}\r\n.fa-online-work:before {\r\n\tcontent: \"\\a077\";\r\n}\r\n.fa-pending-work:before {\r\n\tcontent: \"\\a078\";\r\n}\r\n.fa-person-insurance:before {\r\n\tcontent: \"\\a079\";\r\n}\r\n.fa-pilot:before {\r\n\tcontent: \"\\a080\";\r\n}\r\n.fa-planning:before {\r\n\tcontent: \"\\a081\";\r\n}\r\n.fa-plumbing:before {\r\n\tcontent: \"\\a082\";\r\n}\r\n.fa-power-plant:before {\r\n\tcontent: \"\\a083\";\r\n}\r\n.fa-product-development:before {\r\n\tcontent: \"\\a084\";\r\n}\r\n.fa-productivity:before {\r\n\tcontent: \"\\a085\";\r\n}\r\n.fa-product-launch-release:before {\r\n\tcontent: \"\\a086\";\r\n}\r\n.fa-project:before {\r\n\tcontent: \"\\a087\";\r\n}\r\n.fa-project-management:before {\r\n\tcontent: \"\\a088\";\r\n}\r\n.fa-project-management-timeline:before {\r\n\tcontent: \"\\a089\";\r\n}\r\n.fa-project-manager:before {\r\n\tcontent: \"\\a090\";\r\n}\r\n.fa-project-work:before {\r\n\tcontent: \"\\a091\";\r\n}\r\n.fa-quality-control:before {\r\n\tcontent: \"\\a092\";\r\n}\r\n.fa-receipt:before {\r\n\tcontent: \"\\a093\";\r\n}\r\n.fa-remote-work:before {\r\n\tcontent: \"\\a094\";\r\n}\r\n.fa-repairing:before {\r\n\tcontent: \"\\a095\";\r\n}\r\n.fa-retail-shop:before {\r\n\tcontent: \"\\a096\";\r\n}\r\n.fa-satisfaction:before {\r\n\tcontent: \"\\a097\";\r\n}\r\n.fa-seller:before {\r\n\tcontent: \"\\a098\";\r\n}\r\n.fa-service-desk:before {\r\n\tcontent: \"\\a099\";\r\n}\r\n.fa-services:before {\r\n\tcontent: \"\\a100\";\r\n}\r\n.fa-solution:before {\r\n\tcontent: \"\\a101\";\r\n}\r\n.fa-strategist:before {\r\n\tcontent: \"\\a102\";\r\n}\r\n.fa-successful-businessman:before {\r\n\tcontent: \"\\a103\";\r\n}\r\n.fa-supervisor:before {\r\n\tcontent: \"\\a104\";\r\n}\r\n.fa-supply-chain:before {\r\n\tcontent: \"\\a105\";\r\n}\r\n.fa-tax-calculator:before {\r\n\tcontent: \"\\a106\";\r\n}\r\n.fa-tax-cut:before {\r\n\tcontent: \"\\a107\";\r\n}\r\n.fa-tax-return:before {\r\n\tcontent: \"\\a108\";\r\n}\r\n.fa-team:before {\r\n\tcontent: \"\\a109\";\r\n}\r\n.fa-team-meeting:before {\r\n\tcontent: \"\\a110\";\r\n}\r\n.fa-technician:before {\r\n\tcontent: \"\\a111\";\r\n}\r\n.fa-trade:before {\r\n\tcontent: \"\\a112\";\r\n}\r\n.fa-user-network:before {\r\n\tcontent: \"\\a113\";\r\n}\r\n.fa-value:before {\r\n\tcontent: \"\\a114\";\r\n}\r\n.fa-vat:before {\r\n\tcontent: \"\\a115\";\r\n}\r\n.fa-video-conference:before {\r\n\tcontent: \"\\a116\";\r\n}\r\n.fa-virtual-meeting:before {\r\n\tcontent: \"\\a117\";\r\n}\r\n.fa-meeting-room:before {\r\n\tcontent: \"\\a118\";\r\n}\r\n.fa-workflow:before {\r\n\tcontent: \"\\a119\";\r\n}\r\n.fa-working-hours:before {\r\n\tcontent: \"\\a120\";\r\n}\r\n.fa-working-on-office:before {\r\n\tcontent: \"\\a121\";\r\n}\r\n.fa-working-time:before {\r\n\tcontent: \"\\a122\";\r\n}\r\n.fa-workplace:before {\r\n\tcontent: \"\\a123\";\r\n}\r\n.fa-workshop:before {\r\n\tcontent: \"\\a124\";\r\n}\r\n.fa-waiting-room-area:before {\r\n\tcontent: \"\\a125\";\r\n}\r\n.fa-user-tie-solid:before {\r\n\tcontent: \"\\a127\";\r\n}\r\n.fa-caret-up-solid:before {\r\n\tcontent: \"\\a128\";\r\n}\r\n.fa-check-circle-solid:before {\r\n\tcontent: \"\\a129\";\r\n}\r\n.fa-times-circle-solid:before {\r\n\tcontent: \"\\a130\";\r\n}\r\n.fa-password-reset:before {\r\n\tcontent: \"\\a131\";\r\n}\r\n.fa-password:before {\r\n\tcontent: \"\\a132\";\r\n}\r\n.fa-reset-password:before {\r\n\tcontent: \"\\a133\";\r\n}\r\n.fa-men-gear-circle:before {\r\n\tcontent: \"\\a134\";\r\n}\r\n.fa-men-gear:before {\r\n\tcontent: \"\\a135\";\r\n}\r\n.fa-light-ceilinglight:before {\r\n\tcontent: \"\\a147\";\r\n}\r\n.fa-exclamation-circle-solid:before {\r\n\tcontent: \"\\a148\";\r\n}\r\n.fa-add-multi-dtrecords:before {\r\n\tcontent: \"\\a149\";\r\n}\r\n.fa-branch:before {\r\n\tcontent: \"\\a150\";\r\n}\r\n.fa-deploy-log-download:before {\r\n\tcontent: \"\\a151\";\r\n}\r\n.fa-deployment-log:before {\r\n\tcontent: \"\\a152\";\r\n}\r\n.fa-deploy-rollback:before {\r\n\tcontent: \"\\a153\";\r\n}\r\n.fa-dt-download:before {\r\n\tcontent: \"\\a154\";\r\n}\r\n.fa-ds-filter:before {\r\n\tcontent: \"\\a155\";\r\n}\r\n.fa-dt-functions:before {\r\n\tcontent: \"\\a156\";\r\n}\r\n.fa-import-dtrecords:before {\r\n\tcontent: \"\\a157\";\r\n}\r\n.fa-dt-index:before {\r\n\tcontent: \"\\a158\";\r\n}\r\n.fa-ds-join:before {\r\n\tcontent: \"\\a159\";\r\n}\r\n.fa-manage-stages:before {\r\n\tcontent: \"\\a160\";\r\n}\r\n.fa-records:before {\r\n\tcontent: \"\\a161\";\r\n}\r\n.fa-regular-view:before {\r\n\tcontent: \"\\a162\";\r\n}\r\n.fa-dt-sync:before {\r\n\tcontent: \"\\a163\";\r\n}\r\n.fa-timeline-view:before {\r\n\tcontent: \"\\a164\";\r\n}\r\n.fa-manage-apps:before {\r\n\tcontent: \"\\a165\";\r\n}\r\n.fa-box-key:before {\r\n\tcontent: \"\\a166\";\r\n}\r\n.fa-deploy-branch:before {\r\n\tcontent: \"\\a167\";\r\n}\r\n.fa-version-manage:before {\r\n\tcontent: \"\\a168\";\r\n}\r\n.fa-add-ds:before {\r\n\tcontent: \"\\a169\";\r\n}\r\n.fa-add-dttable:before {\r\n\tcontent: \"\\a170\";\r\n}\r\n.fa-admenu:before {\r\n\tcontent: \"\\a171\";\r\n}\r\n.fa-apps:before {\r\n\tcontent: \"\\a172\";\r\n}\r\n.fa-appstore:before {\r\n\tcontent: \"\\a173\";\r\n}\r\n.fa-crreport:before {\r\n\tcontent: \"\\a174\";\r\n}\r\n.fa-crview:before {\r\n\tcontent: \"\\a175\";\r\n}\r\n.fa-datasource:before {\r\n\tcontent: \"\\a176\";\r\n}\r\n.fa-dsref:before {\r\n\tcontent: \"\\a177\";\r\n}\r\n.fa-dttable:before {\r\n\tcontent: \"\\a178\";\r\n}\r\n.fa-imp-user:before {\r\n\tcontent: \"\\a179\";\r\n}\r\n.fa-ip-white:before {\r\n\tcontent: \"\\a180\";\r\n}\r\n.fa-more:before {\r\n\tcontent: \"\\a181\";\r\n}\r\n.fa-multi-user:before {\r\n\tcontent: \"\\a182\";\r\n}\r\n.fa-pref:before {\r\n\tcontent: \"\\a183\";\r\n}\r\n.fa-config-sms:before {\r\n\tcontent: \"\\a184\";\r\n}\r\n.fa-ham-menu:before {\r\n\tcontent: \"\\a185\";\r\n}\r\n.fa-myroles:before {\r\n\tcontent: \"\\a186\";\r\n}\r\n.fa-dots:before {\r\n\tcontent: \"\\a187\";\r\n}\r\n.fa-add-field:before {\r\n\tcontent: \"\\a188\";\r\n}\r\n.fa-add-plus:before {\r\n\tcontent: \"\\a189\";\r\n}\r\n.fa-avatar:before {\r\n\tcontent: \"\\a190\";\r\n}\r\n.fa-back-arrow:before {\r\n\tcontent: \"\\a191\";\r\n}\r\n.fa-close:before {\r\n\tcontent: \"\\a192\";\r\n}\r\n.fa-close-1:before {\r\n\tcontent: \"\\a193\";\r\n}\r\n.fa-copy-icon:before {\r\n\tcontent: \"\\a194\";\r\n}\r\n.fa-drag:before {\r\n\tcontent: \"\\a195\";\r\n}\r\n.fa-editprofile:before {\r\n\tcontent: \"\\a196\";\r\n}\r\n.fa-group-by:before {\r\n\tcontent: \"\\a197\";\r\n}\r\n.fa-integrations:before {\r\n\tcontent: \"\\a198\";\r\n}\r\n.fa-logout:before {\r\n\tcontent: \"\\a199\";\r\n}\r\n.fa-caret-down-solid:before {\r\n\tcontent: \"\\a200\";\r\n}\r\n.fa-sort-down-solid:before {\r\n\tcontent: \"\\a201\";\r\n}\r\n.fa-mpin:before {\r\n\tcontent: \"\\a202\";\r\n}\r\n.fa-no-notifications:before {\r\n\tcontent: \"\\a203\";\r\n}\r\n.fa-notask:before {\r\n\tcontent: \"\\a204\";\r\n}\r\n.fa-password-lock:before {\r\n\tcontent: \"\\a205\";\r\n}\r\n.fa-preferences:before {\r\n\tcontent: \"\\a206\";\r\n}\r\n.fa-process:before {\r\n\tcontent: \"\\a207\";\r\n}\r\n.fa-profile-notifications:before {\r\n\tcontent: \"\\a208\";\r\n}\r\n.fa-profile-user:before {\r\n\tcontent: \"\\a209\";\r\n}\r\n.fa-reassign:before {\r\n\tcontent: \"\\a210\";\r\n}\r\n.fa-reportproblem:before {\r\n\tcontent: \"\\a211\";\r\n}\r\n.fa-right-arrow:before {\r\n\tcontent: \"\\a212\";\r\n}\r\n.fa-sort:before {\r\n\tcontent: \"\\a213\";\r\n}\r\n.fa-validation:before {\r\n\tcontent: \"\\a214\";\r\n}\r\n.fa-add-record:before {\r\n\tcontent: \"\\a215\";\r\n}\r\n.fa-dafts:before {\r\n\tcontent: \"\\a216\";\r\n}\r\n.fa-dashboard:before {\r\n\tcontent: \"\\a217\";\r\n}\r\n.fa-initiated:before {\r\n\tcontent: \"\\a218\";\r\n}\r\n.fa-manage-app:before {\r\n\tcontent: \"\\a219\";\r\n}\r\n.fa-menu:before {\r\n\tcontent: \"\\a220\";\r\n}\r\n.fa-participated:before {\r\n\tcontent: \"\\a221\";\r\n}\r\n.fa-reports:before {\r\n\tcontent: \"\\a222\";\r\n}\r\n.fa-requests:before {\r\n\tcontent: \"\\a223\";\r\n}\r\n.fa-tasks-circle:before {\r\n\tcontent: \"\\a224\";\r\n}\r\n.fa-tasks_old:before {\r\n\tcontent: \"\\a225\";\r\n}\r\n.fa-solutionview:before {\r\n\tcontent: \"\\a226\";\r\n}\r\n.fa-meeting-room-light:before {\r\n\tcontent: \"\\a227\";\r\n}\r\n.fa-external-rdbms:before {\r\n\tcontent: \"\\a228\";\r\n}\r\n.fa-pin-inclined:before {\r\n\tcontent: \"\\a229\";\r\n}\r\n.fa-generate-data:before {\r\n\tcontent: \"\\a230\";\r\n}\r\n.fa-dt-filter:before {\r\n\tcontent: \"\\a231\";\r\n}\r\n.fa-export-settings:before {\r\n\tcontent: \"\\a400\";\r\n}\r\n.fa-caravan-alt:before {\r\n\tcontent: \"\\e000\";\r\n}\r\n.fa-cat-space:before {\r\n\tcontent: \"\\e001\";\r\n}\r\n.fa-coffee-pot:before {\r\n\tcontent: \"\\e002\";\r\n}\r\n.fa-comet:before {\r\n\tcontent: \"\\e003\";\r\n}\r\n.fa-fan-table:before {\r\n\tcontent: \"\\e004\";\r\n}\r\n.fa-faucet:before {\r\n\tcontent: \"\\e005\";\r\n}\r\n.fa-faucet-drip:before {\r\n\tcontent: \"\\e006\";\r\n}\r\n.fa-galaxy:before {\r\n\tcontent: \"\\e008\";\r\n}\r\n.fa-garage:before {\r\n\tcontent: \"\\e009\";\r\n}\r\n.fa-garage-car:before {\r\n\tcontent: \"\\e00a\";\r\n}\r\n.fa-garage-open:before {\r\n\tcontent: \"\\e00b\";\r\n}\r\n.fa-heat:before {\r\n\tcontent: \"\\e00c\";\r\n}\r\n.fa-house-day:before {\r\n\tcontent: \"\\e00e\";\r\n}\r\n.fa-house-leave:before {\r\n\tcontent: \"\\e00f\";\r\n}\r\n.fa-house-night:before {\r\n\tcontent: \"\\e010\";\r\n}\r\n.fa-house-return:before {\r\n\tcontent: \"\\e011\";\r\n}\r\n.fa-house-signal:before {\r\n\tcontent: \"\\e012\";\r\n}\r\n.fa-lamp-desk:before {\r\n\tcontent: \"\\e014\";\r\n}\r\n.fa-lamp-floor:before {\r\n\tcontent: \"\\e015\";\r\n}\r\n.fa-light-ceiling:before {\r\n\tcontent: \"\\e016\";\r\n}\r\n.fa-light-switch:before {\r\n\tcontent: \"\\e017\";\r\n}\r\n.fa-light-switch-off:before {\r\n\tcontent: \"\\e018\";\r\n}\r\n.fa-microwave:before {\r\n\tcontent: \"\\e01b\";\r\n}\r\n.fa-raygun:before {\r\n\tcontent: \"\\e025\";\r\n}\r\n.fa-rocket-launch:before {\r\n\tcontent: \"\\e027\";\r\n}\r\n.fa-coffin-cross:before {\r\n\tcontent: \"\\e051\";\r\n}\r\n.fa-folder-download:before {\r\n\tcontent: \"\\e053\";\r\n}\r\n.fa-folder-upload:before {\r\n\tcontent: \"\\e054\";\r\n}\r\n.fa-bacteria:before {\r\n\tcontent: \"\\e059\";\r\n}\r\n.fa-bacterium:before {\r\n\tcontent: \"\\e05a\";\r\n}\r\n.fa-box-tissue:before {\r\n\tcontent: \"\\e05b\";\r\n}\r\n.fa-hand-holding-medical:before {\r\n\tcontent: \"\\e05c\";\r\n}\r\n.fa-hand-sparkles:before {\r\n\tcontent: \"\\e05d\";\r\n}\r\n.fa-hands-wash:before {\r\n\tcontent: \"\\e05e\";\r\n}\r\n.fa-handshake-alt-slash:before {\r\n\tcontent: \"\\e05f\";\r\n}\r\n.fa-handshake-slash:before {\r\n\tcontent: \"\\e060\";\r\n}\r\n.fa-head-side-cough:before {\r\n\tcontent: \"\\e061\";\r\n}\r\n.fa-head-side-cough-slash:before {\r\n\tcontent: \"\\e062\";\r\n}\r\n.fa-head-side-mask:before {\r\n\tcontent: \"\\e063\";\r\n}\r\n.fa-head-side-virus:before {\r\n\tcontent: \"\\e064\";\r\n}\r\n.fa-house-user:before {\r\n\tcontent: \"\\e065\";\r\n}\r\n.fa-laptop-house:before {\r\n\tcontent: \"\\e066\";\r\n}\r\n.fa-lungs-virus:before {\r\n\tcontent: \"\\e067\";\r\n}\r\n.fa-angle-double-up:before {\r\n\tcontent: \"\\e92a\";\r\n}\r\n.fa-drum-light:before {\r\n\tcontent: \"\\e963\";\r\n}\r\n.fa-file-signature:before {\r\n\tcontent: \"\\e98a\";\r\n}\r\n.fa-horse-head-light:before {\r\n\tcontent: \"\\ea12\";\r\n}\r\n.fa-image-light:before {\r\n\tcontent: \"\\ea28\";\r\n}\r\n.fa-inventory-light:before {\r\n\tcontent: \"\\ea2f\";\r\n}\r\n.fa-line-columns-light:before {\r\n\tcontent: \"\\ea52\";\r\n}\r\n.fa-location-arrow-light:before {\r\n\tcontent: \"\\ea56\";\r\n}\r\n.fa-location-circle:before {\r\n\tcontent: \"\\ea57\";\r\n}\r\n.fa-mailbox-light:before {\r\n\tcontent: \"\\ea63\";\r\n}\r\n.fa-map-marker-light:before {\r\n\tcontent: \"\\ea6a\";\r\n}\r\n.fa-mug-tea:before {\r\n\tcontent: \"\\ea94\";\r\n}\r\n.fa-music-alt-slash-light:before {\r\n\tcontent: \"\\ea95\";\r\n}\r\n.fa-network-wired-light:before {\r\n\tcontent: \"\\ea96\";\r\n}\r\n.fa-neuter-light:before {\r\n\tcontent: \"\\ea97\";\r\n}\r\n.fa-notes-medical-light:before {\r\n\tcontent: \"\\ea98\";\r\n}\r\n.fa-object-ungroup-light:before {\r\n\tcontent: \"\\ea99\";\r\n}\r\n.fa-oil-temp-light:before {\r\n\tcontent: \"\\ea9a\";\r\n}\r\n.fa-otter-light:before {\r\n\tcontent: \"\\ea9b\";\r\n}\r\n.fa-outdent-light:before {\r\n\tcontent: \"\\ea9c\";\r\n}\r\n.fa-outlet-light:before {\r\n\tcontent: \"\\ea9d\";\r\n}\r\n.fa-oven-light:before {\r\n\tcontent: \"\\ea9e\";\r\n}\r\n.fa-overline-light:before {\r\n\tcontent: \"\\ea9f\";\r\n}\r\n.fa-page-break-light:before {\r\n\tcontent: \"\\eaa0\";\r\n}\r\n.fa-chevron-left-light:before {\r\n\tcontent: \"\\eaa1\";\r\n}\r\n.fa-mobile-android-light:before {\r\n\tcontent: \"\\eaa2\";\r\n}\r\n.fa-comments-alt-dollar-light:before {\r\n\tcontent: \"\\eaa3\";\r\n}\r\n.fa-bus-alt:before {\r\n\tcontent: \"\\eaa4\";\r\n}\r\n.fa-bars-light---f0c9:before {\r\n\tcontent: \"\\eaa5\";\r\n}\r\n.fa-bath:before {\r\n\tcontent: \"\\eaa6\";\r\n}\r\n.fa-user-tag:before {\r\n\tcontent: \"\\eaa7\";\r\n}\r\n.fa-trophy-alt:before {\r\n\tcontent: \"\\eaa8\";\r\n}\r\n.fa-file-light---f15b:before {\r\n\tcontent: \"\\eaa9\";\r\n}\r\n.fa-grip-horizontal-light---f58d:before {\r\n\tcontent: \"\\eaaa\";\r\n}\r\n.fa-blinds-open:before {\r\n\tcontent: \"\\eaab\";\r\n}\r\n.fa-mailbox-light---f813:before {\r\n\tcontent: \"\\eaac\";\r\n}\r\n.fa-glass-martini:before {\r\n\tcontent: \"\\f000\";\r\n}\r\n.fa-music:before {\r\n\tcontent: \"\\f001\";\r\n}\r\n.fa-search:before {\r\n\tcontent: \"\\f002\";\r\n}\r\n.fa-heart:before {\r\n\tcontent: \"\\f004\";\r\n}\r\n.fa-star:before {\r\n\tcontent: \"\\f005\";\r\n}\r\n.fa-user:before {\r\n\tcontent: \"\\f007\";\r\n}\r\n.fa-film:before {\r\n\tcontent: \"\\f008\";\r\n}\r\n.fa-th:before {\r\n\tcontent: \"\\f00a\";\r\n}\r\n.fa-check:before {\r\n\tcontent: \"\\f00c\";\r\n}\r\n.fa-times:before {\r\n\tcontent: \"\\f00d\";\r\n}\r\n.fa-search-plus:before {\r\n\tcontent: \"\\f00e\";\r\n}\r\n.fa-search-minus:before {\r\n\tcontent: \"\\f010\";\r\n}\r\n.fa-power-off:before {\r\n\tcontent: \"\\f011\";\r\n}\r\n.fa-signal:before {\r\n\tcontent: \"\\f012\";\r\n}\r\n.fa-cog:before {\r\n\tcontent: \"\\f013\";\r\n}\r\n.fa-home:before {\r\n\tcontent: \"\\f015\";\r\n}\r\n.fa-clock:before {\r\n\tcontent: \"\\f017\";\r\n}\r\n.fa-road:before {\r\n\tcontent: \"\\f018\";\r\n}\r\n.fa-download:before {\r\n\tcontent: \"\\f019\";\r\n}\r\n.fa-inbox:before {\r\n\tcontent: \"\\f01c\";\r\n}\r\n.fa-redo:before {\r\n\tcontent: \"\\f01e\";\r\n}\r\n.fa-sync:before {\r\n\tcontent: \"\\f021\";\r\n}\r\n.fa-list-alt:before {\r\n\tcontent: \"\\f022\";\r\n}\r\n.fa-lock:before {\r\n\tcontent: \"\\f023\";\r\n}\r\n.fa-flag:before {\r\n\tcontent: \"\\f024\";\r\n}\r\n.fa-headphones:before {\r\n\tcontent: \"\\f025\";\r\n}\r\n.fa-volume-up:before {\r\n\tcontent: \"\\f028\";\r\n}\r\n.fa-qrcode:before {\r\n\tcontent: \"\\f029\";\r\n}\r\n.fa-barcode:before {\r\n\tcontent: \"\\f02a\";\r\n}\r\n.fa-tag:before {\r\n\tcontent: \"\\f02b\";\r\n}\r\n.fa-book:before {\r\n\tcontent: \"\\f02d\";\r\n}\r\n.fa-bookmark:before {\r\n\tcontent: \"\\f02e\";\r\n}\r\n.fa-print:before {\r\n\tcontent: \"\\f02f\";\r\n}\r\n.fa-camera:before {\r\n\tcontent: \"\\f030\";\r\n}\r\n.fa-font:before {\r\n\tcontent: \"\\f031\";\r\n}\r\n.fa-bold:before {\r\n\tcontent: \"\\f032\";\r\n}\r\n.fa-italic:before {\r\n\tcontent: \"\\f033\";\r\n}\r\n.fa-text-width:before {\r\n\tcontent: \"\\f035\";\r\n}\r\n.fa-align-left:before {\r\n\tcontent: \"\\f036\";\r\n}\r\n.fa-align-center:before {\r\n\tcontent: \"\\f037\";\r\n}\r\n.fa-align-right:before {\r\n\tcontent: \"\\f038\";\r\n}\r\n.fa-align-justify:before {\r\n\tcontent: \"\\f039\";\r\n}\r\n.fa-list:before {\r\n\tcontent: \"\\f03a\";\r\n}\r\n.fa-indent:before {\r\n\tcontent: \"\\f03c\";\r\n}\r\n.fa-video:before {\r\n\tcontent: \"\\f03d\";\r\n}\r\n.fa-image:before {\r\n\tcontent: \"\\f03e\";\r\n}\r\n.fa-pencil:before {\r\n\tcontent: \"\\f040\";\r\n}\r\n.fa-map-marker:before {\r\n\tcontent: \"\\f041\";\r\n}\r\n.fa-adjust:before {\r\n\tcontent: \"\\f042\";\r\n}\r\n.fa-tint:before {\r\n\tcontent: \"\\f043\";\r\n}\r\n.fa-edit:before {\r\n\tcontent: \"\\f044\";\r\n}\r\n.fa-arrows:before {\r\n\tcontent: \"\\f047\";\r\n}\r\n.fa-fast-backward:before {\r\n\tcontent: \"\\f049\";\r\n}\r\n.fa-backward:before {\r\n\tcontent: \"\\f04a\";\r\n}\r\n.fa-stop:before {\r\n\tcontent: \"\\f04d\";\r\n}\r\n.fa-forward:before {\r\n\tcontent: \"\\f04e\";\r\n}\r\n.fa-fast-forward:before {\r\n\tcontent: \"\\f050\";\r\n}\r\n.fa-eject:before {\r\n\tcontent: \"\\f052\";\r\n}\r\n.fa-chevron-left:before {\r\n\tcontent: \"\\f053\";\r\n}\r\n.fa-chevron-right:before {\r\n\tcontent: \"\\f054\";\r\n}\r\n.fa-plus-circle:before {\r\n\tcontent: \"\\f055\";\r\n}\r\n.fa-minus-circle:before {\r\n\tcontent: \"\\f056\";\r\n}\r\n.fa-times-circle:before {\r\n\tcontent: \"\\f057\";\r\n}\r\n.fa-check-circle:before {\r\n\tcontent: \"\\f058\";\r\n}\r\n.fa-question-circle:before {\r\n\tcontent: \"\\f059\";\r\n}\r\n.fa-info-circle:before {\r\n\tcontent: \"\\f05a\";\r\n}\r\n.fa-crosshairs:before {\r\n\tcontent: \"\\f05b\";\r\n}\r\n.fa-ban:before {\r\n\tcontent: \"\\f05e\";\r\n}\r\n.fa-arrow-left:before {\r\n\tcontent: \"\\f060\";\r\n}\r\n.fa-arrow-right:before {\r\n\tcontent: \"\\f061\";\r\n}\r\n.fa-arrow-up:before {\r\n\tcontent: \"\\f062\";\r\n}\r\n.fa-arrow-down:before {\r\n\tcontent: \"\\f063\";\r\n}\r\n.fa-share:before {\r\n\tcontent: \"\\f064\";\r\n}\r\n.fa-expand:before {\r\n\tcontent: \"\\f065\";\r\n}\r\n.fa-compress:before {\r\n\tcontent: \"\\f066\";\r\n}\r\n.fa-plus:before {\r\n\tcontent: \"\\f067\";\r\n}\r\n.fa-minus:before {\r\n\tcontent: \"\\f068\";\r\n}\r\n.fa-asterisk:before {\r\n\tcontent: \"\\f069\";\r\n}\r\n.fa-exclamation-circle:before {\r\n\tcontent: \"\\f06a\";\r\n}\r\n.fa-gift:before {\r\n\tcontent: \"\\f06b\";\r\n}\r\n.fa-leaf:before {\r\n\tcontent: \"\\f06c\";\r\n}\r\n.fa-fire:before {\r\n\tcontent: \"\\f06d\";\r\n}\r\n.fa-eye:before {\r\n\tcontent: \"\\f06e\";\r\n}\r\n.fa-eye-slash:before {\r\n\tcontent: \"\\f070\";\r\n}\r\n.fa-exclamation-triangle:before {\r\n\tcontent: \"\\f071\";\r\n}\r\n.fa-plane:before {\r\n\tcontent: \"\\f072\";\r\n}\r\n.fa-calendar-alt:before {\r\n\tcontent: \"\\f073\";\r\n}\r\n.fa-random:before {\r\n\tcontent: \"\\f074\";\r\n}\r\n.fa-comment:before {\r\n\tcontent: \"\\f075\";\r\n}\r\n.fa-magnet:before {\r\n\tcontent: \"\\f076\";\r\n}\r\n.fa-chevron-up:before {\r\n\tcontent: \"\\f077\";\r\n}\r\n.fa-chevron-down:before {\r\n\tcontent: \"\\f078\";\r\n}\r\n.fa-shopping-cart:before {\r\n\tcontent: \"\\f07a\";\r\n}\r\n.fa-folder:before {\r\n\tcontent: \"\\f07b\";\r\n}\r\n.fa-folder-open:before {\r\n\tcontent: \"\\f07c\";\r\n}\r\n.fa-arrows-v:before {\r\n\tcontent: \"\\f07d\";\r\n}\r\n.fa-arrows-h:before {\r\n\tcontent: \"\\f07e\";\r\n}\r\n.fa-chart-bar:before {\r\n\tcontent: \"\\f080\";\r\n}\r\n.fa-camera-retro:before {\r\n\tcontent: \"\\f083\";\r\n}\r\n.fa-key:before {\r\n\tcontent: \"\\f084\";\r\n}\r\n.fa-cogs:before {\r\n\tcontent: \"\\f085\";\r\n}\r\n.fa-comments:before {\r\n\tcontent: \"\\f086\";\r\n}\r\n.fa-sign-out:before {\r\n\tcontent: \"\\f08b\";\r\n}\r\n.fa-thumbtack:before {\r\n\tcontent: \"\\f08d\";\r\n}\r\n.fa-external-link:before {\r\n\tcontent: \"\\f08e\";\r\n}\r\n.fa-upload:before {\r\n\tcontent: \"\\f093\";\r\n}\r\n.fa-lemon:before {\r\n\tcontent: \"\\f094\";\r\n}\r\n.fa-phone-square:before {\r\n\tcontent: \"\\f098\";\r\n}\r\n.fa-credit-card:before {\r\n\tcontent: \"\\f09d\";\r\n}\r\n.fa-rss:before {\r\n\tcontent: \"\\f09e\";\r\n}\r\n.fa-hdd:before {\r\n\tcontent: \"\\f0a0\";\r\n}\r\n.fa-bullhorn:before {\r\n\tcontent: \"\\f0a1\";\r\n}\r\n.fa-certificate:before {\r\n\tcontent: \"\\f0a3\";\r\n}\r\n.fa-hand-point-right:before {\r\n\tcontent: \"\\f0a4\";\r\n}\r\n.fa-hand-point-left:before {\r\n\tcontent: \"\\f0a5\";\r\n}\r\n.fa-hand-point-up:before {\r\n\tcontent: \"\\f0a6\";\r\n}\r\n.fa-hand-point-down:before {\r\n\tcontent: \"\\f0a7\";\r\n}\r\n.fa-arrow-circle-left:before {\r\n\tcontent: \"\\f0a8\";\r\n}\r\n.fa-arrow-circle-right:before {\r\n\tcontent: \"\\f0a9\";\r\n}\r\n.fa-arrow-circle-up:before {\r\n\tcontent: \"\\f0aa\";\r\n}\r\n.fa-arrow-circle-down:before {\r\n\tcontent: \"\\f0ab\";\r\n}\r\n.fa-globe:before {\r\n\tcontent: \"\\f0ac\";\r\n}\r\n.fa-wrench:before {\r\n\tcontent: \"\\f0ad\";\r\n}\r\n.fa-tasks:before {\r\n\tcontent: \"\\f0ae\";\r\n}\r\n.fa-filter:before {\r\n\tcontent: \"\\f0b0\";\r\n}\r\n.fa-briefcase:before {\r\n\tcontent: \"\\f0b1\";\r\n}\r\n.fa-arrows-alt:before {\r\n\tcontent: \"\\f0b2\";\r\n}\r\n.fa-users:before {\r\n\tcontent: \"\\f0c0\";\r\n}\r\n.fa-link:before {\r\n\tcontent: \"\\f0c1\";\r\n}\r\n.fa-cloud:before {\r\n\tcontent: \"\\f0c2\";\r\n}\r\n.fa-flask:before {\r\n\tcontent: \"\\f0c3\";\r\n}\r\n.fa-cut:before {\r\n\tcontent: \"\\f0c4\";\r\n}\r\n.fa-copy:before {\r\n\tcontent: \"\\f0c5\";\r\n}\r\n.fa-paperclip:before {\r\n\tcontent: \"\\f0c6\";\r\n}\r\n.fa-save:before {\r\n\tcontent: \"\\f0c7\";\r\n}\r\n.fa-square:before {\r\n\tcontent: \"\\f0c8\";\r\n}\r\n.fa-bars:before {\r\n\tcontent: \"\\f0c9\";\r\n}\r\n.fa-list-ul:before {\r\n\tcontent: \"\\f0ca\";\r\n}\r\n.fa-list-ol:before {\r\n\tcontent: \"\\f0cb\";\r\n}\r\n.fa-table:before {\r\n\tcontent: \"\\f0ce\";\r\n}\r\n.fa-magic:before {\r\n\tcontent: \"\\f0d0\";\r\n}\r\n.fa-truck:before {\r\n\tcontent: \"\\f0d1\";\r\n}\r\n.fa-money-bill:before {\r\n\tcontent: \"\\f0d6\";\r\n}\r\n.fa-caret-down:before {\r\n\tcontent: \"\\f0d7\";\r\n}\r\n.fa-caret-up:before {\r\n\tcontent: \"\\f0d8\";\r\n}\r\n.fa-caret-left:before {\r\n\tcontent: \"\\f0d9\";\r\n}\r\n.fa-caret-right:before {\r\n\tcontent: \"\\f0da\";\r\n}\r\n.fa-columns:before {\r\n\tcontent: \"\\f0db\";\r\n}\r\n.fa-sort-down:before {\r\n\tcontent: \"\\f0dd\";\r\n}\r\n.fa-envelope:before {\r\n\tcontent: \"\\f0e0\";\r\n}\r\n.fa-undo:before {\r\n\tcontent: \"\\f0e2\";\r\n}\r\n.fa-gavel:before {\r\n\tcontent: \"\\f0e3\";\r\n}\r\n.fa-tachometer:before {\r\n\tcontent: \"\\f0e4\";\r\n}\r\n.fa-bolt:before {\r\n\tcontent: \"\\f0e7\";\r\n}\r\n.fa-sitemap:before {\r\n\tcontent: \"\\f0e8\";\r\n}\r\n.fa-umbrella:before {\r\n\tcontent: \"\\f0e9\";\r\n}\r\n.fa-paste:before {\r\n\tcontent: \"\\f0ea\";\r\n}\r\n.fa-lightbulb:before {\r\n\tcontent: \"\\f0eb\";\r\n}\r\n.fa-exchange:before {\r\n\tcontent: \"\\f0ec\";\r\n}\r\n.fa-cloud-download:before {\r\n\tcontent: \"\\f0ed\";\r\n}\r\n.fa-cloud-upload:before {\r\n\tcontent: \"\\f0ee\";\r\n}\r\n.fa-user-md:before {\r\n\tcontent: \"\\f0f0\";\r\n}\r\n.fa-stethoscope:before {\r\n\tcontent: \"\\f0f1\";\r\n}\r\n.fa-bell:before {\r\n\tcontent: \"\\f0f3\";\r\n}\r\n.fa-coffee:before {\r\n\tcontent: \"\\f0f4\";\r\n}\r\n.fa-hospital:before {\r\n\tcontent: \"\\f0f8\";\r\n}\r\n.fa-ambulance:before {\r\n\tcontent: \"\\f0f9\";\r\n}\r\n.fa-medkit:before {\r\n\tcontent: \"\\f0fa\";\r\n}\r\n.fa-fighter-jet:before {\r\n\tcontent: \"\\f0fb\";\r\n}\r\n.fa-beer:before {\r\n\tcontent: \"\\f0fc\";\r\n}\r\n.fa-h-square:before {\r\n\tcontent: \"\\f0fd\";\r\n}\r\n.fa-plus-square:before {\r\n\tcontent: \"\\f0fe\";\r\n}\r\n.fa-angle-double-left:before {\r\n\tcontent: \"\\f100\";\r\n}\r\n.fa-angle-double-right:before {\r\n\tcontent: \"\\f101\";\r\n}\r\n.fa-angle-double-down:before {\r\n\tcontent: \"\\f103\";\r\n}\r\n.fa-angle-left:before {\r\n\tcontent: \"\\f104\";\r\n}\r\n.fa-angle-right:before {\r\n\tcontent: \"\\f105\";\r\n}\r\n.fa-angle-up:before {\r\n\tcontent: \"\\f106\";\r\n}\r\n.fa-angle-down:before {\r\n\tcontent: \"\\f107\";\r\n}\r\n.fa-desktop:before {\r\n\tcontent: \"\\f108\";\r\n}\r\n.fa-laptop:before {\r\n\tcontent: \"\\f109\";\r\n}\r\n.fa-mobile:before {\r\n\tcontent: \"\\f10b\";\r\n}\r\n.fa-quote-left:before {\r\n\tcontent: \"\\f10d\";\r\n}\r\n.fa-quote-right:before {\r\n\tcontent: \"\\f10e\";\r\n}\r\n.fa-spinner:before {\r\n\tcontent: \"\\f110\";\r\n}\r\n.fa-circle:before {\r\n\tcontent: \"\\f111\";\r\n}\r\n.fa-smile:before {\r\n\tcontent: \"\\f118\";\r\n}\r\n.fa-frown:before {\r\n\tcontent: \"\\f119\";\r\n}\r\n.fa-meh:before {\r\n\tcontent: \"\\f11a\";\r\n}\r\n.fa-gamepad:before {\r\n\tcontent: \"\\f11b\";\r\n}\r\n.fa-keyboard:before {\r\n\tcontent: \"\\f11c\";\r\n}\r\n.fa-flag-checkered:before {\r\n\tcontent: \"\\f11e\";\r\n}\r\n.fa-terminal:before {\r\n\tcontent: \"\\f120\";\r\n}\r\n.fa-code:before {\r\n\tcontent: \"\\f121\";\r\n}\r\n.fa-location-arrow:before {\r\n\tcontent: \"\\f124\";\r\n}\r\n.fa-crop:before {\r\n\tcontent: \"\\f125\";\r\n}\r\n.fa-code-branch:before {\r\n\tcontent: \"\\f126\";\r\n}\r\n.fa-info:before {\r\n\tcontent: \"\\f129\";\r\n}\r\n.fa-exclamation:before {\r\n\tcontent: \"\\f12a\";\r\n}\r\n.fa-eraser:before {\r\n\tcontent: \"\\f12d\";\r\n}\r\n.fa-puzzle-piece:before {\r\n\tcontent: \"\\f12e\";\r\n}\r\n.fa-microphone:before {\r\n\tcontent: \"\\f130\";\r\n}\r\n.fa-microphone-slash:before {\r\n\tcontent: \"\\f131\";\r\n}\r\n.fa-shield:before {\r\n\tcontent: \"\\f132\";\r\n}\r\n.fa-calendar:before {\r\n\tcontent: \"\\f133\";\r\n}\r\n.fa-fire-extinguisher:before {\r\n\tcontent: \"\\f134\";\r\n}\r\n.fa-rocket:before {\r\n\tcontent: \"\\f135\";\r\n}\r\n.fa-chevron-circle-left:before {\r\n\tcontent: \"\\f137\";\r\n}\r\n.fa-chevron-circle-right:before {\r\n\tcontent: \"\\f138\";\r\n}\r\n.fa-chevron-circle-up:before {\r\n\tcontent: \"\\f139\";\r\n}\r\n.fa-chevron-circle-down:before {\r\n\tcontent: \"\\f13a\";\r\n}\r\n.fa-css3:before {\r\n\tcontent: \"\\f13c\";\r\n}\r\n.fa-anchor:before {\r\n\tcontent: \"\\f13d\";\r\n}\r\n.fa-unlock-alt:before {\r\n\tcontent: \"\\f13e\";\r\n}\r\n.fa-bullseye:before {\r\n\tcontent: \"\\f140\";\r\n}\r\n.fa-ellipsis-h:before {\r\n\tcontent: \"\\f141\";\r\n}\r\n.fa-ellipsis-v:before {\r\n\tcontent: \"\\f142\";\r\n}\r\n.fa-play-circle:before {\r\n\tcontent: \"\\f144\";\r\n}\r\n.fa-ticket:before {\r\n\tcontent: \"\\f145\";\r\n}\r\n.fa-minus-square:before {\r\n\tcontent: \"\\f146\";\r\n}\r\n.fa-level-up:before {\r\n\tcontent: \"\\f148\";\r\n}\r\n.fa-level-down:before {\r\n\tcontent: \"\\f149\";\r\n}\r\n.fa-check-square:before {\r\n\tcontent: \"\\f14a\";\r\n}\r\n.fa-external-link-square:before {\r\n\tcontent: \"\\f14c\";\r\n}\r\n.fa-share-square:before {\r\n\tcontent: \"\\f14d\";\r\n}\r\n.fa-compass:before {\r\n\tcontent: \"\\f14e\";\r\n}\r\n.fa-caret-square-down:before {\r\n\tcontent: \"\\f150\";\r\n}\r\n.fa-caret-square-up:before {\r\n\tcontent: \"\\f151\";\r\n}\r\n.fa-caret-square-right:before {\r\n\tcontent: \"\\f152\";\r\n}\r\n.fa-euro-sign:before {\r\n\tcontent: \"\\f153\";\r\n}\r\n.fa-pound-sign:before {\r\n\tcontent: \"\\f154\";\r\n}\r\n.fa-dollar-sign:before {\r\n\tcontent: \"\\f155\";\r\n}\r\n.fa-rupee-sign:before {\r\n\tcontent: \"\\f156\";\r\n}\r\n.fa-yen-sign:before {\r\n\tcontent: \"\\f157\";\r\n}\r\n.fa-ruble-sign:before {\r\n\tcontent: \"\\f158\";\r\n}\r\n.fa-file:before {\r\n\tcontent: \"\\f15b\";\r\n}\r\n.fa-file-alt:before {\r\n\tcontent: \"\\f15c\";\r\n}\r\n.fa-sort-numeric-down:before {\r\n\tcontent: \"\\f162\";\r\n}\r\n.fa-thumbs-up:before {\r\n\tcontent: \"\\f164\";\r\n}\r\n.fa-thumbs-down:before {\r\n\tcontent: \"\\f165\";\r\n}\r\n.fa-adn:before {\r\n\tcontent: \"\\f170\";\r\n}\r\n.fa-bitbucket:before {\r\n\tcontent: \"\\f171\";\r\n}\r\n.fa-long-arrow-down:before {\r\n\tcontent: \"\\f175\";\r\n}\r\n.fa-long-arrow-up:before {\r\n\tcontent: \"\\f176\";\r\n}\r\n.fa-long-arrow-left:before {\r\n\tcontent: \"\\f177\";\r\n}\r\n.fa-long-arrow-right:before {\r\n\tcontent: \"\\f178\";\r\n}\r\n.fa-android:before {\r\n\tcontent: \"\\f17b\";\r\n}\r\n.fa-female:before {\r\n\tcontent: \"\\f182\";\r\n}\r\n.fa-male:before {\r\n\tcontent: \"\\f183\";\r\n}\r\n.fa-sun:before {\r\n\tcontent: \"\\f185\";\r\n}\r\n.fa-moon:before {\r\n\tcontent: \"\\f186\";\r\n}\r\n.fa-archive:before {\r\n\tcontent: \"\\f187\";\r\n}\r\n.fa-bug:before {\r\n\tcontent: \"\\f188\";\r\n}\r\n.fa-pagelines:before {\r\n\tcontent: \"\\f18c\";\r\n}\r\n.fa-caret-square-left:before {\r\n\tcontent: \"\\f191\";\r\n}\r\n.fa-dot-circle:before {\r\n\tcontent: \"\\f192\";\r\n}\r\n.fa-wheelchair:before {\r\n\tcontent: \"\\f193\";\r\n}\r\n.fa-lira-sign:before {\r\n\tcontent: \"\\f195\";\r\n}\r\n.fa-space-shuttle:before {\r\n\tcontent: \"\\f197\";\r\n}\r\n.fa-envelope-square:before {\r\n\tcontent: \"\\f199\";\r\n}\r\n.fa-openid:before {\r\n\tcontent: \"\\f19b\";\r\n}\r\n.fa-university:before {\r\n\tcontent: \"\\f19c\";\r\n}\r\n.fa-graduation-cap:before {\r\n\tcontent: \"\\f19d\";\r\n}\r\n.fa-google:before {\r\n\tcontent: \"\\f1a0\";\r\n}\r\n.fa-stumbleupon:before {\r\n\tcontent: \"\\f1a4\";\r\n}\r\n.fa-drupal:before {\r\n\tcontent: \"\\f1a9\";\r\n}\r\n.fa-language:before {\r\n\tcontent: \"\\f1ab\";\r\n}\r\n.fa-fax:before {\r\n\tcontent: \"\\f1ac\";\r\n}\r\n.fa-building:before {\r\n\tcontent: \"\\f1ad\";\r\n}\r\n.fa-child:before {\r\n\tcontent: \"\\f1ae\";\r\n}\r\n.fa-paw:before {\r\n\tcontent: \"\\f1b0\";\r\n}\r\n.fa-cube:before {\r\n\tcontent: \"\\f1b2\";\r\n}\r\n.fa-cubes:before {\r\n\tcontent: \"\\f1b3\";\r\n}\r\n.fa-behance:before {\r\n\tcontent: \"\\f1b4\";\r\n}\r\n.fa-behance-square:before {\r\n\tcontent: \"\\f1b5\";\r\n}\r\n.fa-recycle:before {\r\n\tcontent: \"\\f1b8\";\r\n}\r\n.fa-car:before {\r\n\tcontent: \"\\f1b9\";\r\n}\r\n.fa-taxi:before {\r\n\tcontent: \"\\f1ba\";\r\n}\r\n.fa-tree:before {\r\n\tcontent: \"\\f1bb\";\r\n}\r\n.fa-deviantart:before {\r\n\tcontent: \"\\f1bd\";\r\n}\r\n.fa-database:before {\r\n\tcontent: \"\\f1c0\";\r\n}\r\n.fa-file-pdf:before {\r\n\tcontent: \"\\f1c1\";\r\n}\r\n.fa-file-word:before {\r\n\tcontent: \"\\f1c2\";\r\n}\r\n.fa-file-excel:before {\r\n\tcontent: \"\\f1c3\";\r\n}\r\n.fa-file-powerpoint:before {\r\n\tcontent: \"\\f1c4\";\r\n}\r\n.fa-file-image:before {\r\n\tcontent: \"\\f1c5\";\r\n}\r\n.fa-file-archive:before {\r\n\tcontent: \"\\f1c6\";\r\n}\r\n.fa-file-audio:before {\r\n\tcontent: \"\\f1c7\";\r\n}\r\n.fa-file-video:before {\r\n\tcontent: \"\\f1c8\";\r\n}\r\n.fa-file-code:before {\r\n\tcontent: \"\\f1c9\";\r\n}\r\n.fa-vine:before {\r\n\tcontent: \"\\f1ca\";\r\n}\r\n.fa-codepen:before {\r\n\tcontent: \"\\f1cb\";\r\n}\r\n.fa-life-ring:before {\r\n\tcontent: \"\\f1cd\";\r\n}\r\n.fa-circle-notch:before {\r\n\tcontent: \"\\f1ce\";\r\n}\r\n.fa-rebel:before {\r\n\tcontent: \"\\f1d0\";\r\n}\r\n.fa-qq:before {\r\n\tcontent: \"\\f1d6\";\r\n}\r\n.fa-paper-plane:before {\r\n\tcontent: \"\\f1d8\";\r\n}\r\n.fa-history:before {\r\n\tcontent: \"\\f1da\";\r\n}\r\n.fa-heading:before {\r\n\tcontent: \"\\f1dc\";\r\n}\r\n.fa-paragraph:before {\r\n\tcontent: \"\\f1dd\";\r\n}\r\n.fa-share-alt:before {\r\n\tcontent: \"\\f1e0\";\r\n}\r\n.fa-bomb:before {\r\n\tcontent: \"\\f1e2\";\r\n}\r\n.fa-futbol:before {\r\n\tcontent: \"\\f1e3\";\r\n}\r\n.fa-binoculars:before {\r\n\tcontent: \"\\f1e5\";\r\n}\r\n.fa-plug:before {\r\n\tcontent: \"\\f1e6\";\r\n}\r\n.fa-newspapers:before {\r\n\tcontent: \"\\f1ea\";\r\n}\r\n.fa-wifi:before {\r\n\tcontent: \"\\f1eb\";\r\n}\r\n.fa-calculator:before {\r\n\tcontent: \"\\f1ec\";\r\n}\r\n.fa-cc-visa:before {\r\n\tcontent: \"\\f1f0\";\r\n}\r\n.fa-cc-mastercard:before {\r\n\tcontent: \"\\f1f1\";\r\n}\r\n.fa-cc-discover:before {\r\n\tcontent: \"\\f1f2\";\r\n}\r\n.fa-cc-amex:before {\r\n\tcontent: \"\\f1f3\";\r\n}\r\n.fa-cc-paypal:before {\r\n\tcontent: \"\\f1f4\";\r\n}\r\n.fa-cc-stripe:before {\r\n\tcontent: \"\\f1f5\";\r\n}\r\n.fa-bell-slash:before {\r\n\tcontent: \"\\f1f6\";\r\n}\r\n.fa-trash:before {\r\n\tcontent: \"\\f1f8\";\r\n}\r\n.fa-copyright:before {\r\n\tcontent: \"\\f1f9\";\r\n}\r\n.fa-at:before {\r\n\tcontent: \"\\f1fa\";\r\n}\r\n.fa-eye-dropper:before {\r\n\tcontent: \"\\f1fb\";\r\n}\r\n.fa-paint-brush:before {\r\n\tcontent: \"\\f1fc\";\r\n}\r\n.fa-birthday-cake:before {\r\n\tcontent: \"\\f1fd\";\r\n}\r\n.fa-chart-area:before {\r\n\tcontent: \"\\f1fe\";\r\n}\r\n.fa-chart-pie:before {\r\n\tcontent: \"\\f200\";\r\n}\r\n.fa-chart-line:before {\r\n\tcontent: \"\\f201\";\r\n}\r\n.fa-toggle-off:before {\r\n\tcontent: \"\\f204\";\r\n}\r\n.fa-toggle-on:before {\r\n\tcontent: \"\\f205\";\r\n}\r\n.fa-bicycle:before {\r\n\tcontent: \"\\f206\";\r\n}\r\n.fa-bus:before {\r\n\tcontent: \"\\f207\";\r\n}\r\n.fa-angellist:before {\r\n\tcontent: \"\\f209\";\r\n}\r\n.fa-closed-captioning:before {\r\n\tcontent: \"\\f20a\";\r\n}\r\n.fa-buysellads:before {\r\n\tcontent: \"\\f20d\";\r\n}\r\n.fa-connectdevelop:before {\r\n\tcontent: \"\\f20e\";\r\n}\r\n.fa-dashcube:before {\r\n\tcontent: \"\\f210\";\r\n}\r\n.fa-cart-plus:before {\r\n\tcontent: \"\\f217\";\r\n}\r\n.fa-cart-arrow-down:before {\r\n\tcontent: \"\\f218\";\r\n}\r\n.fa-diamond:before {\r\n\tcontent: \"\\f219\";\r\n}\r\n.fa-ship:before {\r\n\tcontent: \"\\f21a\";\r\n}\r\n.fa-motorcycle:before {\r\n\tcontent: \"\\f21c\";\r\n}\r\n.fa-heartbeat:before {\r\n\tcontent: \"\\f21e\";\r\n}\r\n.fa-mars:before {\r\n\tcontent: \"\\f222\";\r\n}\r\n.fa-mercury:before {\r\n\tcontent: \"\\f223\";\r\n}\r\n.fa-mars-double:before {\r\n\tcontent: \"\\f227\";\r\n}\r\n.fa-mars-stroke:before {\r\n\tcontent: \"\\f229\";\r\n}\r\n.fa-mars-stroke-v:before {\r\n\tcontent: \"\\f22a\";\r\n}\r\n.fa-mars-stroke-h:before {\r\n\tcontent: \"\\f22b\";\r\n}\r\n.fa-genderless:before {\r\n\tcontent: \"\\f22d\";\r\n}\r\n.fa-whatsapp:before {\r\n\tcontent: \"\\f232\";\r\n}\r\n.fa-server:before {\r\n\tcontent: \"\\f233\";\r\n}\r\n.fa-user-plus:before {\r\n\tcontent: \"\\f234\";\r\n}\r\n.fa-user-times:before {\r\n\tcontent: \"\\f235\";\r\n}\r\n.fa-bed:before {\r\n\tcontent: \"\\f236\";\r\n}\r\n.fa-train:before {\r\n\tcontent: \"\\f238\";\r\n}\r\n.fa-battery-full:before {\r\n\tcontent: \"\\f240\";\r\n}\r\n.fa-battery-three-quarters:before {\r\n\tcontent: \"\\f241\";\r\n}\r\n.fa-battery-half:before {\r\n\tcontent: \"\\f242\";\r\n}\r\n.fa-battery-quarter:before {\r\n\tcontent: \"\\f243\";\r\n}\r\n.fa-battery-empty:before {\r\n\tcontent: \"\\f244\";\r\n}\r\n.fa-mouse-pointer:before {\r\n\tcontent: \"\\f245\";\r\n}\r\n.fa-i-cursor:before {\r\n\tcontent: \"\\f246\";\r\n}\r\n.fa-object-group:before {\r\n\tcontent: \"\\f247\";\r\n}\r\n.fa-sticky-note:before {\r\n\tcontent: \"\\f249\";\r\n}\r\n.fa-cc-jcb:before {\r\n\tcontent: \"\\f24b\";\r\n}\r\n.fa-cc-diners-club:before {\r\n\tcontent: \"\\f24c\";\r\n}\r\n.fa-clone:before {\r\n\tcontent: \"\\f24d\";\r\n}\r\n.fa-balance-scale:before {\r\n\tcontent: \"\\f24e\";\r\n}\r\n.fa-hourglass-start:before {\r\n\tcontent: \"\\f251\";\r\n}\r\n.fa-hourglass-half:before {\r\n\tcontent: \"\\f252\";\r\n}\r\n.fa-hourglass-end:before {\r\n\tcontent: \"\\f253\";\r\n}\r\n.fa-hourglass:before {\r\n\tcontent: \"\\f254\";\r\n}\r\n.fa-hand-rock:before {\r\n\tcontent: \"\\f255\";\r\n}\r\n.fa-hand-paper:before {\r\n\tcontent: \"\\f256\";\r\n}\r\n.fa-hand-scissors:before {\r\n\tcontent: \"\\f257\";\r\n}\r\n.fa-hand-lizard:before {\r\n\tcontent: \"\\f258\";\r\n}\r\n.fa-hand-spock:before {\r\n\tcontent: \"\\f259\";\r\n}\r\n.fa-hand-pointer:before {\r\n\tcontent: \"\\f25a\";\r\n}\r\n.fa-hand-peace:before {\r\n\tcontent: \"\\f25b\";\r\n}\r\n.fa-trademark:before {\r\n\tcontent: \"\\f25c\";\r\n}\r\n.fa-registered:before {\r\n\tcontent: \"\\f25d\";\r\n}\r\n.fa-creative-commons:before {\r\n\tcontent: \"\\f25e\";\r\n}\r\n.fa-gg-circle:before {\r\n\tcontent: \"\\f261\";\r\n}\r\n.fa-chrome:before {\r\n\tcontent: \"\\f268\";\r\n}\r\n.fa-tv:before {\r\n\tcontent: \"\\f26c\";\r\n}\r\n.fa-contao:before {\r\n\tcontent: \"\\f26d\";\r\n}\r\n.fa-500px:before {\r\n\tcontent: \"\\f26e\";\r\n}\r\n.fa-amazon:before {\r\n\tcontent: \"\\f270\";\r\n}\r\n.fa-calendar-plus:before {\r\n\tcontent: \"\\f271\";\r\n}\r\n.fa-calendar-minus:before {\r\n\tcontent: \"\\f272\";\r\n}\r\n.fa-calendar-times:before {\r\n\tcontent: \"\\f273\";\r\n}\r\n.fa-calendar-check:before {\r\n\tcontent: \"\\f274\";\r\n}\r\n.fa-industry:before {\r\n\tcontent: \"\\f275\";\r\n}\r\n.fa-map-pin:before {\r\n\tcontent: \"\\f276\";\r\n}\r\n.fa-map-signs:before {\r\n\tcontent: \"\\f277\";\r\n}\r\n.fa-comment-alt:before {\r\n\tcontent: \"\\f27a\";\r\n}\r\n.fa-black-tie:before {\r\n\tcontent: \"\\f27e\";\r\n}\r\n.fa-codiepie:before {\r\n\tcontent: \"\\f284\";\r\n}\r\n.fa-pause-circle:before {\r\n\tcontent: \"\\f28b\";\r\n}\r\n.fa-stop-circle:before {\r\n\tcontent: \"\\f28d\";\r\n}\r\n.fa-hashtag:before {\r\n\tcontent: \"\\f292\";\r\n}\r\n.fa-bluetooth:before {\r\n\tcontent: \"\\f293\";\r\n}\r\n.fa-bluetooth-b:before {\r\n\tcontent: \"\\f294\";\r\n}\r\n.fa-universal-access:before {\r\n\tcontent: \"\\f29a\";\r\n}\r\n.fa-blind:before {\r\n\tcontent: \"\\f29d\";\r\n}\r\n.fa-audio-description:before {\r\n\tcontent: \"\\f29e\";\r\n}\r\n.fa-braille:before {\r\n\tcontent: \"\\f2a1\";\r\n}\r\n.fa-assistive-listening-systems:before {\r\n\tcontent: \"\\f2a2\";\r\n}\r\n.fa-american-sign-language-interpreting:before {\r\n\tcontent: \"\\f2a3\";\r\n}\r\n.fa-deaf:before {\r\n\tcontent: \"\\f2a4\";\r\n}\r\n.fa-low-vision:before {\r\n\tcontent: \"\\f2a8\";\r\n}\r\n.fa-handshake:before {\r\n\tcontent: \"\\f2b5\";\r\n}\r\n.fa-envelope-open:before {\r\n\tcontent: \"\\f2b6\";\r\n}\r\n.fa-address-book:before {\r\n\tcontent: \"\\f2b9\";\r\n}\r\n.fa-address-card:before {\r\n\tcontent: \"\\f2bb\";\r\n}\r\n.fa-user-circle:before {\r\n\tcontent: \"\\f2bd\";\r\n}\r\n.fa-id-badge:before {\r\n\tcontent: \"\\f2c1\";\r\n}\r\n.fa-id-card:before {\r\n\tcontent: \"\\f2c2\";\r\n}\r\n.fa-thermometer-full:before {\r\n\tcontent: \"\\f2c7\";\r\n}\r\n.fa-shower:before {\r\n\tcontent: \"\\f2cc\";\r\n}\r\n.fa-podcast:before {\r\n\tcontent: \"\\f2ce\";\r\n}\r\n.fa-window-restore:before {\r\n\tcontent: \"\\f2d2\";\r\n}\r\n.fa-microchip:before {\r\n\tcontent: \"\\f2db\";\r\n}\r\n.fa-snowflake:before {\r\n\tcontent: \"\\f2dc\";\r\n}\r\n.fa-watch:before {\r\n\tcontent: \"\\f2e1\";\r\n}\r\n.fa-utensils-alt:before {\r\n\tcontent: \"\\f2e6\";\r\n}\r\n.fa-trophy:before {\r\n\tcontent: \"\\f2eb\";\r\n}\r\n.fa-triangle:before {\r\n\tcontent: \"\\f2ec\";\r\n}\r\n.fa-trash-alt:before {\r\n\tcontent: \"\\f2ed\";\r\n}\r\n.fa-sync-alt:before {\r\n\tcontent: \"\\f2f1\";\r\n}\r\n.fa-stopwatch:before {\r\n\tcontent: \"\\f2f2\";\r\n}\r\n.fa-spade:before {\r\n\tcontent: \"\\f2f4\";\r\n}\r\n.fa-sign-out-alt:before {\r\n\tcontent: \"\\f2f5\";\r\n}\r\n.fa-sign-in-alt:before {\r\n\tcontent: \"\\f2f6\";\r\n}\r\n.fa-uniF2F7:before {\r\n\tcontent: \"\\f2f7\";\r\n}\r\n.fa-uniF2F8:before {\r\n\tcontent: \"\\f2f8\";\r\n}\r\n.fa-rectangle-landscape:before {\r\n\tcontent: \"\\f2fa\";\r\n}\r\n.fa-rectangle-portrait:before {\r\n\tcontent: \"\\f2fb\";\r\n}\r\n.fa-poo:before {\r\n\tcontent: \"\\f2fe\";\r\n}\r\n.fa-images:before {\r\n\tcontent: \"\\f302\";\r\n}\r\n.fa-pencil-alt-light:before {\r\n\tcontent: \"\\f303\";\r\n}\r\n.fa-octagon:before {\r\n\tcontent: \"\\f306\";\r\n}\r\n.fa-minus-hexagon:before {\r\n\tcontent: \"\\f307\";\r\n}\r\n.fa-minus-octagon:before {\r\n\tcontent: \"\\f308\";\r\n}\r\n.fa-long-arrow-alt-down:before {\r\n\tcontent: \"\\f309\";\r\n}\r\n.fa-long-arrow-alt-left:before {\r\n\tcontent: \"\\f30a\";\r\n}\r\n.fa-long-arrow-alt-right:before {\r\n\tcontent: \"\\f30b\";\r\n}\r\n.fa-long-arrow-alt-up:before {\r\n\tcontent: \"\\f30c\";\r\n}\r\n.fa-lock-alt:before {\r\n\tcontent: \"\\f30d\";\r\n}\r\n.fa-jack-o-lantern:before {\r\n\tcontent: \"\\f30e\";\r\n}\r\n.fa-info-square:before {\r\n\tcontent: \"\\f30f\";\r\n}\r\n.fa-inbox-in:before {\r\n\tcontent: \"\\f310\";\r\n}\r\n.fa-inbox-out:before {\r\n\tcontent: \"\\f311\";\r\n}\r\n.fa-hexagon:before {\r\n\tcontent: \"\\f312\";\r\n}\r\n.fa-h1:before {\r\n\tcontent: \"\\f313\";\r\n}\r\n.fa-h2:before {\r\n\tcontent: \"\\f314\";\r\n}\r\n.fa-h3:before {\r\n\tcontent: \"\\f315\";\r\n}\r\n.fa-file-check:before {\r\n\tcontent: \"\\f316\";\r\n}\r\n.fa-file-times:before {\r\n\tcontent: \"\\f317\";\r\n}\r\n.fa-file-minus:before {\r\n\tcontent: \"\\f318\";\r\n}\r\n.fa-file-plus:before {\r\n\tcontent: \"\\f319\";\r\n}\r\n.fa-file-exclamation:before {\r\n\tcontent: \"\\f31a\";\r\n}\r\n.fa-file-edit:before {\r\n\tcontent: \"\\f31c\";\r\n}\r\n.fa-expand-arrows:before {\r\n\tcontent: \"\\f31d\";\r\n}\r\n.fa-expand-arrows-alt:before {\r\n\tcontent: \"\\f31e\";\r\n}\r\n.fa-expand-wide:before {\r\n\tcontent: \"\\f320\";\r\n}\r\n.fa-exclamation-square:before {\r\n\tcontent: \"\\f321\";\r\n}\r\n.fa-chevron-double-down:before {\r\n\tcontent: \"\\f322\";\r\n}\r\n.fa-chevron-double-left:before {\r\n\tcontent: \"\\f323\";\r\n}\r\n.fa-chevron-double-right:before {\r\n\tcontent: \"\\f324\";\r\n}\r\n.fa-chevron-double-up:before {\r\n\tcontent: \"\\f325\";\r\n}\r\n.fa-compress-wide:before {\r\n\tcontent: \"\\f326\";\r\n}\r\n.fa-club:before {\r\n\tcontent: \"\\f327\";\r\n}\r\n.fa-clipboard:before {\r\n\tcontent: \"\\f328\";\r\n}\r\n.fa-chevron-square-down:before {\r\n\tcontent: \"\\f329\";\r\n}\r\n.fa-chevron-square-left:before {\r\n\tcontent: \"\\f32a\";\r\n}\r\n.fa-chevron-square-right:before {\r\n\tcontent: \"\\f32b\";\r\n}\r\n.fa-chevron-square-up:before {\r\n\tcontent: \"\\f32c\";\r\n}\r\n.fa-caret-circle-down:before {\r\n\tcontent: \"\\f32d\";\r\n}\r\n.fa-caret-circle-left:before {\r\n\tcontent: \"\\f32e\";\r\n}\r\n.fa-caret-circle-right:before {\r\n\tcontent: \"\\f330\";\r\n}\r\n.fa-caret-circle-up:before {\r\n\tcontent: \"\\f331\";\r\n}\r\n.fa-camera-alt:before {\r\n\tcontent: \"\\f332\";\r\n}\r\n.fa-calendar-exclamation:before {\r\n\tcontent: \"\\f334\";\r\n}\r\n.fa-badge:before {\r\n\tcontent: \"\\f335\";\r\n}\r\n.fa-badge-check:before {\r\n\tcontent: \"\\f336\";\r\n}\r\n.fa-arrows-alt-h:before {\r\n\tcontent: \"\\f337\";\r\n}\r\n.fa-arrows-alt-v:before {\r\n\tcontent: \"\\f338\";\r\n}\r\n.fa-arrow-square-down:before {\r\n\tcontent: \"\\f339\";\r\n}\r\n.fa-arrow-square-left:before {\r\n\tcontent: \"\\f33a\";\r\n}\r\n.fa-arrow-square-right:before {\r\n\tcontent: \"\\f33b\";\r\n}\r\n.fa-arrow-square-up:before {\r\n\tcontent: \"\\f33c\";\r\n}\r\n.fa-arrow-to-bottom:before {\r\n\tcontent: \"\\f33d\";\r\n}\r\n.fa-arrow-to-left:before {\r\n\tcontent: \"\\f33e\";\r\n}\r\n.fa-arrow-to-right:before {\r\n\tcontent: \"\\f340\";\r\n}\r\n.fa-arrow-to-top:before {\r\n\tcontent: \"\\f341\";\r\n}\r\n.fa-arrow-from-bottom:before {\r\n\tcontent: \"\\f342\";\r\n}\r\n.fa-arrow-from-left:before {\r\n\tcontent: \"\\f343\";\r\n}\r\n.fa-arrow-from-right:before {\r\n\tcontent: \"\\f344\";\r\n}\r\n.fa-arrow-from-top:before {\r\n\tcontent: \"\\f345\";\r\n}\r\n.fa-arrow-alt-from-bottom:before {\r\n\tcontent: \"\\f346\";\r\n}\r\n.fa-arrow-alt-from-left:before {\r\n\tcontent: \"\\f347\";\r\n}\r\n.fa-arrow-alt-from-right:before {\r\n\tcontent: \"\\f348\";\r\n}\r\n.fa-arrow-alt-from-top:before {\r\n\tcontent: \"\\f349\";\r\n}\r\n.fa-arrow-alt-to-bottom:before {\r\n\tcontent: \"\\f34a\";\r\n}\r\n.fa-arrow-alt-to-left:before {\r\n\tcontent: \"\\f34b\";\r\n}\r\n.fa-arrow-alt-to-right:before {\r\n\tcontent: \"\\f34c\";\r\n}\r\n.fa-arrow-alt-to-top:before {\r\n\tcontent: \"\\f34d\";\r\n}\r\n.fa-alarm-clock:before {\r\n\tcontent: \"\\f34e\";\r\n}\r\n.fa-arrow-alt-square-down:before {\r\n\tcontent: \"\\f350\";\r\n}\r\n.fa-arrow-alt-square-left:before {\r\n\tcontent: \"\\f351\";\r\n}\r\n.fa-arrow-alt-square-right:before {\r\n\tcontent: \"\\f352\";\r\n}\r\n.fa-arrow-alt-square-up-:before {\r\n\tcontent: \"\\f353\";\r\n}\r\n.fa-arrow-alt-down:before {\r\n\tcontent: \"\\f354\";\r\n}\r\n.fa-arrow-alt-left:before {\r\n\tcontent: \"\\f355\";\r\n}\r\n.fa-arrow-alt-right:before {\r\n\tcontent: \"\\f356\";\r\n}\r\n.fa-arrow-alt-up:before {\r\n\tcontent: \"\\f357\";\r\n}\r\n.fa-arrow-alt-circle-down:before {\r\n\tcontent: \"\\f358\";\r\n}\r\n.fa-arrow-alt-circle-left:before {\r\n\tcontent: \"\\f359\";\r\n}\r\n.fa-arrow-alt-circle-right:before {\r\n\tcontent: \"\\f35a\";\r\n}\r\n.fa-arrow-alt-circle-up:before {\r\n\tcontent: \"\\f35b\";\r\n}\r\n.fa-external-link-alt:before {\r\n\tcontent: \"\\f35d\";\r\n}\r\n.fa-external-link-square-alt:before {\r\n\tcontent: \"\\f360\";\r\n}\r\n.fa-exchange-alt:before {\r\n\tcontent: \"\\f362\";\r\n}\r\n.fa-repeat:before {\r\n\tcontent: \"\\f363\";\r\n}\r\n.fa-accessible-icon:before {\r\n\tcontent: \"\\f368\";\r\n}\r\n.fa-accusoft:before {\r\n\tcontent: \"\\f369\";\r\n}\r\n.fa-adversalbrands:before {\r\n\tcontent: \"\\f36a\";\r\n}\r\n.fa-affiliatetheme:before {\r\n\tcontent: \"\\f36b\";\r\n}\r\n.fa-algolia:before {\r\n\tcontent: \"\\f36c\";\r\n}\r\n.fa-amilia:before {\r\n\tcontent: \"\\f36d\";\r\n}\r\n.fa-app-store:before {\r\n\tcontent: \"\\f36f\";\r\n}\r\n.fa-app-store-ios:before {\r\n\tcontent: \"\\f370\";\r\n}\r\n.fa-asymmetrik:before {\r\n\tcontent: \"\\f372\";\r\n}\r\n.fa-avianex:before {\r\n\tcontent: \"\\f374\";\r\n}\r\n.fa-aws:before {\r\n\tcontent: \"\\f375\";\r\n}\r\n.fa-battery-bolt:before {\r\n\tcontent: \"\\f376\";\r\n}\r\n.fa-battery-slash:before {\r\n\tcontent: \"\\f377\";\r\n}\r\n.fa-bitcoin:before {\r\n\tcontent: \"\\f379\";\r\n}\r\n.fa-bity:before {\r\n\tcontent: \"\\f37a\";\r\n}\r\n.fa-blackberry:before {\r\n\tcontent: \"\\f37b\";\r\n}\r\n.fa-blogger:before {\r\n\tcontent: \"\\f37c\";\r\n}\r\n.fa-blogger-b:before {\r\n\tcontent: \"\\f37d\";\r\n}\r\n.fa-browser:before {\r\n\tcontent: \"\\f37e\";\r\n}\r\n.fa-buromobelexperte:before {\r\n\tcontent: \"\\f37f\";\r\n}\r\n.fa-centercode:before {\r\n\tcontent: \"\\f380\";\r\n}\r\n.fa-cloud-download-alt:before {\r\n\tcontent: \"\\f381\";\r\n}\r\n.fa-cloud-upload-alt:before {\r\n\tcontent: \"\\f382\";\r\n}\r\n.fa-cloudscale:before {\r\n\tcontent: \"\\f383\";\r\n}\r\n.fa-cloudsmith:before {\r\n\tcontent: \"\\f384\";\r\n}\r\n.fa-cloudversify:before {\r\n\tcontent: \"\\f385\";\r\n}\r\n.fa-code-commit:before {\r\n\tcontent: \"\\f386\";\r\n}\r\n.fa-code-merge:before {\r\n\tcontent: \"\\f387\";\r\n}\r\n.fa-cpanel:before {\r\n\tcontent: \"\\f388\";\r\n}\r\n.fa-credit-card-blank:before {\r\n\tcontent: \"\\f389\";\r\n}\r\n.fa-credit-card-front:before {\r\n\tcontent: \"\\f38a\";\r\n}\r\n.fa-css3-alt:before {\r\n\tcontent: \"\\f38b\";\r\n}\r\n.fa-cuttlefish:before {\r\n\tcontent: \"\\f38c\";\r\n}\r\n.fa-d-and-d:before {\r\n\tcontent: \"\\f38d\";\r\n}\r\n.fa-deskpro:before {\r\n\tcontent: \"\\f38f\";\r\n}\r\n.fa-desktop-alt:before {\r\n\tcontent: \"\\f390\";\r\n}\r\n.fa-ellipsis-v-alt:before {\r\n\tcontent: \"\\f39c\";\r\n}\r\n.fa-film-alt:before {\r\n\tcontent: \"\\f3a0\";\r\n}\r\n.fa-gem:before {\r\n\tcontent: \"\\f3a5\";\r\n}\r\n.fa-industry-alt:before {\r\n\tcontent: \"\\f3b3\";\r\n}\r\n.fa-level-down-alt:before {\r\n\tcontent: \"\\f3be\";\r\n}\r\n.fa-level-up-alt:before {\r\n\tcontent: \"\\f3bf\";\r\n}\r\n.fa-lock-open:before {\r\n\tcontent: \"\\f3c1\";\r\n}\r\n.fa-lock-open-alt:before {\r\n\tcontent: \"\\f3c2\";\r\n}\r\n.fa-map-marker-alt:before {\r\n\tcontent: \"\\f3c5\";\r\n}\r\n.fa-microphone-alt:before {\r\n\tcontent: \"\\f3c9\";\r\n}\r\n.fa-mobile-alt:before {\r\n\tcontent: \"\\f3cd\";\r\n}\r\n.fa-mobile-android:before {\r\n\tcontent: \"\\f3ce\";\r\n}\r\n.fa-mobile-android-alt:before {\r\n\tcontent: \"\\f3cf\";\r\n}\r\n.fa-money-bill-alt:before {\r\n\tcontent: \"\\f3d1\";\r\n}\r\n.fa-portrait:before {\r\n\tcontent: \"\\f3e0\";\r\n}\r\n.fa-reply:before {\r\n\tcontent: \"\\f3e5\";\r\n}\r\n.fa-sliders-v:before {\r\n\tcontent: \"\\f3f1\";\r\n}\r\n.fa-sliders-v-square:before {\r\n\tcontent: \"\\f3f2\";\r\n}\r\n.fa-user-alt:before {\r\n\tcontent: \"\\f406\";\r\n}\r\n.fa-window-alt:before {\r\n\tcontent: \"\\f40f\";\r\n}\r\n.fa-apple-pay:before {\r\n\tcontent: \"\\f415\";\r\n}\r\n.fa-cc-apple-pay:before {\r\n\tcontent: \"\\f416\";\r\n}\r\n.fa-autoprefixer:before {\r\n\tcontent: \"\\f41c\";\r\n}\r\n.fa-angular:before {\r\n\tcontent: \"\\f420\";\r\n}\r\n.fa-compress-alt:before {\r\n\tcontent: \"\\f422\";\r\n}\r\n.fa-expand-alt:before {\r\n\tcontent: \"\\f424\";\r\n}\r\n.fa-amazon-pay:before {\r\n\tcontent: \"\\f42c\";\r\n}\r\n.fa-cc-amazon-pay:before {\r\n\tcontent: \"\\f42d\";\r\n}\r\n.fa-baseball:before {\r\n\tcontent: \"\\f432\";\r\n}\r\n.fa-baseball-ball:before {\r\n\tcontent: \"\\f433\";\r\n}\r\n.fa-basketball-ball:before {\r\n\tcontent: \"\\f434\";\r\n}\r\n.fa-basketball-hoop:before {\r\n\tcontent: \"\\f435\";\r\n}\r\n.fa-bowling-ball:before {\r\n\tcontent: \"\\f436\";\r\n}\r\n.fa-bowling-pins:before {\r\n\tcontent: \"\\f437\";\r\n}\r\n.fa-boxing-glove:before {\r\n\tcontent: \"\\f438\";\r\n}\r\n.fa-chess:before {\r\n\tcontent: \"\\f439\";\r\n}\r\n.fa-chess-bishop:before {\r\n\tcontent: \"\\f43a\";\r\n}\r\n.fa-chess-bishop-alt:before {\r\n\tcontent: \"\\f43b\";\r\n}\r\n.fa-chess-board:before {\r\n\tcontent: \"\\f43c\";\r\n}\r\n.fa-chess-clock:before {\r\n\tcontent: \"\\f43d\";\r\n}\r\n.fa-chess-clock-alt:before {\r\n\tcontent: \"\\f43e\";\r\n}\r\n.fa-chess-king:before {\r\n\tcontent: \"\\f43f\";\r\n}\r\n.fa-chess-king-alt:before {\r\n\tcontent: \"\\f440\";\r\n}\r\n.fa-chess-knight:before {\r\n\tcontent: \"\\f441\";\r\n}\r\n.fa-chess-knight-alt:before {\r\n\tcontent: \"\\f442\";\r\n}\r\n.fa-chess-pawn:before {\r\n\tcontent: \"\\f443\";\r\n}\r\n.fa-chess-pawn-alt:before {\r\n\tcontent: \"\\f444\";\r\n}\r\n.fa-chess-queen:before {\r\n\tcontent: \"\\f445\";\r\n}\r\n.fa-chess-queen-alt:before {\r\n\tcontent: \"\\f446\";\r\n}\r\n.fa-chess-rook:before {\r\n\tcontent: \"\\f447\";\r\n}\r\n.fa-chess-rook-alt:before {\r\n\tcontent: \"\\f448\";\r\n}\r\n.fa-cricket:before {\r\n\tcontent: \"\\f449\";\r\n}\r\n.fa-curling:before {\r\n\tcontent: \"\\f44a\";\r\n}\r\n.fa-dumbbell:before {\r\n\tcontent: \"\\f44b\";\r\n}\r\n.fa-field-hockey:before {\r\n\tcontent: \"\\f44c\";\r\n}\r\n.fa-football-ball:before {\r\n\tcontent: \"\\f44e\";\r\n}\r\n.fa-football-helmet:before {\r\n\tcontent: \"\\f44f\";\r\n}\r\n.fa-golf-ball:before {\r\n\tcontent: \"\\f450\";\r\n}\r\n.fa-golf-club:before {\r\n\tcontent: \"\\f451\";\r\n}\r\n.fa-hockey-puck:before {\r\n\tcontent: \"\\f453\";\r\n}\r\n.fa-hockey-sticks:before {\r\n\tcontent: \"\\f454\";\r\n}\r\n.fa-luchador:before {\r\n\tcontent: \"\\f455\";\r\n}\r\n.fa-racquet:before {\r\n\tcontent: \"\\f45a\";\r\n}\r\n.fa-shuttlecock:before {\r\n\tcontent: \"\\f45b\";\r\n}\r\n.fa-square-full:before {\r\n\tcontent: \"\\f45c\";\r\n}\r\n.fa-table-tennis:before {\r\n\tcontent: \"\\f45d\";\r\n}\r\n.fa-tennis-ball:before {\r\n\tcontent: \"\\f45e\";\r\n}\r\n.fa-whistle:before {\r\n\tcontent: \"\\f460\";\r\n}\r\n.fa-allergies:before {\r\n\tcontent: \"\\f461\";\r\n}\r\n.fa-band-aid:before {\r\n\tcontent: \"\\f462\";\r\n}\r\n.fa-barcode-alt:before {\r\n\tcontent: \"\\f463\";\r\n}\r\n.fa-barcode-read:before {\r\n\tcontent: \"\\f464\";\r\n}\r\n.fa-barcode-scan:before {\r\n\tcontent: \"\\f465\";\r\n}\r\n.fa-box:before {\r\n\tcontent: \"\\f466\";\r\n}\r\n.fa-box-check:before {\r\n\tcontent: \"\\f467\";\r\n}\r\n.fa-boxes:before {\r\n\tcontent: \"\\f468\";\r\n}\r\n.fa-briefcase-medical:before {\r\n\tcontent: \"\\f469\";\r\n}\r\n.fa-burn:before {\r\n\tcontent: \"\\f46a\";\r\n}\r\n.fa-capsules:before {\r\n\tcontent: \"\\f46b\";\r\n}\r\n.fa-clipboard-check:before {\r\n\tcontent: \"\\f46c\";\r\n}\r\n.fa-clipboard-list:before {\r\n\tcontent: \"\\f46d\";\r\n}\r\n.fa-conveyor-belt:before {\r\n\tcontent: \"\\f46e\";\r\n}\r\n.fa-conveyor-belt-alt:before {\r\n\tcontent: \"\\f46f\";\r\n}\r\n.fa-diagnoses:before {\r\n\tcontent: \"\\f470\";\r\n}\r\n.fa-dna:before {\r\n\tcontent: \"\\f471\";\r\n}\r\n.fa-dolly:before {\r\n\tcontent: \"\\f472\";\r\n}\r\n.fa-dolly-empty:before {\r\n\tcontent: \"\\f473\";\r\n}\r\n.fa-dolly-flatbed:before {\r\n\tcontent: \"\\f474\";\r\n}\r\n.fa-dolly-flatbed-alt:before {\r\n\tcontent: \"\\f475\";\r\n}\r\n.fa-dolly-flatbed-empty:before {\r\n\tcontent: \"\\f476\";\r\n}\r\n.fa-file-medical:before {\r\n\tcontent: \"\\f477\";\r\n}\r\n.fa-file-medical-alt:before {\r\n\tcontent: \"\\f478\";\r\n}\r\n.fa-first-aid:before {\r\n\tcontent: \"\\f479\";\r\n}\r\n.fa-forklift:before {\r\n\tcontent: \"\\f47a\";\r\n}\r\n.fa-hand-holding-box:before {\r\n\tcontent: \"\\f47b\";\r\n}\r\n.fa-hand-receiving:before {\r\n\tcontent: \"\\f47c\";\r\n}\r\n.fa-hospital-alt:before {\r\n\tcontent: \"\\f47d\";\r\n}\r\n.fa-hospital-symbol:before {\r\n\tcontent: \"\\f47e\";\r\n}\r\n.fa-id-card-alt:before {\r\n\tcontent: \"\\f47f\";\r\n}\r\n.fa-inventory:before {\r\n\tcontent: \"\\f480\";\r\n}\r\n.fa-pills:before {\r\n\tcontent: \"\\f484\";\r\n}\r\n.fa-smoking:before {\r\n\tcontent: \"\\f48d\";\r\n}\r\n.fa-syringe:before {\r\n\tcontent: \"\\f48e\";\r\n}\r\n.fa-tablets:before {\r\n\tcontent: \"\\f490\";\r\n}\r\n.fa-warehouse:before {\r\n\tcontent: \"\\f494\";\r\n}\r\n.fa-weight:before {\r\n\tcontent: \"\\f496\";\r\n}\r\n.fa-x-ray:before {\r\n\tcontent: \"\\f497\";\r\n}\r\n.fa-blanket:before {\r\n\tcontent: \"\\f498\";\r\n}\r\n.fa-book-heart:before {\r\n\tcontent: \"\\f499\";\r\n}\r\n.fa-box-alt:before {\r\n\tcontent: \"\\f49a\";\r\n}\r\n.fa-box-fragile:before {\r\n\tcontent: \"\\f49b\";\r\n}\r\n.fa-box-full:before {\r\n\tcontent: \"\\f49c\";\r\n}\r\n.fa-box-heart:before {\r\n\tcontent: \"\\f49d\";\r\n}\r\n.fa-box-open:before {\r\n\tcontent: \"\\f49e\";\r\n}\r\n.fa-box-up:before {\r\n\tcontent: \"\\f49f\";\r\n}\r\n.fa-box-usd:before {\r\n\tcontent: \"\\f4a0\";\r\n}\r\n.fa-boxes-alt:before {\r\n\tcontent: \"\\f4a1\";\r\n}\r\n.fa-comment-alt-check:before {\r\n\tcontent: \"\\f4a2\";\r\n}\r\n.fa-comment-alt-dots:before {\r\n\tcontent: \"\\f4a3\";\r\n}\r\n.fa-comment-alt-edit:before {\r\n\tcontent: \"\\f4a4\";\r\n}\r\n.fa-comment-alt-exclamation:before {\r\n\tcontent: \"\\f4a5\";\r\n}\r\n.fa-comment-alt-lines:before {\r\n\tcontent: \"\\f4a6\";\r\n}\r\n.fa-comment-alt-minus:before {\r\n\tcontent: \"\\f4a7\";\r\n}\r\n.fa-comment-alt-plus:before {\r\n\tcontent: \"\\f4a8\";\r\n}\r\n.fa-comment-alt-smile:before {\r\n\tcontent: \"\\f4aa\";\r\n}\r\n.fa-comment-alt-times:before {\r\n\tcontent: \"\\f4ab\";\r\n}\r\n.fa-comment-check:before {\r\n\tcontent: \"\\f4ac\";\r\n}\r\n.fa-comment-dots:before {\r\n\tcontent: \"\\f4ad\";\r\n}\r\n.fa-comment-edit:before {\r\n\tcontent: \"\\f4ae\";\r\n}\r\n.fa-comment-exclamation:before {\r\n\tcontent: \"\\f4af\";\r\n}\r\n.fa-comment-lines:before {\r\n\tcontent: \"\\f4b0\";\r\n}\r\n.fa-comment-minus:before {\r\n\tcontent: \"\\f4b1\";\r\n}\r\n.fa-comment-plus:before {\r\n\tcontent: \"\\f4b2\";\r\n}\r\n.fa-comment-slash:before {\r\n\tcontent: \"\\f4b3\";\r\n}\r\n.fa-comment-smile:before {\r\n\tcontent: \"\\f4b4\";\r\n}\r\n.fa-comment-times:before {\r\n\tcontent: \"\\f4b5\";\r\n}\r\n.fa-comments-alt:before {\r\n\tcontent: \"\\f4b6\";\r\n}\r\n.fa-container-storage:before {\r\n\tcontent: \"\\f4b7\";\r\n}\r\n.fa-couch:before {\r\n\tcontent: \"\\f4b8\";\r\n}\r\n.fa-donate:before {\r\n\tcontent: \"\\f4b9\";\r\n}\r\n.fa-dove:before {\r\n\tcontent: \"\\f4ba\";\r\n}\r\n.fa-fragile:before {\r\n\tcontent: \"\\f4bb\";\r\n}\r\n.fa-hand-heart:before {\r\n\tcontent: \"\\f4bc\";\r\n}\r\n.fa-hand-holding:before {\r\n\tcontent: \"\\f4bd\";\r\n}\r\n.fa-hand-holding-heart:before {\r\n\tcontent: \"\\f4be\";\r\n}\r\n.fa-hand-holding-seedling:before {\r\n\tcontent: \"\\f4bf\";\r\n}\r\n.fa-hand-holding-usd:before {\r\n\tcontent: \"\\f4c0\";\r\n}\r\n.fa-hand-holding-water:before {\r\n\tcontent: \"\\f4c1\";\r\n}\r\n.fa-hands:before {\r\n\tcontent: \"\\f4c2\";\r\n}\r\n.fa-hands-heart:before {\r\n\tcontent: \"\\f4c3\";\r\n}\r\n.fa-hands-helping:before {\r\n\tcontent: \"\\f4c4\";\r\n}\r\n.fa-hands-usd:before {\r\n\tcontent: \"\\f4c5\";\r\n}\r\n.fa-handshake-alt:before {\r\n\tcontent: \"\\f4c6\";\r\n}\r\n.fa-heart-circle:before {\r\n\tcontent: \"\\f4c7\";\r\n}\r\n.fa-heart-square:before {\r\n\tcontent: \"\\f4c8\";\r\n}\r\n.fa-home-heart:before {\r\n\tcontent: \"\\f4c9\";\r\n}\r\n.fa-lamp:before {\r\n\tcontent: \"\\f4ca\";\r\n}\r\n.fa-leaf-heart:before {\r\n\tcontent: \"\\f4cb\";\r\n}\r\n.fa-parachute-box:before {\r\n\tcontent: \"\\f4cd\";\r\n}\r\n.fa-piggy-bank:before {\r\n\tcontent: \"\\f4d3\";\r\n}\r\n.fa-ribbon:before {\r\n\tcontent: \"\\f4d6\";\r\n}\r\n.fa-route:before {\r\n\tcontent: \"\\f4d7\";\r\n}\r\n.fa-seedling:before {\r\n\tcontent: \"\\f4d8\";\r\n}\r\n.fa-creative-commons-by:before {\r\n\tcontent: \"\\f4e7\";\r\n}\r\n.fa-creative-commons-nc:before {\r\n\tcontent: \"\\f4e8\";\r\n}\r\n.fa-creative-commons-nc-jp:before {\r\n\tcontent: \"\\f4ea\";\r\n}\r\n.fa-creative-commons-nd:before {\r\n\tcontent: \"\\f4eb\";\r\n}\r\n.fa-creative-commons-pd:before {\r\n\tcontent: \"\\f4ec\";\r\n}\r\n.fa-creative-commons-pd-alt:before {\r\n\tcontent: \"\\f4ed\";\r\n}\r\n.fa-creative-commons-remix:before {\r\n\tcontent: \"\\f4ee\";\r\n}\r\n.fa-creative-commons-sampling:before {\r\n\tcontent: \"\\f4f0\";\r\n}\r\n.fa-creative-commons-sampling-plus:before {\r\n\tcontent: \"\\f4f1\";\r\n}\r\n.fa-creative-commons-share:before {\r\n\tcontent: \"\\f4f2\";\r\n}\r\n.fa-user-cog:before {\r\n\tcontent: \"\\f4fe\";\r\n}\r\n.fa-user-friends:before {\r\n\tcontent: \"\\f500\";\r\n}\r\n.fa-user-slash:before {\r\n\tcontent: \"\\f506\";\r\n}\r\n.fa-user-tie:before {\r\n\tcontent: \"\\f508\";\r\n}\r\n.fa-balance-scale-left:before {\r\n\tcontent: \"\\f515\";\r\n}\r\n.fa-balance-scale-right:before {\r\n\tcontent: \"\\f516\";\r\n}\r\n.fa-blender:before {\r\n\tcontent: \"\\f517\";\r\n}\r\n.fa-book-open:before {\r\n\tcontent: \"\\f518\";\r\n}\r\n.fa-broadcast-tower:before {\r\n\tcontent: \"\\f519\";\r\n}\r\n.fa-broom:before {\r\n\tcontent: \"\\f51a\";\r\n}\r\n.fa-chalkboard:before {\r\n\tcontent: \"\\f51b\";\r\n}\r\n.fa-chalkboard-teacher:before {\r\n\tcontent: \"\\f51c\";\r\n}\r\n.fa-church:before {\r\n\tcontent: \"\\f51d\";\r\n}\r\n.fa-coins:before {\r\n\tcontent: \"\\f51e\";\r\n}\r\n.fa-compact-disc:before {\r\n\tcontent: \"\\f51f\";\r\n}\r\n.fa-crow:before {\r\n\tcontent: \"\\f520\";\r\n}\r\n.fa-crown:before {\r\n\tcontent: \"\\f521\";\r\n}\r\n.fa-dice:before {\r\n\tcontent: \"\\f522\";\r\n}\r\n.fa-dice-five:before {\r\n\tcontent: \"\\f523\";\r\n}\r\n.fa-dice-four:before {\r\n\tcontent: \"\\f524\";\r\n}\r\n.fa-dice-one:before {\r\n\tcontent: \"\\f525\";\r\n}\r\n.fa-dice-six:before {\r\n\tcontent: \"\\f526\";\r\n}\r\n.fa-dice-three:before {\r\n\tcontent: \"\\f527\";\r\n}\r\n.fa-dice-two:before {\r\n\tcontent: \"\\f528\";\r\n}\r\n.fa-divide:before {\r\n\tcontent: \"\\f529\";\r\n}\r\n.fa-door-closed:before {\r\n\tcontent: \"\\f52a\";\r\n}\r\n.fa-door-open:before {\r\n\tcontent: \"\\f52b\";\r\n}\r\n.fa-equals:before {\r\n\tcontent: \"\\f52c\";\r\n}\r\n.fa-feather:before {\r\n\tcontent: \"\\f52d\";\r\n}\r\n.fa-frog:before {\r\n\tcontent: \"\\f52e\";\r\n}\r\n.fa-gas-pump:before {\r\n\tcontent: \"\\f52f\";\r\n}\r\n.fa-glasses:before {\r\n\tcontent: \"\\f530\";\r\n}\r\n.fa-greater-than:before {\r\n\tcontent: \"\\f531\";\r\n}\r\n.fa-greater-than-equal:before {\r\n\tcontent: \"\\f532\";\r\n}\r\n.fa-helicopter:before {\r\n\tcontent: \"\\f533\";\r\n}\r\n.fa-infinity:before {\r\n\tcontent: \"\\f534\";\r\n}\r\n.fa-kiwi-bird:before {\r\n\tcontent: \"\\f535\";\r\n}\r\n.fa-less-than:before {\r\n\tcontent: \"\\f536\";\r\n}\r\n.fa-less-than-equal:before {\r\n\tcontent: \"\\f537\";\r\n}\r\n.fa-memory:before {\r\n\tcontent: \"\\f538\";\r\n}\r\n.fa-microphone-alt-slash:before {\r\n\tcontent: \"\\f539\";\r\n}\r\n.fa-money-bill-wave:before {\r\n\tcontent: \"\\f53a\";\r\n}\r\n.fa-money-bill-wave-alt:before {\r\n\tcontent: \"\\f53b\";\r\n}\r\n.fa-money-check:before {\r\n\tcontent: \"\\f53c\";\r\n}\r\n.fa-money-check-alt:before {\r\n\tcontent: \"\\f53d\";\r\n}\r\n.fa-not-equal:before {\r\n\tcontent: \"\\f53e\";\r\n}\r\n.fa-palette:before {\r\n\tcontent: \"\\f53f\";\r\n}\r\n.fa-percentage:before {\r\n\tcontent: \"\\f541\";\r\n}\r\n.fa-project-diagram:before {\r\n\tcontent: \"\\f542\";\r\n}\r\n.fa-receipts:before {\r\n\tcontent: \"\\f543\";\r\n}\r\n.fa-robot:before {\r\n\tcontent: \"\\f544\";\r\n}\r\n.fa-ruler:before {\r\n\tcontent: \"\\f545\";\r\n}\r\n.fa-school:before {\r\n\tcontent: \"\\f549\";\r\n}\r\n.fa-screwdriver:before {\r\n\tcontent: \"\\f54a\";\r\n}\r\n.fa-shoe-prints:before {\r\n\tcontent: \"\\f54b\";\r\n}\r\n.fa-skull:before {\r\n\tcontent: \"\\f54c\";\r\n}\r\n.fa-store:before {\r\n\tcontent: \"\\f54e\";\r\n}\r\n.fa-toolbox:before {\r\n\tcontent: \"\\f552\";\r\n}\r\n.fa-tshirt:before {\r\n\tcontent: \"\\f553\";\r\n}\r\n.fa-wallet:before {\r\n\tcontent: \"\\f555\";\r\n}\r\n.fa-angry:before {\r\n\tcontent: \"\\f556\";\r\n}\r\n.fa-archway:before {\r\n\tcontent: \"\\f557\";\r\n}\r\n.fa-atlas:before {\r\n\tcontent: \"\\f558\";\r\n}\r\n.fa-award:before {\r\n\tcontent: \"\\f559\";\r\n}\r\n.fa-backspace:before {\r\n\tcontent: \"\\f55a\";\r\n}\r\n.fa-bezier-curve:before {\r\n\tcontent: \"\\f55b\";\r\n}\r\n.fa-bong:before {\r\n\tcontent: \"\\f55c\";\r\n}\r\n.fa-brush:before {\r\n\tcontent: \"\\f55d\";\r\n}\r\n.fa-cannabis:before {\r\n\tcontent: \"\\f55e\";\r\n}\r\n.fa-check-double:before {\r\n\tcontent: \"\\f560\";\r\n}\r\n.fa-cocktail:before {\r\n\tcontent: \"\\f561\";\r\n}\r\n.fa-concierge-bell:before {\r\n\tcontent: \"\\f562\";\r\n}\r\n.fa-cookie:before {\r\n\tcontent: \"\\f563\";\r\n}\r\n.fa-cookie-bite:before {\r\n\tcontent: \"\\f564\";\r\n}\r\n.fa-crop-alt:before {\r\n\tcontent: \"\\f565\";\r\n}\r\n.fa-digital-tachograph:before {\r\n\tcontent: \"\\f566\";\r\n}\r\n.fa-dizzy:before {\r\n\tcontent: \"\\f567\";\r\n}\r\n.fa-drafting-compass:before {\r\n\tcontent: \"\\f568\";\r\n}\r\n.fa-drum:before {\r\n\tcontent: \"\\f569\";\r\n}\r\n.fa-drum-steelpan:before {\r\n\tcontent: \"\\f56a\";\r\n}\r\n.fa-feather-alt:before {\r\n\tcontent: \"\\f56b\";\r\n}\r\n.fa-file-contract:before {\r\n\tcontent: \"\\f56c\";\r\n}\r\n.fa-file-download:before {\r\n\tcontent: \"\\f56d\";\r\n}\r\n.fa-file-export:before {\r\n\tcontent: \"\\f56e\";\r\n}\r\n.fa-file-import:before {\r\n\tcontent: \"\\f56f\";\r\n}\r\n.fa-file-invoice:before {\r\n\tcontent: \"\\f570\";\r\n}\r\n.fa-file-invoice-dollar:before {\r\n\tcontent: \"\\f571\";\r\n}\r\n.fa-file-prescription:before {\r\n\tcontent: \"\\f572\";\r\n}\r\n.fa-file-certificate:before {\r\n\tcontent: \"\\f573\";\r\n}\r\n.fa-file-upload:before {\r\n\tcontent: \"\\f574\";\r\n}\r\n.fa-fill:before {\r\n\tcontent: \"\\f575\";\r\n}\r\n.fa-fill-drip:before {\r\n\tcontent: \"\\f576\";\r\n}\r\n.fa-fingerprint:before {\r\n\tcontent: \"\\f577\";\r\n}\r\n.fa-fish:before {\r\n\tcontent: \"\\f578\";\r\n}\r\n.fa-flushed:before {\r\n\tcontent: \"\\f579\";\r\n}\r\n.fa-frown-open:before {\r\n\tcontent: \"\\f57a\";\r\n}\r\n.fa-glass-martini-alt:before {\r\n\tcontent: \"\\f57b\";\r\n}\r\n.fa-globe-africa:before {\r\n\tcontent: \"\\f57c\";\r\n}\r\n.fa-globe-americas:before {\r\n\tcontent: \"\\f57d\";\r\n}\r\n.fa-globe-asia:before {\r\n\tcontent: \"\\f57e\";\r\n}\r\n.fa-grimace:before {\r\n\tcontent: \"\\f57f\";\r\n}\r\n.fa-grin:before {\r\n\tcontent: \"\\f580\";\r\n}\r\n.fa-grin-alt:before {\r\n\tcontent: \"\\f581\";\r\n}\r\n.fa-grin-beam:before {\r\n\tcontent: \"\\f582\";\r\n}\r\n.fa-grin-beam-sweat:before {\r\n\tcontent: \"\\f583\";\r\n}\r\n.fa-grin-hearts:before {\r\n\tcontent: \"\\f584\";\r\n}\r\n.fa-grin-squint:before {\r\n\tcontent: \"\\f585\";\r\n}\r\n.fa-grin-squint-tears:before {\r\n\tcontent: \"\\f586\";\r\n}\r\n.fa-grin-stars:before {\r\n\tcontent: \"\\f587\";\r\n}\r\n.fa-grin-tears:before {\r\n\tcontent: \"\\f588\";\r\n}\r\n.fa-grin-tongue:before {\r\n\tcontent: \"\\f589\";\r\n}\r\n.fa-grin-tongue-squint:before {\r\n\tcontent: \"\\f58a\";\r\n}\r\n.fa-grin-tongue-wink:before {\r\n\tcontent: \"\\f58b\";\r\n}\r\n.fa-grin-wink:before {\r\n\tcontent: \"\\f58c\";\r\n}\r\n.fa-grip-horizontal:before {\r\n\tcontent: \"\\f58d\";\r\n}\r\n.fa-grip-vertical:before {\r\n\tcontent: \"\\f58e\";\r\n}\r\n.fa-headphones-alt:before {\r\n\tcontent: \"\\f58f\";\r\n}\r\n.fa-highlighter:before {\r\n\tcontent: \"\\f591\";\r\n}\r\n.fa-hot-tub:before {\r\n\tcontent: \"\\f593\";\r\n}\r\n.fa-hotel:before {\r\n\tcontent: \"\\f594\";\r\n}\r\n.fa-joint:before {\r\n\tcontent: \"\\f595\";\r\n}\r\n.fa-kiss:before {\r\n\tcontent: \"\\f596\";\r\n}\r\n.fa-kiss-beam:before {\r\n\tcontent: \"\\f597\";\r\n}\r\n.fa-kiss-wink-heart:before {\r\n\tcontent: \"\\f598\";\r\n}\r\n.fa-laugh:before {\r\n\tcontent: \"\\f599\";\r\n}\r\n.fa-laugh-beam:before {\r\n\tcontent: \"\\f59a\";\r\n}\r\n.fa-laugh-squint:before {\r\n\tcontent: \"\\f59b\";\r\n}\r\n.fa-laugh-wink:before {\r\n\tcontent: \"\\f59c\";\r\n}\r\n.fa-luggage-cart:before {\r\n\tcontent: \"\\f59d\";\r\n}\r\n.fa-map-marked:before {\r\n\tcontent: \"\\f59f\";\r\n}\r\n.fa-map-marked-alt:before {\r\n\tcontent: \"\\f5a0\";\r\n}\r\n.fa-marker:before {\r\n\tcontent: \"\\f5a1\";\r\n}\r\n.fa-medal:before {\r\n\tcontent: \"\\f5a2\";\r\n}\r\n.fa-meh-blank:before {\r\n\tcontent: \"\\f5a4\";\r\n}\r\n.fa-meh-rolling-eyes:before {\r\n\tcontent: \"\\f5a5\";\r\n}\r\n.fa-monument:before {\r\n\tcontent: \"\\f5a6\";\r\n}\r\n.fa-mortar-pestle:before {\r\n\tcontent: \"\\f5a7\";\r\n}\r\n.fa-paint-roller:before {\r\n\tcontent: \"\\f5aa\";\r\n}\r\n.fa-passport:before {\r\n\tcontent: \"\\f5ab\";\r\n}\r\n.fa-prescription:before {\r\n\tcontent: \"\\f5b1\";\r\n}\r\n.fa-shuttle-van:before {\r\n\tcontent: \"\\f5b6\";\r\n}\r\n.fa-signature:before {\r\n\tcontent: \"\\f5b7\";\r\n}\r\n.fa-solar-panel:before {\r\n\tcontent: \"\\f5ba\";\r\n}\r\n.fa-spray-can:before {\r\n\tcontent: \"\\f5bd\";\r\n}\r\n.fa-stamp:before {\r\n\tcontent: \"\\f5bf\";\r\n}\r\n.fa-swimmer:before {\r\n\tcontent: \"\\f5c4\";\r\n}\r\n.fa-tooth:before {\r\n\tcontent: \"\\f5c9\";\r\n}\r\n.fa-weight-hanging:before {\r\n\tcontent: \"\\f5cd\";\r\n}\r\n.fa-air-freshener:before {\r\n\tcontent: \"\\f5d0\";\r\n}\r\n.fa-apple-alt:before {\r\n\tcontent: \"\\f5d1\";\r\n}\r\n.fa-atom:before {\r\n\tcontent: \"\\f5d2\";\r\n}\r\n.fa-atom-alt:before {\r\n\tcontent: \"\\f5d3\";\r\n}\r\n.fa-backpack:before {\r\n\tcontent: \"\\f5d4\";\r\n}\r\n.fa-bell-school:before {\r\n\tcontent: \"\\f5d5\";\r\n}\r\n.fa-bell-school-slash:before {\r\n\tcontent: \"\\f5d6\";\r\n}\r\n.fa-bone:before {\r\n\tcontent: \"\\f5d7\";\r\n}\r\n.fa-bone-break:before {\r\n\tcontent: \"\\f5d8\";\r\n}\r\n.fa-book-alt:before {\r\n\tcontent: \"\\f5d9\";\r\n}\r\n.fa-book-reader:before {\r\n\tcontent: \"\\f5da\";\r\n}\r\n.fa-books:before {\r\n\tcontent: \"\\f5db\";\r\n}\r\n.fa-brain:before {\r\n\tcontent: \"\\f5dc\";\r\n}\r\n.fa-bus-school:before {\r\n\tcontent: \"\\f5dd\";\r\n}\r\n.fa-car-alt:before {\r\n\tcontent: \"\\f5de\";\r\n}\r\n.fa-car-battery:before {\r\n\tcontent: \"\\f5df\";\r\n}\r\n.fa-car-bump:before {\r\n\tcontent: \"\\f5e0\";\r\n}\r\n.fa-car-crash:before {\r\n\tcontent: \"\\f5e1\";\r\n}\r\n.fa-car-garage:before {\r\n\tcontent: \"\\f5e2\";\r\n}\r\n.fa-car-mechanic:before {\r\n\tcontent: \"\\f5e3\";\r\n}\r\n.fa-car-side:before {\r\n\tcontent: \"\\f5e4\";\r\n}\r\n.fa-car-tilt:before {\r\n\tcontent: \"\\f5e5\";\r\n}\r\n.fa-car-wash:before {\r\n\tcontent: \"\\f5e6\";\r\n}\r\n.fa-charging-station:before {\r\n\tcontent: \"\\f5e7\";\r\n}\r\n.fa-clipboard-prescription:before {\r\n\tcontent: \"\\f5e8\";\r\n}\r\n.fa-compass-slash:before {\r\n\tcontent: \"\\f5e9\";\r\n}\r\n.fa-diploma:before {\r\n\tcontent: \"\\f5ea\";\r\n}\r\n.fa-directions:before {\r\n\tcontent: \"\\f5eb\";\r\n}\r\n.fa-do-not-enter:before {\r\n\tcontent: \"\\f5ec\";\r\n}\r\n.fa-draw-circle:before {\r\n\tcontent: \"\\f5ed\";\r\n}\r\n.fa-draw-polygon:before {\r\n\tcontent: \"\\f5ee\";\r\n}\r\n.fa-draw-square:before {\r\n\tcontent: \"\\f5ef\";\r\n}\r\n.fa-ear:before {\r\n\tcontent: \"\\f5f0\";\r\n}\r\n.fa-engine-warning:before {\r\n\tcontent: \"\\f5f2\";\r\n}\r\n.fa-gas-pump-slash:before {\r\n\tcontent: \"\\f5f4\";\r\n}\r\n.fa-glasses-alt:before {\r\n\tcontent: \"\\f5f5\";\r\n}\r\n.fa-globe-stand:before {\r\n\tcontent: \"\\f5f6\";\r\n}\r\n.fa-heart-rate:before {\r\n\tcontent: \"\\f5f8\";\r\n}\r\n.fa-inhaler:before {\r\n\tcontent: \"\\f5f9\";\r\n}\r\n.fa-kidneys:before {\r\n\tcontent: \"\\f5fb\";\r\n}\r\n.fa-laptop-code:before {\r\n\tcontent: \"\\f5fc\";\r\n}\r\n.fa-layer-group:before {\r\n\tcontent: \"\\f5fd\";\r\n}\r\n.fa-layer-minus:before {\r\n\tcontent: \"\\f5fe\";\r\n}\r\n.fa-layer-plus:before {\r\n\tcontent: \"\\f5ff\";\r\n}\r\n.fa-lips:before {\r\n\tcontent: \"\\f600\";\r\n}\r\n.fa-location:before {\r\n\tcontent: \"\\f601\";\r\n}\r\n.fa-location-slash:before {\r\n\tcontent: \"\\f603\";\r\n}\r\n.fa-lungs:before {\r\n\tcontent: \"\\f604\";\r\n}\r\n.fa-map-marker-alt-slash:before {\r\n\tcontent: \"\\f605\";\r\n}\r\n.fa-map-marker-check:before {\r\n\tcontent: \"\\f606\";\r\n}\r\n.fa-map-marker-edit:before {\r\n\tcontent: \"\\f607\";\r\n}\r\n.fa-map-marker-exclamation:before {\r\n\tcontent: \"\\f608\";\r\n}\r\n.fa-map-marker-minus:before {\r\n\tcontent: \"\\f609\";\r\n}\r\n.fa-map-marker-question:before {\r\n\tcontent: \"\\f60b\";\r\n}\r\n.fa-map-marker-slash:before {\r\n\tcontent: \"\\f60c\";\r\n}\r\n.fa-map-marker-smile:before {\r\n\tcontent: \"\\f60d\";\r\n}\r\n.fa-map-marker-times:before {\r\n\tcontent: \"\\f60e\";\r\n}\r\n.fa-microscope:before {\r\n\tcontent: \"\\f610\";\r\n}\r\n.fa-monitor-heart-rate:before {\r\n\tcontent: \"\\f611\";\r\n}\r\n.fa-oil-can:before {\r\n\tcontent: \"\\f613\";\r\n}\r\n.fa-parking-circle:before {\r\n\tcontent: \"\\f615\";\r\n}\r\n.fa-route-highway:before {\r\n\tcontent: \"\\f61a\";\r\n}\r\n.fa-shapes:before {\r\n\tcontent: \"\\f61f\";\r\n}\r\n.fa-steering-wheel:before {\r\n\tcontent: \"\\f622\";\r\n}\r\n.fa-stomach:before {\r\n\tcontent: \"\\f623\";\r\n}\r\n.fa-teeth-open:before {\r\n\tcontent: \"\\f62f\";\r\n}\r\n.fa-tire:before {\r\n\tcontent: \"\\f631\";\r\n}\r\n.fa-traffic-cone:before {\r\n\tcontent: \"\\f636\";\r\n}\r\n.fa-traffic-light:before {\r\n\tcontent: \"\\f637\";\r\n}\r\n.fa-users-class:before {\r\n\tcontent: \"\\f63d\";\r\n}\r\n.fa-abacus:before {\r\n\tcontent: \"\\f640\";\r\n}\r\n.fa-ad:before {\r\n\tcontent: \"\\f641\";\r\n}\r\n.fa-alipay:before {\r\n\tcontent: \"\\f642\";\r\n}\r\n.fa-analytics:before {\r\n\tcontent: \"\\f643\";\r\n}\r\n.fa-ankh:before {\r\n\tcontent: \"\\f644\";\r\n}\r\n.fa-badge-dollar:before {\r\n\tcontent: \"\\f645\";\r\n}\r\n.fa-badge-percent:before {\r\n\tcontent: \"\\f646\";\r\n}\r\n.fa-bible:before {\r\n\tcontent: \"\\f647\";\r\n}\r\n.fa-bullseye-arrow:before {\r\n\tcontent: \"\\f648\";\r\n}\r\n.fa-bullseye-pointer:before {\r\n\tcontent: \"\\f649\";\r\n}\r\n.fa-business-time:before {\r\n\tcontent: \"\\f64a\";\r\n}\r\n.fa-cabinet-filing:before {\r\n\tcontent: \"\\f64b\";\r\n}\r\n.fa-calculator-alt:before {\r\n\tcontent: \"\\f64c\";\r\n}\r\n.fa-chart-line-down:before {\r\n\tcontent: \"\\f64d\";\r\n}\r\n.fa-chart-pie-alt:before {\r\n\tcontent: \"\\f64e\";\r\n}\r\n.fa-city:before {\r\n\tcontent: \"\\f64f\";\r\n}\r\n.fa-comment-dollar:before {\r\n\tcontent: \"\\f651\";\r\n}\r\n.fa-comments-alt-dollar:before {\r\n\tcontent: \"\\f652\";\r\n}\r\n.fa-comments-dollar:before {\r\n\tcontent: \"\\f653\";\r\n}\r\n.fa-cross:before {\r\n\tcontent: \"\\f654\";\r\n}\r\n.fa-dharmachakra:before {\r\n\tcontent: \"\\f655\";\r\n}\r\n.fa-empty-set:before {\r\n\tcontent: \"\\f656\";\r\n}\r\n.fa-envelope-open-dollar:before {\r\n\tcontent: \"\\f657\";\r\n}\r\n.fa-envelope-open-text:before {\r\n\tcontent: \"\\f658\";\r\n}\r\n.fa-file-chart-line:before {\r\n\tcontent: \"\\f659\";\r\n}\r\n.fa-file-chart-pie:before {\r\n\tcontent: \"\\f65a\";\r\n}\r\n.fa-file-spreadsheet:before {\r\n\tcontent: \"\\f65b\";\r\n}\r\n.fa-file-user:before {\r\n\tcontent: \"\\f65c\";\r\n}\r\n.fa-folder-minus:before {\r\n\tcontent: \"\\f65d\";\r\n}\r\n.fa-folder-plus:before {\r\n\tcontent: \"\\f65e\";\r\n}\r\n.fa-folder-times:before {\r\n\tcontent: \"\\f65f\";\r\n}\r\n.fa-folders:before {\r\n\tcontent: \"\\f660\";\r\n}\r\n.fa-function:before {\r\n\tcontent: \"\\f661\";\r\n}\r\n.fa-funnel-dollar:before {\r\n\tcontent: \"\\f662\";\r\n}\r\n.fa-gift-card:before {\r\n\tcontent: \"\\f663\";\r\n}\r\n.fa-gopuram:before {\r\n\tcontent: \"\\f664\";\r\n}\r\n.fa-hamsa:before {\r\n\tcontent: \"\\f665\";\r\n}\r\n.fa-bahai:before {\r\n\tcontent: \"\\f666\";\r\n}\r\n.fa-integral:before {\r\n\tcontent: \"\\f667\";\r\n}\r\n.fa-intersection:before {\r\n\tcontent: \"\\f668\";\r\n}\r\n.fa-jedi:before {\r\n\tcontent: \"\\f669\";\r\n}\r\n.fa-journal-whills:before {\r\n\tcontent: \"\\f66a\";\r\n}\r\n.fa-kaaba:before {\r\n\tcontent: \"\\f66b\";\r\n}\r\n.fa-keynote:before {\r\n\tcontent: \"\\f66c\";\r\n}\r\n.fa-khanda:before {\r\n\tcontent: \"\\f66d\";\r\n}\r\n.fa-lambda:before {\r\n\tcontent: \"\\f66e\";\r\n}\r\n.fa-landmark:before {\r\n\tcontent: \"\\f66f\";\r\n}\r\n.fa-lightbulb-dollar:before {\r\n\tcontent: \"\\f670\";\r\n}\r\n.fa-lightbulb-exclamation:before {\r\n\tcontent: \"\\f671\";\r\n}\r\n.fa-lightbulb-on:before {\r\n\tcontent: \"\\f672\";\r\n}\r\n.fa-lightbulb-slash:before {\r\n\tcontent: \"\\f673\";\r\n}\r\n.fa-megaphone:before {\r\n\tcontent: \"\\f675\";\r\n}\r\n.fa-menorah:before {\r\n\tcontent: \"\\f676\";\r\n}\r\n.fa-mind-share:before {\r\n\tcontent: \"\\f677\";\r\n}\r\n.fa-mosque:before {\r\n\tcontent: \"\\f678\";\r\n}\r\n.fa-om:before {\r\n\tcontent: \"\\f679\";\r\n}\r\n.fa-omega:before {\r\n\tcontent: \"\\f67a\";\r\n}\r\n.fa-pastafarianism:before {\r\n\tcontent: \"\\f67b\";\r\n}\r\n.fa-peace:before {\r\n\tcontent: \"\\f67c\";\r\n}\r\n.fa-pi:before {\r\n\tcontent: \"\\f67e\";\r\n}\r\n.fa-praying-hands:before {\r\n\tcontent: \"\\f684\";\r\n}\r\n.fa-presentation:before {\r\n\tcontent: \"\\f685\";\r\n}\r\n.fa-quran:before {\r\n\tcontent: \"\\f687\";\r\n}\r\n.fa-sigma:before {\r\n\tcontent: \"\\f68b\";\r\n}\r\n.fa-signal-alt-2:before {\r\n\tcontent: \"\\f692\";\r\n}\r\n.fa-socks:before {\r\n\tcontent: \"\\f696\";\r\n}\r\n.fa-square-root:before {\r\n\tcontent: \"\\f697\";\r\n}\r\n.fa-user-chart:before {\r\n\tcontent: \"\\f6a3\";\r\n}\r\n.fa-volume:before {\r\n\tcontent: \"\\f6a8\";\r\n}\r\n.fa-wifi-slash:before {\r\n\tcontent: \"\\f6ac\";\r\n}\r\n.fa-yin-yang:before {\r\n\tcontent: \"\\f6ad\";\r\n}\r\n.fa-acorn:before {\r\n\tcontent: \"\\f6ae\";\r\n}\r\n.fa-acquisitions-incorporated:before {\r\n\tcontent: \"\\f6af\";\r\n}\r\n.fa-alicorn:before {\r\n\tcontent: \"\\f6b0\";\r\n}\r\n.fa-apple-crate:before {\r\n\tcontent: \"\\f6b1\";\r\n}\r\n.fa-axe:before {\r\n\tcontent: \"\\f6b2\";\r\n}\r\n.fa-axe-battle:before {\r\n\tcontent: \"\\f6b3\";\r\n}\r\n.fa-badger-honey:before {\r\n\tcontent: \"\\f6b4\";\r\n}\r\n.fa-bat:before {\r\n\tcontent: \"\\f6b5\";\r\n}\r\n.fa-blender-phone:before {\r\n\tcontent: \"\\f6b6\";\r\n}\r\n.fa-book-dead:before {\r\n\tcontent: \"\\f6b7\";\r\n}\r\n.fa-book-spells:before {\r\n\tcontent: \"\\f6b8\";\r\n}\r\n.fa-bow-arrow:before {\r\n\tcontent: \"\\f6b9\";\r\n}\r\n.fa-campfire:before {\r\n\tcontent: \"\\f6ba\";\r\n}\r\n.fa-campground:before {\r\n\tcontent: \"\\f6bb\";\r\n}\r\n.fa-candle-holder:before {\r\n\tcontent: \"\\f6bc\";\r\n}\r\n.fa-candy-corn:before {\r\n\tcontent: \"\\f6bd\";\r\n}\r\n.fa-cat:before {\r\n\tcontent: \"\\f6be\";\r\n}\r\n.fa-cauldron:before {\r\n\tcontent: \"\\f6bf\";\r\n}\r\n.fa-chair:before {\r\n\tcontent: \"\\f6c0\";\r\n}\r\n.fa-chair-office:before {\r\n\tcontent: \"\\f6c1\";\r\n}\r\n.fa-claw-marks:before {\r\n\tcontent: \"\\f6c2\";\r\n}\r\n.fa-cloud-moon:before {\r\n\tcontent: \"\\f6c3\";\r\n}\r\n.fa-cloud-sun:before {\r\n\tcontent: \"\\f6c4\";\r\n}\r\n.fa-coffee-togo:before {\r\n\tcontent: \"\\f6c5\";\r\n}\r\n.fa-coffin:before {\r\n\tcontent: \"\\f6c6\";\r\n}\r\n.fa-corn:before {\r\n\tcontent: \"\\f6c7\";\r\n}\r\n.fa-cow:before {\r\n\tcontent: \"\\f6c8\";\r\n}\r\n.fa-critical-role:before {\r\n\tcontent: \"\\f6c9\";\r\n}\r\n.fa-d-and-d-beyond:before {\r\n\tcontent: \"\\f6ca\";\r\n}\r\n.fa-dagger:before {\r\n\tcontent: \"\\f6cb\";\r\n}\r\n.fa-dice-d10:before {\r\n\tcontent: \"\\f6cd\";\r\n}\r\n.fa-dice-d12:before {\r\n\tcontent: \"\\f6ce\";\r\n}\r\n.fa-dice-d20:before {\r\n\tcontent: \"\\f6cf\";\r\n}\r\n.fa-dice-d4:before {\r\n\tcontent: \"\\f6d0\";\r\n}\r\n.fa-dice-d6:before {\r\n\tcontent: \"\\f6d1\";\r\n}\r\n.fa-dice-d8:before {\r\n\tcontent: \"\\f6d2\";\r\n}\r\n.fa-dog:before {\r\n\tcontent: \"\\f6d3\";\r\n}\r\n.fa-dog-leashed:before {\r\n\tcontent: \"\\f6d4\";\r\n}\r\n.fa-dragon:before {\r\n\tcontent: \"\\f6d5\";\r\n}\r\n.fa-drumstick:before {\r\n\tcontent: \"\\f6d6\";\r\n}\r\n.fa-drumstick-bite:before {\r\n\tcontent: \"\\f6d7\";\r\n}\r\n.fa-duck:before {\r\n\tcontent: \"\\f6d8\";\r\n}\r\n.fa-dungeon:before {\r\n\tcontent: \"\\f6d9\";\r\n}\r\n.fa-elephant:before {\r\n\tcontent: \"\\f6da\";\r\n}\r\n.fa-eye-evil:before {\r\n\tcontent: \"\\f6db\";\r\n}\r\n.fa-file-csv:before {\r\n\tcontent: \"\\f6dd\";\r\n}\r\n.fa-fist-raised:before {\r\n\tcontent: \"\\f6de\";\r\n}\r\n.fa-flame:before {\r\n\tcontent: \"\\f6df\";\r\n}\r\n.fa-flask-poison:before {\r\n\tcontent: \"\\f6e0\";\r\n}\r\n.fa-flask-potion:before {\r\n\tcontent: \"\\f6e1\";\r\n}\r\n.fa-ghost:before {\r\n\tcontent: \"\\f6e2\";\r\n}\r\n.fa-hammer:before {\r\n\tcontent: \"\\f6e3\";\r\n}\r\n.fa-hammer-war:before {\r\n\tcontent: \"\\f6e4\";\r\n}\r\n.fa-hand-holding-magic:before {\r\n\tcontent: \"\\f6e5\";\r\n}\r\n.fa-hanukiah:before {\r\n\tcontent: \"\\f6e6\";\r\n}\r\n.fa-hat-witch:before {\r\n\tcontent: \"\\f6e7\";\r\n}\r\n.fa-hat-wizard:before {\r\n\tcontent: \"\\f6e8\";\r\n}\r\n.fa-head-side:before {\r\n\tcontent: \"\\f6e9\";\r\n}\r\n.fa-head-vr:before {\r\n\tcontent: \"\\f6ea\";\r\n}\r\n.fa-helmet-battle:before {\r\n\tcontent: \"\\f6eb\";\r\n}\r\n.fa-hiking:before {\r\n\tcontent: \"\\f6ec\";\r\n}\r\n.fa-hippo:before {\r\n\tcontent: \"\\f6ed\";\r\n}\r\n.fa-hockey-mask:before {\r\n\tcontent: \"\\f6ee\";\r\n}\r\n.fa-hood-cloak:before {\r\n\tcontent: \"\\f6ef\";\r\n}\r\n.fa-horse:before {\r\n\tcontent: \"\\f6f0\";\r\n}\r\n.fa-house-damage:before {\r\n\tcontent: \"\\f6f1\";\r\n}\r\n.fa-hryvnia:before {\r\n\tcontent: \"\\f6f2\";\r\n}\r\n.fa-key-skeleton:before {\r\n\tcontent: \"\\f6f3\";\r\n}\r\n.fa-kite:before {\r\n\tcontent: \"\\f6f4\";\r\n}\r\n.fa-knife-kitchen:before {\r\n\tcontent: \"\\f6f5\";\r\n}\r\n.fa-leaf-maple:before {\r\n\tcontent: \"\\f6f6\";\r\n}\r\n.fa-leaf-oak:before {\r\n\tcontent: \"\\f6f7\";\r\n}\r\n.fa-mace:before {\r\n\tcontent: \"\\f6f8\";\r\n}\r\n.fa-mandolin:before {\r\n\tcontent: \"\\f6f9\";\r\n}\r\n.fa-mask:before {\r\n\tcontent: \"\\f6fa\";\r\n}\r\n.fa-monkey:before {\r\n\tcontent: \"\\f6fb\";\r\n}\r\n.fa-mountain:before {\r\n\tcontent: \"\\f6fc\";\r\n}\r\n.fa-mountains:before {\r\n\tcontent: \"\\f6fd\";\r\n}\r\n.fa-network-wired:before {\r\n\tcontent: \"\\f6ff\";\r\n}\r\n.fa-otter:before {\r\n\tcontent: \"\\f700\";\r\n}\r\n.fa-pie:before {\r\n\tcontent: \"\\f705\";\r\n}\r\n.fa-pumpkin:before {\r\n\tcontent: \"\\f707\";\r\n}\r\n.fa-rabbit:before {\r\n\tcontent: \"\\f708\";\r\n}\r\n.fa-ram:before {\r\n\tcontent: \"\\f70a\";\r\n}\r\n.fa-running:before {\r\n\tcontent: \"\\f70c\";\r\n}\r\n.fa-scarecrow:before {\r\n\tcontent: \"\\f70d\";\r\n}\r\n.fa-scroll:before {\r\n\tcontent: \"\\f70e\";\r\n}\r\n.fa-shovel:before {\r\n\tcontent: \"\\f713\";\r\n}\r\n.fa-slash:before {\r\n\tcontent: \"\\f715\";\r\n}\r\n.fa-snake:before {\r\n\tcontent: \"\\f716\";\r\n}\r\n.fa-spider:before {\r\n\tcontent: \"\\f717\";\r\n}\r\n.fa-spider-web:before {\r\n\tcontent: \"\\f719\";\r\n}\r\n.fa-squirrel:before {\r\n\tcontent: \"\\f71a\";\r\n}\r\n.fa-staff:before {\r\n\tcontent: \"\\f71b\";\r\n}\r\n.fa-sword:before {\r\n\tcontent: \"\\f71c\";\r\n}\r\n.fa-toilet-paper:before {\r\n\tcontent: \"\\f71e\";\r\n}\r\n.fa-tombstone:before {\r\n\tcontent: \"\\f720\";\r\n}\r\n.fa-turtle:before {\r\n\tcontent: \"\\f726\";\r\n}\r\n.fa-vr-cardboard:before {\r\n\tcontent: \"\\f729\";\r\n}\r\n.fa-whale:before {\r\n\tcontent: \"\\f72c\";\r\n}\r\n.fa-wheat:before {\r\n\tcontent: \"\\f72d\";\r\n}\r\n.fa-ballot:before {\r\n\tcontent: \"\\f732\";\r\n}\r\n.fa-ballot-check:before {\r\n\tcontent: \"\\f733\";\r\n}\r\n.fa-booth-curtain:before {\r\n\tcontent: \"\\f734\";\r\n}\r\n.fa-box-ballot:before {\r\n\tcontent: \"\\f735\";\r\n}\r\n.fa-calendar-star:before {\r\n\tcontent: \"\\f736\";\r\n}\r\n.fa-clipboard-list-check:before {\r\n\tcontent: \"\\f737\";\r\n}\r\n.fa-cloud-drizzle:before {\r\n\tcontent: \"\\f738\";\r\n}\r\n.fa-cloud-hail:before {\r\n\tcontent: \"\\f739\";\r\n}\r\n.fa-cloud-hail-mixed:before {\r\n\tcontent: \"\\f73a\";\r\n}\r\n.fa-cloud-meatball:before {\r\n\tcontent: \"\\f73b\";\r\n}\r\n.fa-cloud-moon-rain:before {\r\n\tcontent: \"\\f73c\";\r\n}\r\n.fa-cloud-rain:before {\r\n\tcontent: \"\\f73d\";\r\n}\r\n.fa-cloud-rainbow:before {\r\n\tcontent: \"\\f73e\";\r\n}\r\n.fa-cloud-showers:before {\r\n\tcontent: \"\\f73f\";\r\n}\r\n.fa-cloud-showers-heavy:before {\r\n\tcontent: \"\\f740\";\r\n}\r\n.fa-cloud-sleet:before {\r\n\tcontent: \"\\f741\";\r\n}\r\n.fa-cloud-snow:before {\r\n\tcontent: \"\\f742\";\r\n}\r\n.fa-cloud-sun-rain:before {\r\n\tcontent: \"\\f743\";\r\n}\r\n.fa-clouds:before {\r\n\tcontent: \"\\f744\";\r\n}\r\n.fa-clouds-moon:before {\r\n\tcontent: \"\\f745\";\r\n}\r\n.fa-clouds-sun:before {\r\n\tcontent: \"\\f746\";\r\n}\r\n.fa-democrat:before {\r\n\tcontent: \"\\f747\";\r\n}\r\n.fa-dewpoint:before {\r\n\tcontent: \"\\f748\";\r\n}\r\n.fa-eclipse:before {\r\n\tcontent: \"\\f749\";\r\n}\r\n.fa-eclipse-alt:before {\r\n\tcontent: \"\\f74a\";\r\n}\r\n.fa-fire-smoke:before {\r\n\tcontent: \"\\f74b\";\r\n}\r\n.fa-flag-alt:before {\r\n\tcontent: \"\\f74c\";\r\n}\r\n.fa-flag-usa:before {\r\n\tcontent: \"\\f74d\";\r\n}\r\n.fa-fog:before {\r\n\tcontent: \"\\f74e\";\r\n}\r\n.fa-house-flood:before {\r\n\tcontent: \"\\f74f\";\r\n}\r\n.fa-humidity:before {\r\n\tcontent: \"\\f750\";\r\n}\r\n.fa-hurricane:before {\r\n\tcontent: \"\\f751\";\r\n}\r\n.fa-landmark-alt:before {\r\n\tcontent: \"\\f752\";\r\n}\r\n.fa-meteor:before {\r\n\tcontent: \"\\f753\";\r\n}\r\n.fa-moon-cloud:before {\r\n\tcontent: \"\\f754\";\r\n}\r\n.fa-moon-stars:before {\r\n\tcontent: \"\\f755\";\r\n}\r\n.fa-podium-star:before {\r\n\tcontent: \"\\f758\";\r\n}\r\n.fa-raindrops:before {\r\n\tcontent: \"\\f75c\";\r\n}\r\n.fa-smog:before {\r\n\tcontent: \"\\f75f\";\r\n}\r\n.fa-thunderstorm:before {\r\n\tcontent: \"\\f76c\";\r\n}\r\n.fa-volcano:before {\r\n\tcontent: \"\\f770\";\r\n}\r\n.fa-water:before {\r\n\tcontent: \"\\f773\";\r\n}\r\n.fa-angel:before {\r\n\tcontent: \"\\f779\";\r\n}\r\n.fa-artstation:before {\r\n\tcontent: \"\\f77a\";\r\n}\r\n.fa-atlassian:before {\r\n\tcontent: \"\\f77b\";\r\n}\r\n.fa-baby:before {\r\n\tcontent: \"\\f77c\";\r\n}\r\n.fa-baby-carriage:before {\r\n\tcontent: \"\\f77d\";\r\n}\r\n.fa-ball-pile:before {\r\n\tcontent: \"\\f77e\";\r\n}\r\n.fa-bells:before {\r\n\tcontent: \"\\f77f\";\r\n}\r\n.fa-biohazard:before {\r\n\tcontent: \"\\f780\";\r\n}\r\n.fa-blog:before {\r\n\tcontent: \"\\f781\";\r\n}\r\n.fa-boot:before {\r\n\tcontent: \"\\f782\";\r\n}\r\n.fa-calendar-day:before {\r\n\tcontent: \"\\f783\";\r\n}\r\n.fa-calendar-week:before {\r\n\tcontent: \"\\f784\";\r\n}\r\n.fa-canadian-maple-leaf:before {\r\n\tcontent: \"\\f785\";\r\n}\r\n.fa-candy-cane:before {\r\n\tcontent: \"\\f786\";\r\n}\r\n.fa-carrot:before {\r\n\tcontent: \"\\f787\";\r\n}\r\n.fa-cash-register:before {\r\n\tcontent: \"\\f788\";\r\n}\r\n.fa-centos:before {\r\n\tcontent: \"\\f789\";\r\n}\r\n.fa-chart-network:before {\r\n\tcontent: \"\\f78a\";\r\n}\r\n.fa-chimney:before {\r\n\tcontent: \"\\f78b\";\r\n}\r\n.fa-compress-arrows-alt:before {\r\n\tcontent: \"\\f78c\";\r\n}\r\n.fa-confluence:before {\r\n\tcontent: \"\\f78d\";\r\n}\r\n.fa-deer:before {\r\n\tcontent: \"\\f78e\";\r\n}\r\n.fa-deer-rudolph:before {\r\n\tcontent: \"\\f78f\";\r\n}\r\n.fa-diaspora:before {\r\n\tcontent: \"\\f791\";\r\n}\r\n.fa-dreidel:before {\r\n\tcontent: \"\\f792\";\r\n}\r\n.fa-dumpster:before {\r\n\tcontent: \"\\f793\";\r\n}\r\n.fa-dumpster-fire:before {\r\n\tcontent: \"\\f794\";\r\n}\r\n.fa-ear-muffs:before {\r\n\tcontent: \"\\f795\";\r\n}\r\n.fa-ethernet:before {\r\n\tcontent: \"\\f796\";\r\n}\r\n.fa-fireplace:before {\r\n\tcontent: \"\\f79a\";\r\n}\r\n.fa-frosty-head:before {\r\n\tcontent: \"\\f79b\";\r\n}\r\n.fa-gifts:before {\r\n\tcontent: \"\\f79c\";\r\n}\r\n.fa-gingerbread-man:before {\r\n\tcontent: \"\\f79d\";\r\n}\r\n.fa-glass-champagne:before {\r\n\tcontent: \"\\f79e\";\r\n}\r\n.fa-glass-cheers:before {\r\n\tcontent: \"\\f79f\";\r\n}\r\n.fa-glass-whiskey:before {\r\n\tcontent: \"\\f7a0\";\r\n}\r\n.fa-glass-whiskey-rocks:before {\r\n\tcontent: \"\\f7a1\";\r\n}\r\n.fa-globe-europe:before {\r\n\tcontent: \"\\f7a2\";\r\n}\r\n.fa-globe-snow:before {\r\n\tcontent: \"\\f7a3\";\r\n}\r\n.fa-grip-lines:before {\r\n\tcontent: \"\\f7a4\";\r\n}\r\n.fa-grip-lines-vertical:before {\r\n\tcontent: \"\\f7a5\";\r\n}\r\n.fa-guitar:before {\r\n\tcontent: \"\\f7a6\";\r\n}\r\n.fa-hat-santa:before {\r\n\tcontent: \"\\f7a7\";\r\n}\r\n.fa-hat-winter:before {\r\n\tcontent: \"\\f7a8\";\r\n}\r\n.fa-heart-broken:before {\r\n\tcontent: \"\\f7a9\";\r\n}\r\n.fa-holly-berry:before {\r\n\tcontent: \"\\f7aa\";\r\n}\r\n.fa-horse-head:before {\r\n\tcontent: \"\\f7ab\";\r\n}\r\n.fa-ice-skate:before {\r\n\tcontent: \"\\f7ac\";\r\n}\r\n.fa-icicles:before {\r\n\tcontent: \"\\f7ad\";\r\n}\r\n.fa-igloo:before {\r\n\tcontent: \"\\f7ae\";\r\n}\r\n.fa-lights-holiday:before {\r\n\tcontent: \"\\f7b2\";\r\n}\r\n.fa-mistletoe:before {\r\n\tcontent: \"\\f7b4\";\r\n}\r\n.fa-mitten:before {\r\n\tcontent: \"\\f7b5\";\r\n}\r\n.fa-mug-hot:before {\r\n\tcontent: \"\\f7b6\";\r\n}\r\n.fa-mug-marshmallows:before {\r\n\tcontent: \"\\f7b7\";\r\n}\r\n.fa-ornament:before {\r\n\tcontent: \"\\f7b8\";\r\n}\r\n.fa-radiation-alt:before {\r\n\tcontent: \"\\f7ba\";\r\n}\r\n.fa-restroom:before {\r\n\tcontent: \"\\f7bd\";\r\n}\r\n.fa-satellite:before {\r\n\tcontent: \"\\f7bf\";\r\n}\r\n.fa-scarf:before {\r\n\tcontent: \"\\f7c1\";\r\n}\r\n.fa-sd-card:before {\r\n\tcontent: \"\\f7c2\";\r\n}\r\n.fa-sim-card:before {\r\n\tcontent: \"\\f7c4\";\r\n}\r\n.fa-sleigh:before {\r\n\tcontent: \"\\f7cc\";\r\n}\r\n.fa-sms:before {\r\n\tcontent: \"\\f7cd\";\r\n}\r\n.fa-snowman:before {\r\n\tcontent: \"\\f7d0\";\r\n}\r\n.fa-toilet:before {\r\n\tcontent: \"\\f7d8\";\r\n}\r\n.fa-tools:before {\r\n\tcontent: \"\\f7d9\";\r\n}\r\n.fa-fire-alt:before {\r\n\tcontent: \"\\f7e4\";\r\n}\r\n.fa-bacon:before {\r\n\tcontent: \"\\f7e5\";\r\n}\r\n.fa-book-medical:before {\r\n\tcontent: \"\\f7e6\";\r\n}\r\n.fa-book-user:before {\r\n\tcontent: \"\\f7e7\";\r\n}\r\n.fa-books-medical:before {\r\n\tcontent: \"\\f7e8\";\r\n}\r\n.fa-brackets:before {\r\n\tcontent: \"\\f7e9\";\r\n}\r\n.fa-brackets-curly:before {\r\n\tcontent: \"\\f7ea\";\r\n}\r\n.fa-bread-loaf:before {\r\n\tcontent: \"\\f7eb\";\r\n}\r\n.fa-bread-slice:before {\r\n\tcontent: \"\\f7ec\";\r\n}\r\n.fa-burrito:before {\r\n\tcontent: \"\\f7ed\";\r\n}\r\n.fa-chart-scatter:before {\r\n\tcontent: \"\\f7ee\";\r\n}\r\n.fa-cheese:before {\r\n\tcontent: \"\\f7ef\";\r\n}\r\n.fa-cheese-swiss:before {\r\n\tcontent: \"\\f7f0\";\r\n}\r\n.fa-cheeseburger:before {\r\n\tcontent: \"\\f7f1\";\r\n}\r\n.fa-clinic-medical:before {\r\n\tcontent: \"\\f7f2\";\r\n}\r\n.fa-clipboard-user:before {\r\n\tcontent: \"\\f7f3\";\r\n}\r\n.fa-comment-alt-medical:before {\r\n\tcontent: \"\\f7f4\";\r\n}\r\n.fa-comment-medical:before {\r\n\tcontent: \"\\f7f5\";\r\n}\r\n.fa-croissant:before {\r\n\tcontent: \"\\f7f6\";\r\n}\r\n.fa-crutch:before {\r\n\tcontent: \"\\f7f7\";\r\n}\r\n.fa-crutches:before {\r\n\tcontent: \"\\f7f8\";\r\n}\r\n.fa-debug:before {\r\n\tcontent: \"\\f7f9\";\r\n}\r\n.fa-disease:before {\r\n\tcontent: \"\\f7fa\";\r\n}\r\n.fa-egg:before {\r\n\tcontent: \"\\f7fb\";\r\n}\r\n.fa-egg-fried:before {\r\n\tcontent: \"\\f7fc\";\r\n}\r\n.fa-files-medical:before {\r\n\tcontent: \"\\f7fd\";\r\n}\r\n.fa-fish-cooked:before {\r\n\tcontent: \"\\f7fe\";\r\n}\r\n.fa-flower:before {\r\n\tcontent: \"\\f7ff\";\r\n}\r\n.fa-flower-daffodil:before {\r\n\tcontent: \"\\f800\";\r\n}\r\n.fa-flower-tulip:before {\r\n\tcontent: \"\\f801\";\r\n}\r\n.fa-folder-tree:before {\r\n\tcontent: \"\\f802\";\r\n}\r\n.fa-french-fries:before {\r\n\tcontent: \"\\f803\";\r\n}\r\n.fa-glass:before {\r\n\tcontent: \"\\f804\";\r\n}\r\n.fa-hamburger:before {\r\n\tcontent: \"\\f805\";\r\n}\r\n.fa-hand-middle-finger:before {\r\n\tcontent: \"\\f806\";\r\n}\r\n.fa-hard-hat:before {\r\n\tcontent: \"\\f807\";\r\n}\r\n.fa-head-side-brain:before {\r\n\tcontent: \"\\f808\";\r\n}\r\n.fa-head-side-medical:before {\r\n\tcontent: \"\\f809\";\r\n}\r\n.fa-home-alt:before {\r\n\tcontent: \"\\f80a\";\r\n}\r\n.fa-home-lg:before {\r\n\tcontent: \"\\f80b\";\r\n}\r\n.fa-home-lg-alt:before {\r\n\tcontent: \"\\f80c\";\r\n}\r\n.fa-hospital-user:before {\r\n\tcontent: \"\\f80d\";\r\n}\r\n.fa-hospitals:before {\r\n\tcontent: \"\\f80e\";\r\n}\r\n.fa-hotdog:before {\r\n\tcontent: \"\\f80f\";\r\n}\r\n.fa-ice-cream:before {\r\n\tcontent: \"\\f810\";\r\n}\r\n.fa-island-tropical:before {\r\n\tcontent: \"\\f811\";\r\n}\r\n.fa-laptop-medical:before {\r\n\tcontent: \"\\f812\";\r\n}\r\n.fa-mailbox:before {\r\n\tcontent: \"\\f813\";\r\n}\r\n.fa-meat:before {\r\n\tcontent: \"\\f814\";\r\n}\r\n.fa-pager:before {\r\n\tcontent: \"\\f815\";\r\n}\r\n.fa-pepper-hot:before {\r\n\tcontent: \"\\f816\";\r\n}\r\n.fa-pizza-slice:before {\r\n\tcontent: \"\\f818\";\r\n}\r\n.fa-popcorn:before {\r\n\tcontent: \"\\f819\";\r\n}\r\n.fa-rings-wedding:before {\r\n\tcontent: \"\\f81b\";\r\n}\r\n.fa-sack-dollar:before {\r\n\tcontent: \"\\f81d\";\r\n}\r\n.fa-salad:before {\r\n\tcontent: \"\\f81e\";\r\n}\r\n.fa-sandwich:before {\r\n\tcontent: \"\\f81f\";\r\n}\r\n.fa-sausage:before {\r\n\tcontent: \"\\f820\";\r\n}\r\n.fa-soup:before {\r\n\tcontent: \"\\f823\";\r\n}\r\n.fa-steak:before {\r\n\tcontent: \"\\f824\";\r\n}\r\n.fa-stretcher:before {\r\n\tcontent: \"\\f825\";\r\n}\r\n.fa-user-headset:before {\r\n\tcontent: \"\\f82d\";\r\n}\r\n.fa-users-medical:before {\r\n\tcontent: \"\\f830\";\r\n}\r\n.fa-walker:before {\r\n\tcontent: \"\\f831\";\r\n}\r\n.fa-webcam:before {\r\n\tcontent: \"\\f832\";\r\n}\r\n.fa-airbnb:before {\r\n\tcontent: \"\\f834\";\r\n}\r\n.fa-battle-net:before {\r\n\tcontent: \"\\f835\";\r\n}\r\n.fa-bootstrap:before {\r\n\tcontent: \"\\f836\";\r\n}\r\n.fa-chromecast:before {\r\n\tcontent: \"\\f838\";\r\n}\r\n.fa-alarm-exclamation:before {\r\n\tcontent: \"\\f843\";\r\n}\r\n.fa-alarm-plus:before {\r\n\tcontent: \"\\f844\";\r\n}\r\n.fa-alarm-snooze:before {\r\n\tcontent: \"\\f845\";\r\n}\r\n.fa-align-slash:before {\r\n\tcontent: \"\\f846\";\r\n}\r\n.fa-bags-shopping:before {\r\n\tcontent: \"\\f847\";\r\n}\r\n.fa-bell-exclamation:before {\r\n\tcontent: \"\\f848\";\r\n}\r\n.fa-bell-plus:before {\r\n\tcontent: \"\\f849\";\r\n}\r\n.fa-biking:before {\r\n\tcontent: \"\\f84a\";\r\n}\r\n.fa-biking-mountain:before {\r\n\tcontent: \"\\f84b\";\r\n}\r\n.fa-border-all:before {\r\n\tcontent: \"\\f84c\";\r\n}\r\n.fa-border-bottom:before {\r\n\tcontent: \"\\f84d\";\r\n}\r\n.fa-border-inner:before {\r\n\tcontent: \"\\f84e\";\r\n}\r\n.fa-border-left:before {\r\n\tcontent: \"\\f84f\";\r\n}\r\n.fa-border-none:before {\r\n\tcontent: \"\\f850\";\r\n}\r\n.fa-border-outer:before {\r\n\tcontent: \"\\f851\";\r\n}\r\n.fa-border-right:before {\r\n\tcontent: \"\\f852\";\r\n}\r\n.fa-border-style:before {\r\n\tcontent: \"\\f853\";\r\n}\r\n.fa-border-style-alt:before {\r\n\tcontent: \"\\f854\";\r\n}\r\n.fa-border-top:before {\r\n\tcontent: \"\\f855\";\r\n}\r\n.fa-bring-forward:before {\r\n\tcontent: \"\\f856\";\r\n}\r\n.fa-bring-front:before {\r\n\tcontent: \"\\f857\";\r\n}\r\n.fa-burger-soda:before {\r\n\tcontent: \"\\f858\";\r\n}\r\n.fa-car-building:before {\r\n\tcontent: \"\\f859\";\r\n}\r\n.fa-car-bus:before {\r\n\tcontent: \"\\f85a\";\r\n}\r\n.fa-cars:before {\r\n\tcontent: \"\\f85b\";\r\n}\r\n.fa-coin:before {\r\n\tcontent: \"\\f85c\";\r\n}\r\n.fa-construction:before {\r\n\tcontent: \"\\f85d\";\r\n}\r\n.fa-digging:before {\r\n\tcontent: \"\\f85e\";\r\n}\r\n.fa-drone:before {\r\n\tcontent: \"\\f85f\";\r\n}\r\n.fa-drone-alt:before {\r\n\tcontent: \"\\f860\";\r\n}\r\n.fa-dryer:before {\r\n\tcontent: \"\\f861\";\r\n}\r\n.fa-dryer-alt:before {\r\n\tcontent: \"\\f862\";\r\n}\r\n.fa-fan:before {\r\n\tcontent: \"\\f863\";\r\n}\r\n.fa-farm:before {\r\n\tcontent: \"\\f864\";\r\n}\r\n.fa-file-search:before {\r\n\tcontent: \"\\f865\";\r\n}\r\n.fa-font-case:before {\r\n\tcontent: \"\\f866\";\r\n}\r\n.fa-game-board:before {\r\n\tcontent: \"\\f867\";\r\n}\r\n.fa-game-board-alt:before {\r\n\tcontent: \"\\f868\";\r\n}\r\n.fa-glass-citrus:before {\r\n\tcontent: \"\\f869\";\r\n}\r\n.fa-h4:before {\r\n\tcontent: \"\\f86a\";\r\n}\r\n.fa-hat-chef:before {\r\n\tcontent: \"\\f86b\";\r\n}\r\n.fa-horizontal-rule:before {\r\n\tcontent: \"\\f86c\";\r\n}\r\n.fa-icons:before {\r\n\tcontent: \"\\f86d\";\r\n}\r\n.fa-kerning:before {\r\n\tcontent: \"\\f86f\";\r\n}\r\n.fa-line-columns:before {\r\n\tcontent: \"\\f870\";\r\n}\r\n.fa-line-height:before {\r\n\tcontent: \"\\f871\";\r\n}\r\n.fa-money-check-edit:before {\r\n\tcontent: \"\\f872\";\r\n}\r\n.fa-money-check-edit-alt:before {\r\n\tcontent: \"\\f873\";\r\n}\r\n.fa-mug:before {\r\n\tcontent: \"\\f874\";\r\n}\r\n.fa-phone-alt:before {\r\n\tcontent: \"\\f879\";\r\n}\r\n.fa-snooze:before {\r\n\tcontent: \"\\f880\";\r\n}\r\n.fa-sort-alt:before {\r\n\tcontent: \"\\f883\";\r\n}\r\n.fa-sort-amount-down-alt:before {\r\n\tcontent: \"\\f884\";\r\n}\r\n.fa-sort-size-down:before {\r\n\tcontent: \"\\f88c\";\r\n}\r\n.fa-sparkles:before {\r\n\tcontent: \"\\f890\";\r\n}\r\n.fa-text:before {\r\n\tcontent: \"\\f893\";\r\n}\r\n.fa-text-size:before {\r\n\tcontent: \"\\f894\";\r\n}\r\n.fa-voicemail:before {\r\n\tcontent: \"\\f897\";\r\n}\r\n.fa-washer:before {\r\n\tcontent: \"\\f898\";\r\n}\r\n.fa-wind-turbine:before {\r\n\tcontent: \"\\f89b\";\r\n}\r\n.fa-border-center-h:before {\r\n\tcontent: \"\\f89c\";\r\n}\r\n.fa-border-center-v:before {\r\n\tcontent: \"\\f89d\";\r\n}\r\n.fa-cotton-bureau:before {\r\n\tcontent: \"\\f89e\";\r\n}\r\n.fa-album:before {\r\n\tcontent: \"\\f89f\";\r\n}\r\n.fa-album-collection:before {\r\n\tcontent: \"\\f8a0\";\r\n}\r\n.fa-amp-guitar:before {\r\n\tcontent: \"\\f8a1\";\r\n}\r\n.fa-badge-sheriff:before {\r\n\tcontent: \"\\f8a2\";\r\n}\r\n.fa-banjo:before {\r\n\tcontent: \"\\f8a3\";\r\n}\r\n.fa-betamax:before {\r\n\tcontent: \"\\f8a4\";\r\n}\r\n.fa-boombox:before {\r\n\tcontent: \"\\f8a5\";\r\n}\r\n.fa-buy-n-large:before {\r\n\tcontent: \"\\f8a6\";\r\n}\r\n.fa-cactus:before {\r\n\tcontent: \"\\f8a7\";\r\n}\r\n.fa-camcorder:before {\r\n\tcontent: \"\\f8a8\";\r\n}\r\n.fa-camera-movie:before {\r\n\tcontent: \"\\f8a9\";\r\n}\r\n.fa-camera-polaroid:before {\r\n\tcontent: \"\\f8aa\";\r\n}\r\n.fa-cassette-tape:before {\r\n\tcontent: \"\\f8ab\";\r\n}\r\n.fa-cctv:before {\r\n\tcontent: \"\\f8ac\";\r\n}\r\n.fa-clarinet:before {\r\n\tcontent: \"\\f8ad\";\r\n}\r\n.fa-cloud-music:before {\r\n\tcontent: \"\\f8ae\";\r\n}\r\n.fa-comment-alt-music:before {\r\n\tcontent: \"\\f8af\";\r\n}\r\n.fa-comment-music:before {\r\n\tcontent: \"\\f8b0\";\r\n}\r\n.fa-computer-classic:before {\r\n\tcontent: \"\\f8b1\";\r\n}\r\n.fa-computer-speaker:before {\r\n\tcontent: \"\\f8b2\";\r\n}\r\n.fa-cowbell:before {\r\n\tcontent: \"\\f8b3\";\r\n}\r\n.fa-cowbell-more:before {\r\n\tcontent: \"\\f8b4\";\r\n}\r\n.fa-disc-drive:before {\r\n\tcontent: \"\\f8b5\";\r\n}\r\n.fa-file-music:before {\r\n\tcontent: \"\\f8b6\";\r\n}\r\n.fa-film-canister:before {\r\n\tcontent: \"\\f8b7\";\r\n}\r\n.fa-flashlight:before {\r\n\tcontent: \"\\f8b8\";\r\n}\r\n.fa-flute:before {\r\n\tcontent: \"\\f8b9\";\r\n}\r\n.fa-flux-capacitor:before {\r\n\tcontent: \"\\f8ba\";\r\n}\r\n.fa-game-console-handheld:before {\r\n\tcontent: \"\\f8bb\";\r\n}\r\n.fa-gamepad-alt:before {\r\n\tcontent: \"\\f8bc\";\r\n}\r\n.fa-gramophone:before {\r\n\tcontent: \"\\f8bd\";\r\n}\r\n.fa-guitar-electric:before {\r\n\tcontent: \"\\f8be\";\r\n}\r\n.fa-guitars:before {\r\n\tcontent: \"\\f8bf\";\r\n}\r\n.fa-hat-cowboy:before {\r\n\tcontent: \"\\f8c0\";\r\n}\r\n.fa-hat-cowboy-side:before {\r\n\tcontent: \"\\f8c1\";\r\n}\r\n.fa-head-side-headphones:before {\r\n\tcontent: \"\\f8c2\";\r\n}\r\n.fa-horse-saddle:before {\r\n\tcontent: \"\\f8c3\";\r\n}\r\n.fa-image-polaroid:before {\r\n\tcontent: \"\\f8c4\";\r\n}\r\n.fa-joystick:before {\r\n\tcontent: \"\\f8c5\";\r\n}\r\n.fa-jug:before {\r\n\tcontent: \"\\f8c6\";\r\n}\r\n.fa-kazoo:before {\r\n\tcontent: \"\\f8c7\";\r\n}\r\n.fa-lasso:before {\r\n\tcontent: \"\\f8c8\";\r\n}\r\n.fa-list-music:before {\r\n\tcontent: \"\\f8c9\";\r\n}\r\n.fa-microphone-stand:before {\r\n\tcontent: \"\\f8cb\";\r\n}\r\n.fa-mouse:before {\r\n\tcontent: \"\\f8cc\";\r\n}\r\n.fa-mouse-alt:before {\r\n\tcontent: \"\\f8cd\";\r\n}\r\n.fa-mp3-player:before {\r\n\tcontent: \"\\f8ce\";\r\n}\r\n.fa-music-slash:before {\r\n\tcontent: \"\\f8d1\";\r\n}\r\n.fa-piano-keyboard:before {\r\n\tcontent: \"\\f8d5\";\r\n}\r\n.fa-projector:before {\r\n\tcontent: \"\\f8d6\";\r\n}\r\n.fa-radio-alt:before {\r\n\tcontent: \"\\f8d8\";\r\n}\r\n.fa-router:before {\r\n\tcontent: \"\\f8da\";\r\n}\r\n.fa-saxophone:before {\r\n\tcontent: \"\\f8dc\";\r\n}\r\n.fa-speakers:before {\r\n\tcontent: \"\\f8e0\";\r\n}\r\n.fa-trumpet:before {\r\n\tcontent: \"\\f8e3\";\r\n}\r\n.fa-usb-drive:before {\r\n\tcontent: \"\\f8e9\";\r\n}\r\n.fa-walkie-talkie:before {\r\n\tcontent: \"\\f8ef\";\r\n}\r\n.fa-waveform:before {\r\n\tcontent: \"\\f8f1\";\r\n}\r\n.fa-scanner-image:before {\r\n\tcontent: \"\\f8f3\";\r\n}\r\n.fa-air-conditioner:before {\r\n\tcontent: \"\\f8f4\";\r\n}\r\n.fa-alien:before {\r\n\tcontent: \"\\f8f5\";\r\n}\r\n.fa-alien-monster:before {\r\n\tcontent: \"\\f8f6\";\r\n}\r\n.fa-bed-alt:before {\r\n\tcontent: \"\\f8f7\";\r\n}\r\n.fa-bed-bunk:before {\r\n\tcontent: \"\\f8f8\";\r\n}\r\n.fa-bed-empty:before {\r\n\tcontent: \"\\f8f9\";\r\n}\r\n.fa-bell-on:before {\r\n\tcontent: \"\\f8fa\";\r\n}\r\n.fa-blinds:before {\r\n\tcontent: \"\\f8fb\";\r\n}\r\n.fa-blinds-raised:before {\r\n\tcontent: \"\\f8fd\";\r\n}\r\n.fa-camera-home:before {\r\n\tcontent: \"\\f8fe\";\r\n}\r\n.fa-caravan:before {\r\n\tcontent: \"\\f8ff\";\r\n}\r\n", ".qadpt-grdicon {\r\n\twidth: 30px !important;\r\n\tpadding: 4px !important;\r\n}\r\n.qadpt-menupopup {\r\n\tz-index: 999;\r\n\tpadding: 10px;\r\n\tmin-width: 170px;\r\n\tborder-radius: 8px;\r\n\tposition: absolute;\r\n\tbackground: var(--white-color);\r\n    box-shadow: -5px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);\r\n\tright: 20px;\r\n\tbutton {\r\n\t\tbackground-color: transparent !important;\r\n\t}\r\n\t.qadpt-actionpopup {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #000000;\r\n\t\tspan {\r\n\t\t\tmargin-left: 10px;\r\n\t\t}\r\n\t}\r\n}\r\n// .qadpt-iconspositions {\r\n// \tmargin-right: 8;\r\n// \tfont-size: 18;\r\n// }\r\n\r\n.qadpt-loaderstyles {\r\n\ttransform: translate(-50%, -50%);\r\n\tz-index: 1;\r\n\tposition: absolute;\r\n\ttop: 225px;\r\n\tleft: 475px;\r\n\twidth: 100%;\r\n\theight: 77%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tbackground-color: rgba(255, 255, 255, 0.8);\r\n}\r\n.qadpt-loaderSpinnerStyles {\r\n\twidth: 45px;\r\n\theight: 45px;\r\n}\r\n.qadpt-deletepopupwidth {\r\n\twidth: 200px;\r\n}\r\n\r\n.qadpt-memberButton {\r\n\tbackground-color: var(--button-bg-color);\r\n\tborder: none;\r\n\tborder-radius: 15px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 10px;\r\n\theight: 40px;\r\n\r\n\ti {\r\n\t\tmargin-right: 10px;\r\n\t\tfont-size: 14px !important;\r\n\t}\r\n\r\n\t// Styling for the Save button when updated\r\n\t&.qadpt-mem-updated {\r\n\t\tbackground-color: var(--button-bg-color); // The same background color as the member button\r\n\t}\r\n\r\n\t// Disabled state styling\r\n\t&.qadpt-disabled {\r\n\t\tbackground-color: #ccc; // Disabled background color\r\n\t\tcursor: not-allowed; // Show as disabled\r\n\t}\r\n}\r\n\r\n.qadpt-membersave {\r\n\tposition: absolute;\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tbackground: transparent;\r\n\tcolor: var(--white-color);\r\n\tborder-radius: 15px;\r\n\t// padding: 12px 31px;\r\n\tbackground-color: #91a7d9;\r\n\tborder: 0px solid #91a7d9;\r\n\t&.isLabelUpdated {\r\n\t\tbackground-color: var(--button-bg-color);\r\n\t}\r\n}\r\n.qadpt-deletepopupCancelbutton {\r\n\tposition: relative;\r\n\tbackground: var(--white-color);\r\n\tcolor: #007bff;\r\n\tborder: 1px solid #007bff;\r\n\tmargin-right: 27px;\r\n\tright: 0px;\r\n\ttop: 10px;\r\n}\r\n.qadpt-deletepopupButton {\r\n\tposition: relative;\r\n\tright: 0px;\r\n\ttop: 10px;\r\n}\r\n.qadpt-usereditcancelButton {\r\n\tposition: absolute;\r\n\tright: 220px;\r\n\tz-index: 99;\r\n\ttop: 67px;\r\n\tbackground-color: #007bff;\r\n\tborder: none;\r\n\tborder-radius: 5px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 7px 21px;\r\n\ttop: 440px;\r\n}\r\n.qadpt-usereditbuttonsdiv {\r\n\tmargin-left: 40px;\r\n\tdisplay: flex;\r\n\tgap: 20px;\r\n}\r\n.qadpt-usereditsaveButton {\r\n\tposition: absolute;\r\n\tright: 37px;\r\n\tz-index: 99;\r\n\tbackground-color: #a5c3c5;\r\n\tborder: none;\r\n\tborder-radius: 25px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 7px 21px;\r\n\ttop: 460px;\r\n\twidth: 110px;\r\n\theight: 44px;\r\n\tpadding: 10px 12px 10px 12px;\r\n}\r\n\r\n.qadpt-userpasswordpopup {\r\n\t/* overflow-y: scroll;\r\n\t z-index: 99999; */\r\n\tz-index: 100;\r\n\twidth: 350px !important;\r\n\theight: 37vh;\r\n\ttop: 200px;\r\n\tright: 400px;\r\n\tpadding: 0 20px;\r\n\tposition: fixed !important;\r\n\tdisplay: block !important;\r\n\tbackground: var(--white-color) !important;\r\n\tbox-shadow: 0 3px 8px #000000;\r\n\tborder-radius: 8px;\r\n}\r\n\r\n.qadpt-usrhdrpossideoff {\r\n\ttop: -38px;\r\n\tposition: relative;\r\n\tleft: -440px;\r\n}\r\n.qadpt-usrhdrpossideon {\r\n\ttop: 2px;\r\n\tposition: relative;\r\n\tright: 560px;\r\n}\r\n.qadpt-userCreateButtonPosition {\r\n\ttop: -130px;\r\n\tposition: relative;\r\n}\r\n\r\n.qadpt-custom-data-grid {\r\n\tborder-color: var(--grid-border-color);\r\n\r\n\t.MuiDataGrid-columnHeaders {\r\n\t\tbackground: var(--grid-head-background);\r\n\t}\r\n\r\n\t.MuiDataGrid-columnHeaderTitle {\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.MuiDataGrid-cell {\r\n\t\tborder-right: 1px solid var(--grid-border-color);\r\n\t}\r\n\r\n\t.MuiDataGrid-columnHeader,\r\n\t.MuiDataGrid-cell {\r\n\t\tborder-right: 1px solid var(--grid-border-color);\r\n\t}\r\n\r\n\t.MuiDataGrid-row {\r\n\t\t.MuiDataGrid-cell {\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t}\r\n\r\n\tposition: relative;\r\n}\r\n\r\n.st-ina {\r\n\tborder: 1px solid #9a9a9a;\r\n\tcolor: #9a9a9a;\r\n\tbackground: rgba(154, 154, 154, 0.2);\r\n\tborder-radius: 20px;\r\n\theight: 32px;\r\n\tmargin: 10px 5px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tplace-content: center;\r\n}\r\n.st-blo {\r\n\tborder: 1px solid #e77a7a;\r\n\tcolor: #f57676;\r\n\tbackground: rgba(250, 152, 152, 0.2);\r\n\tborder-radius: 20px;\r\n\theight: 32px;\r\n\tmargin: 10px 5px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tplace-content: center;\r\n}\r\n.st-pen {\r\n\tborder: 1px solid #fc8d33;\r\n\tcolor: #f77722;\r\n\tbackground: rgba(250, 169, 19, 0.2);\r\n\tborder-radius: 20px;\r\n\theight: 32px;\r\n\tmargin: 10px 5px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tplace-content: center;\r\n}\r\n.st-act {\r\n\tborder: 1px solid var(--button-bg-color);\r\n\tcolor: var(--button-bg-color);\r\n\tbackground: #dae7e7;\r\n\tborder-radius: 20px;\r\n\theight: 32px;\r\n\tmargin: 10px 5px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tplace-content: center;\r\n}\r\n.MuiDataGrid-columnHeader {\r\n\tbackground: var(--grid-head-background);\r\n\tpadding: 0 15px !important;\r\n\tborder-right: 1px solid var(--grid-border-color);\r\n\theight: 40px !important;\r\n\tborder-color: transparent !important;\r\n}\r\n.qadpt-setting-grd,\r\n.qadpt-account-grd,\r\n.qadpt-audit-grd {\r\n\tborder-color: var(--grid-border-color) !important;\r\n\t.MuiDataGrid-main {\r\n\t\t--DataGrid-topContainerHeight: 40px !important;\r\n\t}\r\n\t.MuiDataGrid-virtualScroller {\r\n\t\theight: calc(100vh - 295px) !important;\r\n\t}\r\n\t.MuiDataGrid-footerContainer .MuiTablePagination-root .MuiInputBase-input {\r\n\t\tpadding: 20px !important;\r\n\t}\r\n\t.MuiDataGrid-columnHeader {\r\n\t\t.MuiDataGrid-sortIcon {\r\n\t\t\topacity: 1 !important;\r\n\t\t\tvisibility: visible;\r\n\t\t}\r\n\t}\r\n\t.MuiDataGrid-row .MuiDataGrid-cell i {\r\n\t\tfont-weight: 600;\r\n\t}\r\n\t.MuiDataGrid-columnHeaderTitle {\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t.MuiDataGrid-columnHeader {\r\n\t\tbackground: var(--grid-head-background);\r\n\t\tcolor: black;\r\n\t\tborder-right: 1px solid var(--grid-border-color);\r\n\t\theight: 40px !important;\r\n\t}\r\n\r\n\t.MuiDataGrid-cell {\r\n\t\tborder-right: 1px solid var(--grid-border-color);\r\n\t\t// height: 40px !important;\r\n\t}\r\n\r\n\t.MuiDataGrid-row {\r\n\t\t.MuiDataGrid-cell {\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t\t.MuiButtonBase-root.MuiChip-root{\r\n\t\t\tbackground: var(--button-bg-color) !important;\r\n\t\t}\r\n\t}\r\n}\r\n// .qadpt-account-grd {\r\n// \t.MuiDataGrid-virtualScroller {\r\n// \t\theight: calc(100vh - 320px) !important;\r\n// \t}\r\n// }\r\n.qadpt-setting-grd {\r\n\t// .MuiDataGrid-virtualScroller {\r\n\t// \theight: calc(100vh - 280px) !important;\r\n\t// }\r\n\t.MuiDataGrid-columnHeader:nth-child(2),\r\n\t.MuiDataGrid-cell:nth-child(2),\r\n\t.MuiDataGrid-columnHeader:nth-child(3),\r\n\t.MuiDataGrid-cell:nth-child(3) {\r\n\t\twidth: 25% !important;\r\n\t\tmin-width: 25% !important;\r\n\t\tmax-width: 25% !important;\r\n\t}\r\n\r\n\t.MuiDataGrid-columnHeader:nth-child(5),\r\n\t.MuiDataGrid-cell:nth-child(5) {\r\n\t\twidth: 14% !important;\r\n\t\tmin-width: 14% !important;\r\n\t\tmax-width: 14% !important;\r\n\t}\r\n\t.MuiDataGrid-columnHeader:nth-child(6),\r\n\t.MuiDataGrid-cell:nth-child(6),\r\n\t.MuiDataGrid-columnHeader:nth-child(4),\r\n\t.MuiDataGrid-cell:nth-child(4) {\r\n\t\twidth: 20% !important;\r\n\t\tmin-width: 20% !important;\r\n\t\tmax-width: 20% !important;\r\n\t}\r\n}\r\n.qadpt-midpart {\r\n\t.qadpt-content-block {\r\n\t\tbackground: var(--white-color);\r\n\t\tpadding: 0 20px;\r\n\t\tborder-radius: 6px;\r\n\t\tmin-height: calc(100vh - 80px);\r\n\t\t.qadpt-head {\r\n\t\t\t//border-bottom: 1px solid var(--border-color);\r\n\t\t\tmargin: 0 -20px;\r\n\t\t\tpadding: 20px;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\t\t\r\n    justify-content: space-between;\r\n\t\t\t\r\n\r\n\t\t\t.qadpt-title-sec {\r\n\t\t\t\t// width: calc(100% - 200px);\r\n\t\t\t\t.qadpt-title {\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tfont-size: 22px;\r\n\t\t\t\t\tline-height: 20px;\r\n\t\t\t\t}\r\n\t\t\t\t.qadpt-description {\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\tline-height: 18px;\r\n\t\t\t\t\tpadding: 5px 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.qadpt-right-part {\r\n\t\t\t\t    text-align: right;\r\n    width: auto;\r\n    display: flex;\r\n    gap: 10px;\r\n    align-items: center;\r\n\t.open-ai-key-wrapper {\r\n  .open-ai-key-out {\r\n    .open-ai-key-box {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .open-ai-key-box-icon {\r\n        background: #f0f0f0;\r\n        padding: 10px 12px;\r\n        border-radius: 8px;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 16px;\r\n          height: 16px;\r\n        }\r\n      }\r\n\r\n      .open-ai-key-input-wrapper {\r\n        position: relative;\r\n        animation: expandWidth 0.5s ease forwards;\r\n        overflow: hidden;\r\n\r\n        .input-icon {\r\n          position: absolute;\r\n          left: 0;\r\n          top: 0;\r\n          bottom: 0;\r\n          width: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          background: #f0f0f0;\r\n          border-top-left-radius: 8px;\r\n          border-bottom-left-radius: 8px;\r\n          cursor: pointer;\r\n\r\n          img {\r\n            width: 16px;\r\n            height: 16px;\r\n          }\r\n        }\r\n\r\n        .open-ai-key-input {\r\n          width: 100%;\r\n          padding: 10px 12px 10px 48px;\r\n          border: 1px solid #ccc;\r\n          border-radius: 8px;\r\n          outline: none;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /* Error state */\r\n  &.open-ai-key-error {\r\n    .open-ai-key-input-wrapper {\r\n      .input-icon {\r\n        background: #FF616147 !important; // semi-transparent red\r\n      }\r\n\r\n      .open-ai-key-input {\r\n        border: 1px solid #FF4A4A !important; // red border\r\n      }\r\n    }\r\n  }\r\n\r\n  /* Submitted state */\r\n  &.open-ai-key-submitted {\r\n    .open-ai-key-box-icon {\r\n      border: 1px solid #5DE86B !important;\r\n      background: #E5F8E7 !important;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes expandWidth {\r\n  from {\r\n    width: 0;\r\n  }\r\n  to {\r\n    width: 320px;\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.qadpt-content-box {\r\n\t\t\tpadding-bottom: 20px;\r\n\t\t\tmargin: 20px 0 10px;\r\n\t\t\t.qadpt-src-flt {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tplace-content: end;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t.qadpt-teamsearch {\r\n\t\t\t\t\twidth: 210px;\r\n\t\t\t\t\tright: 10px;\r\n\t\t\t\t\ttop: -14px;\r\n\t\t\t\t\t.MuiFormHelperText-root {\r\n\t\t\t\t\t\tcolor: inherit;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.MuiOutlinedInput-root {\r\n\t\t\t\t\t\tpadding-top: 6px;\r\n\t\t\t\t\t\tborder-radius: 12px;\r\n\t\t\t\t\t\theight: 40px;\r\n\r\n\t\t\t\t\t\t&.Mui-focused .MuiOutlinedInput-notchedOutline {\r\n\t\t\t\t\t\t\tborder: 1px solid #ccc; /* Set your preferred border color here */\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.MuiOutlinedInput-notchedOutline {\r\n\t\t\t\t\t\theight: 43px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.MuiInputBase-input {\r\n\t\t\t\t\t\theight: 1em;\r\n\t\t\t\t\t\tpadding-left: 0px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbutton {\r\n\t\t\t\t\t\tpadding: 0px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.qadpt-input-field-error {\r\n\t\t\t\t\t.MuiFormHelperText-root {\r\n\t\t\t\t\t\tcolor: var(--error-color);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.qadpt-usrfilter button {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 0 8px;\r\n\t\t\t\tborder: 1px solid var(--border-color);\r\n\t\t\t\tborder-radius: 10px;\r\n\t\t\t\ttop: -10px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\t\t.qadpt-grd-head {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tfloat: right;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tplace-content: flex-end;\r\n\t\t\t\tmargin-bottom: 10px;\r\n\t\t\t\t.slt-acc-drp {\r\n\t\t\t\t\twidth: 210px;\r\n\t\t\t\t\tmargin: -7px 10px;\r\n\t\t\t\t\tpadding: 0 !important;\r\n\t\t\t\t\t&.MuiBox-root {\r\n\t\t\t\t\t\tpadding: 0 !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.MuiOutlinedInput-input {\r\n\t\t\t\t\t\t// padding: 5px 15px !important;\r\n\t\t\t\t\t\twidth: 210px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.MuiOutlinedInput-notchedOutline {\r\n\t\t\t\t\t\tborder-radius: 10px !important;\r\n\t\t\t\t\t\theight: 42px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.MuiInputLabel-outlined {\r\n\t\t\t\t\t\ttop: -9px !important;\r\n\t\t\t\t\t\tpadding: 6px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.MuiAutocomplete-inputRoot {\r\n\t\t\t\t\t\theight: 42px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.MuiSelect-icon {\r\n\t\t\t\t\t\ttop: 10px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.MuiInputLabel-outlined.MuiInputLabel-shrink {\r\n\t\t\t\t\t\ttop: 2px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.qadpt-usrfilter button {\r\n\t\t\t\t\ttop: -4px !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.qadpt-userButton {\r\n\tposition: relative;\r\n\tright: 0;\r\n\ttop: 10px;\r\n}\r\n\r\n/* create userpop start */\r\n.qadpt-usercreatepopup,\r\n.qadpt-userEditpopup {\r\n\tborder: 0 !important;\r\n\tmargin: 0 !important;\r\n\twidth: 400px !important;\r\n\tmax-height: 580px;\r\n\tmin-height: 400px;\r\n\ttop: 70px;\r\n\tright: 460px;\r\n\tposition: fixed !important;\r\n\tdisplay: block !important;\r\n\tbackground: var(--white-color) !important;\r\n\tbox-shadow: 0 3px 8px #000000;\r\n\tborder-radius: 4px;\r\n\t.qadpt-title-sec {\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid var(--border-color);\r\n\t\t.qadpt-title {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 18px;\r\n\t\t\tline-height: 20px;\r\n\t\t}\r\n\t\t.qadpt-description {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tline-height: 18px;\r\n\t\t}\r\n\t}\r\n\t.qadpt-formcontent {\r\n\t\twidth: 100% !important;\r\n\t\tmax-height: calc(100vh - 200px);\r\n\t\toverflow: hidden auto;\r\n\t\tfont-size: 14px;\r\n\t\theight: 353px;\r\n\t\t.qadpt-usrname {\r\n\t\t\tdisplay: flex;\r\n\t\t\t> div {\r\n\t\t\t\twidth: 50%;\r\n\t\t\t}\r\n\t\t\t.qadpt-userfields {\r\n\t\t\t\t.MuiFormHelperText-root {\r\n\t\t\t\t\twidth: 150px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tdiv:nth-child(2) {\r\n\t\t\t\tmargin-left: 10px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.MuiGrid-root {\r\n\t\t\tpadding-top: 4px;\r\n\t\t}\r\n\t}\r\n\t.qadpt-userfields {\r\n\t\twidth: 100%;\r\n\t\tmargin-bottom: -2px;\r\n\r\n\t\t.MuiFormHelperText-root {\r\n\t\t\t&.error {\r\n\t\t\t\tcolor: var(--error-color) !important;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.MuiSvgIcon-root {\r\n\t\t\theight: 16px;\r\n\t\t\twidth: 16px;\r\n\t\t}\r\n\t}\r\n\t.qadpt-genderbuttons {\r\n\t\theight: 40px;\r\n\t\tgap: 7px;\r\n\t\tmargin: 0 -7px;\r\n\t\t.MuiButton-root {\r\n\t\t\tmin-width: 100px;\r\n\t\t\tcolor: var(--button-bg-color);\r\n\t\t\tborder: 1px solid var(--button-bg-color) !important;\r\n\t\t\tfont-size: 12px;\r\n\t\t\twhite-space: nowrap;\r\n\t\t\tborder-radius: 14px;\r\n\t\t\ttext-transform: capitalize;\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-color: var(--button-bg-color);\r\n\t\t\t\tcolor: var(--white-color);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.selected {\r\n\t\t\tbackground-color: var(--button-bg-color);\r\n\t\t\tcolor: var(--white-color);\r\n\t\t}\r\n\t}\r\n\t.qadpt-submitbutton {\r\n\t\tpadding: 15px;\r\n\t\twidth: 100%;\r\n\t\ttext-align: end;\r\n\t\tborder-top: 1px solid var(--bordaer-color);\r\n\t\tbutton {\r\n\t\t\tcolor: var(--white-color);\r\n\t\t\tborder-radius: 4px;\r\n\t\t\tborder: 1px solid var(--button-bg-color);\r\n\t\t\tbackground-color: var(--button-bg-color);\r\n\t\t\ttext-transform: capitalize;\r\n\t\t\tpadding: var(--button-padding) !important;\r\n\t\t\tline-height: var(--button-lineheight) !important;\r\n\t\t\t// &:hover {\r\n\t\t\t// \tcolor: white;\r\n\t\t\t// \tbackground-color: var(--button-bg-color);\r\n\t\t\t// }\r\n\t\t}\r\n\t}\r\n}\r\n/*create userpop ends */\r\n", ".qadpt-account {\r\n\tmargin-right: 6px;\r\n\tbackground-color: var(--white-color);\r\n\tborder-radius: 6px;\r\n\theight: calc(100vh - 90px);\r\n\r\n\t&.sidebaropen {\r\n\t\tmargin-left: 190px;\r\n\t}\r\n\t.qadpt-accttitles {\r\n\t\ttop: 1px;\r\n\t\tposition: relative;\r\n\t\tleft: -420px;\r\n\t\th1 {\r\n\t\t\tposition: relative;\r\n\t\t\tright: 28px;\r\n\t\t}\r\n\t\t.qadpt-hdrsideon {\r\n\t\t\ttop: -15px;\r\n\t\t\tposition: relative;\r\n\t\t\tleft: 440px;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t.qadpt-hdrsideoff {\r\n\t\t\ttop: -15px;\r\n\t\t\tposition: relative;\r\n\t\t\tleft: 50px;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t}\r\n}\r\n.accountlistGrid {\r\n\theight: calc(100vh - 195px);\r\n\twidth: calc(100% - 20px);\r\n\tmargin-top: 2px;\r\n\ttop: -17px;\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\r\n\t&.sidebarOpen {\r\n\t\tmargin-left: 12px;\r\n\t}\r\n\r\n\t&.sidebarClosed {\r\n\t\tmargin-left: 1px;\r\n\t}\r\n\r\n\t.qadpt-searchfilter {\r\n\t\tright: 20px;\r\n\t\tfloat: right;\r\n\t\twidth: 200px;\r\n\t\tmargin-bottom: 8px !important;\r\n\r\n\t\tinput {\r\n\t\t\tpadding: 10px 14px;\r\n\t\t}\r\n\r\n\t\t// Combine sx styles here\r\n\t\t.MuiFormHelperText-root {\r\n\t\t\tcolor: inherit;\r\n\r\n\t\t\t// Change to red if there are errors\r\n\t\t\t&.error {\r\n\t\t\t\tcolor: var(--error-color);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.MuiOutlinedInput-root {\r\n\t\t\tpadding: 0px;\r\n\t\t\tborder-radius: 20px;\r\n\t\t}\r\n\r\n\t\t.MuiInputBase-input {\r\n\t\t\theight: 1em;\r\n\t\t\tpadding-left: 0px;\r\n\t\t}\r\n\t}\r\n}\r\n.qadpt-accountCreateButtonPosition {\r\n\ttop: -163px;\r\n\tposition: relative;\r\n\t//left: -350px;\r\n}\r\n\r\n.qadpt-accountcreatepopup,\r\n.qadpt-accounteditpopup {\r\n\tz-index: 99;\r\n\twidth: 400px !important;\r\n\tmax-height: 350px;\r\n\ttop: 200px;\r\n\tright: 30%;\r\n\tposition: fixed !important;\r\n\tbackground: var(--white-color) !important;\r\n\tbox-shadow: 0 3px 8px #000000;\r\n\tborder-radius: 4px;\r\n\t.qadpt-title-sec {\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid var(--border-color);\r\n\t\t.qadpt-title {\r\n\t\t\tfont-size: 18px;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n\r\n\t.qadpt-accountcreatefield {\r\n\t\tposition: relative;\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 600;\r\n\t\tmargin: 16px 16px 0;\r\n\t\t&.qadpt-error {\r\n\t\t\tcolor: var(--error-color);\r\n\t\t}\r\n\t\t.qadpt-acctfield {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\t.MuiFormHelperText-root {\r\n\t\t\t\tcolor: var(--error-color);\r\n\t\t\t\tline-height: 12px;\r\n\t\t\t}\r\n\t\t\t.MuiInputBase-root-MuiOutlinedInput-root {\r\n\t\t\t\tborder-radius: 4px !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tform {\r\n\t\theight: calc(100% - 100px);\r\n\t}\r\n\t.qadpt-account-buttons {\r\n\t\tpadding: 15px;\r\n\t\tborder-top: 1px solid var(--border-color);\r\n\t\ttext-align: end;\r\n\t\t.qadpt-save-btn {\r\n\t\t\tbackground-color: var(--button-bg-color);\r\n\t\t\tcolor: var(--white-color);\r\n\t\t\ttext-transform: capitalize;\r\n\t\t\tpadding: var(--button-padding) !important;\r\n\t\t\tline-height: var(--button-lineheight) !important;\r\n\t\t\t&.invalid {\r\n\t\t\t\tbackground-color: #a5c3c5;\r\n\t\t\t\tpointer-events: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.qadpt-roleeditpopup,\r\n.qadpt-roledeletepopup {\r\n\tz-index: 9;\r\n\twidth: 500px !important;\r\n\t// padding: 20px;\r\n\tposition: fixed !important;\r\n\tbackground: var(--white-color) !important;\r\n\tbox-shadow: 0 3px 8px #000000;\r\n\tborder-radius: 4px;\r\n\r\n\t&.qadpt-roleeditpopup {\r\n\t\tmax-height: 500px;\r\n\t\ttop: 140px;\r\n\t\tright: 420px;\r\n\t}\r\n\r\n\t&.qadpt-roledeletepopup {\r\n\t\twidth: 395px !important;\r\n\t\theight: 23%;\r\n\t\ttop: 252px;\r\n\t\tright: 30%;\r\n\t\tpadding: 10px;\r\n\t}\r\n\t.qadpt-title-sec {\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid var(--border-color);\r\n\t\t.qadpt-title {\r\n\t\t\tfont-size: 18px;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tmargin-top: 0;\r\n\t\t}\r\n\t}\r\n\r\n\t.qadpt-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tpadding: 10px 20px 20px 5px;\r\n\t}\r\n\r\n\t.qadpt-addrole {\r\n\t\theight: calc(100% - 75px);\r\n\t\toverflow: auto;\r\n\t\tpadding: 0 15px 15px 15px;\r\n\t\t.MuiGrid-container {\r\n\t\t\tmargin-top: 0 !important;\r\n\t\t}\r\n\t\t.MuiGrid-item .MuiInputLabel-outlined.MuiInputLabel-shrink {\r\n\t\t\ttop: 7px !important;\r\n\t\t}\r\n\t}\r\n\r\n\t.qadpt-role-buttons {\r\n\t\tposition: relative;\r\n\t\tbottom: 0;\r\n\t\ttext-align: right;\r\n\t\tpadding: 15px;\r\n\t\tborder-top: 1px solid var(--border-color);\r\n\r\n\t\tbutton {\r\n\t\t\tmargin-left: 16px;\r\n\t\t\tborder-radius: 4px;\r\n\t\t\tborder: 1px solid var(--button-bg-color);\r\n\t\t\tcolor: var(--button-bg-color);\r\n\t\t\ttext-transform: capitalize;\r\n\t\t\tpadding: var(--button-padding) !important;\r\n\t\t\tline-height: var(--button-lineheight) !important;\r\n\t\t}\r\n\r\n\t\t.qadpt-conform-button {\r\n\t\t\tbackground-color: var(--button-bg-color) !important;\r\n\t\t\tcolor: var(--white-color) !important;\r\n\r\n\t\t\t&.qadpt-disabled {\r\n\t\t\t\tbackground-color: #a5c3c5 !important;\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.qadpt-modal-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\tz-index: 9;\r\n\tpointer-events: auto;\r\n}\r\n\r\n.qadpt-iconcloseaccountcreate {\r\n\tposition: absolute;\r\n\ttop: 20px;\r\n\tcursor: pointer;\r\n\tright: 15px;\r\n\twidth: 15px;\r\n}\r\n.qadpt-accountCreateButtonsdiv {\r\n\tdisplay: flex;\r\n\tgap: 150px;\r\n\tposition: relative;\r\n\ttop: -25px;\r\n\tleft: 60px;\r\n}\r\n.qadpt-accountCreatesaveButtonsdiv {\r\n\tposition: relative;\r\n\tright: -280px;\r\n\ttop: 95px;\r\n\twidth: 80px;\r\n}\r\n\r\n.qadpt-accountcreateButton {\r\n\tposition: absolute;\r\n\tright: 20px;\r\n\tz-index: 99;\r\n\ttop: 55px;\r\n\tbackground-color: var(--button-bg-color);\r\n\tborder: none;\r\n\tborder-radius: 15px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 10px 14px 10px 14px;\r\n\theight: 44px;\r\n\twidth: 183px;\r\n}\r\n.qadpt-buttoncolor {\r\n\tcolor: black;\r\n}\r\n.accountlistGrid {\r\n\theight: calc(100vh - 195px);\r\n\twidth: calc(100% - 20px);\r\n\tmargin-top: 2px;\r\n\ttop: -17px;\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\r\n\t&.sidebarOpen {\r\n\t\tmargin-left: 12px;\r\n\t}\r\n\r\n\t&.sidebarClosed {\r\n\t\tmargin-left: 1px;\r\n\t}\r\n}\r\n.qadpt-editaccounttexthdr {\r\n\tposition: relative;\r\n\tbottom: 60px;\r\n\tright: 100px;\r\n}\r\n.qadpt-editaccountfields {\r\n\tposition: relative;\r\n\tbottom: 70px;\r\n\tright: 10px;\r\n}\r\n.qadpt-editaccountbuttonsposition {\r\n\tposition: relative;\r\n\tbottom: 70px;\r\n\tdisplay: flex;\r\n\tgap: 120px;\r\n}\r\n.qadpt-editaccountcancelbuttons {\r\n\tposition: absolute;\r\n\tright: 300px;\r\n\tz-index: 99;\r\n\ttop: 0px;\r\n\tbackground-color: #007bff;\r\n\tborder: none;\r\n\tborder-radius: 5px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 7px 21px;\r\n}\r\n.qadpt-editaccountsavebuttons {\r\n\tposition: absolute;\r\n\tright: 60px;\r\n\tz-index: 99;\r\n\ttop: 0px;\r\n\tbackground-color: #007bff;\r\n\tborder: none;\r\n\tborder-radius: 10px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 10px 25px;\r\n}\r\n.qadpt-accountalert {\r\n\ttop: 40px !important;\r\n\twidth: 400px;\r\n\tleft: 55% !important;\r\n\tz-index: 999999 !important;\r\n\t.MuiPaper-root.MuiAlert-root {\r\n\t\twidth: 100%;\r\n\t}\r\n}\r\n\r\n.Addiconingrid {\r\n\tcursor: pointer !important;\r\n\ttransition: color 0.2s ease-in-out;\r\n\r\n\t&:hover {\r\n\t\tcolor: #5f9ea0 !important;\r\n\t}\r\n}\r\n", "/* TeamSettings.module.scss */\r\n\r\n.smoothtransition {\r\n\ttransition: margin-left 0.3s ease;\r\n}\r\n\r\n.team-settings {\r\n\tpadding: 20px;\r\n}\r\n.UserRoleTitle {\r\n\tfont-weight: 700;\r\n\tfont-size: 23px;\r\n\tmargin-top: -14px;\r\n}\r\n.Loaderstyles {\r\n\tposition: absolute;\r\n\ttop: 50%;\r\n\tleft: 50%;\r\n\ttransform: translate(-50%, -50%);\r\n\tz-index: 1;\r\n}\r\n.LoaderSpinnerStyles {\r\n\twidth: 50px;\r\n\theight: 50px;\r\n}\r\n\r\n\r\n.selectaccountGlobal {\r\n\ttop: 25px;\r\n\tright: 51px;\r\n\tleft: 567px;\r\n\twidth: 200px;\r\n}\r\n.selectaccountGloballeft {\r\n\ttop: 25px;\r\n\tright: 51px;\r\n\tleft: 581px;\r\n\twidth: 200px;\r\n}\r\n.addUserroleglobal {\r\n\tright: 35px;\r\n\theight: 34px;\r\n\ttop: 27px;\r\n\tfont-size: x-small;\r\n\tleft: 586px;\r\n}\r\n.addUserrolegloballeft {\r\n\tright: 35px;\r\n\theight: 34px;\r\n\ttop: 27px;\r\n\tfont-size: x-small;\r\n\tleft: 611px;\r\n}\r\n\r\n.header {\r\n\tbackground-color: #a3c2c2;\r\n}\r\n\r\n.searchContainer {\r\n\tdisplay: flex;\r\n\tjustify-content: flex-end;\r\n\tmargin-bottom: 16px;\r\n}\r\n\r\n.selectAccount {\r\n\tmax-width: 200px;\r\n\tright: 51px;\r\n}\r\n.MuiOutlinedInput-input {\r\n\tpadding: 0;\r\n\tmargin-top: 5px;\r\n\tmargin-left: 31px;\r\n}\r\n\r\n.MuiOutlinedInput-notchedOutline {\r\n\tborder-radius: 5px;\r\n\theight: 35px;\r\n}\r\n\r\n.MuiInputLabel-outlined {\r\n\ttop: -6px !important;\r\n\t&.MuiInputLabel-shrink {\r\n\t\ttop: 0 !important;\r\n\t}\r\n}\r\n\r\n.MuiAutocomplete-inputRoot {\r\n\theight: 35px;\r\n}\r\n\r\n.addUserRoleButton {\r\n\tright: 35px;\r\n\theight: 34px;\r\n\ttop: 13px;\r\n\tfont-size: x-small;\r\n\r\n\t.MuiSvgIcon-root {\r\n\t\tmargin-right: 8px;\r\n\t}\r\n}\r\n\r\n.dataGridContainer {\r\n\theight: 344px;\r\n\twidth: 100%;\r\n\tmargin-top: 16px;\r\n\r\n\t.header {\r\n\t\tbackground-color: #a3c2c2;\r\n\t}\r\n}\r\n/* setting styles starts*/\r\n.qadpt-setsidebar {\r\n\ttop: 20px;\r\n\tposition: relative;\r\n\tpadding: 10px 20px;\r\n\r\n\t// &.sidebar-open {\r\n\t// \tmargin-left: 255px;\r\n\t// }\r\n\r\n\t// &.sidebar-closed {\r\n\t// \tmargin-left: 39px;\r\n\t// }\r\n\t.qadpt-sidebarlist {\r\n\t\tfont-size: 12px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #8a8a8a;\r\n\t\ttext-transform: uppercase;\r\n\t}\r\n\t// .qadpt-sidebarele {\r\n\t// \twidth: 110px;\r\n\t// \tgap: 4px;\r\n\t// }\r\n\t.qadpt-sidebarinput {\r\n\t\tpadding: 3px 22px;\r\n\t\tmargin-bottom: 4px;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground-color: transparent;\r\n\r\n\t\t&.active {\r\n\t\t\tbackground-color: rgba(95, 158, 160, 1) !important;\r\n\t\t\tcolor: #ffffff !important;\r\n\t\t\t.MuiTypography-root {\r\n\t\t\t\tcolor: #ffffff !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.08);\r\n\t\t}\r\n\t}\r\n\t.qadpt-sidebarval {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 400;\r\n\t\tline-height: 21px;\r\n\t\tletter-spacing: 0.3px;\r\n\t\ttext-align: left;\r\n\t\tmargin-left: -2px;\r\n\t\tcolor: #202224;\r\n\r\n\t\t&.active {\r\n\t\t\tcolor: #ffffff !important;\r\n\t\t}\r\n\t}\r\n}\r\n/* setting styles ends */\r\n\r\n/* uninstall page starts */\r\n.qadpt-unistpg{\r\n\tdisplay: flex !important;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tbackground-color: #f9f1f0;\r\n\tmargin: 20px;\r\n    padding: 0;\r\n    max-width: calc(100% - 40px) !important;\r\n    border-radius: 20px;\r\n    height: calc(100vh - 40px);\r\n\r\n\t.qadpt-brand-logo {\r\n\t\tmargin: 40px;\r\n\t\t.qadpt-brand-logo-img {\r\n\t\t\twidth: 300px;\r\n\t\t\theight: auto;\r\n\t\t}\r\n\t}\r\n\t.qadpt-mgs{\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 50px;\r\n\t\t> div:first-child{\r\n\t\t\tcolor: #222222;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 20px;\r\n\t\t\tmargin-bottom: 10px;\r\n\t\t}\r\n\t\t> div:nth-child(2){\r\n\t\t\tfont-size: 16px;\r\n\t\t\tmargin-bottom: 3px;\r\n\t\t}\r\n\t\t> div:nth-child(3){\r\n\t\t\tfont-size: 16px;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tplace-content: center;\r\n\t\t\timg{\r\n\t\t\t\tpadding: 0 4px;\r\n\t\t\t}\r\n\t\t\ta{\r\n\t\t\t\tcolor: var(--button-bg-color);\r\n\t\t\t\tpadding-right: 4px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.qadpt-feedbkfrm{\r\n\t\tmax-width: 500px;\r\n\t\tmargin-top: 20px;\r\n\t\tdiv{\r\n\t\t\tmargin: 0 0 5px 0  !important;\r\n\t\t}\r\n\t\ttextarea{\r\n\t\t\twidth: -webkit-fill-available;\r\n        \tpadding: 10px;\r\n            font-size: 16px;\r\n            border-radius: 8px;\r\n            border: 1px solid #ddd;\r\n        \tmargin-bottom: 20px;\r\n            resize: none;\r\n            outline: none;\r\n\t\t}\r\n\t}\r\n\t.qadpt-btn  {\r\n\t\tpadding: 10px 20px;\r\n\t\tbackground-color: #5f9ea0;\r\n\t\twidth: 100%;\r\n\t\tcolor: white;\r\n\t\tborder: none;\r\n\t\tborder-radius: 15px;\r\n\t\tfont-size: 16px;\r\n\t\tcursor: pointer;\r\n\t\ttransition: background-color 0.3s ease;\r\n\t  }\r\n\t  .qadpt-thkpg{\r\n\t\ttext-align: center;\r\n    margin-top: 40px;\r\n\t\t.qadpt-thkmsg{\r\n\t\t\tdisplay: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    place-content: center;\r\n    margin-top: 20px;\r\n\t> div:nth-child(1){\r\n\t\tfont-weight: 600;\r\n\t\tfont-size: 20px;\r\n\t}\r\n\t> div:nth-child(2){\r\n\t\tfont-size: 16px;\r\n\t\twidth: calc(100% - 180px);\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 8px;\r\n\t}\r\n\t\t}\r\n\t  }\r\n}\r\n/* uninstall page ends */", ".userEditpopup h1 {\r\n\tbackground-image: linear-gradient(to right, rgb(37, 81, 181), rgba(0, 0, 0, 0.7));\r\n\tfont-size: 17px;\r\n\tcolor: var(--white-color);\r\n\theight: 44px;\r\n\tpadding: 5px 18px;\r\n\tmargin: 6px -194px 17px;\r\n\ttext-align: center;\r\n\twidth: 340px;\r\n\tmargin-right: 101px;\r\n\tmargin-left: -21px;\r\n}\r\n.userEditpopup {\r\n\tz-index: 16000;\r\n\twidth: 334px !important;\r\n\theight: 35vh;\r\n\ttop: 223px;\r\n\tright: 683px;\r\n\tpadding: 0px 20px;\r\n\tposition: fixed !important;\r\n\tdisplay: block !important;\r\n\tbackground: var(--white-color) !important;\r\n\tbox-shadow: 0 3px 4px #000000;\r\n}\r\n.cancel {\r\n\tposition: relative;\r\n\tbackground: rgb(255, 255, 255);\r\n\tcolor: rgb(0, 123, 255);\r\n\tborder: 1px solid rgb(0, 123, 255);\r\n\tmargin-right: 173px;\r\n\tright: -71px;\r\n\ttop: 64px;\r\n\tposition: absolute;\r\n\tright: 37px;\r\n\tz-index: 99;\r\n\ttop: 142px;\r\n\r\n\tborder-radius: 5px;\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 7px 21px;\r\n}\r\n.deactivate {\r\n\tposition: relative;\r\n\tright: -147px;\r\n\ttop: 65px;\r\n\tposition: absolute;\r\n\tright: 37px;\r\n\tz-index: 99;\r\n\ttop: 67px;\r\n\tbackground-color: #007bff;\r\n\tborder: none;\r\n\tborder-radius: 5px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 7px 21px;\r\n}\r\n.userButton {\r\n\tposition: absolute;\r\n\tz-index: 99;\r\n\tbackground-color: #007bff;\r\n\tborder: none;\r\n\tborder-radius: 5px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 7px 21px;\r\n\tposition: relative;\r\n\tright: -147px;\r\n\ttop: 66px;\r\n}\r\n\r\n.createButton {\r\n\tposition: absolute;\r\n\tright: 37px;\r\n\tz-index: 99;\r\n\ttop: 67px;\r\n\tbackground-color: rgb(95, 158, 160);\r\n\tborder: none;\r\n\tborder-radius: 5px;\r\n\tcolor: var(--white-color);\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\tpadding: 7px 21px;\r\n}\r\n.qadpt-org-filter {\r\n\t\r\n\t// .MuiPaper-root.MuiAutocomplete-paper {\r\n\t// \tmargin-top: 70px;\r\n\t// \tleft: 40px;\r\n\t// }\r\n\t.MuiFormControl-root{\r\n\r\n\t .MuiFormLabel-root{\r\n\t\tpadding-left: 5px;\r\n\t\t\r\n\t\t&.MuiInputLabel-shrink {\r\n\t\t\tleft: 6px;\r\n\t\t\ttop: 9px !important;\t\t\r\n\t\t}\r\n\t}\r\n\t.MuiInputBase-root.Mui-focused{\r\n\t\tinput{\r\n\t\t\tpadding-top: 5px !important;\r\n\t\t}\r\n\t\t.MuiAutocomplete-endAdornment{\r\n\t\t\tpadding-top: 5px !important;\r\n\r\n\t\t}\r\n\t}\r\n\tfieldset{\r\n\t\tmargin: 0 10px;\r\n\t}\r\n}\r\n\r\n}\r\n.qadpt-orgcont {\r\n\t.qadpt-head {\r\n\t\tborder-bottom: 1px solid var(--border-color);\r\n\t\tmargin: 0 -20px;\r\n\t\tpadding: 20px 20px 10px 20px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t.qadpt-title-sec {\r\n\t\t\twidth: calc(100% - 200px);\r\n\t\t\t.qadpt-title {\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tfont-size: 22px;\r\n\t\t\t\tline-height: 20px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.qadpt-right-part {\r\n\t\t\ttext-align: right;\r\n\t\t\twidth: 210px;\r\n\t\t\t.MuiInputLabel-outlined.MuiInputLabel-shrink {\r\n\t\t\t\ttop: 7px !important;\r\n\t\t\t}\r\n\t\t\t.MuiAutocomplete-root {\r\n\t\t\t\tpadding: 5px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n.qadpt-org-grd {\r\n\tborder-color: var(--grid-border-color) !important;\r\n\ttop: 10px;\r\n\tposition: relative !important;\r\n\theight: calc(100vh - 127px) !important;\r\n\t.MuiDataGrid-virtualScroller {\r\n\t\theight: calc(100vh - 237px) !important;\r\n\t}\r\n\t.MuiDataGrid-footerContainer .MuiTablePagination-root .MuiInputBase-root .MuiSelect-select.MuiTablePagination-select {\r\n\t\tpadding-right: 24px !important;\r\n\t}\r\n}\r\n\r\n\r\n  ", ".title {\r\n\tfont-family: Syncopate;\r\n\tfont-size: 19px;\r\n\tfont-weight: 700;\r\n\tline-height: 19.78px;\r\n\tletter-spacing: 0.30000001192092896px;\r\n\ttext-align: center;\r\n\tcolor: rgba(95, 158, 160, 1);\r\n}\r\n\r\n.welcomeback {\r\n\tfont-family: Poppins;\r\n\tfont-size: 24px;\r\n\tfont-weight: 700;\r\n\tline-height: 36px;\r\n\tletter-spacing: 0.30000001192092896px;\r\n\ttext-align: center;\r\n\twidth: 188px;\r\n\theight: 36px;\r\n\tgap: 0px;\r\n\topacity: 0px;\r\n\tcolor: rgba(34, 34, 34, 1);\r\n}\r\n\r\n.email {\r\n\tfont-family: Poppins;\r\n\tfont-size: 16px;\r\n\tfont-weight: 400;\r\n\tline-height: 24px;\r\n\ttext-align: center;\r\n\twidth: 44px;\r\n\theight: 24px;\r\n\tgap: 0px;\r\n\topacity: 0px;\r\n\tcolor: rgba(68, 68, 68, 1);\r\n}\r\n\r\n.qadpt-superadminlogin,\r\n.qadpt-resetpassword {\r\n\tdisplay: flex !important;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tbackground-color: #f9f1f0;\r\n\tpadding: 20px;\r\n\tmax-width: 100% !important;\r\n\theight: 100%;\r\n\r\n\t.qadpt-brand-logo {\r\n\t\tmargin: 40px;\r\n\t\t.qadpt-brand-logo-img {\r\n\t\t\twidth: 300px;\r\n\t\t\theight: auto;\r\n\t\t}\r\n\t}\r\n\r\n\t.qadpt-welcome-message {\r\n\t\tmargin-top: 60px;\r\n\t\tmargin-bottom: 20px;\r\n\t\ttext-align: center;\r\n\r\n\t\t.qadpt-welcome-message-text {\r\n\t\t\tfont-family: \"Arial\", sans-serif;\r\n\t\t\tcolor: #333;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 22px;\r\n\t\t}\r\n\t}\r\n\r\n\t.qadpt-login-form {\r\n\t\twidth: 100%;\r\n\t\tmax-width: 300px;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 30px;\r\n\r\n\t\t.qadpt-form-label {\r\n\t\t\ttext-align: left;\r\n\t\t\tcolor: #444;\r\n\t\t\tmargin-top: 10px;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tspan {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.qadpt-custom-input {\r\n\t\t\tborder-radius: 8px !important;\r\n\t\t\tborder: 1px solid #ccc;\r\n\t\t\tmargin-top: 8px;\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\tbackground-color: var(--white-color);\r\n\t\t}\r\n\t\t.qadpt-text-danger {\r\n\t\t\tcolor: #d9534f;\r\n\t\t\tfont-size: 0.9rem;\r\n\t\t}\r\n\r\n\t\t// .qadpt-btn-default {\r\n\t\t// \twidth: 100%;\r\n\t\t// \tbackground-color: rgba(95, 158, 160, 1);\r\n\t\t// \tcolor: white;\r\n\t\t// \tpadding: 10px;\r\n\t\t// \tborder-radius: 15px;\r\n\t\t// \tfont-size: 1rem;\r\n\t\t// \tmargin-top: 30px;\r\n\r\n\t\t// \t&:hover {\r\n\t\t// \t\tbackground-color: rgba(95, 158, 160, 1);\r\n\t\t// \t\tcolor: white !important;\r\n\t\t// \t}\r\n\t\t// }\r\n\r\n\t\t.qadpt-button-text {\r\n\t\t\tfont-family: \"Arial\", sans-serif;\r\n\t\t\tfont-size: 1rem;\r\n\t\t\tfont-weight: 500;\r\n\t\t\ttext-transform: none;\r\n\t\t}\r\n\t\t.MuiOutlinedInput-notchedOutline {\r\n\t\t\tborder: 0 !important;\r\n\t\t}\r\n\t\t.qadpt-passwordhint {\r\n\t\t\twidth: 300px;\r\n\t\t\theight: 50px;\r\n\t\t\tborder-radius: 6px;\r\n\t\t\tborder: 1px solid rgb(220, 221, 225);\r\n\t\t\tmargin-top: 10px;\r\n\r\n\t\t\t.qadpt-passwordhint-text {\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tmargin-left: 13px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t}\r\n\r\n\t\t\t.qadpt-passwordhint-container {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-left: 13px;\r\n\t\t\t\tgap: 10px;\r\n\t\t\t\tsvg {\r\n\t\t\t\t\tfont-size: 18px !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.qadpt-checkicon-valid {\r\n\t\t\t\tcolor: rgba(95, 158, 160, 1);\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t}\r\n\r\n\t\t\t.qadpt-passwordhint-item {\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tline-height: 21px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.qadpt-login-footer {\r\n\t\tposition: absolute;\r\n\t\tbottom: 10px;\r\n\t\tleft: 42%;\r\n\t\tfont-size: 14px;\r\n\t\ttext-align: center;\r\n\r\n\t\t.qadpt-footer-text {\r\n\t\t\tcolor: rgba(95, 158, 160, 1);\r\n\r\n\t\t\t.qadpt-footer-link {\r\n\t\t\t\tcolor: rgba(95, 158, 160, 1);\r\n\t\t\t\ttext-decoration: none;\r\n\t\t\t\tmargin: 0 10px;\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\ttext-decoration: underline;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n.qadpt-resetpassword {\r\n\t.qadpt-pwd-changed {\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 50%;\r\n\t}\r\n\r\n\t.qadpt-pwd-title {\r\n\t\tfont-size: 20px !important;\r\n\t\tfont-weight: 600 !important;\r\n\t}\r\n\r\n\t.qadpt-changed-msg {\r\n\t\twidth: 300px;\r\n\t\tmargin-bottom: -10px !important;\r\n\t}\r\n}\r\n", ".qadpt-feedbackpopup {\r\n\twidth: calc(100vh - 270px);\r\n\theight: calc(100vh - 190px);\r\n\tborder-radius: 8px !important;\r\n\tposition: relative !important;\r\n\tpadding: 20px;\r\n\toverflow: hidden !important;\r\n\tmargin-top: 76px !important;\r\n\r\n\t.qadpt-feedback-header {\r\n\t\tmargin-bottom: 10px;\r\n\t\tdisplay: flex;\r\n\r\n\t\t.qadpt-feedback-title {\r\n\t\t\tfont-size: 18px;\r\n\t\t\tfont-weight: 600;\r\n\r\n\t\t\t.qadpt-feedback-subtitle {\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #c3c3c3;\r\n\t\t\t\tmargin-top: 8px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.qadpt-close-icon {\r\n\t\t\tposition: absolute !important;\r\n\t\t\tright: 15px;\r\n\r\n\t\t\tsvg {\r\n\t\t\t\theight: 16px;\r\n\t\t\t\twidth: 16px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.qadpt-content {\r\n\t\theight: calc(100vh - 240px);\r\n\t\toverflow: auto;\r\n\t\t.qadpt-container {\r\n\t\t\tmargin-top: 6px;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tfont-weight: 400;\r\n\r\n\t\t\t.qadpt-label {\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.qadpt-rating {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-top: 10px;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t.qadpt-rating-stars {\r\n\t\t\t\t.MuiRating-icon {\r\n\t\t\t\t\tfont-size: 25px;\r\n\t\t\t\t\ttransition: none;\r\n\t\t\t\t\tpadding: 0 10px;\r\n\t\t\t\t\tmargin: 0 10px !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.MuiRating-iconHover {\r\n\t\t\t\t\tcolor: rgba(255, 255, 255, 1);\r\n\t\t\t\t\ttransform: none;\r\n\t\t\t\t\ttransition: none;\r\n\t\t\t\t\topacity: 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.MuiRating-iconFilled {\r\n\t\t\t\t\tcolor: rgba(95, 158, 160, 1);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.MuiRating-iconEmpty {\r\n\t\t\t\t\tcolor: rgba(34, 34, 34, 1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.qadpt-textarea {\r\n\t\t\tmargin-top: 10px;\r\n\r\n\t\t\ttextarea {\r\n\t\t\t\theight: 100px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.qadpt-upload-container {\r\n\t\t\tmargin-top: 10px;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder: 1px solid #ccc;\r\n\t\t\tborder-radius: 4px;\r\n\t\t\tpadding: 5px;\r\n\r\n\t\t\t.qadpt-upload-button {\r\n\t\t\t\tbackground-color: white !important;\r\n\t\t\t\tcolor: black !important;\r\n\t\t\t\tbox-shadow: none !important;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 20px;\r\n\t\t\t\ttext-transform: capitalize !important;\r\n\t\t\t\tpadding: 0 !important;\r\n\t\t\t\tfont-weight: 600 !important;\r\n\t\t\t\tfont-size: 12px !important;\r\n\t\t\t}\r\n\r\n\t\t\t.qadpt-upload-description {\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\tfont-size: 11px;\r\n\t\t\t\ttext-transform: capitalize;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.qadpt-file-list {\r\n\t\t\t.qadpt-list-item {\r\n\t\t\t\tborder: 1px solid #ccc;\r\n\t\t\t\tpadding: 2px !important;\r\n\t\t\t\tdisplay: flex !important;\r\n\t\t\t\talign-items: center !important;\r\n\t\t\t\tjustify-content: space-between !important;\r\n\t\t\t\tmargin-top: 5px;\r\n\t\t\t\tborder-radius: 5px;\r\n\t\t\t}\r\n\r\n\t\t\t.qadpt-file-name {\r\n\t\t\t\tmargin: 7px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t}\r\n\r\n\t\t\t.qadpt-file-actions {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tsvg {\r\n\t\t\t\t\theight: 21px;\r\n\t\t\t\t\twidth: 21px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.qadpt-fileerror {\r\n\t\t\t\tcolor: var(--error-color);\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tmargin-top: -10px;\r\n\t\t\t\tmargin-left: 61px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.qadpt-footer {\r\n\t\tbutton {\r\n\t\t\tfloat: right;\r\n\t\t}\r\n\r\n\t\t.qadpt-submit-button {\r\n\t\t\tbackground-color: var(--color-primary-600) !important;\r\n\t\t\tcolor: var(--color-white) !important;\r\n\t\t\tborder-radius: var(--radius-md) !important;\r\n\t\t\ttop: 10px;\r\n\t\t\ttransition: var(--transition-fast);\r\n\t\t\tfont-weight: var(--font-weight-medium);\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-color: var(--color-primary-700) !important;\r\n\t\t\t\tbox-shadow: var(--shadow-md);\r\n\t\t\t}\r\n\r\n\t\t\t&.qadpt-submit-disabled {\r\n\t\t\t\tbackground-color: var(--color-gray-300) !important;\r\n\t\t\t\tcolor: var(--color-gray-500) !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.qadpt-feedback-dialog {\r\n\tpadding: 20px;\r\n\tborder-radius: 8px;\r\n\twidth: 320px;\r\n\ttext-align: center;\r\n\theight: 423px;\r\n\r\n\t.qadpt-dialogcontent {\r\n\t\tposition: relative;\r\n\t\ttop: 40%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\r\n\t\t.qadpt-message {\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 15px;\r\n\t\t}\r\n\r\n\t\t.qadpt-actionbutton {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tplace-content: center;\r\n\r\n\t\t\tbutton {\r\n\t\t\t\tbackground: var(--color-primary-600);\r\n\t\t\t\tcolor: var(--color-white) !important;\r\n\t\t\t\tborder-radius: var(--radius-md);\r\n\t\t\t\ttransition: var(--transition-fast);\r\n\t\t\t\tfont-weight: var(--font-weight-medium);\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground: var(--color-primary-700);\r\n\t\t\t\t\tbox-shadow: var(--shadow-md);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", ".rtl {\r\n\t.qadpt-banner {\r\n\t\tdirection: rtl !important;\r\n\t\t.adapat-banner-right {\r\n\t\t\tright: auto !important;\r\n\t\t\tleft: 9px;\r\n\t\t}\r\n\t}\r\n\t.qadpt-page-content {\r\n\t\t.qadpt-side-menu .qadpt-smenu .MuiDrawer-paper {\r\n\t\t\tdirection: rtl;\r\n\t\t\tfloat: right;\r\n\t\t\tright: 0px;\r\n\t\t\tborder-right: 0;\r\n\t\t\tborder-left: 1px solid var(--border-color);\r\n\t\r\n.qadpt-smenu-list{\r\n\t.qadpt-sm-icon img[alt=\"Banners\"],\r\n.qadpt-sm-icon img[alt=\"Tooltips\"],\r\n.qadpt-sm-icon img[alt=\"Surveys\"] {\r\n  transform: scaleX(-1);\r\n}\r\n}\r\n\t\t}\r\n\t\t.qadpt-settings-content {\r\n\t\t\tleft: 0 !important;\r\n\t\t\tright: 240px;\r\n\t\t}\r\n\t}\r\n\t.qadpt-orgcont {\r\n\t\tright: 20px;\r\n\t\tleft: auto;\r\n\t}\r\n\t.qadpt-homepg,\r\n\t.qadpt-web {\r\n\t\tleft: auto;\r\n\t\tright: 220px;\r\n\t\t.qadpt-webcontent{\r\n\t\t\t.qadpt-setting-title {\r\n\t\t\t.qadpt-back-text{\r\n\t\t\t\tright: 10px;\r\n\t\t\t\tleft: 0 !important;\r\n\t\t\t}\r\n\t\t\t.qadpt-titsec .qadpt-action-btn .MuiButton-icon{\r\n\t\t\t\tmargin-left: 8px !important;\r\n    \t\t\tmargin-right: -4px !important;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.qadpt-set-right {\r\n\t\t\t.qadpt-page-target .qadpt-header-container .qadpt-add-target-btn.save{\r\n\t\t\tmargin-right: 10px;\r\n\t\t\tmargin-left: 0 !important;\r\n\t\t}\r\n\t\t.qadpt-rev-publish{ \r\n\t\t\t.MuiCardContent-root .MuiGrid-root .MuiGrid-item{\r\n\t\t\t\tpadding-right: 16px;\r\n\t\t\t\tpadding-left: 0 !important;\r\n\t\t\t}\r\n\t\t\t.MuiCardContent-root .MuiGrid-root .MuiGrid-item:first-of-type {\r\n\t\t\tpadding-right: 0 !important;\r\n\t\t\tpadding-left: 0 !important;\r\n\t\t\t}\r\n\t\t\t.qadpt-gridleft{\r\n\t\t\tborder-left: 1px solid #ccc;\r\n\t\t\tborder-right: 0 !important;\r\n\t\t}\r\n\t}\r\n\t.qadpt-frequency .qadpt-card fieldset .MuiGrid-root .MuiGrid-item:nth-child(4) label{\r\n\t\tmargin-right: 0px !important; \r\n    margin-left: 16px !important;\r\n\t}\r\n\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.css-5kysay-MuiDataGrid-root .MuiDataGrid-cell--textLeft {\r\n\t\ttext-align: right;\r\n\t}\r\n\t.MuiTablePagination-actions {\r\n\t\t.MuiButtonBase-root .MuiSvgIcon-root {\r\n\t\t\ttransform: rotate(180deg) !important;\r\n\t\t}\r\n\t}\r\n\t.qadpt-orgcont .qadpt-head .qadpt-right-part {\r\n\t\ttext-align: left;\r\n\t}\r\n\t.qadpt-createpopup {\r\n\t\t.qadpt-closeicon {\r\n\t\t\tleft: 15px;\r\n\t\t\tright: auto;\r\n\t\t}\r\n\t}\r\n\t.MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--vertical {\r\n\t\tright: auto;\r\n\t\tleft: 0;\r\n\t}\r\n\t.qadpt-page-content .qadpt-side-menu {\r\n\t\tright: 10px;\r\n\t\tleft: auto;\r\n\t}\r\n\t.qadpt-feedbackpopup {\r\n\t\t.character-count {\r\n\t\t\ttext-align: left !important;\r\n\t\t}\r\n\t\t.qadpt-upload-button {\r\n\t\t\t.css-1d6wzja-MuiButton-startIcon {\r\n\t\t\t\tmargin: 0 8px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.qadpt-rating-stars.css-1qqgbpl-MuiRating-root {\r\n\t\t\ttext-align: right !important;\r\n\t\t}\r\n\t\t.qadpt-feedback-header .qadpt-close-icon {\r\n\t\t\tleft: 15px;\r\n\t\t\tright: auto !important;\r\n\t\t}\r\n\t}\r\n\t.confirm-actions {\r\n\t\tgap: 5px;\r\n\t}\r\n\t.qadpt-prfsidebar {\r\n\t\t.qadpt-prftitle {\r\n\t\t\ttext-align: right !important;\r\n\t\t\tfloat: right;\r\n\t\t\tmargin-right: 10px !important;\r\n\t\t\tmargin-left: auto !important;\r\n\t\t}\r\n\t}\r\n\t.qadpt-prfbox .qadpt-txtctrl {\r\n\t\t.qadpt-prffldval .MuiOutlinedInput-root input {\r\n\t\t\ttext-align: right !important;\r\n\t\t}\r\n\t\t&.gender-fld {\r\n\t\t\t.css-hfutr2-MuiSvgIcon-root-MuiSelect-icon {\r\n\t\t\t\tright: auto !important;\r\n\t\t\t\tleft: 8px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.qadpt-crossIcon {\r\n\t\tfloat: left !important;\r\n\t}\r\n\t.css-nswfot-MuiDialogActions-root {\r\n\t\tgap: 5px !important;\r\n\t}\r\n.qadpt-midpart .qadpt-content-block {\r\n\t.slt-acc-drp,.qadpt-filter-left .qadpt-select-form:nth-of-type(2){\r\n\t\tfieldset{\r\n\t\t\t\ttext-align: right !important;\r\n\t\t\t}\r\n\t\t\t .MuiInputLabel-root {\r\n      right: 21px;\r\n    \tleft: auto;\r\n\t\t &:not(.qadpt-filter-left .qadpt-select-form:nth-of-type(2) .MuiInputLabel-root).MuiInputLabel-shrink {\r\n      right: -4px !important;\r\n    }\r\n      }\r\n\t}\r\n\t.qadpt-head .qadpt-right-part {\r\n\t\ttext-align: left !important;\r\n\t}\r\n\r\n\r\n\t.qadpt-content-box .qadpt-src-flt .qadpt-teamsearch {\r\n\t\tright: auto !important;\r\n\t\tleft: 10px;\r\n\t}\r\n\r\n\r\n\t.qadpt-account-grd {\r\n\t\t.MuiDataGrid-root .MuiChip-deleteIcon {\r\n\t\t\tmargin: 0 -6px 0 5px !important;\r\n\r\n\t\t}\r\n\t}\r\n\r\n\t.grid-toolbar-options .left-options {\r\n\r\n\t\t.drp-fields,\r\n\t\t.dt-fields {\r\n\t\t\tmargin-left: 15px;\r\n\t\t\tmargin-right: 0 !important;\r\n\t\t}\r\n\t\t.drp-fields{\r\n\t\t\t.MuiAutocomplete-endAdornment{\r\n\t\t\t\t      left: 9px;\r\n    right: auto;\r\n\t\t\t}\r\n\t\t}\r\n\t\t .drp-fields, .dt-fields ,.name-fld{\r\n\t\t\tfieldset{\r\n\t\t\t\ttext-align: right !important;\r\n\t\t\t}\r\n\t\t\t .MuiInputLabel-root {\r\n      right: 21px;\r\n    \tleft: auto;\r\n\t\t&.MuiInputLabel-shrink{\r\n\t\t\tright: 10px !important;\r\n\t\t}\r\n      }\r\n\t}\r\n\r\n\t\t.qadpt-DateTime.dt-fld2 {\r\n\t\t\tmargin-left: 15px;\r\n\t\t\tmargin-right: 0 !important;\r\n\t\t}\r\n\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t\t.MuiSelect-icon {\r\n\t\t\t\t\t\t\t\t\tright: auto;\r\n\t\t\t\t\t\t\t\t\tleft: 7px;\r\n\t\t\t\t\t\t\t\t}\r\n\t\r\n.qadpt-closeicon{\r\n    left: 15px;\r\n\tright : auto !important\r\n}\r\n.qadpt-mngpwd-popup .MuiDialogActions-root button:last-child{\r\n\tmargin-right: 8px;\r\n\tmargin-left: 0 !important;\r\n\r\n}\r\n.qadpt-trainpop .qadpt-uplddoc{\r\n\t.MuiButton-icon{\r\n\t\tmargin-left: 8px !important;\r\n   \t margin-right: 0px !important;\r\n\t}\r\n}\r\n.qadpt-usercreatepopup .qadpt-formcontent .qadpt-usrname div:nth-child(2){\r\n\t    margin-right: 10px;\r\n\t\tmargin-left: 0 !important;\r\n}\r\n.qadpt-roleeditpopup .qadpt-addrole .MuiGrid-root .MuiGrid-item .MuiFormControl-root{\r\n\t\tfieldset{\r\n\t\t\t\ttext-align: right !important;\r\n\t\t\t}\r\n\t\t\t .MuiInputLabel-root {\r\n      right: 21px;\r\n    \tleft: auto;\r\n\t\t &.MuiInputLabel-shrink {\r\n      right: 4px !important;\r\n    }\r\n      }\r\n\t}\r\n\t.qadpt-roleeditpopup .qadpt-addrole .MuiGrid-root .MuiGrid-item:last-child .MuiFormControl-root .MuiInputLabel-root.MuiInputLabel-shrink {\r\n  right: 18px !important;\r\n\t}\r\n\t.qadpt-rolesfltpopup .MuiDialog-container{\r\n\t.qadpt-title .qadpt-close{\r\n\t\t\r\n\t\t\tleft : 8px;\r\n\t\t\tright: auto !important;\r\n\t}\r\n\t.MuiDialogContent-root .MuiButton-icon{\r\n\t\tmargin-left: 8px !important;\r\n    \tmargin-right: 0px !important;\r\n\r\n\t}\r\n\t}\r\n\t.qadpt-prfpopup .qadpt-logout img{\r\n\t\ttransform: scaleX(-1);\r\n\t}\r\n\r\n\t\t\t\t\t\t\t\t}", "/*\n * Container style\n */\n.ps {\n  overflow: hidden !important;\n  overflow-anchor: none;\n  -ms-overflow-style: none;\n  touch-action: auto;\n  -ms-touch-action: auto;\n}\n\n/*\n * Scrollbar rail styles\n */\n.ps__rail-x {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  height: 15px;\n  /* there must be 'bottom' or 'top' for ps__rail-x */\n  bottom: 0px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-y {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  width: 15px;\n  /* there must be 'right' or 'left' for ps__rail-y */\n  right: 0;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps--active-x > .ps__rail-x,\n.ps--active-y > .ps__rail-y {\n  display: block;\n  background-color: transparent;\n}\n\n.ps:hover > .ps__rail-x,\n.ps:hover > .ps__rail-y,\n.ps--focus > .ps__rail-x,\n.ps--focus > .ps__rail-y,\n.ps--scrolling-x > .ps__rail-x,\n.ps--scrolling-y > .ps__rail-y {\n  opacity: 0.6;\n}\n\n.ps .ps__rail-x:hover,\n.ps .ps__rail-y:hover,\n.ps .ps__rail-x:focus,\n.ps .ps__rail-y:focus,\n.ps .ps__rail-x.ps--clicking,\n.ps .ps__rail-y.ps--clicking {\n  background-color: #eee;\n  opacity: 0.9;\n}\n\n/*\n * Scrollbar thumb styles\n */\n.ps__thumb-x {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, height .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, height .2s ease-in-out;\n  height: 6px;\n  /* there must be 'bottom' for ps__thumb-x */\n  bottom: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__thumb-y {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, width .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, width .2s ease-in-out;\n  width: 6px;\n  /* there must be 'right' for ps__thumb-y */\n  right: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-x:hover > .ps__thumb-x,\n.ps__rail-x:focus > .ps__thumb-x,\n.ps__rail-x.ps--clicking .ps__thumb-x {\n  background-color: #999;\n  height: 11px;\n}\n\n.ps__rail-y:hover > .ps__thumb-y,\n.ps__rail-y:focus > .ps__thumb-y,\n.ps__rail-y.ps--clicking .ps__thumb-y {\n  background-color: #999;\n  width: 11px;\n}\n\n/* MS supports */\n@supports (-ms-overflow-style: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./node_modules/_perfect-scrollbar@1.5.0@perfect-scrollbar/css/perfect-scrollbar.css", "/*\n * Container style\n */\n.ps {\n  overflow: hidden !important;\n  overflow-anchor: none;\n  -ms-overflow-style: none;\n  touch-action: auto;\n  -ms-touch-action: auto;\n}\n\n/*\n * Scrollbar rail styles\n */\n.ps__rail-x {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  height: 15px;\n  /* there must be 'bottom' or 'top' for ps__rail-x */\n  bottom: 0px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-y {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  width: 15px;\n  /* there must be 'right' or 'left' for ps__rail-y */\n  right: 0;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps--active-x > .ps__rail-x,\n.ps--active-y > .ps__rail-y {\n  display: block;\n  background-color: transparent;\n}\n\n.ps:hover > .ps__rail-x,\n.ps:hover > .ps__rail-y,\n.ps--focus > .ps__rail-x,\n.ps--focus > .ps__rail-y,\n.ps--scrolling-x > .ps__rail-x,\n.ps--scrolling-y > .ps__rail-y {\n  opacity: 0.6;\n}\n\n.ps .ps__rail-x:hover,\n.ps .ps__rail-y:hover,\n.ps .ps__rail-x:focus,\n.ps .ps__rail-y:focus,\n.ps .ps__rail-x.ps--clicking,\n.ps .ps__rail-y.ps--clicking {\n  background-color: #eee;\n  opacity: 0.9;\n}\n\n/*\n * Scrollbar thumb styles\n */\n.ps__thumb-x {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, height .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, height .2s ease-in-out;\n  height: 6px;\n  /* there must be 'bottom' for ps__thumb-x */\n  bottom: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__thumb-y {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, width .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, width .2s ease-in-out;\n  width: 6px;\n  /* there must be 'right' for ps__thumb-y */\n  right: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-x:hover > .ps__thumb-x,\n.ps__rail-x:focus > .ps__thumb-x,\n.ps__rail-x.ps--clicking .ps__thumb-x {\n  background-color: #999;\n  height: 11px;\n}\n\n.ps__rail-y:hover > .ps__thumb-y,\n.ps__rail-y:focus > .ps__thumb-y,\n.ps__rail-y.ps--clicking .ps__thumb-y {\n  background-color: #999;\n  width: 11px;\n}\n\n/* MS supports */\n@supports (-ms-overflow-style: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n.scrollbar-container {\n  position: relative;\n  height: 100%; }\n\n", "@import '../node_modules/perfect-scrollbar/css/perfect-scrollbar.css';\n\n.scrollbar-container {\n  position: relative;\n  height: 100%;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/styles.scss"], "names": [], "sourceRoot": ""}