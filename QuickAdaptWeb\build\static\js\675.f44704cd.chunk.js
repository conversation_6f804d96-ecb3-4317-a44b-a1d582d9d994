"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[675],{675:(e,t,o)=>{o.r(t),o.d(t,{default:()=>G});var a=o(5043),r=o(4117),n=(o(6094),o(9155),o(6064),o(585),o(4605),o(7392)),i=(o(8446),o(2518)),s=(o(5424),o(2143),o(6446)),l=o(1081),c=o(6600),d=o(5316),p=o(9347),u=o(2110),m=o(8587),h=o(8168),x=o(9292),g=o(8610),y=o(8206),f=o(4535),v=o(2532),A=o(2372);function w(e){return(0,A.Ay)("MuiCardMedia",e)}(0,v.A)("MuiCardMedia",["root","media","img"]);var j=o(579);const b=["children","className","component","image","src","style"],C=(0,f.Ay)("div",{name:"MuiCardMedia",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e,{isMediaComponent:a,isImageComponent:r}=o;return[t.root,a&&t.media,r&&t.img]}})((e=>{let{ownerState:t}=e;return(0,h.A)({display:"block",backgroundSize:"cover",backgroundRepeat:"no-repeat",backgroundPosition:"center"},t.isMediaComponent&&{width:"100%"},t.isImageComponent&&{objectFit:"cover"})})),S=["video","audio","picture","iframe","img"],I=["picture","img"],M=a.forwardRef((function(e,t){const o=(0,y.b)({props:e,name:"MuiCardMedia"}),{children:a,className:r,component:n="div",image:i,src:s,style:l}=o,c=(0,m.A)(o,b),d=-1!==S.indexOf(n),p=!d&&i?(0,h.A)({backgroundImage:'url("'.concat(i,'")')},l):l,u=(0,h.A)({},o,{component:n,isMediaComponent:d,isImageComponent:-1!==I.indexOf(n)}),f=(e=>{const{classes:t,isMediaComponent:o,isImageComponent:a}=e,r={root:["root",o&&"media",a&&"img"]};return(0,g.A)(r,w,t)})(u);return(0,j.jsx)(C,(0,h.A)({className:(0,x.A)(f.root,r),as:n,role:!d&&i?"img":void 0,ref:t,style:p,ownerState:u,src:d?i||s:void 0},c,{children:a}))}));function k(e){return(0,A.Ay)("MuiCardActions",e)}(0,v.A)("MuiCardActions",["root","spacing"]);const F=["disableSpacing","className"],N=(0,f.Ay)("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,!o.disableSpacing&&t.spacing]}})((e=>{let{ownerState:t}=e;return(0,h.A)({display:"flex",alignItems:"center",padding:8},!t.disableSpacing&&{"& > :not(style) ~ :not(style)":{marginLeft:8}})})),D=a.forwardRef((function(e,t){const o=(0,y.b)({props:e,name:"MuiCardActions"}),{disableSpacing:a=!1,className:r}=o,n=(0,m.A)(o,F),i=(0,h.A)({},o,{disableSpacing:a}),s=(e=>{const{classes:t,disableSpacing:o}=e,a={root:["root",!o&&"spacing"]};return(0,g.A)(a,k,t)})(i);return(0,j.jsx)(N,(0,h.A)({className:(0,x.A)(s.root,r),ownerState:i,ref:t},n))}));var R=o(5865),U=o(7739),H=o(1245),z=o(3158),E=o(7254),L=(o(3560),o(9662));(0,L.A)((0,j.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete"),(0,L.A)((0,j.jsx)("path",{d:"M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7zm-6 .67 2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"}),"SaveAlt");o(8462);var T=o(2102),B=o(5335);var P=o(7587),V=o(2505),W=o(4379);const G=()=>{const[e,t]=(0,a.useState)([]),[o,m]=(0,a.useState)(!1),[h,x]=(0,a.useState)(""),[g,y]=(0,a.useState)(""),[f,v]=(0,a.useState)(null),[A,w]=(0,a.useState)((0,P.Fl)()),[b,C]=(0,a.useState)(!1),[S,I]=(0,a.useState)(null),[k,F]=(0,a.useState)(!1),[N,L]=(0,a.useState)(!1),[G,O]=(0,a.useState)(!1),[q,Y]=(0,a.useState)([]),[X,_]=(0,a.useState)(!1),[J,K]=(0,a.useState)(""),[Q,Z]=(0,a.useState)("success"),[$,ee]=(0,a.useState)(""),[te,oe]=(0,a.useState)(""),[ae,re]=(0,a.useState)(),[ne,ie]=(0,a.useState)([]),se=()=>{_(!1)},le=()=>{_(!0),setTimeout((()=>{_(!1)}),2e3)};(0,a.useEffect)((()=>{const e=(0,P.B1)(w);return()=>e()}),[]);const ce=async()=>{m(!0);try{const e=await(async()=>{try{return(await B.jD.get("/FileUpload/GetAllFiles")).data}catch(e){throw console.error("Error fetching all users:",e),e}})();if(e){const o=e.map((e=>({ImageId:e.Id,FileName:e.Name||null,Url:e.Url+"?timestamp="+(new Date).getTime()||""})));t(o)}}catch(e){}finally{m(!1)}};(0,a.useEffect)((()=>{b||ce()}),[b]);const de=()=>{C(!1),I(null),F(!1)},pe=async()=>{if(S)try{m(!0),await(async(e,t,o)=>{const a=new FormData;for(let n of e)a.append("file",n);try{t(!0);if(!(await B.jD.post("/FileUpload/UploadFiles",a,{headers:{"Content-Type":"multipart/form-data"}})).data)throw new Error("Network response was not ok");o(!1)}catch(r){throw r}finally{t(!1)}})(S,m,C)}catch(t){var e;let o="Upload failed. Please try again.";const a=t;null!==a&&void 0!==a&&null!==(e=a.response)&&void 0!==e&&e.data&&(o="string"===typeof a.response.data?a.response.data:a.response.data.message||o),K(o),Z("error"),le()}finally{m(!1)}},ue=async e=>{try{const t=await(async e=>{try{return(await B.jD.delete("/FileUpload/DeleteFile",{params:{fileId:e}})).data}catch(t){throw console.error("Error deleting file",t),t}})(e.ImageId);0!=t.Success?(K("File Deleted Succesfully"),Z("success"),le(),ce()):(K(t.ErrorMessage||"Error in Deleting File"),Z("error"),le())}catch(t){console.log(t)}},me=async e=>{var o;const a=null===(o=e.target.files)||void 0===o?void 0:o[0];if(console.log($,"Is replace file id"),a)try{m(!0),await(async(e,t,o)=>{const a=new FormData;a.append("file",t);try{o(!0);if(!(await B.jD.put("/FileUpload/ReplaceFile",a,{params:{fileId:e},headers:{"Content-Type":"multipart/form-data"}})).data)throw new Error("Network response was not ok")}catch(r){throw console.error("Error uploading file:",r),r}finally{o(!1)}})($,a,m),t([]),ce()}catch(n){var r;let e="Upload failed. Please try again.";const t=n;null!==t&&void 0!==t&&null!==(r=t.response)&&void 0!==r&&r.data&&(e="string"===typeof t.response.data?t.response.data:t.response.data.message||e),K(e),Z("error"),le()}finally{m(!1)}},he=async e=>{console.log("Preview clicked");const t=await(async e=>{try{return(await B.jD.get("/FileUpload/GetGuidesUsedByFile",{params:{fileId:e}})).data}catch(t){throw console.error("Error Getting Guides ",t),t}})(e.ImageId);ie(t),F(!0),re(e)},{t:xe}=(0,r.Bd)();return(0,j.jsx)("div",{className:"smooth-transition",style:{marginLeft:A?"253px":"23px"},children:(0,j.jsxs)(s.A,{sx:{maxHeight:"calc(90vh)",overflow:"auto"},children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h1",{children:xe("File Management")}),(0,j.jsx)("div",{style:{position:"absolute",top:"5px",right:"2px"},children:(0,j.jsx)(i.A,{onClick:()=>C(!0),className:"userButton",startIcon:(0,j.jsx)(V.A,{}),variant:"contained",children:(0,j.jsx)("span",{style:{position:"relative"},children:"Add File"})})}),(0,j.jsxs)(l.A,{open:b,onClose:de,maxWidth:"md",PaperProps:{style:{minHeight:"35vh",minWidth:"30vw"}},children:[(0,j.jsx)(c.A,{display:"flex",justifyContent:"center",alignItems:"center",style:{marginLeft:"-100px",marginTop:"40px"},children:"Upload File"}),(0,j.jsx)(d.A,{children:(0,j.jsx)(s.A,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"50px",children:(0,j.jsx)("input",{type:"file",onChange:e=>{I(e.target.files)},accept:"image/*",multiple:!0})})}),(0,j.jsxs)(p.A,{children:[(0,j.jsx)(i.A,{onClick:de,color:"primary",children:"Cancel"}),(0,j.jsx)(i.A,{onClick:async()=>{await pe(),C(!1)},color:"primary",children:"Upload"})]})]})]}),o?(0,j.jsx)("div",{className:"Loaderstyles",children:(0,j.jsx)("img",{src:T,alt:"Spinner",className:"LoaderSpinnerStyles"})}):(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(s.A,{sx:{display:"flex",flexWrap:"wrap"},children:e.map(((e,t)=>(0,j.jsxs)(u.A,{sx:{display:"flex",flexDirection:"column",height:"223px",position:"relative",margin:"10px",width:"277px",borderRadius:"15px","&:hover":{transform:"scale(1.05)",boxShadow:10}},children:[(0,j.jsxs)(s.A,{sx:{position:"relative",display:"flex",justifyContent:"center",alignItems:"center",flexGrow:1,cursor:"pointer"},onMouseEnter:()=>oe(e.ImageId),onMouseLeave:()=>oe(""),children:[(0,j.jsx)(M,{component:"img",image:e.Url||"",alt:e.FileName||"Image",sx:{height:"162px",width:"261px",objectFit:"cover",filter:te==e.ImageId?"blur(1px)":"none",borderRadius:2,margin:1},onClick:()=>he(e),className:"image-hover"}),te==e.ImageId&&(0,j.jsx)(s.A,{sx:{position:"absolute",display:"flex",justifyContent:"center",alignItems:"center"},onClick:()=>he(e),children:(0,j.jsx)("img",{src:W._,alt:"Preview Image"})}),(0,j.jsx)(n.A,{onClick:()=>ue(e),style:{position:"absolute",top:5,right:5,color:"red"},children:(0,j.jsx)("img",{src:W.NU,alt:"Delete"})})]}),(0,j.jsxs)(D,{style:{display:"flex",justifyContent:"space-between",padding:"0px 9px"},children:[(0,j.jsx)(R.A,{variant:"body2",noWrap:!0,style:{maxWidth:"50%"},children:e.FileName}),(0,j.jsxs)(s.A,{children:[(0,j.jsx)(n.A,{onClick:()=>(async e=>{try{await navigator.clipboard.writeText(e),K("Url Copied Succesfully"),Z("success"),le()}catch(t){console.log(t)}})(e.Url),children:(0,j.jsx)("img",{src:W.C,alt:"copy"})}),(0,j.jsx)(U.A,{title:"Replace Image",children:(0,j.jsx)(n.A,{onClick:t=>{var o,a;a=e.ImageId,console.log(a,"from click"),ee(a),null===t||void 0===t||t.stopPropagation(),null===(o=document.getElementById("file-upload"))||void 0===o||o.click()},children:(0,j.jsx)("img",{src:W.Nt,alt:"Replace"})})}),(0,j.jsx)("input",{type:"file",id:"file-upload",style:{display:"none"},accept:"image/*",onChange:me})]})]})]},t)))}),k&&(0,j.jsx)(H.A,{open:k,onClose:()=>{F(!1),re(void 0)},"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:(0,j.jsxs)(s.A,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",bgcolor:"background.paper",boxShadow:24,maxWidth:"600px",maxHeight:"600px",p:4},children:[(0,j.jsx)("img",{src:(null===ae||void 0===ae?void 0:ae.Url)||"",alt:"Model Image",style:{maxWidth:"500px",maxHeight:"500px"}}),(0,j.jsxs)(R.A,{id:"modal-modal-description",sx:{mt:2},children:["Name: ",null===ae||void 0===ae?void 0:ae.FileName]}),(0,j.jsxs)(R.A,{id:"modal-modal-description",sx:{mt:2},children:["Used Guides : ",(null===ne||void 0===ne?void 0:ne.length)<1?"No Guides Used this file ":ne]})]})}),(0,j.jsx)(z.A,{open:X,autoHideDuration:6e3,onClose:se,anchorOrigin:{vertical:"top",horizontal:"center"},sx:{zIndex:1e4,marginTop:4},children:(0,j.jsx)(E.A,{onClose:se,severity:Q,sx:{width:"100%"},children:J})})]})]})})}},2110:(e,t,o)=>{o.d(t,{A:()=>y});var a=o(8168),r=o(8587),n=o(5043),i=o(9292),s=o(8610),l=o(4535),c=o(8206),d=o(3336),p=o(2532),u=o(2372);function m(e){return(0,u.Ay)("MuiCard",e)}(0,p.A)("MuiCard",["root"]);var h=o(579);const x=["className","raised"],g=(0,l.Ay)(d.A,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})((()=>({overflow:"hidden"}))),y=n.forwardRef((function(e,t){const o=(0,c.b)({props:e,name:"MuiCard"}),{className:n,raised:l=!1}=o,d=(0,r.A)(o,x),p=(0,a.A)({},o,{raised:l}),u=(e=>{const{classes:t}=e;return(0,s.A)({root:["root"]},m,t)})(p);return(0,h.jsx)(g,(0,a.A)({className:(0,i.A)(u.root,n),elevation:l?8:void 0,ref:t,ownerState:p},d))}))},2505:(e,t,o)=>{var a=o(4994);t.A=void 0;var r=a(o(39)),n=o(579);t.A=(0,r.default)((0,n.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},8446:(e,t,o)=>{o.d(t,{A:()=>S});var a=o(8587),r=o(8168),n=o(5043),i=o(9292),s=o(8610),l=o(6803),c=o(4535),d=o(8206),p=o(3574),u=o(5849),m=o(5865),h=o(2532),x=o(2372);function g(e){return(0,x.Ay)("MuiLink",e)}const y=(0,h.A)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]);var f=o(7162),v=o(7266);const A={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},w=e=>{let{theme:t,ownerState:o}=e;const a=(e=>A[e]||e)(o.color),r=(0,f.Yn)(t,"palette.".concat(a),!1)||o.color,n=(0,f.Yn)(t,"palette.".concat(a,"Channel"));return"vars"in t&&n?"rgba(".concat(n," / 0.4)"):(0,v.X4)(r,.4)};var j=o(579);const b=["className","color","component","onBlur","onFocus","TypographyClasses","underline","variant","sx"],C=(0,c.Ay)(m.A,{name:"MuiLink",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,t["underline".concat((0,l.A)(o.underline))],"button"===o.component&&t.button]}})((e=>{let{theme:t,ownerState:o}=e;return(0,r.A)({},"none"===o.underline&&{textDecoration:"none"},"hover"===o.underline&&{textDecoration:"none","&:hover":{textDecoration:"underline"}},"always"===o.underline&&(0,r.A)({textDecoration:"underline"},"inherit"!==o.color&&{textDecorationColor:w({theme:t,ownerState:o})},{"&:hover":{textDecorationColor:"inherit"}}),"button"===o.component&&{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(y.focusVisible)]:{outline:"auto"}})})),S=n.forwardRef((function(e,t){const o=(0,d.b)({props:e,name:"MuiLink"}),{className:c,color:m="primary",component:h="a",onBlur:x,onFocus:y,TypographyClasses:f,underline:v="always",variant:w="inherit",sx:S}=o,I=(0,a.A)(o,b),{isFocusVisibleRef:M,onBlur:k,onFocus:F,ref:N}=(0,p.A)(),[D,R]=n.useState(!1),U=(0,u.A)(t,N),H=(0,r.A)({},o,{color:m,component:h,focusVisible:D,underline:v,variant:w}),z=(e=>{const{classes:t,component:o,focusVisible:a,underline:r}=e,n={root:["root","underline".concat((0,l.A)(r)),"button"===o&&"button",a&&"focusVisible"]};return(0,s.A)(n,g,t)})(H);return(0,j.jsx)(C,(0,r.A)({color:m,className:(0,i.A)(z.root,c),classes:f,component:h,onBlur:e=>{k(e),!1===M.current&&R(!1),x&&x(e)},onFocus:e=>{F(e),!0===M.current&&R(!0),y&&y(e)},ref:U,ownerState:H,variant:w,sx:[...Object.keys(A).includes(m)?[]:[{color:m}],...Array.isArray(S)?S:[S]]},I))}))},8462:(e,t,o)=>{var a=o(4994);t.A=void 0;var r=a(o(39)),n=o(579);t.A=(0,r.default)((0,n.jsx)("path",{d:"M6.99 11 3 15l3.99 4v-3H14v-2H6.99zM21 9l-3.99-4v3H10v2h7.01v3z"}),"SwapHoriz")}}]);
//# sourceMappingURL=675.f44704cd.chunk.js.map