{"version": 3, "file": "static/js/639.32f7e6ff.chunk.js", "mappings": "wHAGO,MAAMA,EAAeC,eAC1BC,EACAC,EACAC,EACAC,GAIkB,IAFlBC,EAAgBC,UAAAC,OAAA,EAAAD,UAAA,QAAAE,EAChBC,EAAeH,UAAAC,OAAA,EAAAD,UAAA,QAAAE,EAEf,IACE,MAAME,EAAaC,6CACbC,EAAO,IAAIC,gBAAgB,CAC/BC,WAAY,WACZC,UAAW,gBACXC,cAAe,mBACfC,MAAO,sBACPC,SAAUjB,EACVC,SAAUA,EACVG,SAAUA,EACVI,SAASA,IAGLU,EAAU,CACd,eAAgB,qCAGZC,QAAiBC,MAAMX,EAAY,CACvCY,OAAQ,OACRC,KAAMX,EACNO,QAASA,IAGX,GAAwB,MAApBC,EAASI,OAAgB,CAE3B,aAD2BJ,EAASK,MAEtC,CAEE,aAD4BL,EAASK,MAGzC,CACA,MAAOC,GAEL,MADAC,QAAQD,MAAM,qBAAsBA,GAC9BA,CACR,CACF,C,uOC3BA,IAAIE,EACAC,EAAwC,CAAC,EAE9B,SAASC,IACpB,MAAM,KAAEC,EAAI,QAACC,EAAO,UAAEC,IAAaC,EAAAA,EAAAA,MAGnC,MAAOC,EAAcC,IAAmBC,EAAAA,EAAAA,WAAS,IAC1CC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,KAC5BnC,EAAUsC,IAAeH,EAAAA,EAAAA,UAAS,KAClCI,EAAOC,IAAWL,EAAAA,EAAAA,UAAuB,OACzCX,EAAOiB,IAAYN,EAAAA,EAAAA,UAAwB,OAC3CO,EAAeC,IAAoBR,EAAAA,EAAAA,eAAoC7B,IACvEY,EAAU0B,IAAeT,EAAAA,EAAAA,UAAS,KAClCU,EAASC,IAAaX,EAAAA,EAAAA,UAAS,KAC/BY,EAAqBC,IAA0Bb,EAAAA,EAAAA,UAA8B,OAC7Ec,EAAkBC,IAAkBf,EAAAA,EAAAA,UAAsB,MAY3DgB,GAAWC,EAAAA,EAAAA,MAsFjB,OACIC,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAACC,SAAS,KAAKC,UAAU,wBAAuBC,SAAA,EACtDC,EAAAA,EAAAA,KAACC,EAAAA,EAAG,CAACC,GAAI,EAAGJ,UAAU,mBAAkBC,UACpCC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACC,QAAQ,KAAKN,UAAU,wBAAuBC,SAAC,kBAK/DC,EAAAA,EAAAA,KAACC,EAAAA,EAAG,CAACH,UAAU,wBAAuBC,UAClCC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACC,QAAQ,KAAKN,UAAU,6BAA4BC,SAAC,oBAKpEJ,EAAAA,EAAAA,MAACM,EAAAA,EAAG,CAACH,UAAU,mBAAkBC,SAAA,EAC7BC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACL,UAAU,mBAAkBC,SAAC,WAGzCC,EAAAA,EAAAA,KAACK,EAAAA,EAAS,CACNC,UAAQ,EACRC,WAAS,EACTC,KAAK,QACLC,GAAG,QACHC,KAAK,QACLC,aAAa,QACbC,WAAS,EACTC,MAAOnC,EACPoC,SAxHWC,IACvBpC,EAASoC,EAAMC,OAAOH,MAAM,EAwHhBI,YAAY,uBACZnB,UAAU,wBAGdE,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACL,UAAU,mBAAkBC,SAAC,cAGzCC,EAAAA,EAAAA,KAACK,EAAAA,EAAS,CACNC,UAAQ,EACRC,WAAS,EACTC,KAAMjC,EAAe,OAAS,WAC9BkC,GAAG,WACHC,KAAK,WACLC,aAAa,WACbC,WAAS,EACTC,MAAOvE,EACPwE,SArIcC,IAC1BnC,EAAYmC,EAAMC,OAAOH,MAAM,EAqInBI,YAAY,sBACZnB,UAAU,qBACVoB,WAAY,CACRC,cACInB,EAAAA,EAAAA,KAACoB,EAAAA,EAAc,CAACC,SAAS,MAAKtB,UAC1BC,EAAAA,EAAAA,KAACsB,EAAAA,EAAU,CACP,aAAW,6BACXC,QArJAC,KAC5BhD,GAAiBD,EAAa,EAqJFkD,KAAK,MAAK1B,UAGVC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,OAAA4B,OAASnD,EAAe,eAAiB,mBAQxET,IACGkC,EAAAA,EAAAA,KAAC2B,EAAAA,EAAc,CAAC7D,OAAK,EAACgC,UAAU,oBAAmBC,SAC9CjC,KAITkC,EAAAA,EAAAA,KAAC4B,EAAAA,EAAM,CACHpB,KAAK,SACLD,WAAS,EACTH,QAAQ,YACRN,UAAU,oBACVyB,QAhKKnF,UACjB,IAEI,GAAiB,KAAbE,GAA+B,MAAZA,EAEnByC,EAAS,qCAER,GAAc,KAAVL,GAAyB,MAATA,EAErBK,EAAS,iCAER,CACD,MAAM8C,GAAsB9E,EACtB+E,EAAY/E,2NACZgF,EAAY,IAAIC,EAAAA,EACtBD,EAAUE,aAAaH,GACvB,MAAMI,GAAM,IAAIC,MAAOC,cACjBC,EAAoBN,EAAUO,QAAQhG,EAAW,IAAM4F,EAAIK,QAAQC,WACxEH,GACDtD,EAAS,0BAEb,MAAMxC,EAAiB,IACjBC,GAAgB,EAChBiG,EAAY,GACRjF,QAAiBrB,EAAAA,EAAAA,GAAauC,EAAOmD,EAAsBQ,EAAoB/F,EAAUC,EAAgBC,EAAeiG,EAAU,QAAQ,SAChJ,GAAIjF,EAASkF,aAAc,CAAC,IAADC,EAAAC,EAC3B3E,EAAc,aAAe4E,KAAKC,UAAUtF,GAC5CuF,aAAaC,QAAQ,eAAexF,EAASkF,cAC7C,MAAMO,QAAqBC,EAAAA,EAAAA,MAC3B1D,EAAeyD,EAAeA,EAAajG,KAAO,MAClD,MAAMmG,EAAiC,OAAZF,QAAY,IAAZA,GAAAA,EAAcjG,KAAKoG,WAA0B,OAAZH,QAAY,IAAZA,GAAAA,EAAcjG,KAAKoG,UAAwB,OAAZH,QAAY,IAAZA,OAAY,EAAZA,EAAcjG,KAAKoG,UAAUC,UAAU,EAAG,GAAGC,cAAgB,GAClJC,EAAgC,OAAZN,QAAY,IAAZA,GAAAA,EAAcjG,MAAqB,OAAZiG,QAAY,IAAZA,GAAAA,EAAcjG,KAAKwG,SAAuB,OAAZP,QAAY,IAAZA,OAAY,EAAZA,EAAcjG,KAAKwG,SAASH,UAAU,EAAG,GAAGC,cAAgB,GAE3ItF,EADkBmF,EAAoBI,EAEtCR,aAAaC,QAAQ,WAAwC,QAA9BL,EAAc,OAAZM,QAAY,IAAZA,GAAkB,QAANL,EAAZK,EAAcjG,YAAI,IAAA4F,OAAN,EAAZA,EAAoBa,gBAAQ,IAAAd,EAAAA,EAAI,IACjE1E,EAAoB,KAAI4E,KAAKC,UAAsB,OAAZG,QAAY,IAAZA,OAAY,EAAZA,EAAcjG,MACrD+F,aAAaC,QAAQ,WAAWH,KAAKC,UAAU7E,IAC3CwB,EAAS,4BAA6B,CAClCiE,MAAO,CAAEC,WAAwB,OAAZV,QAAY,IAAZA,OAAY,EAAZA,EAAcjG,KAAMqC,oBAAiC,OAAZ4D,QAAY,IAAZA,OAAY,EAAZA,EAAcjG,KAAK4G,iBAEzF,MACI7E,EAASvB,EAASqG,kBAE1B,CACJ,CACA,MAAO/F,GACHC,QAAQD,MAAM,iBACdiB,EAAS,gCACb,GAgHkCgB,SACzB,iBAKLC,EAAAA,EAAAA,KAACC,EAAAA,EAAG,CAAC6D,GAAI,GAAIhE,UAAU,qBAAoBC,UACvCJ,EAAAA,EAAAA,MAACQ,EAAAA,EAAU,CAACC,QAAQ,QAAQN,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAC+D,EAAAA,EAAI,CAACC,KAAK,gBAAgBlE,UAAU,oBAAmBC,SAAC,iBAAmB,MAC5EC,EAAAA,EAAAA,KAAC+D,EAAAA,EAAI,CAACC,KAAK,kBAAkBlE,UAAU,oBAAmBC,SAAC,0BAK/E,C,wKC7NO,SAASkE,EAAoBC,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,CACA,MACA,GADoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,OAAQ,gBAAiB,iBAAkB,kBAAmB,SAAU,iB,wBCHxH,MAAMC,EAAuB,CAClCC,QAAS,eACTC,YAAa,eACbC,UAAW,iBACXC,cAAe,iBACf3G,MAAO,cAiBT,EAZ0B4G,IAGpB,IAHqB,MACzBC,EAAK,WACLC,GACDF,EACC,MAAMG,EAP0BC,IACzBT,EAAqBS,IAAUA,EAMbC,CAA0BH,EAAWE,OACxDA,GAAQE,EAAAA,EAAAA,IAAQL,EAAO,WAAFjD,OAAamD,IAAoB,IAAUD,EAAWE,MAC3EG,GAAeD,EAAAA,EAAAA,IAAQL,EAAO,WAAFjD,OAAamD,EAAgB,YAC/D,MAAI,SAAUF,GAASM,EACd,QAAPvD,OAAeuD,EAAY,YAEtBC,EAAAA,EAAAA,IAAMJ,EAAO,GAAI,E,aClB1B,MAAMK,EAAY,CAAC,YAAa,QAAS,YAAa,SAAU,UAAW,oBAAqB,YAAa,UAAW,MA2BlHC,GAAWC,EAAAA,EAAAA,IAAOlF,EAAAA,EAAY,CAClCO,KAAM,UACNwD,KAAM,OACNoB,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJZ,GACEW,EACJ,MAAO,CAACC,EAAOC,KAAMD,EAAO,YAAD9D,QAAagE,EAAAA,EAAAA,GAAWd,EAAWe,aAAwC,WAAzBf,EAAWgB,WAA0BJ,EAAOK,OAAO,GAPnHR,EASdX,IAGG,IAHF,MACFC,EAAK,WACLC,GACDF,EACC,OAAOoB,EAAAA,EAAAA,GAAS,CAAC,EAA4B,SAAzBlB,EAAWe,WAAwB,CACrDI,eAAgB,QACU,UAAzBnB,EAAWe,WAAyB,CACrCI,eAAgB,OAChB,UAAW,CACTA,eAAgB,cAEQ,WAAzBnB,EAAWe,YAA0BG,EAAAA,EAAAA,GAAS,CAC/CC,eAAgB,aACM,YAArBnB,EAAWE,OAAuB,CACnCkB,oBAAqBC,EAAkB,CACrCtB,QACAC,gBAED,CACD,UAAW,CACToB,oBAAqB,aAEI,WAAzBpB,EAAWgB,WAA0B,CACvCvE,SAAU,WACV6E,wBAAyB,cACzBC,gBAAiB,cAGjBC,QAAS,EACTC,OAAQ,EACRC,OAAQ,EAERC,aAAc,EACdC,QAAS,EAETC,OAAQ,UACRC,WAAY,OACZC,cAAe,SACfC,cAAe,OAEfC,iBAAkB,OAElB,sBAAuB,CACrBC,YAAa,QAEf,CAAC,KAADpF,OAAMqF,EAAYC,eAAiB,CACjCZ,QAAS,SAEX,IA0HJ,EAxH0Ba,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAM5B,GAAQ6B,EAAAA,EAAAA,GAAgB,CAC5B7B,MAAO2B,EACPxG,KAAM,aAEF,UACFZ,EAAS,MACTgF,EAAQ,UAAS,UACjBc,EAAY,IAAG,OACfyB,EAAM,QACNC,EAAO,kBACPC,EAAiB,UACjB5B,EAAY,SAAQ,QACpBvF,EAAU,UAAS,GACnBoH,GACEjC,EACJkC,GAAQC,EAAAA,EAAAA,GAA8BnC,EAAOJ,IACzC,kBACJwC,EACAN,OAAQO,EACRN,QAASO,EACTV,IAAKW,IACHC,EAAAA,EAAAA,MACGf,EAAcgB,GAAmBf,EAAAA,UAAe,GACjDgB,GAAaC,EAAAA,EAAAA,GAAWf,EAAKW,GAmB7BlD,GAAakB,EAAAA,EAAAA,GAAS,CAAC,EAAGP,EAAO,CACrCT,QACAc,YACAoB,eACArB,YACAvF,YAEI+H,EAzHkBvD,KACxB,MAAM,QACJuD,EAAO,UACPvC,EAAS,aACToB,EAAY,UACZrB,GACEf,EACEwD,EAAQ,CACZ3C,KAAM,CAAC,OAAQ,YAAF/D,QAAcgE,EAAAA,EAAAA,GAAWC,IAA4B,WAAdC,GAA0B,SAAUoB,GAAgB,iBAE1G,OAAOqB,EAAAA,EAAAA,GAAeD,EAAOnE,EAAqBkE,EAAQ,EA+G1CG,CAAkB1D,GAClC,OAAoB5E,EAAAA,EAAAA,KAAKoF,GAAUU,EAAAA,EAAAA,GAAS,CAC1ChB,MAAOA,EACPhF,WAAWyI,EAAAA,EAAAA,GAAKJ,EAAQ1C,KAAM3F,GAC9BqI,QAASZ,EACT3B,UAAWA,EACXyB,OA/BiBtG,IACjB6G,EAAkB7G,IACgB,IAA9B4G,EAAkBa,SACpBR,GAAgB,GAEdX,GACFA,EAAOtG,EACT,EAyBAuG,QAvBkBvG,IAClB8G,EAAmB9G,IACe,IAA9B4G,EAAkBa,SACpBR,GAAgB,GAEdV,GACFA,EAAQvG,EACV,EAiBAoG,IAAKc,EACLrD,WAAYA,EACZxE,QAASA,EACToH,GAAI,IAAMiB,OAAOC,KAAKrE,GAAsBsE,SAAS7D,GAEhD,GAFyD,CAAC,CAC7DA,aACY8D,MAAMC,QAAQrB,GAAMA,EAAK,CAACA,KACvCC,GACL,G", "sources": ["services/LoginService.tsx", "components/login/Superadminloginpage.tsx", "../node_modules/@mui/material/Link/linkClasses.js", "../node_modules/@mui/material/Link/getTextDecoration.js", "../node_modules/@mui/material/Link/Link.js"], "sourcesContent": ["import JSEncrypt from 'jsencrypt';\r\nimport { adminApiService, idsApiService } from \"./APIService\";\r\n\r\nexport const LoginService = async (\r\n  emailId: string,\r\n  password: string,\r\n  organizationId: string,\r\n  rememberLogin: boolean,\r\n  returnUrl: string = \"\",\r\n  authType: string,\r\n  tenantid:string\r\n): Promise<any> => {\r\n  try {\r\n    const requestUrl = process.env.REACT_APP_IDS_API + `/connect/token`;\r\n    const data = new URLSearchParams({\r\n      grant_type: 'password',\r\n      client_id: 'dap_extension',\r\n      client_secret: 'user_interaction',\r\n      scope: 'openid profile api1',\r\n      username: emailId,\r\n      password: password,\r\n      authType: authType,\r\n      tenantid:tenantid\r\n    });\r\n\r\n    const headers = {\r\n      'Content-Type': 'application/x-www-form-urlencoded',\r\n    };\r\n\r\n    const response = await fetch(requestUrl, {\r\n      method: 'POST',\r\n      body: data,\r\n      headers: headers,\r\n    });\r\n\r\n    if (response.status === 200) {\r\n      const jsonResponse = await response.json(); \r\n      return jsonResponse; \r\n    } else {\r\n      const errorResponse = await response.json(); \r\n      return errorResponse; \r\n    }\r\n  }\r\n  catch (error) {\r\n    console.error(\"An error occurred:\", error);\r\n    throw error; \r\n  }\r\n};\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { Con<PERSON>er, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport { useAuth } from '../auth/AuthProvider';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport { superAdminLogin } from \"../../services/SuperAdminLoginService\"\r\nimport { GetUserDetails, encryptPassword } from '../../services/UserService';\r\nimport { JSEncrypt } from 'jsencrypt';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { GetUserDetailsById } from '../../services/UserService';\r\nimport userManager from '../auth/UseAuth';\r\nimport { LoginUserInfo } from '../../models/LoginUserInfo';\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { getAllUsers } from '../../services/UserService';\r\nimport { getOrganizationById } from '../../services/OrganizationService';\r\nimport { Organization } from \"../../models/Organization\";\r\nimport { User } from \"../../models/User\";\r\nimport { User as Users, UserManager } from 'oidc-client-ts';\r\nimport { FormHelperText } from '@mui/material';\r\nimport { LoginService } from \"../../services/LoginService\";\r\nlet SAinitialsData: string;\r\nlet userLocalData: { [key: string]: any } = {}\r\nlet userDetails: User;\r\nexport default function LoginPage() {\r\n    const { user,signOut ,loggedOut} = useAuth();\r\n    let UserId: string;\r\n    let OrganizationId: string;\r\n    const [showPassword, setShowPassword] = useState(false);\r\n    const [email, setEmail] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [users, setUser] = useState<Users | null>(null);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);\r\n    const [response, setresponse] = useState('');\r\n    const [userIds, setuserId] = useState(\"\");\r\n    const [organizationDetails, setOrganizationDetails] = useState<Organization | null>(null);\r\n    const [loginUserDetails, setUserDetails] = useState<User | null>(null);\r\n    const handleClickShowPassword = () => {\r\n        setShowPassword(!showPassword);\r\n    };\r\n\r\n    const handleEmailChange = (event: any) => {\r\n        setEmail(event.target.value);\r\n    };\r\n\r\n    const handlePasswordChange = (event: any) => {\r\n        setPassword(event.target.value);\r\n    };\r\n    const navigate = useNavigate();\r\n    const handleSubmit = async () => {\r\n        try {\r\n            \r\n            if (password === '' || password == null)\r\n            {\r\n                setError('password should not be empty');\r\n            }\r\n            else if (email === '' || email == null)\r\n            {\r\n                setError('email should not be empty');\r\n            }\r\n            else {\r\n                const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\r\n                const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\r\n                const encryptor = new JSEncrypt();\r\n                encryptor.setPublicKey(publicKey);\r\n                const now = new Date().toISOString();\r\n                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\r\n            if (!encryptedPassword) {\r\n                setError('Enter correct password');\r\n            }\r\n            const organizationId = \"1\";\r\n            const rememberLogin = true;\r\n            const returnUrl = \"\"\r\n                const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl,\"super\",\"admin\");\r\n                if (response.access_token) {                    \r\n                userLocalData[\"oidc-info\"] = JSON.stringify(response)\r\n                localStorage.setItem(\"access_token\",response.access_token)          \r\n                const userResponse = await GetUserDetails();\r\n                setUserDetails(userResponse ? userResponse.data : null);\r\n                const firstNameInitials =  userResponse?.data.FirstName &&  userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';\r\n                const lastNameinitials =  userResponse?.data &&  userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';\r\n                const finalData = firstNameInitials + lastNameinitials;\r\n                SAinitialsData = finalData;\r\n                localStorage.setItem(\"userType\", userResponse?.data?.UserType ?? \"\");            \r\n                userLocalData[\"user\"] = JSON.stringify(userResponse?.data);\r\n                localStorage.setItem(\"userInfo\",JSON.stringify(userLocalData)) \r\n                    navigate(\"/superadmin/organizations\", {\r\n                        state: { userDetail: userResponse?.data, organizationDetails: userResponse?.data.OrganizationId }\r\n                    });\r\n                } else {\r\n                    setError(response.error_description);\r\n                }                \r\n            }\r\n        }\r\n        catch (error) {\r\n            console.error('Login failed:');\r\n            setError('An unexpected error occurred.'); // Handle unexpected errors\r\n        }\r\n    };\r\n   \r\n    // async function GetLoginUserInfo(userResponse : User) {\r\n    //     try {\r\n    //         const firstNameInitials =  userResponse?.FirstName &&  userResponse?.FirstName ? userResponse?.FirstName.substring(0, 1).toUpperCase() : '';\r\n    //         const lastNameinitials =  userResponse &&  userResponse?.LastName ? userResponse?.LastName.substring(0, 1).toUpperCase() : '';\r\n    //         const finalData = firstNameInitials + lastNameinitials;\r\n    //         SAinitialsData = finalData;\r\n    //         localStorage.setItem(\"userType\", userResponse?.UserType ?? \"\");\r\n    //     } catch (error) {\r\n    //         console.error('Error fetching user or organization details', error);\r\n    //     }\r\n    // }\r\n    // useEffect(() => {\r\n    //     let token = localStorage.getItem(\"access_token\");\r\n\t// \tconst userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n\t// \tif (userInfo['oidc-info'] && userInfo['user']) {\r\n\t// \t\tuserDetails = JSON.parse(userInfo['user'])\r\n\t// \t\ttoken = userInfo['oidc-info'].access_token;\r\n\t// \t}\r\n    //     if (token) {\r\n    //         try {\r\n    //             const loggedinUserInfo = jwt_decode<LoginUserInfo>(token);\r\n    //             setLoginUserInfo(loggedinUserInfo);\r\n    //             GetLoginUserInfo(userDetails);\r\n    //             UserId = loggedinUserInfo.UserId;                \r\n    //         } catch (error) {\r\n    //             signOut();\r\n    //         }\r\n    //     }\r\n    //     else {\r\n    //         signOut();\r\n    //     }\r\n\r\n    // }, [user]);\r\n\r\n    return (\r\n        <Container maxWidth=\"sm\" className=\"qadpt-superadminlogin\">\r\n            <Box mb={4} className=\"qadpt-brand-logo\">\r\n                <Typography variant=\"h3\" className=\"qadpt-brand-logo-text\">\r\n                    QUICKADOPT\r\n                </Typography>\r\n            </Box>\r\n\r\n            <Box className=\"qadpt-welcome-message\">\r\n                <Typography variant=\"h4\" className=\"qadpt-welcome-message-text\">\r\n                    Welcome back\r\n                </Typography>\r\n            </Box>\r\n\r\n            <Box className=\"qadpt-login-form\">\r\n                <Typography className=\"qadpt-form-label\">\r\n                    Email\r\n                </Typography>\r\n                <TextField\r\n                    required\r\n                    fullWidth\r\n                    type=\"email\"\r\n                    id=\"email\"\r\n                    name=\"Email\"\r\n                    autoComplete=\"Email\"\r\n                    autoFocus\r\n                    value={email}\r\n                    onChange={handleEmailChange}\r\n                    placeholder=\"eg, <EMAIL>\"\r\n                    className=\"qadpt-custom-input\"\r\n                />\r\n\r\n                <Typography className=\"qadpt-form-label\">\r\n                    Password\r\n                </Typography>\r\n                <TextField\r\n                    required\r\n                    fullWidth\r\n                    type={showPassword ? \"text\" : \"password\"}\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    autoComplete=\"password\"\r\n                    autoFocus\r\n                    value={password}\r\n                    onChange={handlePasswordChange}\r\n                    placeholder=\"Enter your password\"\r\n                    className=\"qadpt-custom-input\"\r\n                    InputProps={{\r\n                        endAdornment: (\r\n                            <InputAdornment position=\"end\">\r\n                                <IconButton\r\n                                    aria-label=\"toggle password visibility\"\r\n                                    onClick={handleClickShowPassword}\r\n                                    edge=\"end\"\r\n                                >\r\n                                    {/* {showPassword ? <VisibilityOff /> : <Visibility />} */}\r\n                                    <i className={`fal ${showPassword ? \"fa-eye-slash\" : \"fa-eye\"}`}></i>\r\n\r\n                                </IconButton>\r\n                            </InputAdornment>\r\n                        ),\r\n                    }}\r\n                   \r\n                />\r\n                {error && (\r\n                    <FormHelperText error className=\"qadpt-text-danger\">\r\n                        {error}\r\n                    </FormHelperText>\r\n                )}\r\n\r\n                <Button\r\n                    type=\"button\"\r\n                    fullWidth\r\n                    variant=\"contained\"\r\n                    className=\"qadpt-btn-default\"\r\n                    onClick={handleSubmit}\r\n                > \r\n                        Continue\r\n                </Button>\r\n            </Box>\r\n\r\n            <Box mt={12} className=\"qadpt-login-footer\">\r\n                <Typography variant=\"body2\" className=\"qadpt-footer-text\">\r\n                    <Link href=\"/terms-of-use\" className=\"qadpt-footer-link\">Terms of use</Link> | \r\n                    <Link href=\"/privacy-policy\" className=\"qadpt-footer-link\">Privacy Policy</Link>\r\n                </Typography>\r\n            </Box>\r\n        </Container>\r\n    );\r\n}\r\nexport {SAinitialsData}", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "import { getPath } from '@mui/system';\nimport { alpha } from '@mui/system/colorManipulator';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"onBlur\", \"onFocus\", \"TypographyClasses\", \"underline\", \"variant\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useForkRef from '../utils/useForkRef';\nimport Typography from '../Typography';\nimport linkClasses, { getLinkUtilityClass } from './linkClasses';\nimport getTextDecoration, { colorTransformations } from './getTextDecoration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    component,\n    focusVisible,\n    underline\n  } = ownerState;\n  const slots = {\n    root: ['root', `underline${capitalize(underline)}`, component === 'button' && 'button', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getLinkUtilityClass, classes);\n};\nconst LinkRoot = styled(Typography, {\n  name: 'MuiLink',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`underline${capitalize(ownerState.underline)}`], ownerState.component === 'button' && styles.button];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({}, ownerState.underline === 'none' && {\n    textDecoration: 'none'\n  }, ownerState.underline === 'hover' && {\n    textDecoration: 'none',\n    '&:hover': {\n      textDecoration: 'underline'\n    }\n  }, ownerState.underline === 'always' && _extends({\n    textDecoration: 'underline'\n  }, ownerState.color !== 'inherit' && {\n    textDecorationColor: getTextDecoration({\n      theme,\n      ownerState\n    })\n  }, {\n    '&:hover': {\n      textDecorationColor: 'inherit'\n    }\n  }), ownerState.component === 'button' && {\n    position: 'relative',\n    WebkitTapHighlightColor: 'transparent',\n    backgroundColor: 'transparent',\n    // Reset default value\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    border: 0,\n    margin: 0,\n    // Remove the margin in Safari\n    borderRadius: 0,\n    padding: 0,\n    // Remove the padding in Firefox\n    cursor: 'pointer',\n    userSelect: 'none',\n    verticalAlign: 'middle',\n    MozAppearance: 'none',\n    // Reset\n    WebkitAppearance: 'none',\n    // Reset\n    '&::-moz-focus-inner': {\n      borderStyle: 'none' // Remove Firefox dotted outline.\n    },\n    [`&.${linkClasses.focusVisible}`]: {\n      outline: 'auto'\n    }\n  });\n});\nconst Link = /*#__PURE__*/React.forwardRef(function Link(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiLink'\n  });\n  const {\n      className,\n      color = 'primary',\n      component = 'a',\n      onBlur,\n      onFocus,\n      TypographyClasses,\n      underline = 'always',\n      variant = 'inherit',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const handlerRef = useForkRef(ref, focusVisibleRef);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    focusVisible,\n    underline,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(LinkRoot, _extends({\n    color: color,\n    className: clsx(classes.root, className),\n    classes: TypographyClasses,\n    component: component,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    ref: handlerRef,\n    ownerState: ownerState,\n    variant: variant,\n    sx: [...(!Object.keys(colorTransformations).includes(color) ? [{\n      color\n    }] : []), ...(Array.isArray(sx) ? sx : [sx])]\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Link.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the link.\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Typography`](/material-ui/api/typography/) element.\n   */\n  TypographyClasses: PropTypes.object,\n  /**\n   * Controls when the link should have an underline.\n   * @default 'always'\n   */\n  underline: PropTypes.oneOf(['always', 'hover', 'none']),\n  /**\n   * Applies the theme typography styles.\n   * @default 'inherit'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string])\n} : void 0;\nexport default Link;"], "names": ["LoginService", "async", "emailId", "password", "organizationId", "<PERSON><PERSON><PERSON><PERSON>", "authType", "arguments", "length", "undefined", "tenantid", "requestUrl", "process", "data", "URLSearchParams", "grant_type", "client_id", "client_secret", "scope", "username", "headers", "response", "fetch", "method", "body", "status", "json", "error", "console", "SAinitialsData", "userLocalData", "LoginPage", "user", "signOut", "loggedOut", "useAuth", "showPassword", "setShowPassword", "useState", "email", "setEmail", "setPassword", "users", "setUser", "setError", "loginUserInfo", "setLoginUserInfo", "setresponse", "userIds", "setuserId", "organizationDetails", "setOrganizationDetails", "loginUserDetails", "setUserDetails", "navigate", "useNavigate", "_jsxs", "Container", "max<PERSON><PERSON><PERSON>", "className", "children", "_jsx", "Box", "mb", "Typography", "variant", "TextField", "required", "fullWidth", "type", "id", "name", "autoComplete", "autoFocus", "value", "onChange", "event", "target", "placeholder", "InputProps", "endAdornment", "InputAdornment", "position", "IconButton", "onClick", "handleClickShowPassword", "edge", "concat", "FormHelperText", "<PERSON><PERSON>", "isEncryptionEnabled", "public<PERSON>ey", "encryptor", "JSEncrypt", "setPublicKey", "now", "Date", "toISOString", "encryptedPassword", "encrypt", "trim", "toString", "returnUrl", "access_token", "_userResponse$data$Us", "_userResponse$data", "JSON", "stringify", "localStorage", "setItem", "userResponse", "GetUserDetails", "firstNameInitials", "FirstName", "substring", "toUpperCase", "lastNameinitials", "LastName", "UserType", "state", "userDetail", "OrganizationId", "error_description", "mt", "Link", "href", "getLinkUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "_ref", "theme", "ownerState", "transformedColor", "color", "transformDeprecatedColors", "<PERSON><PERSON><PERSON>", "channelColor", "alpha", "_excluded", "LinkRoot", "styled", "overridesResolver", "props", "styles", "root", "capitalize", "underline", "component", "button", "_extends", "textDecoration", "textDecorationColor", "getTextDecoration", "WebkitTapHighlightColor", "backgroundColor", "outline", "border", "margin", "borderRadius", "padding", "cursor", "userSelect", "verticalAlign", "MozAppearance", "WebkitAppearance", "borderStyle", "linkClasses", "focusVisible", "React", "inProps", "ref", "useDefaultProps", "onBlur", "onFocus", "TypographyClasses", "sx", "other", "_objectWithoutPropertiesLoose", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "focusVisibleRef", "useIsFocusVisible", "setFocusVisible", "handler<PERSON>ef", "useForkRef", "classes", "slots", "composeClasses", "useUtilityClasses", "clsx", "current", "Object", "keys", "includes", "Array", "isArray"], "sourceRoot": ""}