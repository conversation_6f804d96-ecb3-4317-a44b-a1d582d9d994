{"version": 3, "file": "static/js/43.fcf20b46.chunk.js", "mappings": "oWA2BA,MACA,EAAe,IAA0B,kECDzC,MACA,EAAe,IAA0B,gECezC,MACA,EAAe,IAA0B,gECjBzC,MACA,EAAe,IAA0B,6DCiBzC,MACA,EAAe,IAA0B,4DCMzC,MACA,EAAe,IAA0B,8DCTzC,MACA,EAAe,IAA0B,iE,6FCqEzC,MAwqCA,EAxqCiCA,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACnC,MAAMC,IAAkB,IAAIC,MAAOC,cAAcC,MAAM,EAAG,IACpDC,GAAc,IAAIH,MASjBI,GAAYC,KAAiBC,EAAAA,EAAAA,WAAS,IACtCC,GAAOC,KAAYF,EAAAA,EAAAA,UAAuB,OAC1CG,GAAkBC,KAAuBJ,EAAAA,EAAAA,WAAS,IAClDK,GAAkBC,KAAuBN,EAAAA,EAAAA,WAAS,GACnDO,IAAWC,EAAAA,EAAAA,MACXC,IAAWC,EAAAA,EAAAA,OACX,aAAEC,KAAiBC,EAAAA,EAAAA,MAClBC,GAAWC,KAAgBd,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdxB,EAALwB,GAAOc,oBAAY,IAAAtC,OAAd,EAALA,EAAqBuC,OAAQ,MAClE,QAAEC,KAAYC,EAAAA,EAAAA,MACbC,GAAgBC,KAAcpB,EAAAA,EAAAA,UAASiB,IAAW,KAClDI,GAAgBC,KAAqBtB,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,QAAK,EAALA,GAAOc,aAAaQ,iBAAkB,KACpFC,GAAWC,KAAgBzB,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdvB,EAALuB,GAAOc,oBAAY,IAAArC,OAAd,EAALA,EAAqBgD,YAAa,KACtEC,GAAaC,KAAkB5B,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdtB,EAALsB,GAAOc,oBAAY,IAAApC,OAAd,EAALA,EAAqBkD,cAAe,KAC5EC,GAAWC,KAAgB/B,EAAAA,EAAAA,WAAS,IACpCgC,GAAmBC,KAAwBjC,EAAAA,EAAAA,WAAsB,OAALC,SAAK,IAALA,IAAmB,QAAdrB,EAALqB,GAAOc,oBAAY,IAAAnC,OAAd,EAALA,EAAqBsD,YAAa,kBAC9FC,GAAWC,KAAgBpC,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdpB,EAALoB,GAAOc,oBAAY,IAAAlC,OAAd,EAALA,EAAqBwD,YAAa,KACtEC,GAAWC,KAAgBvC,EAAAA,EAAAA,UAAc,OAALC,SAAK,IAALA,IAAmB,QAAdnB,EAALmB,GAAOc,oBAAY,IAAAjC,OAAd,EAALA,EAAqB0D,YACzDC,GAAWC,KAAgB1C,EAAAA,EAAAA,UAAc,OAALC,SAAK,IAALA,IAAmB,QAAdlB,EAALkB,GAAOc,oBAAY,IAAAhC,OAAd,EAALA,EAAqB4D,YACzDC,GAAsBC,KAA2B7C,EAAAA,EAAAA,UAAc,OAALC,SAAK,IAALA,IAAmB,QAAdjB,EAALiB,GAAOc,oBAAY,IAAA/B,OAAd,EAALA,EAAqB8D,cAC/EC,GAAeC,KAAoBhD,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdhB,EAALgB,GAAOc,oBAAY,IAAA9B,OAAd,EAALA,EAAqBgE,cAAe,gBAChFC,GAAiBC,KAAsBnD,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdf,EAALe,GAAOc,oBAAY,IAAA7B,OAAd,EAALA,EAAqBkE,gBAAiB,aACtFC,GAAkBC,KAAuBtD,EAAAA,EAAAA,UAAc,OAALC,SAAK,IAALA,IAAmB,QAAdd,EAALc,GAAOc,oBAAY,IAAA5B,OAAd,EAALA,EAAqBoE,cACvEC,GAAWC,KAAgBzD,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdb,EAALa,GAAOc,oBAAY,IAAA3B,OAAd,EAALA,EAAqBsE,YAAa,KACtEC,GAAaC,KAAkB5D,EAAAA,EAAAA,UAAc,OAALC,SAAK,IAALA,IAAmB,QAAdZ,EAALY,GAAOc,oBAAY,IAAA1B,OAAd,EAALA,EAAqBsE,cAC7DE,GAAaC,KAAkB9D,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdX,EAALW,GAAOc,oBAAY,IAAAzB,OAAd,EAALA,EAAqBuE,cAAehE,KAC5EkE,GAAeC,KAAoBhE,EAAAA,EAAAA,WAAc,OAALC,SAAK,IAALA,IAAmB,QAAdV,EAALU,GAAOc,oBAAY,IAAAxB,OAAd,EAALA,EAAqBwE,gBAAiBlE,KAClFoE,GAAmBC,KAAwBlE,EAAAA,EAAAA,UAAS,KACpDmE,GAAaC,KAAkBpE,EAAAA,EAAAA,WAAS,IACxCqE,GAAeC,KAAoBtE,EAAAA,EAAAA,WAAS,IAC5CuE,GAAYC,KAAiBxE,EAAAA,EAAAA,WAAS,IACtCyE,GAAaC,KAAkB1E,EAAAA,EAAAA,UAAwB,OACvD2E,GAAUC,KAAe5E,EAAAA,EAAAA,UAAoB,CAAC,CAAE6E,SAAU,SAAUC,KAAU,OAAL7E,SAAK,IAALA,IAAmB,QAAdT,EAALS,GAAOc,oBAAY,IAAAvB,OAAd,EAALA,EAAqBkE,YAAa,GAAIqB,gBAAiB,GAAIC,aAAc,OAClJC,GAAcC,KAAmBlF,EAAAA,EAAAA,UAAmB,KACpDmF,GAAMC,KAAWpF,EAAAA,EAAAA,WAAS,IAC1BqF,GAAcC,KAAmBtF,EAAAA,EAAAA,UAAS,CAC7CuF,QAAS,GACTlD,UAAW,GACXrB,KAAM,GACNO,eAAgB,GAChBoB,UAAW,GACXH,UAAW,GACXN,UAAW,GACXR,UAAW,GACXG,YAAa,GACbiB,aAAa,EACb0C,SAAS,EACTC,WAAW,EACXlC,YAAa,GACbG,UAAW,MAGRgC,GAAmBC,KAAwB3F,EAAAA,EAAAA,WAAS,IACpD4F,GAAmBC,KAAwB7F,EAAAA,EAAAA,UAAS,qBACpD8F,GAAqBC,KAA0B/F,EAAAA,EAAAA,UAAS,qBAWxDgG,GAAWC,KAAgBjG,EAAAA,EAAAA,UAC9BkG,MAAMC,QAAa,OAALlG,SAAK,IAALA,QAAK,EAALA,GAAOc,aAAaqF,WACvB,OAALnG,SAAK,IAALA,QAAK,EAALA,GAAOc,aAAaqF,UACpB,KAGYC,EAAAA,EAAAA,IAAO,MAAPA,EAAcC,IAAA,IAAC,MAAEC,GAAOD,EAAA,MAAM,CAChDE,QAAS,OACTC,WAAY,SACZC,UAAW,SAEX,oBAAqB,CACjBC,QAAS,KAGTC,OAAQ,SAEf,KACDC,EAAAA,EAAAA,YAAU,KACoBC,WACtB,MAAMC,QAAgBC,EAAAA,EAAAA,IAAwB7F,IAC1C4F,IACA7G,GAAS6G,GACTnF,GAAemF,EAAQhG,aAAac,aACpCuC,GAAoD,WAArC2C,EAAQhG,aAAac,aACpCyC,GAAsD,aAArCyC,EAAQhG,aAAac,aACtCP,GAAkByF,EAAQhG,aAAaQ,gBACvCa,GAAa2E,EAAQhG,aAAasB,WAClCvB,GAAaiG,EAAQhG,aAAaC,MAClCS,GAAasF,EAAQhG,aAAaW,WAClCa,GAAawE,EAAQhG,aAAayB,WAClCE,GAAaqE,EAAQhG,aAAa4B,WAClCsD,GAAac,EAAQhG,aAAaqF,WAClC9C,GAAoByD,EAAQhG,aAAawC,aACzCtB,GAAqB8E,EAAQhG,aAAamB,WAC1CW,GAAwBkE,EAAQhG,aAAa+B,aAC7CE,GAAiB+D,EAAQhG,aAAagC,eACtCI,GAAmB4D,EAAQhG,aAAamC,iBACxCO,GAAasD,EAAQhG,aAAa2C,WAClCE,GAAemD,EAAQhG,aAAa4C,aACpCG,GAAeiD,EAAQhG,aAAa8C,aACpCG,GAAiB+C,EAAQhG,aAAakG,eACtCxG,GAASF,GAAS2G,SAAU,CACxBC,MAAO,CAAEC,SAAUL,GACnBM,SAAS,IAEjB,EAGJC,EAAmB,GACpB,CAACnG,GAAgBQ,KAEpB,MAAM4F,GAAsBT,UACxB,IAGI,SAFMU,KAEFrD,GAAa,CACb,MAAMsD,QAAeC,EAAAA,EAAAA,IAAevG,IACpC,GAAIsG,EAAOE,QAAS,CAChBhH,GAAa,GAADiH,OAAI/G,GAAS,KAAA+G,OAAIzF,GAAS,6BAA6B,WACnEP,GAAe,YACfwC,IAAe,GACfE,IAAiB,GAEjB,MAAMuD,QAAuBb,EAAAA,EAAAA,IAAwB7F,IACjD0G,IACA3H,GAAS2H,GACTjG,GAAeiG,EAAe9G,aAAac,aAC3CuC,GAA2D,WAA5CyD,EAAe9G,aAAac,aAC3CyC,GAA6D,aAA5CuD,EAAe9G,aAAac,aAErD,MACIlB,GAAa8G,EAAOK,eAAgB,QAE5C,KAAO,CAEH,MAAML,QAAeM,EAAAA,EAAAA,IAAa5G,IAClC,GAAIsG,EAAOE,QAAS,CAChBhH,GAAa,GAADiH,OAAI/G,GAAS,KAAA+G,OAAIzF,GAAS,2BAA2B,WACjEP,GAAe,UACfwC,IAAe,GACfE,IAAiB,GAEjB,MAAMuD,QAAuBb,EAAAA,EAAAA,IAAwB7F,IACjD0G,IACA3H,GAAS2H,GACTjG,GAAeiG,EAAe9G,aAAac,aAC3CuC,GAA2D,WAA5CyD,EAAe9G,aAAac,aAC3CyC,GAA6D,aAA5CuD,EAAe9G,aAAac,aAErD,MACIlB,GAAa8G,EAAOK,eAAgB,QAE5C,CAEJ,CAAE,MAAOE,GACLC,QAAQD,MAAM,+BAAgCA,EAClD,GAMEE,GAAkBA,KACpB,MAAM7F,EAAc,GAAAuF,OAAMzF,GAAUgG,cAAa,KACjD1H,GAAS,IAADmH,OAAKvF,GAAY,EAMvB+F,GAAatB,UACf/E,IAAa,GACb,IACI,MAAM0F,QAAeY,EAAAA,EAAAA,IAAelH,GAAgBE,GAAgBR,GAAWW,GAAWW,KACnE,IAAnBsF,EAAOE,QACPhH,GAAa8G,EAAOK,eAAgB,WAEpCnH,GAAa8G,EAAOa,aAAc,QAE1C,CAAE,MAAON,GACLC,QAAQD,MAAM,6BAA8BA,EAChD,IAQJnB,EAAAA,EAAAA,YAAU,KACN,GAAS,OAAL5G,SAAK,IAALA,IAAAA,GAAOsI,YAAa,CACpB,MAAMC,EAAsB,OAALvI,SAAK,IAALA,QAAK,EAALA,GAAOsI,YAAYE,KAAKC,IAAY,CACvD7D,SAAU6D,EAAQC,UAClB7D,IAAK4D,EAAQE,MACb7D,gBAAiB2D,EAAQG,SACzB7D,aAAc0D,EAAQ1D,iBAE1BJ,GAAY4D,EAChB,IACD,CAAM,OAALvI,SAAK,IAALA,QAAK,EAALA,GAAOsI,eAEX1B,EAAAA,EAAAA,YAAU,KACkB,IAApBlC,GAASmE,QACTlE,GAAY,CAAC,CAAEC,SAAU,SAAUC,KAAU,OAAL7E,SAAK,IAALA,QAAK,EAALA,GAAOc,aAAa2C,YAAa,GAAIsB,aAAc,GAAID,gBAAiB,KACpH,GACD,CAACJ,KAEJ,MAQMoE,GAAsBA,CAACC,EAAeC,EAAsBC,KAC9D,MAAMC,EAAY,GACZC,EAAkB,IAAIzE,IAC5ByE,EAAgBJ,GAAOC,GAASC,EAChC,IAAIlB,EAAQ,GACRkB,EAAMJ,OAAS,GAAe,QAAVG,EACtBjB,EAAQ,+BACCkB,EAAMJ,OAAS,IACxBd,EAAQ,mCACC,KAAKqB,KAAKH,IAAqB,QAAVD,EAC9BjB,EAAQ,wCAGNpD,GAAYwE,GAEZpB,EAAMc,OAAS,IAAKK,EAAUH,GAAShB,GAC3C9C,GAAgBiE,GAChBpJ,IAAc,EAAK,GAYvB8G,EAAAA,EAAAA,YAAU,KAAO,IAADyC,EACZ,MAAMC,EAAwB,OAALtJ,SAAK,IAALA,IAAmB,QAAdqJ,EAALrJ,GAAOc,oBAAY,IAAAuI,OAAd,EAALA,EAAqBpH,UAC9C,GAAyB,kBAArBqH,EAAsC,CACtC,MAAM,cAAEC,EAAa,cAAEC,GAZPC,KACpB,GAAa,OAATA,QAAS,IAATA,GAAAA,EAAWC,WAAW,WAA2B,kBAAdD,EAA+B,CAClE,MAAME,EAAQF,EAAUG,MAAM,KAG9B,MAAO,CAAEL,cAFII,EAAM,GAEWH,cADRG,EAAMd,OAAS,EAAIc,EAAM,GAAK,GAExD,CACA,MAAO,CAAEJ,cAAeE,EAAWD,cAAe,GAAI,EAKTK,CAAeP,GACxDtH,GAAqBuH,GACrBtF,GAAqBuF,EACzB,IACD,CAAClJ,GAAS4G,QACb,MAAO4C,GAAmBC,KAAwBhK,EAAAA,EAAAA,UAAS,KACpDiK,GAAQC,KAAalK,EAAAA,EAAAA,UAA0C,CAAC,IAYvE6G,EAAAA,EAAAA,YAAU,KACFjE,IACAX,GAAqB,gBACzB,GACD,CAACW,KAEJ,MAAMuH,GAAyBjB,IAEvBjH,GADU,WAAViH,EACqB,SAADtB,OAAU3D,IAETiF,GAEX,WAAVA,GACAhF,GAAqB,GACzB,GAiEGkG,GAAqBC,KAA0BrK,EAAAA,EAAAA,WAAqB,OAALC,SAAK,IAALA,QAAK,EAALA,GAAOsI,cAAe,KAE5F1B,EAAAA,EAAAA,YAAU,KACG,OAAL5G,SAAK,IAALA,IAAAA,GAAOsI,cAAoB,OAALtI,SAAK,IAALA,QAAK,EAALA,GAAOsI,YAAYO,QAAS,GAClDuB,GAA4B,OAALpK,SAAK,IAALA,QAAK,EAALA,GAAOsI,YAClC,GACD,CAAM,OAALtI,SAAK,IAALA,QAAK,EAALA,GAAOsI,cACX,MAAM+B,GAAyBxD,UAC3B/G,IAAc,GACd,MAAMwK,EAAc5F,GAAS6F,QAAO9B,IAAYA,EAAQ1D,eAClDyF,EAAmB9F,GAAS6F,QAAO9B,GAAWA,EAAQ1D,eAE5D,GAAIuF,EAAYzB,OAAS,EAAG,CACxB,MAAM4B,EAA0BH,EAAY9B,KAAKC,IAAO,CACpD1D,aAAc,GACd2D,UAAWD,EAAQ7D,SACnBgE,UAAiB,OAAPH,QAAO,IAAPA,OAAO,EAAPA,EAAS3D,kBAAmB,KACtC6D,MAAOF,EAAQ5D,IACfS,QAASpE,GACTI,eAAgB,OAEpB,IACI,MAAM6F,QAAiBuD,EAAAA,EAAAA,IAAeD,GAClCtD,EAASO,SACThH,GAAayG,EAASU,eAAgB,WACtC1H,IAAoB,GACpBwE,IAAYgG,GAAQA,EAAKJ,QAAO9B,KAAaA,EAAQ1D,iBACrD1E,IAAoB,IAGpBK,GAAayG,EAASkB,aAAc,QAE5C,CAAE,MAAON,GACLC,QAAQD,MAAM,iCAAkCA,EACpD,CACJ,CAGA,MAAM6C,EAAmBJ,EAAiBD,QAAQ9B,IAC9C,MAAMoC,EAAkBV,GAAoBW,MACvCC,GAAqBA,EAAYhG,eAAiB0D,EAAQ1D,eAG/D,OAAO8F,IACHA,EAAgBnC,YAAcD,EAAQ7D,UACtCiG,EAAgBjC,WAAaH,EAAQ3D,iBACrC+F,EAAgBlC,QAAUF,EAAQ5D,IACrC,IAGL,GAAI+F,EAAiB/B,OAAS,EAAG,CAC7B,MAAMmC,EAA+BJ,EAAiBpC,KAAKC,IAAO,CAC9D1D,aAAc0D,EAAQ1D,aACtB2D,UAAWD,EAAQ7D,SACnBgE,UAAiB,OAAPH,QAAO,IAAPA,OAAO,EAAPA,EAAS3D,kBAAmB,KACtC6D,MAAOF,EAAQ5D,IACfS,QAASpE,GACTI,eAAgB,OAGpB,IACI,MAAM6F,QAAiB8D,EAAAA,EAAAA,IAAiBD,GACpC7D,EAASO,SACThH,GAAayG,EAASU,eAAgB,WACtC1H,IAAoB,GACpBE,IAAoB,IAEpBK,GAAayG,EAASkB,aAAc,QAE5C,CAAE,MAAON,GACLC,QAAQD,MAAM,wCAAyCA,EAC3D,CACJ,CAEA,IACI,MAAMmD,QAA4BC,EAAAA,EAAAA,IAAejK,IACjD,GAAIgK,EAAqB,CAErB,MAQME,EAAmB,IAREF,EAAoB1C,KAAKC,IAAY,CAC5D7D,SAAU6D,EAAQC,UAClB7D,IAAK4D,EAAQE,MACb7D,gBAAiB2D,EAAQG,SACzB7D,aAAc0D,EAAQ1D,kBAkB1BqF,GAAuBc,GACvBvG,GAAYyG,EAChB,MACI1K,GAAawK,EAAoB7C,aAAc,QAEvD,CAAE,MAAON,GACLC,QAAQD,MAAM,+BAAgCA,EAClD,GAIER,GAAuBV,UAAa,IAADwE,EAErC,IAAIC,EAAY5J,GAIZ4J,EADAlH,IAAiBF,KAAgBrE,IAAcO,IAAoByB,IAAauB,MAA0B,OAALpD,SAAK,IAALA,IAAmB,QAAdqL,EAALrL,GAAOc,oBAAY,IAAAuK,OAAd,EAALA,EAAqB/H,cAC9G,QACLY,GACK,SACLE,GACK,WAEA,QAIhBzC,GAAe2J,GAEf,MAAMC,GAAQC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACVlG,QAASpE,GACTkB,UAAWF,GACXnB,KAAMH,GAAU6K,OAChBC,QAAQ,GAAD/D,OAAKzF,GAAS,YACrBZ,eAAgBF,GAChBsC,YAAaA,GACbiI,aAAa,IAAIlM,MAAOC,cACxBgD,UAAWF,GACXD,UAAWF,GACXJ,UAAW,gBACX2J,QAAS,YACTnK,UAAWF,GACX4E,UAAWJ,GACXnE,YAAa0J,EACbzI,aAAa,EACbG,YAAaF,GACbK,cAAeF,GACfK,YAAaF,GACbK,UAAWF,KAGQ,UAAd+H,GAAuC,aAAdA,IAA6B,CACvD1H,YAA+B,WAAlBd,GACPc,GACAhE,GACNkE,cAAmC,WAApBb,GACTa,GACA,OAGNI,IAAe,CACf8C,cAAmC,WAApB/D,GACTa,GACA,KACNF,YAA+B,WAAlBd,GACPc,GACAhE,MAITM,IAAoBE,UACfiK,KAEV,MAAMlD,QAAiB0E,EAAAA,EAAAA,IAAiBN,IAEf,IAArBpE,EAASO,SACThH,GAAa,GAADiH,OAAI/G,GAAS,KAAA+G,OAAIzF,GAAS,yBAAyB,WAC/D/B,IAAoB,GACpBE,IAAoB,GAGpBgE,GAA+B,aAAdiH,IAEjB5K,GAAayG,EAASkB,aAAc,SAExCvG,IAAa,EAAM,EA4BvB,OAzBJ8E,EAAAA,EAAAA,YAAU,KACN,GACwC,YAA/B,OAAL5G,SAAK,IAALA,QAAK,EAALA,GAAOc,aAAac,cACkB,YAAjC,OAAL5B,SAAK,IAALA,QAAK,EAALA,GAAOc,aAAaqC,gBACf,OAALnD,SAAK,IAALA,IAAAA,GAAOc,aAAagD,cACtB,CACE,MAAMgI,EAAWC,aAAYlF,UACzB,MAAMmF,EAAM,IAAIvM,KACVwM,EAAgBjM,GAAMc,aAAagD,cAAgB,IAAIrE,KAAKO,GAAMc,aAAagD,eAAiB,KACtG,GAAImI,GAAiBD,GAAOC,EAAe,OACjC3E,KACN,MAAMR,QAAgBC,EAAAA,EAAAA,IAAwB7F,IAC1C4F,IACA7G,GAAS6G,GACTnF,GAAemF,EAAQhG,aAAac,aACpCuC,GAAoD,WAArC2C,EAAQhG,aAAac,aACpCyC,GAAsD,aAArCyC,EAAQhG,aAAac,cAE1CsK,cAAcJ,EAClB,IACD,IACH,MAAO,IAAMI,cAAcJ,EAC/B,IACD,CAAC9L,MAGImM,EAAAA,EAAAA,KAACC,EAAAA,EAAS,CAACC,SAAS,KAAIC,UACpBH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,YAAWD,UACtBE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,mBAAkBD,SAAA,EAC7BE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,sBAAqBD,SAAA,EAChCH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gBAAeD,UAC1BE,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACC,WAAS,EAAClG,WAAW,SAAS+F,UAAU,oBAAmBD,SAAA,EAC7DH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAACS,EAAAA,EAAU,CAACC,QAAS5E,GAAgBqE,UACjCH,EAAAA,EAAAA,KAACW,EAAAA,EAAa,SAItBX,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAACC,QAAQ,QAAQT,UAAU,kBAAkBM,QAAS5E,GAAgBqE,SAAA,CAAC,WACtE,GAAA3E,OAAIzF,GAAS,gBAMrCiK,EAAAA,EAAAA,KAAA,OAAAG,UACIE,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACC,WAAS,EAACH,UAAU,eAAcD,SAAA,EACpCH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNE,EAAAA,EAAAA,MAACS,EAAAA,EAAG,CAACV,UAAU,iBAAgBD,SAAA,CAC1BzK,IACGsK,EAAAA,EAAAA,KAACe,EAAAA,EAAS,CACNC,KAAK,YACLH,QAAQ,WACR/D,MAAOrI,GACPwM,SA5UtBC,IAClB,MAAM,KAAEF,EAAI,MAAElE,GAAUoE,EAAMC,OAC9B,IAAIvF,EAAQ,GAEC,cAAToF,GAAwBlE,EAAMwC,OAAO5C,OAAS,IAC9Cd,EAAQ,6CAEZlH,GAAawM,EAAMC,OAAOrE,OAC1BgB,IAAWU,IAAIa,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAWb,GAAI,IAAE,CAACwC,GAAOpF,KAAS,EAqUTwF,UA3SrBF,IACD,UAAdA,EAAMG,IACNrF,KACqB,WAAdkF,EAAMG,MACb3M,GAAaiJ,IACbhI,IAAa,GACjB,EAsSwC2L,WAAYzD,GAAO0D,UACnB3F,QAASiC,GAAO0D,UAChBC,GAAI,CAAEC,YAAa,OAAQC,MAAO,YAGtC1B,EAAAA,EAAAA,KAACY,EAAAA,EAAU,CAACC,QAAQ,KAAKc,WAAW,OAAOvB,UAAU,kBAAiBD,SACjE1L,MAGTuL,EAAAA,EAAAA,KAACS,EAAAA,EAAU,CAACC,QAAShL,GAAYsG,GAhb7C4F,KACpBhE,GAAqBnJ,IACrBkB,IAAa,EAAK,EA+akBkM,SAAUnM,MAAemI,GAAO0D,UAAUpB,SAEzCzK,IAAasK,EAAAA,EAAAA,KAAC8B,EAAAA,EAAO,CAClBC,OAAK,EACLC,MAAM,OAAM7B,UAGZH,EAAAA,EAAAA,KAAA,OAAKiC,IAAKC,EAAUC,IAAI,gBAGxBnC,EAAAA,EAAAA,KAAC8B,EAAAA,EAAO,CAACC,OAAK,EAACC,MAAM,OAAM7B,UACvBH,EAAAA,EAAAA,KAACoC,EAAAA,EAAQ,cAO7BpC,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNE,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACC,WAAS,EAAC8B,QAAS,EAAElC,SAAA,EACvBH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNE,EAAAA,EAAAA,MAACiC,EAAAA,EAAM,CACHzB,QAAQ,WACR0B,MAAM,UACNnC,UAAU,mBACVoC,WAAWxC,EAAAA,EAAAA,KAAA,OAAKiC,IAAKQ,EAAiBN,IAAI,oBAC1CzB,QAASA,KAAMgC,EAAAA,EAAAA,GAAmBtL,GAAWrC,IAAgBoL,SAAA,CAChE,QACSpK,SAGdiK,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAACsC,EAAAA,EAAM,CACHzB,QAAQ,WACRH,QAAStF,GACTgF,UAAU,mBACVoC,WAAWxC,EAAAA,EAAAA,KAAA,OAAKiC,IAAKC,EAAUC,IAAI,aAAchC,SACpD,YAILH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAACsC,EAAAA,EAAM,CACHzB,QAAQ,YACRH,QAASvF,GACTiF,UAAS,6CAAA5E,OAA+CzD,GAAc,kBAAoB,IAAMyK,WAAWxC,EAAAA,EAAAA,KAAA,OAAKiC,IAAKlK,GAAc4K,EAAAA,GAAWC,EAAaT,IAAKpK,GAAc,WAAa,gBAAkBoI,SAE5MpI,GAAc,YAAc,eAGrCiI,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAACsC,EAAAA,EAAM,CACHzB,QAAQ,WACRgB,UAAU,EACVzB,UAAU,+BACVoC,WAAWxC,EAAAA,EAAAA,KAAA,OAAKiC,IAAKY,EAAWV,IAAI,gCAWhE9B,EAAAA,EAAAA,MAAA,OAAKD,UAAU,gBAAeD,SAAA,EAC1BE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,iBAAgBD,SAAA,EAC3BH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,oBAAmBD,UAC9BH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,GAAIC,GAAI,EAAE5C,UACrBH,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC5C,UAAU,aAAYD,UACxBE,EAAAA,EAAAA,MAAC4C,EAAAA,EAAW,CAAA9C,SAAA,EACRH,EAAAA,EAAAA,KAAA,SAAOkD,QAAQ,cAAc9C,UAAU,cAAaD,SAAE,iBACtDH,EAAAA,EAAAA,KAAA,YAAUmD,GAAG,cACTrG,MAAO7F,GACPmM,UAAW,IACXC,YAAY,mDACZpC,SAAWqC,GAAMpM,GAAoBoM,EAAEnC,OAAOrE,mBAUlEkD,EAAAA,EAAAA,KAAA,OAAKI,UAAU,mBAAkBD,UAC7BH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,GAAIC,GAAI,EAAE5C,UACrBH,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC5C,UAAU,6BAA4BD,UACxCE,EAAAA,EAAAA,MAAC4C,EAAAA,EAAW,CAAA9C,SAAA,EACRH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaD,SAAC,gBAInDvG,GAAU8C,OAAS,IAChB2D,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAACC,QAAQ,QAAQW,GAAI,CAAE+B,UAAW,KAAMC,SAAU,OAAQjB,MAAO,QAASpC,SAAA,CAChFvG,GAAU8C,OAAO,QAAM9C,GAAU8C,OAAS,EAAI,IAAM,OAG7DsD,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBD,SAC/BvG,GAAU8C,OAAS,EAChB9C,GAAUyC,KAAI,CAACoH,EAAW7G,KACtBoD,EAAAA,EAAAA,KAACc,EAAAA,EAAG,CAA4BV,UAAU,cAAaD,UACnDH,EAAAA,EAAAA,KAACe,EAAAA,EAAS,CACNF,QAAQ,WACR6C,WAAS,EACTC,QAAS,EACTC,QAAS,EACTC,WAAS,EACTC,UAAY,EACZC,WAAY,CAAED,UAAW,GACzBtC,GAAI,CACAwC,aAAc,MACd,2BAA4B,CACxB,aAAc,CACVC,YAAa,eAEjB,mBAAoB,CAChBA,YAAa,eAEjB,yBAA0B,CACtBA,YAAa,gBAGrB,aAAc,CACVC,SAAU,SACVC,OAAQ,OACRC,WAAY,MACZC,WAAY,QAGpBC,WAAY,CACRC,cACIvE,EAAAA,EAAAA,KAACwE,EAAAA,EAAc,CAACC,SAAS,MAAKtE,UAC1BE,EAAAA,EAAAA,MAACI,EAAAA,EAAU,CAACqD,UAAY,EAAE3D,SAAA,CAAC,KACvBH,EAAAA,EAAAA,KAAC0E,EAAAA,EAAgB,SAI7BC,UAAU,GAEdC,aAAY,GAAApJ,QAAS,OAAJiI,QAAI,IAAJA,OAAI,EAAJA,EAAMoB,YAAS,QAAArJ,OAAYoB,EAAQ,GAAG,MAAApB,OAAKpE,OAvC1DqM,EAAKqB,QAAUlI,MA4C7BoD,EAAAA,EAAAA,KAACY,EAAAA,EAAU,CAACC,QAAQ,QAAQW,GAAI,CAAE+B,UAAW,OAAQhB,MAAO,QAASpC,SAAC,gEAY3EE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,kBAAiBD,SAAA,EAG5BH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,oBAAmBD,UAC9BH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,GAAIC,GAAI,EAAE5C,UACrBH,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC5C,UAAU,0BAAyBD,UACrCE,EAAAA,EAAAA,MAAC4C,EAAAA,EAAW,CAAA9C,SAAA,EAERE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,yBAAwBD,SAAA,EACnCE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,uBAAsBD,SAAA,EACjCH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaD,SAAC,oBAC7BH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,iBAAgBD,SAAC,iDAMpCE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACIH,EAAAA,EAAAA,KAACsC,EAAAA,EAAM,CACHzB,QAAQ,WACRT,UAAU,uBACVM,QA5jB3BqE,KACjBxM,GAASmE,OAAS,IAClBlE,GAAY,IAAID,GAAU,CAAEE,SAAU,SAAUC,IAAK,GAAIE,aAAc,GAAID,gBAAiB,QAC5F3E,IAAoB,GACpBE,IAAoB,GACpB4E,GAAgB,IACpB,EAujBgD+I,SAAUtJ,GAASmE,QAAU,EAC7B8F,WAAWxC,EAAAA,EAAAA,KAACgF,EAAAA,EAAe,IAAI7E,SAClC,gBAIDH,EAAAA,EAAAA,KAACS,EAAAA,EAAU,CAACC,QAASxC,GAAwB2D,UAAYnO,IAAcmF,GAAa6D,OAAS,EAAG0D,UAAU,4BAA6B6E,MAAO,CAAEC,SAAUxR,IAAcmF,GAAa6D,OAAS,EAAI,GAAM,GAAIyD,UACxMH,EAAAA,EAAAA,KAAA,OAAKiC,IAAKC,EAAUC,IAAI,WAAa8C,MAAO,CAAEC,SAAUxR,IAAcmF,GAAa6D,OAAS,EAAI,GAAM,cAIlH2D,EAAAA,EAAAA,MAAA,OAAKD,UAAU,mBAAkBD,SAAA,CAChC5H,GAASmE,OAAS,IACX2D,EAAAA,EAAAA,MAAC8E,EAAAA,EAAW,CAAC/E,UAAU,iBAAgBD,SAAA,EACnCH,EAAAA,EAAAA,KAAA,QAAAG,SAAM,eACNE,EAAAA,EAAAA,MAAC+E,EAAAA,EAAM,CACHtI,MAAO,KACP+E,UAAQ,EACRZ,SAAWqC,GAjdtC+B,EAACzI,EAAeE,KACzC,MAAME,EAAkB,IAAIzE,IAGxBqE,EAAQI,EAAgBN,OAAS,IACjCM,EAAgBJ,EAAQ,GAAGjE,gBAAkBmE,GAGjDtE,GAAYwE,EAAgB,EAycyCqI,CAAqB,EAAG/B,EAAEnC,OAAOrE,OAAQqD,SAAA,EAE1DH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,MAAKqD,SAAC,SACtBH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,KAAIqD,SAAC,aAIhC5H,GAAS8D,KAAI,CAACC,EAASM,KACpByD,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACC,WAAS,EAAC8B,QAAS,EAAElC,SAAA,EAEvBH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,EAAE3C,UACZE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACzDH,EAAAA,EAAAA,KAAA,SAAOI,UAAU,oBAAmBD,SAAC,eACrCH,EAAAA,EAAAA,KAACmF,EAAAA,EAAW,CAACtB,WAAS,EAAA1D,UAClBE,EAAAA,EAAAA,MAAC+E,EAAAA,EAAM,CACHtI,MAAOR,EAAQ7D,SAC6CwI,SAAWqC,GAAM3G,GAAoBC,EAAO,WAAY0G,EAAEnC,OAAOrE,OACjEyI,YAAcC,GAAaA,EAC3FhE,GAAI,CACA,2CAA4C,CACxCiE,OAAQ,kBAEZ,iDAAkD,CAC9CA,OAAQ,kBAEZ,oCAAqC,CACjCC,OAAQ,oBAEdvF,SAAA,EAEEE,EAAAA,EAAAA,MAACiF,EAAAA,EAAQ,CAAC9D,GAAI,CAAEgC,SAAU,OAAQpJ,QAAS,OAAQuL,eAAgB,iBAAmB7I,MAAM,SAAQqD,SAAA,CAAC,UAErCH,EAAAA,EAAAA,KAAA,OAAKiC,IAAK2D,EAAYzD,IAAI,cAE1F9B,EAAAA,EAAAA,MAACiF,EAAAA,EAAQ,CAAC9D,GAAI,CAAEgC,SAAU,OAAOpJ,QAAS,OAAQuL,eAAgB,iBAAmB7I,MAAM,aAAYqD,SAAA,CAAC,gBAAYH,EAAAA,EAAAA,KAAA,OAAKiC,IAAK2D,EAAYzD,IAAI,cAC9I9B,EAAAA,EAAAA,MAACiF,EAAAA,EAAQ,CAAC9D,GAAI,CAAEgC,SAAU,OAAOpJ,QAAS,OAAQuL,eAAgB,iBAAmB7I,MAAM,cAAaqD,SAAA,CAAC,iBAAaH,EAAAA,EAAAA,KAAA,OAAKiC,IAAK2D,EAAYzD,IAAI,cAChJ9B,EAAAA,EAAAA,MAACiF,EAAAA,EAAQ,CAAC9D,GAAI,CAAEgC,SAAU,OAAOpJ,QAAS,OAAQuL,eAAgB,iBAAmB7I,MAAM,YAAWqD,SAAA,CAAC,eAAWH,EAAAA,EAAAA,KAAA,OAAKiC,IAAK2D,EAAYzD,IAAI,cAC5I9B,EAAAA,EAAAA,MAACiF,EAAAA,EAAQ,CAAC9D,GAAI,CAAEgC,SAAU,OAAOpJ,QAAS,OAAQuL,eAAgB,iBAAkB7I,MAAM,WAAUqD,SAAA,CAAC,cAAUH,EAAAA,EAAAA,KAAA,OAAKiC,IAAK2D,EAAYzD,IAAI,cACzI9B,EAAAA,EAAAA,MAACiF,EAAAA,EAAQ,CAAC9D,GAAI,CAAEgC,SAAU,OAAQpJ,QAAS,OAAQuL,eAAgB,iBAAiB7I,MAAM,eAAcqD,SAAA,CAAC,sBAAkBH,EAAAA,EAAAA,KAAA,OAAKiC,IAAK2D,EAAYzD,IAAI,wBAOzGnC,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,EAAE3C,UACjBE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACpDH,EAAAA,EAAAA,KAAA,SAAOI,UAAU,oBAAmBD,SAAC,SACrCH,EAAAA,EAAAA,KAACe,EAAAA,EAAS,CACN8C,WAAS,EACT/G,MAAOR,EAAQ5D,IACfuI,SAAWqC,GAAM3G,GAAoBC,EAAO,MAAO0G,EAAEnC,OAAOrE,OAC5DlB,QAAS/C,GAAa+D,GACtB0E,aACMzI,GAAa+D,KACXyD,EAAAA,EAAAA,MAAA,QAAM4E,MAAO,CAAE7K,QAAS,OAAQC,WAAY,SAAUwL,IAAK,OAAQ1F,SAAA,EAC/DH,EAAAA,EAAAA,KAAA,OAAKiC,IAAK6D,EAAS3D,IAAI,eACtBtJ,GAAa+D,MAI1B0H,WAAY,CACR9C,GAAI,CACA,2CAA4C,CACxCiE,OAAQ,kBAEd,iDAAkD,CAChDA,OAAQ,kBAER,oCAAqC,CACjCC,OAAQ,mBAEZ,+CAAgD,CAChDzB,YAAa,gCAO2BjE,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,EAAG1C,UAAS,iBAAA5E,OAAqB3C,GAAa+D,GAAS,kBAAoB,IAAOqI,MAAO,CAAEC,QAA6B,IAApB3M,GAASmE,OAAe,GAAM,GAAIyD,UACjJH,EAAAA,EAAAA,KAACS,EAAAA,EAAU,CAACC,QAASA,KACjBpI,GAAesE,GACfxE,IAAc,EAAK,EACpByJ,SAA8B,IAApBtJ,GAASmE,OAAayD,UAE7CH,EAAAA,EAAAA,KAAA,OACFiC,IAAK8D,EACL5D,IAAI,YACJ8C,MAAO,CAAEC,QAA6B,IAApB3M,GAASmE,OAAe,GAAM,WAjFXE,KA6H5EzE,KACE6H,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBD,UACvCE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,sCAAqCD,SAAA,EAClDH,EAAAA,EAAAA,KAAA,OAAAG,UACEH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,aAAYD,UACzBH,EAAAA,EAAAA,KAACS,EAAAA,EAAU,CACOL,UAAU,YAAWD,UACjBH,EAAAA,EAAAA,KAAA,KAAGI,UAAU,4BAI3BJ,EAAAA,EAAAA,KAAA,OAAKI,UAAU,oBAAmBD,SAAC,oBAI7CH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gBAAeD,SAAC,mDAIrCE,EAAAA,EAAAA,MAAA,OAAKD,UAAU,gBAAeD,SAAA,EAC5BH,EAAAA,EAAAA,KAAA,UACEU,QAASA,IAAMtI,IAAc,GAC7BgI,UAAU,sBAAqBD,SAChC,YAIDH,EAAAA,EAAAA,KAAA,UACEU,QAtpBsBhG,UACxB,GAAoB,OAAhBrC,GAAsB,OAE1B,MAAM2N,EAAkBzN,GAASF,IAEjC,IACI,GAAI2N,EAAgBpN,aAAc,CAC9B,MAAMqN,EAAeD,EAAgBpN,aAC/BsN,EAAS,CACXnR,kBACAkR,gBAEEjL,QAAiBmL,EAAAA,EAAAA,IAAiBD,GACxC,GAAIlL,EAASO,QAAS,CAClBhH,GAAayG,EAASU,eAAgB,WACtC,MAAMsB,EAAkBzE,GAAS6F,QAAO,CAACgI,EAAGC,IAAMA,IAAMhO,KACxDG,GAAYwE,EAChB,MACIzI,GAAayG,EAASkB,aAAc,QAE5C,MACI,GAAI3D,GAASmE,OAAS,EAAG,CACrB,MAAMM,EAAkBzE,GAAS6F,QAAO,CAACgI,EAAGC,IAAMA,IAAMhO,KACxDG,GAAYwE,EAChB,CAER,CAAE,MAAOpB,GACLC,QAAQD,MAAM,8BAA+BA,GAC7CrH,GAAa,kDAAmD,QACpE,CAAC,QAEG+D,GAAe,MACfF,IAAc,EAClB,GAsnBEgI,UAAU,uBACVkG,KAAK,SAAQnG,SACd,iCAkBmBH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,qBAAoBD,UAE/BH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,GAAIC,GAAI,EAAE5C,UACrBH,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC5C,UAAU,2BAA0BD,UACtCE,EAAAA,EAAAA,MAAC4C,EAAAA,EAAW,CAAA9C,SAAA,EACRE,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACC,WAAS,EAAAJ,SAAA,EACXH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaD,SAAC,kBAC7BH,EAAAA,EAAAA,KAACuG,EAAAA,EAAM,CACHC,SAAS,EACTvF,SAAWqC,GAAM7M,GAAwB6M,EAAEnC,OAAOqF,SAClD3E,UAAU,QAGlBxB,EAAAA,EAAAA,MAAA,OAAKD,UAAU,iBAAiB6E,MAAO,CAAEZ,WAAY,GAAIlE,SAAA,CAAC,4CACZpK,GAAUgG,cAAc,qEAStFiE,EAAAA,EAAAA,KAAA,OAAKI,UAAU,kBAAiBD,UAE5BH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,GAAIC,GAAI,EAAE5C,UACrBH,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC5C,UAAU,aAAYD,UACxBE,EAAAA,EAAAA,MAAC4C,EAAAA,EAAW,CAAA9C,SAAA,EACRH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaD,SAAC,eAC7BH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,iBAAgBD,SAAC,yCAChCH,EAAAA,EAAAA,KAACmF,EAAAA,EAAW,CAACsB,UAAU,WAAW5E,UAAWrL,GAAqB2J,UAC9DE,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACC,WAAS,EAAC8B,QAAS,EAAGhI,WAAW,SAASqM,KAAK,SAAQvG,SAAA,EACzDH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAAC2G,EAAAA,EAAgB,CACbC,SACI5G,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,CACFL,QAA+B,aAAtB5Q,GACTqL,SAAUA,IAAMlD,GAAsB,YACtC8D,UAAQ,IAGhBiF,OAAO9G,EAAAA,EAAAA,KAAA,QAAMI,UAAU,mBAAkBD,SAAC,mBAGlDH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAAC2G,EAAAA,EAAgB,CACbC,SACI5G,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,CAEFL,SAAS,EACTvF,SAAUA,IAAMlD,GAAsB,mBAG9C+I,OAAO9G,EAAAA,EAAAA,KAAA,QAAMI,UAAU,mBAAkBD,SAAC,2BAGlDH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAAC2G,EAAAA,EAAgB,CACbC,SACI5G,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,CACFL,QAA+B,aAAtB5Q,GACTqL,SAAUA,IAAMlD,GAAsB,YACtC8D,UAAQ,IAGhBiF,OAAO9G,EAAAA,EAAAA,KAAA,QAAMI,UAAU,mBAAkBD,SAAC,oBAGlDH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAAC2G,EAAAA,EAAgB,CACbC,SACI5G,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,CACFL,SAA0B,OAAjB5Q,SAAiB,IAAjBA,QAAiB,EAAjBA,GAAmB2H,WAAW,YAAmC,kBAAtB3H,GACpDqL,SAAUA,IAAMlD,GAAsB,UACtC8D,UAAQ,IAGhBiF,OAAO9G,EAAAA,EAAAA,KAAA,QAAMI,UAAU,mBAAkBD,SAAC,iBAGlDH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAAAL,UACNH,EAAAA,EAAAA,KAACmF,EAAAA,EAAW,CAAC3D,GAAI,CAAEuF,WAAY,MAAOC,SAAU,QAAUnF,UAAWrL,MAA0C,OAAjBZ,SAAiB,IAAjBA,IAAAA,GAAmB2H,WAAW,WAAU4C,UAClIE,EAAAA,EAAAA,MAAC+E,EAAAA,EAAM,CACH6B,QAAQ,yBACRnK,MAAOjF,GACP+M,aAAa,QACb3D,SAjxB3BqC,IAC7B,MAAMxG,EAAQwG,EAAEnC,OAAOrE,MACF,OAAjBlH,SAAiB,IAAjBA,IAAAA,GAAmB2H,WAAW,WAAmC,kBAAtB3H,KAC3CkC,GAAqBgF,GACrBjH,GAAqB,UAAD2F,OAAWsB,IACnC,EA6wBwD+E,UAAQ,EAAA1B,SAAA,EAERH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,QAAOqD,SAAC,YACxBH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,QAAOqD,SAAC,YACxBH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,OAAMqD,SAAC,UACvBH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,QAAOqD,SAAC,WACxBH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,UAASqD,SAAC,aAC1BH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,cAAaqD,SAAC,iBAC9BH,EAAAA,EAAAA,KAACsF,EAAAA,EAAQ,CAACxI,MAAM,SAAQqD,SAAC,iCAe7DH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,oBAAmBD,UAC9BH,EAAAA,EAAAA,KAACM,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,GAAIC,GAAI,EAAE5C,UACrBH,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAC5C,UAAU,uBAAsBD,UAClCE,EAAAA,EAAAA,MAAC4C,EAAAA,EAAW,CAAA9C,SAAA,EACRH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaD,SAAC,sBAC7BE,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACC,WAAS,EAAC8B,QAAS,EAAElC,SAAA,EACvBE,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,EAAG1C,UAAU,iBAAgBD,SAAA,EACxCE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAT,SAAA,CAAC,WAAQ,GAAA3E,OAAIzF,IAAY,QACpCiK,EAAAA,EAAAA,KAACmF,EAAAA,EAAW,CAACsB,UAAU,WAAUtG,UAC7BE,EAAAA,EAAAA,MAAC6G,EAAAA,EAAU,CACPpK,MAAOnG,GACPsK,SAAWqC,GAAM1M,GAAiB0M,EAAEnC,OAAOrE,OAAOqD,SAAA,EAElDH,EAAAA,EAAAA,KAAC2G,EAAAA,EAAgB,CACb7J,MAAM,cACN8J,SAAS5G,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,IACfC,MAAM,iBAEV9G,EAAAA,EAAAA,KAAC2G,EAAAA,EAAgB,CACb7J,MAAM,SACN8J,SAAS5G,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,IACfC,MAAM,qBAIC,WAAlBnQ,KACGqJ,EAAAA,EAAAA,KAACe,EAAAA,EAAS,CACNuF,KAAK,iBACLxJ,MAAOrF,GACPwJ,SA3iCxBC,IAC5BxJ,GAAewJ,EAAMC,OAAOrE,MAAM,EA2iCkB+G,WAAS,EACTrC,GAAI,CAAE+B,UAAW,OACjBQ,WAAY,CACRoD,IAAK9T,UAMrBgN,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACE,MAAI,EAACsC,GAAI,EAAG1C,UAAU,kBAAiBD,SAAA,EACzCE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAT,SAAA,CAAC,aAAU,GAAA3E,OAAIzF,IAAY,QACtCiK,EAAAA,EAAAA,KAACmF,EAAAA,EAAW,CAACsB,UAAU,WAAUtG,UAC7BE,EAAAA,EAAAA,MAAC6G,EAAAA,EAAU,CACPpK,MAAOhG,GACPmK,SAAWqC,GAAMvM,GAAmBuM,EAAEnC,OAAOrE,OAAOqD,SAAA,EAEpDH,EAAAA,EAAAA,KAAC2G,EAAAA,EAAgB,CACb7J,MAAM,WACN8J,SAAS5G,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,IACfC,MAAM,cAEV9G,EAAAA,EAAAA,KAAC2G,EAAAA,EAAgB,CACb7J,MAAM,SACN8J,SAAS5G,EAAAA,EAAAA,KAAC6G,EAAAA,EAAK,IACfC,MAAM,qBAKG,WAApBhQ,KACGkJ,EAAAA,EAAAA,KAACe,EAAAA,EAAS,CACNuF,KAAK,iBACLxJ,MAAOnF,GACPsJ,SAzkCrBC,IAC/BtJ,GAAiBsJ,EAAMC,OAAOrE,MAAM,EAykCgB+G,WAAS,EACTrC,GAAI,CAAE+B,UAAW,OACjBQ,WAAY,CACRoD,IAAK9T,iCAejD,C", "sources": ["assets/icons/OpenNewWindow.svg", "assets/icons/PublishIcon.svg", "assets/icons/check_small.svg", "assets/icons/SaveIcon.svg", "assets/icons/Warning.svg", "assets/icons/ShareIcon.svg", "assets/icons/Targetdelete.svg", "components/webappsettingspage/WebAppSettings.tsx"], "sourcesContent": ["var _path;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgOpenNewWindow(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 18,\n    height: 18,\n    viewBox: \"0 0 18 18\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M2 18C1.45 18 0.979167 17.8042 0.5875 17.4125C0.195833 17.0208 0 16.55 0 16V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H9V2H2V16H16V9H18V16C18 16.55 17.8042 17.0208 17.4125 17.4125C17.0208 17.8042 16.55 18 16 18H2ZM6.7 12.7L5.3 11.3L14.6 2H11V0H18V7H16V3.4L6.7 12.7Z\",\n    fill: \"#5F9EA0\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgOpenNewWindow);\nexport default __webpack_public_path__ + \"static/media/OpenNewWindow.059466fa7af6ab96a666d8d50091ed20.svg\";\nexport { ForwardRef as ReactComponent };", "var _path;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgPublishIcon(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 22,\n    height: 16,\n    viewBox: \"0 0 22 16\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.5 16C3.98333 16 2.6875 15.475 1.6125 14.425C0.5375 13.375 0 12.0917 0 10.575C0 9.275 0.391667 8.11667 1.175 7.1C1.95833 6.08333 2.98333 5.43333 4.25 5.15C4.66667 3.61667 5.5 2.375 6.75 1.425C8 0.475 9.41667 0 11 0C12.95 0 14.6042 0.679167 15.9625 2.0375C17.3208 3.39583 18 5.05 18 7C19.15 7.13333 20.1042 7.62917 20.8625 8.4875C21.6208 9.34583 22 10.35 22 11.5C22 12.75 21.5625 13.8125 20.6875 14.6875C19.8125 15.5625 18.75 16 17.5 16H12C11.45 16 10.9792 15.8042 10.5875 15.4125C10.1958 15.0208 10 14.55 10 14V8.85L8.4 10.4L7 9L11 5L15 9L13.6 10.4L12 8.85V14H17.5C18.2 14 18.7917 13.7583 19.275 13.275C19.7583 12.7917 20 12.2 20 11.5C20 10.8 19.7583 10.2083 19.275 9.725C18.7917 9.24167 18.2 9 17.5 9H16V7C16 5.61667 15.5125 4.4375 14.5375 3.4625C13.5625 2.4875 12.3833 2 11 2C9.61667 2 8.4375 2.4875 7.4625 3.4625C6.4875 4.4375 6 5.61667 6 7H5.5C4.53333 7 3.70833 7.34167 3.025 8.025C2.34167 8.70833 2 9.53333 2 10.5C2 11.4667 2.34167 12.2917 3.025 12.975C3.70833 13.6583 4.53333 14 5.5 14H8V16H5.5Z\",\n    fill: \"white\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgPublishIcon);\nexport default __webpack_public_path__ + \"static/media/PublishIcon.6a1093cdcc5e542585986fcb8cacb3d0.svg\";\nexport { ForwardRef as ReactComponent };", "var _rect, _g;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgCheckSmall(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: \"mask0_16792_1306\",\n    style: {\n      maskType: \"alpha\"\n    },\n    maskUnits: \"userSpaceOnUse\",\n    x: 0,\n    y: 0,\n    width: 24,\n    height: 24\n  }, _rect || (_rect = /*#__PURE__*/React.createElement(\"rect\", {\n    width: 24,\n    height: 24,\n    fill: \"#D9D9D9\"\n  }))), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    mask: \"url(#mask0_16792_1306)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M9.33333 18L4 12.4681L5.86667 10.5319L9.33333 14.1277L18.1333 5L20 6.93617L9.33333 18Z\",\n    fill: \"#5F9EA0\"\n  }))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgCheckSmall);\nexport default __webpack_public_path__ + \"static/media/check_small.cb756a537b1bd4966a6b125d7f380848.svg\";\nexport { ForwardRef as ReactComponent };", "var _path;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgSaveIcon(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 18,\n    height: 18,\n    viewBox: \"0 0 18 18\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M18 4V16C18 16.55 17.8042 17.0208 17.4125 17.4125C17.0208 17.8042 16.55 18 16 18H2C1.45 18 0.979167 17.8042 0.5875 17.4125C0.195833 17.0208 0 16.55 0 16V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H14L18 4ZM16 4.85L13.15 2H2V16H16V4.85ZM9 15C9.83333 15 10.5417 14.7083 11.125 14.125C11.7083 13.5417 12 12.8333 12 12C12 11.1667 11.7083 10.4583 11.125 9.875C10.5417 9.29167 9.83333 9 9 9C8.16667 9 7.45833 9.29167 6.875 9.875C6.29167 10.4583 6 11.1667 6 12C6 12.8333 6.29167 13.5417 6.875 14.125C7.45833 14.7083 8.16667 15 9 15ZM3 7H12V3H3V7Z\",\n    fill: \"#5F9EA0\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgSaveIcon);\nexport default __webpack_public_path__ + \"static/media/SaveIcon.f6c223459d0635e3565f4cef408c20fa.svg\";\nexport { ForwardRef as ReactComponent };", "var _rect, _g;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgWarning(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 21,\n    height: 21,\n    viewBox: \"0 0 21 21\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: \"mask0_16750_6337\",\n    style: {\n      maskType: \"alpha\"\n    },\n    maskUnits: \"userSpaceOnUse\",\n    x: 0,\n    y: 0,\n    width: 21,\n    height: 21\n  }, _rect || (_rect = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 0.078125,\n    y: 0.5,\n    width: 20,\n    height: 20,\n    fill: \"#D9D9D9\"\n  }))), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    mask: \"url(#mask0_16750_6337)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M10.0738 14.5C10.285 14.5 10.4635 14.4285 10.6094 14.2856C10.7552 14.1427 10.8281 13.9656 10.8281 13.7544C10.8281 13.5431 10.7567 13.3646 10.6138 13.2188C10.4708 13.0729 10.2938 13 10.0825 13C9.87125 13 9.69271 13.0715 9.54688 13.2144C9.40104 13.3573 9.32812 13.5344 9.32812 13.7456C9.32812 13.9569 9.39958 14.1354 9.5425 14.2812C9.68542 14.4271 9.8625 14.5 10.0738 14.5ZM9.32812 11.5H10.8281V6.5H9.32812V11.5ZM10.084 18.5C8.98285 18.5 7.94618 18.2917 6.97396 17.875C6.00174 17.4583 5.15104 16.8854 4.42188 16.1562C3.69271 15.4271 3.11979 14.5767 2.70312 13.605C2.28646 12.6333 2.07812 11.5951 2.07812 10.4904C2.07812 9.38569 2.28646 8.35069 2.70312 7.38542C3.11979 6.42014 3.69271 5.57292 4.42188 4.84375C5.15104 4.11458 6.00146 3.54167 6.97312 3.125C7.94479 2.70833 8.98299 2.5 10.0877 2.5C11.1924 2.5 12.2274 2.70833 13.1927 3.125C14.158 3.54167 15.0052 4.11458 15.7344 4.84375C16.4635 5.57292 17.0365 6.42167 17.4531 7.39C17.8698 8.35847 18.0781 9.39319 18.0781 10.4942C18.0781 11.5953 17.8698 12.6319 17.4531 13.6042C17.0365 14.5764 16.4635 15.4271 15.7344 16.1562C15.0052 16.8854 14.1565 17.4583 13.1881 17.875C12.2197 18.2917 11.1849 18.5 10.084 18.5Z\",\n    fill: \"#E6A957\"\n  }))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgWarning);\nexport default __webpack_public_path__ + \"static/media/Warning.e0885f593d31d785ec668e8106ec19b4.svg\";\nexport { ForwardRef as ReactComponent };", "var _rect, _rect2, _g;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgShareIcon(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: \"mask0_5211_2241\",\n    style: {\n      maskType: \"alpha\"\n    },\n    maskUnits: \"userSpaceOnUse\",\n    x: 0,\n    y: 0,\n    width: 24,\n    height: 24\n  }, _rect || (_rect = /*#__PURE__*/React.createElement(\"rect\", {\n    width: 24,\n    height: 24,\n    fill: \"#5F9EA0\"\n  })), _rect2 || (_rect2 = /*#__PURE__*/React.createElement(\"rect\", {\n    width: 24,\n    height: 24,\n    fill: \"white\",\n    fillOpacity: 0.5\n  }))), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    mask: \"url(#mask0_5211_2241)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17 22C16.1667 22 15.4583 21.7083 14.875 21.125C14.2917 20.5417 14 19.8333 14 19C14 18.9 14.025 18.6667 14.075 18.3L7.05 14.2C6.78333 14.45 6.475 14.6458 6.125 14.7875C5.775 14.9292 5.4 15 5 15C4.16667 15 3.45833 14.7083 2.875 14.125C2.29167 13.5417 2 12.8333 2 12C2 11.1667 2.29167 10.4583 2.875 9.875C3.45833 9.29167 4.16667 9 5 9C5.4 9 5.775 9.07083 6.125 9.2125C6.475 9.35417 6.78333 9.55 7.05 9.8L14.075 5.7C14.0417 5.58333 14.0208 5.47083 14.0125 5.3625C14.0042 5.25417 14 5.13333 14 5C14 4.16667 14.2917 3.45833 14.875 2.875C15.4583 2.29167 16.1667 2 17 2C17.8333 2 18.5417 2.29167 19.125 2.875C19.7083 3.45833 20 4.16667 20 5C20 5.83333 19.7083 6.54167 19.125 7.125C18.5417 7.70833 17.8333 8 17 8C16.6 8 16.225 7.92917 15.875 7.7875C15.525 7.64583 15.2167 7.45 14.95 7.2L7.925 11.3C7.95833 11.4167 7.97917 11.5292 7.9875 11.6375C7.99583 11.7458 8 11.8667 8 12C8 12.1333 7.99583 12.2542 7.9875 12.3625C7.97917 12.4708 7.95833 12.5833 7.925 12.7L14.95 16.8C15.2167 16.55 15.525 16.3542 15.875 16.2125C16.225 16.0708 16.6 16 17 16C17.8333 16 18.5417 16.2917 19.125 16.875C19.7083 17.4583 20 18.1667 20 19C20 19.8333 19.7083 20.5417 19.125 21.125C18.5417 21.7083 17.8333 22 17 22ZM17 20C17.2833 20 17.5208 19.9042 17.7125 19.7125C17.9042 19.5208 18 19.2833 18 19C18 18.7167 17.9042 18.4792 17.7125 18.2875C17.5208 18.0958 17.2833 18 17 18C16.7167 18 16.4792 18.0958 16.2875 18.2875C16.0958 18.4792 16 18.7167 16 19C16 19.2833 16.0958 19.5208 16.2875 19.7125C16.4792 19.9042 16.7167 20 17 20ZM5 13C5.28333 13 5.52083 12.9042 5.7125 12.7125C5.90417 12.5208 6 12.2833 6 12C6 11.7167 5.90417 11.4792 5.7125 11.2875C5.52083 11.0958 5.28333 11 5 11C4.71667 11 4.47917 11.0958 4.2875 11.2875C4.09583 11.4792 4 11.7167 4 12C4 12.2833 4.09583 12.5208 4.2875 12.7125C4.47917 12.9042 4.71667 13 5 13ZM17 6C17.2833 6 17.5208 5.90417 17.7125 5.7125C17.9042 5.52083 18 5.28333 18 5C18 4.71667 17.9042 4.47917 17.7125 4.2875C17.5208 4.09583 17.2833 4 17 4C16.7167 4 16.4792 4.09583 16.2875 4.2875C16.0958 4.47917 16 4.71667 16 5C16 5.28333 16.0958 5.52083 16.2875 5.7125C16.4792 5.90417 16.7167 6 17 6Z\",\n    fill: \"#5F9EA0\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17 22C16.1667 22 15.4583 21.7083 14.875 21.125C14.2917 20.5417 14 19.8333 14 19C14 18.9 14.025 18.6667 14.075 18.3L7.05 14.2C6.78333 14.45 6.475 14.6458 6.125 14.7875C5.775 14.9292 5.4 15 5 15C4.16667 15 3.45833 14.7083 2.875 14.125C2.29167 13.5417 2 12.8333 2 12C2 11.1667 2.29167 10.4583 2.875 9.875C3.45833 9.29167 4.16667 9 5 9C5.4 9 5.775 9.07083 6.125 9.2125C6.475 9.35417 6.78333 9.55 7.05 9.8L14.075 5.7C14.0417 5.58333 14.0208 5.47083 14.0125 5.3625C14.0042 5.25417 14 5.13333 14 5C14 4.16667 14.2917 3.45833 14.875 2.875C15.4583 2.29167 16.1667 2 17 2C17.8333 2 18.5417 2.29167 19.125 2.875C19.7083 3.45833 20 4.16667 20 5C20 5.83333 19.7083 6.54167 19.125 7.125C18.5417 7.70833 17.8333 8 17 8C16.6 8 16.225 7.92917 15.875 7.7875C15.525 7.64583 15.2167 7.45 14.95 7.2L7.925 11.3C7.95833 11.4167 7.97917 11.5292 7.9875 11.6375C7.99583 11.7458 8 11.8667 8 12C8 12.1333 7.99583 12.2542 7.9875 12.3625C7.97917 12.4708 7.95833 12.5833 7.925 12.7L14.95 16.8C15.2167 16.55 15.525 16.3542 15.875 16.2125C16.225 16.0708 16.6 16 17 16C17.8333 16 18.5417 16.2917 19.125 16.875C19.7083 17.4583 20 18.1667 20 19C20 19.8333 19.7083 20.5417 19.125 21.125C18.5417 21.7083 17.8333 22 17 22ZM17 20C17.2833 20 17.5208 19.9042 17.7125 19.7125C17.9042 19.5208 18 19.2833 18 19C18 18.7167 17.9042 18.4792 17.7125 18.2875C17.5208 18.0958 17.2833 18 17 18C16.7167 18 16.4792 18.0958 16.2875 18.2875C16.0958 18.4792 16 18.7167 16 19C16 19.2833 16.0958 19.5208 16.2875 19.7125C16.4792 19.9042 16.7167 20 17 20ZM5 13C5.28333 13 5.52083 12.9042 5.7125 12.7125C5.90417 12.5208 6 12.2833 6 12C6 11.7167 5.90417 11.4792 5.7125 11.2875C5.52083 11.0958 5.28333 11 5 11C4.71667 11 4.47917 11.0958 4.2875 11.2875C4.09583 11.4792 4 11.7167 4 12C4 12.2833 4.09583 12.5208 4.2875 12.7125C4.47917 12.9042 4.71667 13 5 13ZM17 6C17.2833 6 17.5208 5.90417 17.7125 5.7125C17.9042 5.52083 18 5.28333 18 5C18 4.71667 17.9042 4.47917 17.7125 4.2875C17.5208 4.09583 17.2833 4 17 4C16.7167 4 16.4792 4.09583 16.2875 4.2875C16.0958 4.47917 16 4.71667 16 5C16 5.28333 16.0958 5.52083 16.2875 5.7125C16.4792 5.90417 16.7167 6 17 6Z\",\n    fill: \"white\",\n    fillOpacity: 0.5\n  }))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgShareIcon);\nexport default __webpack_public_path__ + \"static/media/ShareIcon.31ff833787dcfd0d114788c6c5dfadfc.svg\";\nexport { ForwardRef as ReactComponent };", "var _rect, _g;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgTargetdelete(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 25,\n    height: 24,\n    viewBox: \"0 0 25 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"mask\", {\n    id: \"mask0_16753_8905\",\n    style: {\n      maskType: \"alpha\"\n    },\n    maskUnits: \"userSpaceOnUse\",\n    x: 0,\n    y: 0,\n    width: 25,\n    height: 24\n  }, _rect || (_rect = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 0.929688,\n    width: 24,\n    height: 24,\n    fill: \"#D9D9D9\"\n  }))), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    mask: \"url(#mask0_16753_8905)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.92969 21C7.37969 21 6.90885 20.8042 6.51719 20.4125C6.12552 20.0208 5.92969 19.55 5.92969 19V6C5.64635 6 5.40885 5.90417 5.21719 5.7125C5.02552 5.52083 4.92969 5.28333 4.92969 5C4.92969 4.71667 5.02552 4.47917 5.21719 4.2875C5.40885 4.09583 5.64635 4 5.92969 4H9.92969C9.92969 3.71667 10.0255 3.47917 10.2172 3.2875C10.4089 3.09583 10.6464 3 10.9297 3H14.9297C15.213 3 15.4505 3.09583 15.6422 3.2875C15.8339 3.47917 15.9297 3.71667 15.9297 4H19.9297C20.213 4 20.4505 4.09583 20.6422 4.2875C20.8339 4.47917 20.9297 4.71667 20.9297 5C20.9297 5.28333 20.8339 5.52083 20.6422 5.7125C20.4505 5.90417 20.213 6 19.9297 6V19C19.9297 19.55 19.7339 20.0208 19.3422 20.4125C18.9505 20.8042 18.4797 21 17.9297 21H7.92969ZM17.9297 6H7.92969V19H17.9297V6ZM10.9297 17C11.213 17 11.4505 16.9042 11.6422 16.7125C11.8339 16.5208 11.9297 16.2833 11.9297 16V9C11.9297 8.71667 11.8339 8.47917 11.6422 8.2875C11.4505 8.09583 11.213 8 10.9297 8C10.6464 8 10.4089 8.09583 10.2172 8.2875C10.0255 8.47917 9.92969 8.71667 9.92969 9V16C9.92969 16.2833 10.0255 16.5208 10.2172 16.7125C10.4089 16.9042 10.6464 17 10.9297 17ZM14.9297 17C15.213 17 15.4505 16.9042 15.6422 16.7125C15.8339 16.5208 15.9297 16.2833 15.9297 16V9C15.9297 8.71667 15.8339 8.47917 15.6422 8.2875C15.4505 8.09583 15.213 8 14.9297 8C14.6464 8 14.4089 8.09583 14.2172 8.2875C14.0255 8.47917 13.9297 8.71667 13.9297 9V16C13.9297 16.2833 14.0255 16.5208 14.2172 16.7125C14.4089 16.9042 14.6464 17 14.9297 17Z\",\n    fill: \"#F68E8E\"\n  }))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgTargetdelete);\nexport default __webpack_public_path__ + \"static/media/Targetdelete.5730a2469df52a795fa013c4e36773b7.svg\";\nexport { ForwardRef as ReactComponent };", "import React, { useState, useEffect, ChangeEvent } from 'react';\r\nimport {\r\n    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, Grid, Switch, TextField, Select, MenuItem, FormControl, InputLabel, Card, CardContent, Divider, FormControlLabel, IconButton, Box, InputAdornment, Tooltip, RadioGroup\r\n} from '@mui/material';\r\nimport UnPublishIcon from '../../assets/icons/UnPublishIcon.svg';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport { useLocation, useNavigate, useParams } from 'react-router-dom';\r\nimport EditIcon from '@mui/icons-material/Edit';\r\nimport OpeninNewWindow from '../../assets/icons/OpenNewWindow.svg';\r\nimport PublishIcon from '../../assets/icons/PublishIcon.svg';\r\nimport checksmall from '../../assets/icons/check_small.svg';\r\nimport Saveicon from '../../assets/icons/SaveIcon.svg';\r\nimport Warning from '../../assets/icons/Warning.svg';\r\nimport ShareIcon from '../../assets/icons/ShareIcon.svg';\r\nimport Targetdelete from '../../assets/icons/Targetdelete.svg';\r\nimport DrawOutlinedIcon from '@mui/icons-material/DrawOutlined';\r\nimport Radio, { RadioProps } from '@mui/material/Radio';\r\nimport { styled } from '@mui/material/styles';\r\nimport { SavePageTargets } from '../../models/SavePageTarget';\r\nimport { UpdateGuidName, PublishGuide, UnPublishGuide, SubmitUpdateGuid, SavePageTarget, DeletePageTarget, UpdatePageTarget, GetPageTargets, GetGudeDetailsByGuideId } from '../../services/GuideService';\r\nimport { useSnackbar } from '../../SnackbarContext';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport AddOutlinedIcon from '@mui/icons-material/AddOutlined';\r\nimport ConfirmationDialog from './ConfirmationDialog';\r\nimport { cloudoff, Delete } from '../../assets/icons/icons';\r\nimport { getAllGuides } from '../../services/ProfileSettingPageService';\r\nimport { openGuideInBuilder } from '../../utils/openGuideInBuilder';\r\n\r\ninterface Trigger {\r\n    pageRule: string;\r\n    url: string;\r\n    logicalOperator?: string;\r\n    PageTargetId: string;\r\n}\r\ninterface TextFieldProperties {\r\n    Id: string; \r\n    Text: string;\r\n    Alignment: string; \r\n    Hyperlink: string; \r\n    Emoji: string;\r\n    TextProperties: string; \r\n  }\r\ninterface GuideStep {\r\n    TextFieldProperties: TextFieldProperties[]; \r\n   // ImageProperties: ImageProperties[]; // Array of ImageProperties\r\n    setting: string;\r\n    VideoEmbedCode: string; \r\n    StepId: string; \r\n    Overlay: boolean;\r\n    StepTitle: string;\r\n    Arrow: boolean; \r\n    ElementPath: string; \r\n    Classes: string; \r\n    IsClickable: boolean;\r\n    AutoPosition: boolean; \r\n    Position: string; \r\n    HtmlSnippet: string; \r\n    Modal: string;\r\n    Canvas: string; \r\n    Design: string; \r\n    Hotspot: string; \r\n    Tooltip: string; \r\n    Advanced: string; \r\n    Animation: string; \r\n    //ButtonSection: ButtonSection[]; \r\n    //LayoutPositions: SectionLayout[]; \r\n}\r\n\r\n  \r\n  \r\ninterface GuideDetails {\r\n    GuideId: string;\r\n    GuideType: string;\r\n    Description: string; \r\n    Name: string;\r\n    Content: string; \r\n    OrganizationId: string;\r\n    CreatedDate: string | null; \r\n    UpdatedDate: string | null; \r\n    CreatedBy: string;\r\n    UpdatedBy: string;\r\n    TargetUrl: string;\r\n    Frequency: string;\r\n    TemplateId: string; \r\n    Segment: string; \r\n    AccountId: string;\r\n    GuideStatus: string;\r\n    PublishType: string;\r\n    UnPublishType: string; \r\n    PublishDate: string | null; \r\n    UnPublishDate: string | null; \r\n    Visited: boolean; \r\n    VisitedDate: string | null; \r\n    AutoTrigger: boolean;\r\n    GuideStep: GuideStep[];\r\n  }\r\n  \r\n  interface PageTarget {\r\n    PageTargetId: string; \r\n    GuideId: string; \r\n    OrganizationId: string; \r\n    Condition: string; \r\n    Operator: string; \r\n    Value: string;\r\n    CreatedBy: string; \r\n    CreatedDate: string; \r\n    UpdatedBy: string; \r\n    UpdatedDate: string; \r\n}\r\n  \r\n  interface Guide {\r\n      GuideDetails: GuideDetails;\r\n      PageTargets: PageTarget[];   \r\n  }\r\nconst WebappSettings: React.FC = () => {\r\n    const currentDateTime = new Date().toISOString().slice(0, 16);\r\n    const currentDate = new Date();\r\n    // const utcDate = new Date(currentDate);\r\n\r\n    // Add 2 hours to the current date and time\r\n\r\n    // Format both the current date and the unpublish date\r\n    //const currentdatTime = utcDate.toLocaleString();\r\n    // utcDate.setHours(utcDate.getHours() + 2);\r\n   \r\n    const [hasChanges, setHasChanges] = useState(false);\r\n    const [guide, setGuide] = useState<Guide | null>(null);\r\n    const [pageTargetsSaved, setPageTargetsSaved] = useState(false);  // Track if page targets are saved\r\n    const [newTriggersAdded, setNewTriggersAdded] = useState(false);  // Track if new page targets are added\r\n    const location = useLocation();\r\n    const navigate = useNavigate();\r\n    const { openSnackbar } = useSnackbar();\r\n    const [guideName, setGuideName] = useState(guide?.GuideDetails?.Name || \" \");\r\n    const { guideId } = useParams<{ guideId: string }>();\r\n    const [currentGuideId, setGuideId] = useState(guideId || \"\");\r\n    const [organizationId, setOrganizationId] = useState(guide?.GuideDetails.OrganizationId || \"\");\r\n    const [accountId, setAccountId] = useState(guide?.GuideDetails?.AccountId || \"\");\r\n    const [guideStatus, setGuideStatus] = useState(guide?.GuideDetails?.GuideStatus || \"\");\r\n    const [isEditing, setIsEditing] = useState(false);\r\n    const [selectedFrequency, setSelectedFrequency] = useState<string>(guide?.GuideDetails?.Frequency || 'onceInSession');\r\n    const [guideType, setguideType] = useState(guide?.GuideDetails?.GuideType || \"\");\r\n    const [updatedBy, setUpdatedBy] = useState(guide?.GuideDetails?.UpdatedBy);\r\n    const [createdBy, setCreatedBy] = useState(guide?.GuideDetails?.CreatedBy);\r\n    const [isAutoTriggerEnabled, setIsAutoTriggerEnabled] = useState(guide?.GuideDetails?.AutoTrigger);\r\n    const [publishOption, setPublishOption] = useState(guide?.GuideDetails?.PublishType || \"immediately\");\r\n    const [unpublishOption, setUnpublishOption] = useState(guide?.GuideDetails?.UnPublishType || \"Manually\");\r\n    const [Descriptionvalue, setDescriptionValue] = useState(guide?.GuideDetails?.Description);\r\n    const [targetUrl, setTargetUrl] = useState(guide?.GuideDetails?.TargetUrl || \"\");\r\n    const [CreatedDate, setCreatedDate] = useState(guide?.GuideDetails?.CreatedDate);\r\n    const [PublishDate, setPublishDate] = useState(guide?.GuideDetails?.PublishDate || currentDate);\r\n    const [UnPublishDate, setUnPublishDate] = useState(guide?.GuideDetails?.UnPublishDate || currentDate);\r\n    const [frequencyDropdown, setfrequencyDropdown] = useState('');\r\n    const [isPublished, setIsPublished] = useState(false);\r\n    const [isUnPublished, setIsUnPublished] = useState(false);\r\n    const [dialogOpen, setDialogOpen] = useState(false);\r\n    const [deleteIndex, setDeleteIndex] = useState<number | null>(null);\r\n    const [triggers, setTriggers] = useState<Trigger[]>([{ pageRule: \"Equals\", url: guide?.GuideDetails?.TargetUrl || '', logicalOperator: \"\", PageTargetId: \"\" }]);\r\n    const [errorMessage, setErrorMessage] = useState<string[]>([]);\r\n    const [open, setOpen] = useState(false); // Controls the popup visibility\r\n    const [initialGuide, setInitialGuide] = useState({\r\n        GuideId: '',\r\n        GuideType: '',\r\n        Name: '',\r\n        OrganizationId: '',\r\n        CreatedBy: '',\r\n        UpdatedBy: '',\r\n        Frequency: '',\r\n        AccountId: '',\r\n        GuideStatus: '',\r\n        AutoTrigger: false,\r\n        Publish: false,\r\n        UnPublish: false,\r\n        Description: '',\r\n        TargetUrl: '',\r\n    })\r\n\r\n    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false); // To track unsaved changes\r\n    const [customPublishDate, setCustomPublishDate] = useState(\"2024-09-26T07:00\");\r\n    const [customUnPublishDate, setCustomUnPublishDate] = useState(\"2024-09-26T07:00\");\r\n    \r\n   \r\n    \r\n    const handleCustomDateChange = (event: any) => {\r\n        setPublishDate(event.target.value); // Update state with new date value\r\n    };\r\n\r\n    const handleCustomDateChangeTwo = (event: any) => {\r\n        setUnPublishDate(event.target.value); // Update state with new date value\r\n    };\r\n    const [guidestep, setGuidestep] = useState(\r\n        Array.isArray(guide?.GuideDetails.GuideStep)\r\n            ? guide?.GuideDetails.GuideStep\r\n            : []\r\n    );\r\n\r\n    const CustomDivider = styled('div')(({ theme }) => ({\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        textAlign: 'center',\r\n        // margin: '8px 0',\r\n        '&:before, &:after': {\r\n            content: '\"\"',\r\n            //flex: 1,\r\n            //borderBottom: `1px solid ${theme.palette.divider}`,\r\n            margin: '0 8px',\r\n        },\r\n    }));\r\n    useEffect(() => {\r\n        const fetchGuideDetails = async () => {\r\n            const details = await GetGudeDetailsByGuideId(currentGuideId);\r\n            if (details) {\r\n                setGuide(details)\r\n                setGuideStatus(details.GuideDetails.GuideStatus);\r\n                setIsPublished(details.GuideDetails.GuideStatus === \"Active\");\r\n                setIsUnPublished(details.GuideDetails.GuideStatus === \"InActive\");\r\n                setOrganizationId(details.GuideDetails.OrganizationId);\r\n                setguideType(details.GuideDetails.GuideType);\r\n                setGuideName(details.GuideDetails.Name);\r\n                setAccountId(details.GuideDetails.AccountId);\r\n                setUpdatedBy(details.GuideDetails.UpdatedBy);\r\n                setCreatedBy(details.GuideDetails.CreatedBy);\r\n                setGuidestep(details.GuideDetails.GuideStep);\r\n                setDescriptionValue(details.GuideDetails.Description);\r\n                setSelectedFrequency(details.GuideDetails.Frequency);\r\n                setIsAutoTriggerEnabled(details.GuideDetails.AutoTrigger);\r\n                setPublishOption(details.GuideDetails.publishOption);\r\n                setUnpublishOption(details.GuideDetails.unpublishOption);\r\n                setTargetUrl(details.GuideDetails.TargetUrl);\r\n                setCreatedDate(details.GuideDetails.CreatedDate);\r\n                setPublishDate(details.GuideDetails.PublishDate);\r\n                setUnPublishDate(details.GuideDetails.UnpublishDate);\r\n                navigate(location.pathname, {\r\n                    state: { response: details },\r\n                    replace: true,\r\n                  });\r\n            }\r\n        };\r\n\r\n        fetchGuideDetails();\r\n    }, [currentGuideId, guideStatus]);\r\n\r\n    const HandlePublishToggle = async () => {\r\n        try {\r\n            await handleFinalSaveClick();\r\n\r\n            if (isPublished) {\r\n                const result = await UnPublishGuide(currentGuideId);\r\n                if (result.Success) {\r\n                    openSnackbar(`${guideName} ${guideType} Unpublished Successfully`, \"success\");\r\n                    setGuideStatus(\"InActive\");\r\n                    setIsPublished(false);\r\n                    setIsUnPublished(true);\r\n                    // Fetch and update the latest guide details\r\n                    const updatedDetails = await GetGudeDetailsByGuideId(currentGuideId);\r\n                    if (updatedDetails) {\r\n                        setGuide(updatedDetails);\r\n                        setGuideStatus(updatedDetails.GuideDetails.GuideStatus);\r\n                        setIsPublished(updatedDetails.GuideDetails.GuideStatus === \"Active\");\r\n                        setIsUnPublished(updatedDetails.GuideDetails.GuideStatus === \"InActive\");\r\n                    }\r\n                } else {\r\n                    openSnackbar(result.SuccessMessage, \"error\");\r\n                }\r\n            } else {\r\n\r\n                const result = await PublishGuide(currentGuideId);\r\n                if (result.Success) {\r\n                    openSnackbar(`${guideName} ${guideType} Published Successfully`, \"success\");\r\n                    setGuideStatus(\"Active\");\r\n                    setIsPublished(true);\r\n                    setIsUnPublished(false);\r\n                    // Fetch and update the latest guide details\r\n                    const updatedDetails = await GetGudeDetailsByGuideId(currentGuideId);\r\n                    if (updatedDetails) {\r\n                        setGuide(updatedDetails);\r\n                        setGuideStatus(updatedDetails.GuideDetails.GuideStatus);\r\n                        setIsPublished(updatedDetails.GuideDetails.GuideStatus === \"Active\");\r\n                        setIsUnPublished(updatedDetails.GuideDetails.GuideStatus === \"InActive\");\r\n                    }\r\n                } else {\r\n                    openSnackbar(result.SuccessMessage, \"error\");\r\n                }\r\n            }\r\n\r\n        } catch (error) {\r\n            console.error('Error updating guide status:', error);\r\n        }\r\n    };\r\n\r\n\r\n\r\n\r\n    const handleBackClick = () => {\r\n        const GuideType: any = `${guideType.toLowerCase()}s`;\r\n        navigate(`/${GuideType}`);\r\n    };\r\n    const handleEditClick = () => {\r\n        setOriginalGuideName(guideName);\r\n        setIsEditing(true);\r\n    };\r\n    const handleSave = async () => {\r\n        setIsEditing(false);\r\n        try {\r\n            const result = await UpdateGuidName(currentGuideId, organizationId, guideName, accountId, guideType);\r\n            if (result.Success === true) {\r\n                openSnackbar(result.SuccessMessage, \"success\");\r\n            } else {\r\n                openSnackbar(result.ErrorMessage, \"error\");\r\n            }\r\n        } catch (error) {\r\n            console.error('Error updating guide name:', error);\r\n        }\r\n    };\r\n    const handleDrawClick = () => {\r\n        if (targetUrl) {\r\n            window.open(targetUrl, '_blank');\r\n        }\r\n\r\n    };\r\n    useEffect(() => {\r\n        if (guide?.PageTargets) {\r\n            const mappedTriggers = guide?.PageTargets.map((trigger: any) => ({\r\n                pageRule: trigger.Condition,\r\n                url: trigger.Value,\r\n                logicalOperator: trigger.Operator,\r\n                PageTargetId: trigger.PageTargetId,\r\n            }));\r\n            setTriggers(mappedTriggers);\r\n        }\r\n    }, [guide?.PageTargets]);\r\n\r\n    useEffect(() => {\r\n        if (triggers.length === 0) {\r\n            setTriggers([{ pageRule: \"Equals\", url: guide?.GuideDetails.TargetUrl || '', PageTargetId: \"\", logicalOperator: \"\" }]);\r\n        }\r\n    }, [triggers]);\r\n\r\n    const handleAddTrigger = () => {\r\n        if (triggers.length < 5) {\r\n            setTriggers([...triggers, { pageRule: \"Equals\", url: \"\", PageTargetId: \"\", logicalOperator: \"OR\" }]);\r\n            setPageTargetsSaved(false);\r\n            setNewTriggersAdded(true);\r\n            setErrorMessage([]);\r\n        }\r\n    };\r\n    const handleTriggerChange = (index: number, field: keyof Trigger, value: string) => {\r\n        const newErrors = [];\r\n        const updatedTriggers = [...triggers];\r\n        updatedTriggers[index][field] = value;\r\n        let error = \"\";    \r\n        if (value.length < 1 && field === 'url')  {\r\n          error = \"Minimun Length : 1 Character\";\r\n        } else if (value.length > 500 ) {\r\n          error = \"Maximum Length : 500 characters.\";\r\n        } else if (/\\s/.test(value) &&  field === 'url') {\r\n          error = \"Restriction : Spaces are not allowed \";\r\n        } \r\n        else {\r\n            setTriggers(updatedTriggers);\r\n        }\r\n        if (error.length > 0) { newErrors[index] = error };\r\n        setErrorMessage(newErrors); \r\n        setHasChanges(true);\r\n\r\n    };\r\n    const parseFrequency = (frequency: any) => {\r\n        if (frequency?.startsWith('onceIn') && frequency !== \"onceInSession\") {\r\n            const parts = frequency.split('-');\r\n            const base = parts[0];\r\n            const dropdownValue = parts.length > 1 ? parts[1] : '';\r\n            return { frequencyBase: base, dropdownValue };\r\n        }\r\n        return { frequencyBase: frequency, dropdownValue: '' };\r\n    };\r\n    useEffect(() => {\r\n        const initialFrequency = guide?.GuideDetails?.Frequency;\r\n        if (initialFrequency !== \"onceInSession\") {\r\n            const { frequencyBase, dropdownValue } = parseFrequency(initialFrequency);\r\n            setSelectedFrequency(frequencyBase);\r\n            setfrequencyDropdown(dropdownValue);\r\n        }\r\n    }, [location.state]);\r\n    const [originalGuideName, setOriginalGuideName] = useState(\"\");\r\n    const [errors, setErrors] = useState<Partial<Record<string, string>>>({});\r\n\r\n    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {\r\n        const { name, value } = event.target;\r\n        let error = \"\";\r\n\r\n        if (name === \"GuideName\" && value.trim().length < 3) {\r\n            error = \"Guide Name must be at least 3 characters.\";\r\n        }\r\n        setGuideName(event.target.value)\r\n        setErrors((prev) => ({ ...prev, [name]: error }));\r\n    };\r\n    useEffect(() => {\r\n        if (isAutoTriggerEnabled) {\r\n            setSelectedFrequency('onceInSession');\r\n        }\r\n    }, [isAutoTriggerEnabled]);\r\n\r\n    const handleFrequencyChange = (value: string) => {\r\n        if (value === \"onceIn\") {\r\n            setSelectedFrequency(`onceIn${frequencyDropdown}`)\r\n        } else {\r\n            setSelectedFrequency(value);\r\n        }\r\n        if (value !== \"onceIn\") {\r\n            setfrequencyDropdown(\"\");\r\n        }\r\n    };\r\n    const handleFrequencyDropdown = (e: any) => {\r\n        const value = e.target.value;\r\n        if (selectedFrequency?.startsWith(\"onceIn\") && selectedFrequency !== \"onceInSession\") {\r\n            setfrequencyDropdown(value);\r\n            setSelectedFrequency(`onceIn-${value}`);\r\n        }\r\n    };\r\n\r\n    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n        if (event.key === 'Enter') {\r\n            handleSave();\r\n        } else if (event.key === 'Escape') {\r\n            setGuideName(originalGuideName);\r\n            setIsEditing(false);\r\n        }\r\n    };\r\n    const handleDeleteTrigger = async () => {\r\n        if (deleteIndex === null) return; // No index to delete\r\n\r\n        const triggerToDelete = triggers[deleteIndex];\r\n\r\n        try {\r\n            if (triggerToDelete.PageTargetId) {\r\n                const pageTargetId = triggerToDelete.PageTargetId;\r\n                const reqObj = {\r\n                    currentGuideId,\r\n                    pageTargetId,\r\n                };\r\n                const response = await DeletePageTarget(reqObj);\r\n                if (response.Success) {\r\n                    openSnackbar(response.SuccessMessage, \"success\");\r\n                    const updatedTriggers = triggers.filter((_, i) => i !== deleteIndex);\r\n                    setTriggers(updatedTriggers);\r\n                } else {\r\n                    openSnackbar(response.ErrorMessage, \"error\");\r\n                }\r\n            } else {\r\n                if (triggers.length > 1) {\r\n                    const updatedTriggers = triggers.filter((_, i) => i !== deleteIndex);\r\n                    setTriggers(updatedTriggers);\r\n                }\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error deleting page target:\", error);\r\n            openSnackbar(\"Failed to delete the trigger. Please try again.\", \"error\");\r\n        } finally {\r\n            // Reset the state regardless of the outcome\r\n            setDeleteIndex(null);\r\n            setDialogOpen(false);\r\n        }\r\n    };\r\n\r\n    const handleOperatorChange = (index: number, value: string) => {\r\n        const updatedTriggers = [...triggers];\r\n\r\n        // Since logicalOperator is for the next trigger, update the next trigger's operator\r\n        if (index < updatedTriggers.length - 1) {\r\n            updatedTriggers[index + 1].logicalOperator = value; // Update the next trigger's operator\r\n        }\r\n\r\n        setTriggers(updatedTriggers);\r\n    };\r\n\r\n    const [originalPageTargets, setOriginalPageTargets] = useState<any[]>(guide?.PageTargets || []);\r\n\r\n    useEffect(() => {\r\n        if (guide?.PageTargets && guide?.PageTargets.length > 0) {\r\n            setOriginalPageTargets(guide?.PageTargets);\r\n        }\r\n    }, [guide?.PageTargets]);\r\n    const handleSavePageTriggers = async () => {\r\n        setHasChanges(false)\r\n        const newTriggers = triggers.filter(trigger => !trigger.PageTargetId);\r\n        const existingTriggers = triggers.filter(trigger => trigger.PageTargetId);\r\n\r\n        if (newTriggers.length > 0) {\r\n            const formattedNewPageTargets = newTriggers.map((trigger) => ({\r\n                PageTargetId: \"\",\r\n                Condition: trigger.pageRule,\r\n                Operator: trigger?.logicalOperator || 'OR',\r\n                Value: trigger.url,\r\n                GuideId: currentGuideId,\r\n                OrganizationId: \"\",\r\n            }));\r\n            try {\r\n                const response = await SavePageTarget(formattedNewPageTargets);\r\n                if (response.Success) {\r\n                    openSnackbar(response.SuccessMessage, \"success\");\r\n                    setPageTargetsSaved(true);\r\n                    setTriggers(prev => prev.filter(trigger => !!trigger.PageTargetId));\r\n                    setNewTriggersAdded(false);\r\n                    \r\n                } else {\r\n                    openSnackbar(response.ErrorMessage, \"error\");\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Error saving new page targets:\", error);\r\n            }\r\n        }\r\n\r\n        // Updating existing page targets\r\n        const modifiedTriggers = existingTriggers.filter((trigger) => {\r\n            const originalTrigger = originalPageTargets.find(\r\n                (origTrigger: any) => origTrigger.PageTargetId === trigger.PageTargetId\r\n            );\r\n\r\n            return originalTrigger && (\r\n                originalTrigger.Condition !== trigger.pageRule ||\r\n                originalTrigger.Operator !== trigger.logicalOperator ||\r\n                originalTrigger.Value !== trigger.url\r\n            );\r\n        });\r\n\r\n        if (modifiedTriggers.length > 0) {\r\n            const formattedExistingPageTargets = modifiedTriggers.map((trigger) => ({\r\n                PageTargetId: trigger.PageTargetId,\r\n                Condition: trigger.pageRule,\r\n                Operator: trigger?.logicalOperator || 'OR',\r\n                Value: trigger.url,\r\n                GuideId: currentGuideId,\r\n                OrganizationId: \"\",\r\n            }));\r\n\r\n            try {\r\n                const response = await UpdatePageTarget(formattedExistingPageTargets);\r\n                if (response.Success) {\r\n                    openSnackbar(response.SuccessMessage, \"success\");\r\n                    setPageTargetsSaved(true);\r\n                    setNewTriggersAdded(false);\r\n                } else {\r\n                    openSnackbar(response.ErrorMessage, \"error\");\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Error updating existing page targets:\", error);\r\n            }\r\n        }\r\n\r\n        try {\r\n            const pageTargetsResponse = await GetPageTargets(currentGuideId);\r\n            if (pageTargetsResponse) {\r\n                // Map the response to a format suitable for your triggers\r\n                const updatedPageTargets = pageTargetsResponse.map((trigger: any) => ({\r\n                    pageRule: trigger.Condition,\r\n                    url: trigger.Value,\r\n                    logicalOperator: trigger.Operator,\r\n                    PageTargetId: trigger.PageTargetId,\r\n                }));\r\n\r\n                // Combine the existing targets and new targets\r\n                const combinedTriggers = [...updatedPageTargets];\r\n\r\n                // Use a Set to keep track of seen URLs\r\n                // const seenUrls = new Set();\r\n                // const uniqueTriggers = combinedTriggers.filter(trigger => {\r\n                //     // If the URL is not in the seen set, add it and return true to keep the trigger\r\n                //     if (!seenUrls.has(trigger.url)) {\r\n                //         seenUrls.add(trigger.url);\r\n                //         return true; // Keep this trigger\r\n                //     }\r\n                //     return false; // Ignore this trigger (duplicate)\r\n                // });\r\n\r\n                // Set the original page targets and the unique triggers\r\n                setOriginalPageTargets(pageTargetsResponse);\r\n                setTriggers(combinedTriggers);\r\n            } else {\r\n                openSnackbar(pageTargetsResponse.ErrorMessage, \"error\");\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error fetching page targets:\", error);\r\n        }\r\n    };\r\n\r\n\r\n    const handleFinalSaveClick = async () => {\r\n        // Determine the guide status based on changes and current state\r\n        let newStatus = guideStatus;\r\n        \r\n        // If the guide was inactive (isUnPublished) but changes were made, set to Draft\r\n        if (isUnPublished || isPublished && (hasChanges || newTriggersAdded || isEditing || Descriptionvalue !== guide?.GuideDetails?.Description)) {\r\n            newStatus = 'Draft';\r\n        } else if (isPublished) {\r\n            newStatus = 'Active';\r\n        } else if (isUnPublished) {\r\n            newStatus = 'InActive';\r\n        } else {\r\n            newStatus = 'Draft';\r\n        }\r\n    \r\n        // Update the state\r\n        setGuideStatus(newStatus);\r\n    \r\n        const newGuide = {\r\n            GuideId: currentGuideId,\r\n            GuideType: guideType,\r\n            Name: guideName.trim(),\r\n            Content: `${guideType} content`,\r\n            OrganizationId: organizationId,\r\n            CreatedDate: CreatedDate,\r\n            UpdatedDate: new Date().toISOString(),\r\n            CreatedBy: createdBy,\r\n            UpdatedBy: updatedBy,\r\n            Frequency: \"onceInSession\",\r\n            Segment: \"All users\",\r\n            AccountId: accountId,\r\n            GuideStep: guidestep,\r\n            GuideStatus: newStatus, // Using newly determined status\r\n            AutoTrigger: true,\r\n            PublishType: publishOption,\r\n            UnPublishType: unpublishOption,\r\n            Description: Descriptionvalue,\r\n            TargetUrl: targetUrl,\r\n             //  PublishDate: publishOption === 'custom' ? customPublishDate :  new Date().toISOString(),\r\n            //  UnpublishDate: unpublishOption === 'custom' ? customUnPublishDate :  new Date().toISOString(),\r\n            ...((newStatus === 'Draft' || newStatus === 'InActive') && {\r\n                PublishDate: publishOption === 'custom'\r\n                    ? PublishDate\r\n                    : currentDate,\r\n                UnPublishDate: unpublishOption === 'custom'\r\n                    ? UnPublishDate\r\n                    : null,\r\n            }),\r\n    \r\n            ...(isPublished && {\r\n                UnpublishDate: unpublishOption === 'custom'\r\n                    ? UnPublishDate\r\n                    : null,\r\n                PublishDate: publishOption === 'custom'\r\n                    ? PublishDate\r\n                    : currentDate,\r\n            }),\r\n        };\r\n    \r\n        if (!pageTargetsSaved && newTriggersAdded) {\r\n            await handleSavePageTriggers();\r\n        }       \r\n        const response = await SubmitUpdateGuid(newGuide);\r\n    \r\n        if (response.Success === true) {\r\n            openSnackbar(`${guideName} ${guideType} updated Successfully`, \"success\");\r\n            setPageTargetsSaved(false);\r\n            setNewTriggersAdded(false);\r\n            \r\n            // Update states to reflect the new status\r\n            setIsUnPublished(newStatus === 'InActive');\r\n        } else {\r\n            openSnackbar(response.ErrorMessage, \"error\");\r\n        }\r\n        setIsEditing(false);\r\n    };\r\n\r\nuseEffect(() => {\r\n    if (\r\n        guide?.GuideDetails.GuideStatus === \"Active\" &&\r\n        guide?.GuideDetails.UnPublishType === \"custom\" &&\r\n        guide?.GuideDetails.UnPublishDate\r\n    ) {\r\n        const interval = setInterval(async () => {\r\n            const now = new Date();\r\n            const unpublishDate = guide.GuideDetails.UnPublishDate ? new Date(guide.GuideDetails.UnPublishDate) : null;\r\n            if (unpublishDate && now >= unpublishDate) {\r\n                await HandlePublishToggle();\r\n                const details = await GetGudeDetailsByGuideId(currentGuideId);\r\n                if (details) {\r\n                    setGuide(details);\r\n                    setGuideStatus(details.GuideDetails.GuideStatus);\r\n                    setIsPublished(details.GuideDetails.GuideStatus === \"Active\");\r\n                    setIsUnPublished(details.GuideDetails.GuideStatus === \"InActive\");\r\n                }\r\n                clearInterval(interval);\r\n            }\r\n        }, 10);\r\n        return () => clearInterval(interval);\r\n    }\r\n}, [guide]);\r\n\r\n    return (\r\n        <Container maxWidth=\"xl\">\r\n            <div className='qadpt-web'>\r\n                <div className='qadpt-webcontent'>\r\n                    <div className='qadpt-setting-title'>\r\n                        <div className='qadpt-backbtn'>\r\n                            <Grid container alignItems=\"center\" className='qadpt-titsec-grid'>\r\n                                <Grid item>\r\n                                    <IconButton onClick={handleBackClick}>\r\n                                        <ArrowBackIcon />\r\n                                    </IconButton>\r\n                                </Grid>\r\n\r\n                                <Grid item>\r\n                                    <Typography variant=\"body1\" className='qadpt-back-text' onClick={handleBackClick}>\r\n                                        Back to {`${guideType}s`}\r\n                                    </Typography>\r\n                                </Grid>\r\n                            </Grid>\r\n                        </div>\r\n\r\n                        <div>\r\n                            <Grid container className='qadpt-titsec'>\r\n                                <Grid item>\r\n                                    <Box className='qadpt-name-box'>\r\n                                        {isEditing ? (\r\n                                            <TextField\r\n                                                name=\"GuideName\"\r\n                                                variant=\"outlined\"\r\n                                                value={guideName}\r\n                                                onChange={handleChange}\r\n                                                onKeyDown={handleKeyDown}\r\n                                                helperText={errors.GuideName}\r\n                                                error={!!errors.GuideName}\r\n                                                sx={{ marginRight: '16px', width: '300px' }}\r\n                                            />\r\n                                        ) : (\r\n                                            <Typography variant=\"h5\" fontWeight=\"bold\" className='qadpt-name-text'>\r\n                                                {guideName}\r\n                                            </Typography>\r\n                                        )}\r\n                                        <IconButton onClick={isEditing ? handleSave : handleEditClick}\r\n                                            disabled={isEditing && !!errors.GuideName}\r\n                                        >\r\n                                            {isEditing ? (<Tooltip\r\n                                                arrow\r\n                                                title=\"Save\"\r\n\r\n                                            >\r\n                                                <img src={Saveicon} alt='saveicon' />\r\n                                            </Tooltip>\r\n                                            ) : (\r\n                                                <Tooltip arrow title=\"Edit\">\r\n                                                    <EditIcon />\r\n                                                </Tooltip>\r\n                                            )}\r\n                                        </IconButton>\r\n                                    </Box>\r\n                                </Grid>\r\n\r\n                                <Grid item>\r\n                                    <Grid container spacing={1}>\r\n                                        <Grid item>\r\n                                            <Button\r\n                                                variant=\"outlined\"\r\n                                                color=\"primary\"\r\n                                                className=\"qadpt-action-btn\"\r\n                                                startIcon={<img src={OpeninNewWindow} alt=\"openinnewwindow\" />}\r\n                                                onClick={() => openGuideInBuilder(targetUrl, currentGuideId)}\r\n                                            >\r\n                                                Edit {guideType}\r\n                                            </Button>\r\n                                        </Grid>\r\n                                        <Grid item>\r\n                                            <Button\r\n                                                variant=\"outlined\"\r\n                                                onClick={handleFinalSaveClick}\r\n                                                className=\"qadpt-action-btn\"\r\n                                                startIcon={<img src={Saveicon} alt=\"saveicon\" />}\r\n                                            >\r\n                                                Save\r\n                                            </Button>\r\n                                        </Grid>\r\n                                        <Grid item>\r\n                                            <Button\r\n                                                variant=\"contained\"\r\n                                                onClick={HandlePublishToggle}\r\n                                                className={`qadpt-action-btn qadpt-action-btn-primary ${isPublished ? 'qadpt-unpublish' : ''}`} startIcon={<img src={isPublished ? cloudoff : PublishIcon} alt={isPublished ? 'cloudoff' : 'PublishIcon'} />}\r\n                                            >\r\n                                                {isPublished ? 'UnPublish' : 'Publish'}\r\n                                            </Button>\r\n                                        </Grid>\r\n                                        <Grid item>\r\n                                            <Button\r\n                                                variant=\"outlined\"\r\n                                                disabled={true}\r\n                                                className=\"qadpt-action-btn qadpt-share\"\r\n                                                startIcon={<img src={ShareIcon} alt=\"Shareicon\" />}\r\n                                            />\r\n                                        </Grid>\r\n                                    </Grid>\r\n                                </Grid>\r\n\r\n                            </Grid>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Description and Auto Trigger */}\r\n                    <div className='qadpt-content'>\r\n                        <div className='qadpt-set-left'>\r\n                            <div className='qadpt-description'>\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card'>\r\n                                        <CardContent>\r\n                                            <label htmlFor=\"description\" className=\"qadpt-label\" >Description</label>\r\n                                            <textarea id=\"description\"\r\n                                                value={Descriptionvalue}\r\n                                                maxLength={500}\r\n                                                placeholder=\"Enter your description here (max 500 characters)\"\r\n                                                onChange={(e) => setDescriptionValue(e.target.value)} />\r\n\r\n                                            {/* <div className=\"character-count\">\r\n                    Count: {Descriptionvalue.length}/500\r\n                </div> */}\r\n\r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n                            </div>\r\n                            <div className='qadpt-buildsteps'>\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card qadpt-buildcard'>\r\n                                        <CardContent>\r\n                                            <div className=\"qadpt-label\">\r\n                                                Build Steps\r\n                                            </div>\r\n\r\n\t\t\t\t\t                {guidestep.length > 0 && (\r\n\t\t\t\t\t                    <Typography variant=\"body1\" sx={{ marginTop: '10', fontSize: '1rem', color: '#555' }}>\r\n\t\t\t\t\t                        {guidestep.length} step{guidestep.length > 1 ? 's' : ''}\r\n\t\t\t\t\t                    </Typography>\r\n\t\t\t\t\t                )}\r\n\t\t\t\t\t                <div className=\"qadpt-build-content\">\r\n\t\t\t\t\t                    {guidestep.length > 0 ? (\r\n\t\t\t\t\t                        guidestep.map((step: any, index: any) => (\r\n\t\t\t\t\t                            <Box key={step.StepId || index} className=\"qadpt-steps\">\r\n\t\t\t\t\t                                <TextField\r\n\t\t\t\t\t                                    variant=\"outlined\"\r\n\t\t\t\t\t                                    multiline\r\n\t\t\t\t\t                                    minRows={1}\r\n\t\t\t\t\t                                    maxRows={1}\r\n\t\t\t\t\t                                    fullWidth\r\n\t\t\t\t\t                                    tabIndex={ -1} // Added tabIndex=\"-1\" to make it unfocusable via tab\r\n\t\t\t\t\t                                    inputProps={{ tabIndex: -1 }} // Make the input element unfocusable\r\n\t\t\t\t\t                                    sx={{\r\n\t\t\t\t\t                                        borderRadius: \"8px\",\r\n\t\t\t\t\t                                        '& .MuiOutlinedInput-root': {\r\n\t\t\t\t\t                                            '& fieldset': {\r\n\t\t\t\t\t                                                borderColor: 'transparent',\r\n\t\t\t\t\t                                            },\r\n\t\t\t\t\t                                            '&:hover fieldset': {\r\n\t\t\t\t\t                                                borderColor: 'transparent',\r\n\t\t\t\t\t                                            },\r\n\t\t\t\t\t                                            '&.Mui-focused fieldset': {\r\n\t\t\t\t\t                                                borderColor: 'transparent',\r\n\t\t\t\t\t                                            },\r\n\t\t\t\t\t                                        },\r\n\t\t\t\t\t                                        '& textarea': {\r\n\t\t\t\t\t                                            overflow: \"hidden\",\r\n\t\t\t\t\t                                            resize: \"none\",\r\n\t\t\t\t\t                                            paddingTop: \"4px\",\r\n\t\t\t\t\t                                            lineHeight: \"1.2\",\r\n\t\t\t\t\t                                        },\r\n\t\t\t\t\t                                    }}\r\n\t\t\t\t\t                                    InputProps={{\r\n\t\t\t\t\t                                        endAdornment: (\r\n\t\t\t\t\t                                            <InputAdornment position=\"end\">\r\n\t\t\t\t\t                                                <IconButton tabIndex={ -1}> {/* Added tabIndex=\"-1\" to IconButton */}\r\n\t\t\t\t\t                                                    <DrawOutlinedIcon />\r\n\t\t\t\t\t                                                </IconButton>\r\n\t\t\t\t\t                                            </InputAdornment>\r\n\t\t\t\t\t                                        ),\r\n\t\t\t\t\t                                        readOnly: true, // Added readOnly property to enforce read-only behavior\r\n\t\t\t\t\t                                    }}\r\n\t\t\t\t\t                                    defaultValue={`${step?.StepTitle || `Step-${index + 1}`}\\n${targetUrl}`}\r\n\t\t\t\t\t                                />\r\n\t\t\t\t\t                            </Box>\r\n\t\t\t\t\t                        ))\r\n\t\t\t\t\t                    ) : (\r\n\t\t\t\t\t                        <Typography variant=\"body1\" sx={{ marginTop: '16px', color: '#888' }}>\r\n\t\t\t\t\t                            No steps available. Please check back later.\r\n\t\t\t\t\t                        </Typography>\r\n\t\t\t\t\t                    )}\r\n\t\t\t\t\t                </div>\r\n\t\t\t\t\t            </CardContent>\r\n\t\t\t\t\t        </Card>\r\n\t\t\t\t\t    </Grid>\r\n\t\t\t\t\t</div>\r\n\r\n                        </div>\r\n\r\n                        <div className='qadpt-set-right'>\r\n\r\n                            {/* Page Targeting Section */}\r\n                            <div className=\"qadpt-page-target\">\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className=\"qadpt-card qadpt-target\">\r\n                                        <CardContent>\r\n                                            {/* Flex container for label, sublabel, and buttons */}\r\n                                            <div className=\"qadpt-header-container\">\r\n                                                <div className=\"qadpt-label-sublabel\">\r\n                                                    <div className=\"qadpt-label\">Page Targeting</div>\r\n                                                    <div className=\"qadpt-sublabel\">\r\n                                                        Which pages should the guide be visible?\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                {/* Align Add Target and Save buttons on the same row */}\r\n                                                <div>\r\n                                                    <Button\r\n                                                        variant=\"outlined\"\r\n                                                        className=\"qadpt-add-target-btn\"\r\n                                                        onClick={handleAddTrigger}\r\n                                                        disabled={triggers.length >= 5}\r\n                                                        startIcon={<AddOutlinedIcon />}\r\n                                                    >\r\n                                                        Add Target\r\n                                                    </Button>\r\n\r\n                                                    <IconButton onClick={handleSavePageTriggers} disabled={ !hasChanges || errorMessage.length > 0} className='qadpt-add-target-btn save'  style={{ opacity: !hasChanges || errorMessage.length > 0 ? 0.5 : 1 }} >                                                        \r\n                                                        <img src={Saveicon} alt=\"saveicon\"   style={{ opacity: !hasChanges || errorMessage.length > 0 ? 0.5 : 1 }} />\r\n                                                    </IconButton>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className='qadpt-conditions'>  \r\n                                            {triggers.length > 1 && (\r\n                                                    <FormControl className='qadpt-operator'>\r\n                                                        <span>Condition</span>\r\n                                                        <Select\r\n                                                            value={'OR'}\r\n                                                            disabled\r\n                                                            onChange={(e) => handleOperatorChange(0, e.target.value)} // Adjust handler as needed\r\n                                                        >\r\n                                                            <MenuItem value=\"AND\">AND</MenuItem>\r\n                                                            <MenuItem value=\"OR\">OR</MenuItem>\r\n                                                        </Select>\r\n                                                    </FormControl>\r\n                                                )}    \r\n                                                {triggers.map((trigger, index) => (  \r\n                                                    <Grid container spacing={2} key={index}>\r\n                                                       \r\n                                                        <Grid item xs={6}>\r\n                                                             <div>\r\n    <label className=\"qadpt-field-label\">Page Rule</label>\r\n    <FormControl fullWidth>\r\n        <Select\r\n            value={trigger.pageRule}\r\n                                                                        onChange={(e) => handleTriggerChange(index, 'pageRule', e.target.value)}\r\n                                                                        renderValue={(selected) => selected}\r\n        sx={{\r\n            \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n                border: \"1px solid #ccc\", // Border on hover\r\n            },\r\n            \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n                border: \"1px solid #ccc\", // Ensures no blue border on focus\r\n            },\r\n            \"&.MuiOutlinedInput-notchedOutline\": {\r\n                height :\"40px !important\",\r\n            },\r\n        }}                                                              \r\n        >\r\n            <MenuItem sx={{ fontSize: '14px', display: 'flex', justifyContent: 'space-between' }} value=\"Equals\">\r\n    Equals\r\n                                                                            <img src={checksmall} alt=\"check\" />\r\n</MenuItem>\r\n            <MenuItem sx={{ fontSize: '14px',display: 'flex', justifyContent: 'space-between' }} value=\"Not Equals\">Not Equals  <img src={checksmall} alt=\"check\" /></MenuItem>\r\n            <MenuItem sx={{ fontSize: '14px',display: 'flex', justifyContent: 'space-between' }} value=\"Starts With\">Starts With  <img src={checksmall} alt=\"check\" /></MenuItem>\r\n            <MenuItem sx={{ fontSize: '14px',display: 'flex', justifyContent: 'space-between' }} value=\"Ends With\">Ends With  <img src={checksmall} alt=\"check\" /></MenuItem>\r\n            <MenuItem sx={{ fontSize: '14px',display: 'flex', justifyContent: 'space-between' }}value=\"Contains\">Contains  <img src={checksmall} alt=\"check\" /></MenuItem>\r\n            <MenuItem sx={{ fontSize: '14px' ,display: 'flex', justifyContent: 'space-between'}}value=\"Not Contains\">Does Not Contain  <img src={checksmall} alt=\"check\" /></MenuItem>\r\n            {/* <MenuItem value=\"Wildcards\">Wildcards</MenuItem> */}\r\n        </Select>\r\n    </FormControl>\r\n</div>\r\n\r\n                                                        </Grid>\r\n                                                        <Grid item xs={5}>\r\n                                                        <div>\r\n    <label className=\"qadpt-field-label\">URL</label>\r\n    <TextField\r\n        fullWidth\r\n        value={trigger.url}\r\n        onChange={(e) => handleTriggerChange(index, 'url', e.target.value)}\r\n        error={!!errorMessage[index]} \r\n        helperText={\r\n            !!errorMessage[index] && (\r\n                <span style={{ display: \"flex\", alignItems: \"center\", gap: \"5px\" }}>\r\n                    <img src={Warning} alt=\"error-icon\"/>\r\n                    {errorMessage[index]}\r\n                </span>\r\n            )\r\n        }\r\n        InputProps={{\r\n            sx: {\r\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n                    border: \"1px solid #ccc\", // Border on hover\r\n                },\r\n              \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n                border: \"1px solid #ccc\", // Removes the blue border on focus\r\n                },\r\n                \"&.MuiOutlinedInput-notchedOutline\": {\r\n                    height :\"40px !important\",\r\n                },\r\n                \"&.Mui-error .MuiOutlinedInput-notchedOutline\": {\r\n                borderColor: \"#e6a957 !important\", // Custom error border color\r\n            },\r\n            },\r\n          }}\r\n    />\r\n</div>\r\n                                                        </Grid>\r\n                                                        <Grid item xs={1} className={`qadpt-btngrid ${!!errorMessage[index] ? 'qadpt-error-btn' : ''}`}  style={{ opacity: triggers.length === 1 ? 0.5 : 1 }}>\r\n                                                            <IconButton onClick={() => {\r\n                                                                setDeleteIndex(index); // Set the index of the trigger to delete\r\n                                                                setDialogOpen(true); // Open the confirmation dialog\r\n                                                            }} disabled={triggers.length === 1} >\r\n\r\n                                                  <img \r\n                                                src={Targetdelete} \r\n                                                alt='tardelete' \r\n                                                style={{ opacity: triggers.length === 1 ? 0.5 : 1 }} \r\n                                                                />\r\n                                                            </IconButton>\r\n                                                        </Grid>\r\n                                                        \r\n                                                       \r\n                                                    </Grid>\r\n                                                ))}\r\n                                                {/* <ConfirmationDialog\r\n                                                    open={dialogOpen}\r\n                                                    onClose={() => setDialogOpen(false)}\r\n                                                    onConfirm={handleDeleteTrigger} // Call delete function on confirmation\r\n                                                    title=\"Delete Trigger\"\r\n                                                    message=\"Are you sure you want to delete this trigger?\"\r\n                                                /> */}\r\n {/* {dialogOpen && (\r\n  <div className=\"qadpt-modal-overlay\">\r\n    <div className=\"qadpt-usrconfirm-popup qadpt-danger qadpt-deltrigger\">\r\n      <div className=\"qadpt-popup-title\">Delete Trigger</div>\r\n\r\n      <div className=\"qadpt-warning\">\r\n        Are you sure you want to delete this trigger?\r\n      </div>\r\n\r\n      <div className=\"qadpt-buttons\">\r\n        <button\r\n          onClick={() => setDialogOpen(false)}\r\n          className=\"qadpt-cancel-button\"\r\n        >\r\n          Cancel\r\n        </button>\r\n\r\n        <button\r\n          onClick={handleDeleteTrigger}\r\n          className=\"qadpt-conform-button\"\r\n          type=\"button\"\r\n        >\r\n          Confirm\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n)} */}\r\n\r\n        {dialogOpen && (\r\n           <div className=\"qadpt-modal-overlay\">       \r\n        <div className=\"qadpt-usrconfirm-popup qadpt-danger\">\r\n          <div>\r\n            <div className=\"qadpt-icon\">      \r\n              <IconButton\r\n                                className=\"qadpt-svg\">\r\n                                    <i className='fal fa-trash-alt'></i>\r\n                                    </IconButton>\r\n            </div>\r\n                      </div>\r\n                      <div className=\"qadpt-popup-title\">\r\n                      Delete Trigger\r\n            </div>\r\n      \r\n            <div className=\"qadpt-warning\">\r\n        Are you sure you want to delete this trigger?\r\n      </div>\r\n      \r\n      <div className=\"qadpt-buttons\">\r\n        <button\r\n          onClick={() => setDialogOpen(false)}\r\n          className=\"qadpt-cancel-button\"\r\n        >\r\n          Cancel\r\n        </button>\r\n\r\n        <button\r\n          onClick={handleDeleteTrigger}\r\n          className=\"qadpt-conform-button\"\r\n          type=\"button\"\r\n        >\r\n          Confirm\r\n        </button>\r\n      </div>\r\n                    </div>\r\n                    </div>\r\n      )}\r\n\r\n\r\n\r\n                                            </div>\r\n\r\n                                            \r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n                            </div>\r\n\r\n                            <div className='qadpt-auto-trigger'>\r\n                                {/* Auto Trigger Section */}\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card qadpt-trigger'>\r\n                                        <CardContent>\r\n                                            <Grid container>\r\n                                                <div className=\"qadpt-label\">Auto Trigger</div>\r\n                                                <Switch\r\n                                                    checked={true}\r\n                                                    onChange={(e) => setIsAutoTriggerEnabled(e.target.checked)}\r\n                                                    disabled={true}\r\n                                                />\r\n                                            </Grid>\r\n                                            <div className='qadpt-sublabel' style={{ lineHeight: 1 }}>\r\n                                                Enable this to automatically display the {guideType.toLowerCase()} on target pages, or trigger manually via checklists.\r\n                                            </div>\r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n                            </div>\r\n\r\n\r\n                            {/* Frequency Section */}\r\n                            <div className='qadpt-frequency'>\r\n\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card'>\r\n                                        <CardContent>\r\n                                            <div className=\"qadpt-label\">Frequency</div>\r\n                                            <div className='qadpt-sublabel'>How often do you want it to trigger</div>\r\n                                            <FormControl component=\"fieldset\" disabled={!isAutoTriggerEnabled}>\r\n                                                <Grid container spacing={1} alignItems=\"center\" wrap=\"nowrap\">\r\n                                                    <Grid item>\r\n                                                        <FormControlLabel\r\n                                                            control={\r\n                                                                <Radio\r\n                                                                    checked={selectedFrequency === 'onlyOnce'}\r\n                                                                    onChange={() => handleFrequencyChange('onlyOnce')}\r\n                                                                    disabled\r\n                                                                />\r\n                                                            }\r\n                                                            label={<span className=\"qadpt-freqselect\">Only Once</span>}\r\n                                                        />\r\n                                                    </Grid>\r\n                                                    <Grid item>\r\n                                                        <FormControlLabel\r\n                                                            control={\r\n                                                                <Radio\r\n\r\n                                                                    checked={true}\r\n                                                                    onChange={() => handleFrequencyChange('onceInSession')}\r\n                                                                />\r\n                                                            }\r\n                                                            label={<span className=\"qadpt-freqselect\">Once in a session</span>}\r\n                                                        />\r\n                                                    </Grid>\r\n                                                    <Grid item>\r\n                                                        <FormControlLabel\r\n                                                            control={\r\n                                                                <Radio\r\n                                                                    checked={selectedFrequency === 'onceADay'}\r\n                                                                    onChange={() => handleFrequencyChange('onceADay')}\r\n                                                                    disabled\r\n                                                                />\r\n                                                            }\r\n                                                            label={<span className=\"qadpt-freqselect\">Once a day</span>}\r\n                                                        />\r\n                                                    </Grid>\r\n                                                    <Grid item>\r\n                                                        <FormControlLabel\r\n                                                            control={\r\n                                                                <Radio\r\n                                                                    checked={selectedFrequency?.startsWith('onceIn') && selectedFrequency !== 'onceInSession'}\r\n                                                                    onChange={() => handleFrequencyChange('onceIn')}\r\n                                                                    disabled\r\n                                                                />\r\n                                                            }\r\n                                                            label={<span className=\"qadpt-freqselect\">Once in</span>}\r\n                                                        />\r\n                                                    </Grid>\r\n                                                    <Grid item>\r\n                                                        <FormControl sx={{ marginLeft: '8px', minWidth: '80px' }} disabled={!isAutoTriggerEnabled || !selectedFrequency?.startsWith('onceIn')}>\r\n                                                            <Select\r\n                                                                labelId=\"frequency-select-label\"\r\n                                                                value={frequencyDropdown}\r\n                                                                defaultValue='2days'\r\n                                                                onChange={handleFrequencyDropdown}\r\n                                                                disabled\r\n                                                            >\r\n                                                                <MenuItem value=\"2days\">2 days</MenuItem>\r\n                                                                <MenuItem value=\"3days\">3 days</MenuItem>\r\n                                                                <MenuItem value=\"Week\">Week</MenuItem>\r\n                                                                <MenuItem value=\"month\">Month</MenuItem>\r\n                                                                <MenuItem value=\"Quarter\">Quarter</MenuItem>\r\n                                                                <MenuItem value=\"Semi-Yearly\">Semi-Yearly</MenuItem>\r\n                                                                <MenuItem value=\"Yearly\">Yearly</MenuItem>\r\n                                                            </Select>\r\n                                                        </FormControl>\r\n                                                    </Grid>\r\n                                                </Grid>\r\n                                            </FormControl>\r\n\r\n\r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n\r\n                            </div>\r\n\r\n                            {/* Review & Publish Section */}\r\n                            <div className='qadpt-rev-publish'>\r\n                                <Grid item xs={12} md={6}>\r\n                                    <Card className='qadpt-card qadpt-rev'>\r\n                                        <CardContent>\r\n                                            <div className=\"qadpt-label\">Review & Publish</div>\r\n                                            <Grid container spacing={2}>\r\n                                                <Grid item xs={6} className='qadpt-gridleft'>\r\n                                                    <Typography>Publish {`${guideType}`}:</Typography>\r\n                                                    <FormControl component=\"fieldset\">\r\n                                                        <RadioGroup\r\n                                                            value={publishOption}\r\n                                                            onChange={(e) => setPublishOption(e.target.value)}\r\n                                                        >\r\n                                                            <FormControlLabel\r\n                                                                value=\"immediately\"\r\n                                                                control={<Radio />}\r\n                                                                label=\"Immediately\"\r\n                                                            />\r\n                                                            <FormControlLabel\r\n                                                                value=\"custom\"\r\n                                                                control={<Radio />}\r\n                                                                label=\"Custom date\"\r\n                                                            />\r\n                                                        </RadioGroup>\r\n                                                    </FormControl>\r\n                                                    {publishOption === 'custom' && (\r\n                                                        <TextField\r\n                                                            type=\"datetime-local\"\r\n                                                            value={PublishDate}\r\n                                                            onChange={handleCustomDateChange}\r\n                                                            fullWidth\r\n                                                            sx={{ marginTop: '8px' }}\r\n                                                            inputProps={{\r\n                                                                min: currentDateTime,\r\n                                                            }}\r\n                                                        />\r\n                                                    )}\r\n                                                </Grid>\r\n\r\n                                                <Grid item xs={6} className='qadpt-gridright'>\r\n                                                    <Typography>Unpublish {`${guideType}`}:</Typography>\r\n                                                    <FormControl component=\"fieldset\">\r\n                                                        <RadioGroup\r\n                                                            value={unpublishOption}\r\n                                                            onChange={(e) => setUnpublishOption(e.target.value)}\r\n                                                        >\r\n                                                            <FormControlLabel\r\n                                                                value=\"Manually\"\r\n                                                                control={<Radio />}\r\n                                                                label=\"Manually\"\r\n                                                            />\r\n                                                            <FormControlLabel\r\n                                                                value=\"custom\"\r\n                                                                control={<Radio />}\r\n                                                                label=\"Custom date\"\r\n\r\n                                                            />\r\n                                                        </RadioGroup>\r\n                                                    </FormControl>\r\n                                                    {unpublishOption === 'custom' && (\r\n                                                        <TextField\r\n                                                            type=\"datetime-local\"\r\n                                                            value={UnPublishDate} // Use value instead of defaultValue for controlled input\r\n                                                            onChange={handleCustomDateChangeTwo} // Handle input changes\r\n                                                            fullWidth\r\n                                                            sx={{ marginTop: '8px' }}\r\n                                                            inputProps={{\r\n                                                                min: currentDateTime, \r\n                                                            }}\r\n                                                        />\r\n                                                    )}\r\n                                                </Grid>\r\n                                            </Grid>\r\n                                        </CardContent>\r\n                                    </Card>\r\n                                </Grid>\r\n                            </div>\r\n\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default WebappSettings;\r\n"], "names": ["WebappSettings", "_guide$GuideDetails", "_guide$GuideDetails2", "_guide$GuideDetails3", "_guide$GuideDetails4", "_guide$GuideDetails5", "_guide$GuideDetails6", "_guide$GuideDetails7", "_guide$GuideDetails8", "_guide$GuideDetails9", "_guide$GuideDetails0", "_guide$GuideDetails1", "_guide$GuideDetails10", "_guide$GuideDetails11", "_guide$GuideDetails12", "_guide$GuideDetails13", "_guide$GuideDetails14", "currentDateTime", "Date", "toISOString", "slice", "currentDate", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "useState", "guide", "setGuide", "pageTargetsSaved", "setPageTargetsSaved", "newTriggersAdded", "setNewTriggersAdded", "location", "useLocation", "navigate", "useNavigate", "openSnackbar", "useSnackbar", "guideName", "setGuideName", "GuideDetails", "Name", "guideId", "useParams", "currentGuideId", "setGuideId", "organizationId", "setOrganizationId", "OrganizationId", "accountId", "setAccountId", "AccountId", "guideStatus", "setGuideStatus", "GuideStatus", "isEditing", "setIsEditing", "selectedFrequency", "setSelectedFrequency", "Frequency", "guideType", "setguideType", "GuideType", "updatedBy", "setUpdatedBy", "UpdatedBy", "created<PERSON>y", "setCreated<PERSON>y", "CreatedBy", "isAutoTriggerEnabled", "setIsAutoTriggerEnabled", "AutoTrigger", "publishOption", "setPublishOption", "PublishType", "unpublishOption", "setUnpublishOption", "UnPublishType", "Descriptionvalue", "setDescriptionValue", "Description", "targetUrl", "setTargetUrl", "TargetUrl", "CreatedDate", "setCreatedDate", "PublishDate", "setPublishDate", "UnPublishDate", "setUnPublishDate", "frequencyDropdown", "setfrequencyDropdown", "isPublished", "setIsPublished", "isUnPublished", "setIsUnPublished", "dialogOpen", "setDialogOpen", "deleteIndex", "setDeleteIndex", "triggers", "setTriggers", "pageRule", "url", "logicalOperator", "PageTargetId", "errorMessage", "setErrorMessage", "open", "<PERSON><PERSON><PERSON>", "initialGuide", "setInitialGuide", "GuideId", "Publish", "UnPublish", "hasUnsavedChanges", "setHasUnsavedChanges", "customPublishDate", "setCustomPublishDate", "customUnPublishDate", "setCustomUnPublishDate", "guidestep", "setGuidestep", "Array", "isArray", "GuideStep", "styled", "_ref", "theme", "display", "alignItems", "textAlign", "content", "margin", "useEffect", "async", "details", "GetGudeDetailsByGuideId", "UnpublishDate", "pathname", "state", "response", "replace", "fetchGuideDetails", "HandlePublishToggle", "handleFinalSaveClick", "result", "UnPublishGuide", "Success", "concat", "updatedDetails", "SuccessMessage", "PublishGuide", "error", "console", "handleBackClick", "toLowerCase", "handleSave", "UpdateGuidName", "ErrorMessage", "PageTargets", "mappedTriggers", "map", "trigger", "Condition", "Value", "Operator", "length", "handleTriggerChange", "index", "field", "value", "newErrors", "updatedTriggers", "test", "_guide$GuideDetails15", "initialFrequency", "frequencyBase", "dropdownValue", "frequency", "startsWith", "parts", "split", "parseFrequency", "originalGuideName", "setOriginalGuideName", "errors", "setErrors", "handleFrequencyChange", "originalPageTargets", "setOriginalPageTargets", "handleSavePageTriggers", "newTriggers", "filter", "existingTriggers", "formattedNewPageTargets", "SavePageTarget", "prev", "modifiedTriggers", "originalTrigger", "find", "origTrigger", "formattedExistingPageTargets", "UpdatePageTarget", "pageTargetsResponse", "GetPageTargets", "combinedTriggers", "_guide$GuideDetails16", "newStatus", "newGuide", "_objectSpread", "trim", "Content", "UpdatedDate", "Segment", "SubmitUpdateGuid", "interval", "setInterval", "now", "unpublishDate", "clearInterval", "_jsx", "Container", "max<PERSON><PERSON><PERSON>", "children", "className", "_jsxs", "Grid", "container", "item", "IconButton", "onClick", "ArrowBackIcon", "Typography", "variant", "Box", "TextField", "name", "onChange", "event", "target", "onKeyDown", "key", "helperText", "GuideName", "sx", "marginRight", "width", "fontWeight", "handleEditClick", "disabled", "<PERSON><PERSON><PERSON>", "arrow", "title", "src", "Saveicon", "alt", "EditIcon", "spacing", "<PERSON><PERSON>", "color", "startIcon", "OpeninNewWindow", "openGuideInBuilder", "cloudoff", "PublishIcon", "ShareIcon", "xs", "md", "Card", "<PERSON><PERSON><PERSON><PERSON>", "htmlFor", "id", "max<PERSON><PERSON><PERSON>", "placeholder", "e", "marginTop", "fontSize", "step", "multiline", "minRows", "maxRows", "fullWidth", "tabIndex", "inputProps", "borderRadius", "borderColor", "overflow", "resize", "paddingTop", "lineHeight", "InputProps", "endAdornment", "InputAdornment", "position", "DrawOutlinedIcon", "readOnly", "defaultValue", "<PERSON><PERSON><PERSON><PERSON>", "StepId", "handleAddTrigger", "AddOutlinedIcon", "style", "opacity", "FormControl", "Select", "handleOperatorChange", "MenuItem", "renderValue", "selected", "border", "height", "justifyContent", "checksmall", "gap", "Warning", "Targetdelete", "triggerToDelete", "pageTargetId", "req<PERSON>bj", "DeletePageTarget", "_", "i", "type", "Switch", "checked", "component", "wrap", "FormControlLabel", "control", "Radio", "label", "marginLeft", "min<PERSON><PERSON><PERSON>", "labelId", "RadioGroup", "min"], "sourceRoot": ""}