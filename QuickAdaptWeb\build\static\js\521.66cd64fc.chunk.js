"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[521],{1227:(t,e,a)=>{var o=a(4994);e.A=void 0;var r=o(a(39)),c=a(579);e.A=(0,r.default)((0,c.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"AddOutlined")},2110:(t,e,a)=>{a.d(e,{A:()=>A});var o=a(8168),r=a(8587),c=a(5043),n=a(9292),s=a(8610),i=a(4535),l=a(8206),d=a(3336),h=a(2532),p=a(2372);function u(t){return(0,p.Ay)("MuiCard",t)}(0,h.A)("MuiCard",["root"]);var m=a(579);const v=["className","raised"],g=(0,i.Ay)(d.A,{name:"Mui<PERSON><PERSON>",slot:"Root",overridesResolver:(t,e)=>e.root})((()=>({overflow:"hidden"}))),A=c.forwardRef((function(t,e){const a=(0,l.b)({props:t,name:"MuiCard"}),{className:c,raised:i=!1}=a,d=(0,r.A)(a,v),h=(0,o.A)({},a,{raised:i}),p=(t=>{const{classes:e}=t;return(0,s.A)({root:["root"]},u,e)})(h);return(0,m.jsx)(g,(0,o.A)({className:(0,n.A)(p.root,c),elevation:i?8:void 0,ref:e,ownerState:h},d))}))},4598:(t,e,a)=>{a.d(e,{A:()=>C});var o=a(8587),r=a(8168),c=a(5043),n=a(9292),s=a(8610),i=a(7266),l=a(6803),d=a(3064),h=a(4535),p=a(8206),u=a(2532),m=a(2372);function v(t){return(0,m.Ay)("MuiSwitch",t)}const g=(0,u.A)("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]);var A=a(579);const w=["className","color","edge","size","sx"],b=(0,h.Ay)("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.root,a.edge&&e["edge".concat((0,l.A)(a.edge))],e["size".concat((0,l.A)(a.size))]]}})({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,["& .".concat(g.thumb)]:{width:16,height:16},["& .".concat(g.switchBase)]:{padding:4,["&.".concat(g.checked)]:{transform:"translateX(16px)"}}}}]}),k=(0,h.Ay)(d.A,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.switchBase,{["& .".concat(g.input)]:e.input},"default"!==a.color&&e["color".concat((0,l.A)(a.color))]]}})((t=>{let{theme:e}=t;return{position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:"".concat("light"===e.palette.mode?e.palette.common.white:e.palette.grey[300]),transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),["&.".concat(g.checked)]:{transform:"translateX(20px)"},["&.".concat(g.disabled)]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:"".concat("light"===e.palette.mode?e.palette.grey[100]:e.palette.grey[600])},["&.".concat(g.checked," + .").concat(g.track)]:{opacity:.5},["&.".concat(g.disabled," + .").concat(g.track)]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:"".concat("light"===e.palette.mode?.12:.2)},["& .".concat(g.input)]:{left:"-100%",width:"300%"}}}),(t=>{let{theme:e}=t;return{"&:hover":{backgroundColor:e.vars?"rgba(".concat(e.vars.palette.action.activeChannel," / ").concat(e.vars.palette.action.hoverOpacity,")"):(0,i.X4)(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter((t=>{let[,e]=t;return e.main&&e.light})).map((t=>{let[a]=t;return{props:{color:a},style:{["&.".concat(g.checked)]:{color:(e.vars||e).palette[a].main,"&:hover":{backgroundColor:e.vars?"rgba(".concat(e.vars.palette[a].mainChannel," / ").concat(e.vars.palette.action.hoverOpacity,")"):(0,i.X4)(e.palette[a].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(g.disabled)]:{color:e.vars?e.vars.palette.Switch["".concat(a,"DisabledColor")]:"".concat("light"===e.palette.mode?(0,i.a)(e.palette[a].main,.62):(0,i.e$)(e.palette[a].main,.55))}},["&.".concat(g.checked," + .").concat(g.track)]:{backgroundColor:(e.vars||e).palette[a].main}}}}))]}})),f=(0,h.Ay)("span",{name:"MuiSwitch",slot:"Track",overridesResolver:(t,e)=>e.track})((t=>{let{theme:e}=t;return{height:"100%",width:"100%",borderRadius:7,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:"".concat("light"===e.palette.mode?e.palette.common.black:e.palette.common.white),opacity:e.vars?e.vars.opacity.switchTrack:"".concat("light"===e.palette.mode?.38:.3)}})),y=(0,h.Ay)("span",{name:"MuiSwitch",slot:"Thumb",overridesResolver:(t,e)=>e.thumb})((t=>{let{theme:e}=t;return{boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}})),C=c.forwardRef((function(t,e){const a=(0,p.b)({props:t,name:"MuiSwitch"}),{className:c,color:i="primary",edge:d=!1,size:h="medium",sx:u}=a,m=(0,o.A)(a,w),g=(0,r.A)({},a,{color:i,edge:d,size:h}),C=(t=>{const{classes:e,edge:a,size:o,color:c,checked:n,disabled:i}=t,d={root:["root",a&&"edge".concat((0,l.A)(a)),"size".concat((0,l.A)(o))],switchBase:["switchBase","color".concat((0,l.A)(c)),n&&"checked",i&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},h=(0,s.A)(d,v,e);return(0,r.A)({},e,h)})(g),S=(0,A.jsx)(y,{className:C.thumb,ownerState:g});return(0,A.jsxs)(b,{className:(0,n.A)(C.root,c),sx:u,ownerState:g,children:[(0,A.jsx)(k,(0,r.A)({type:"checkbox",icon:S,checkedIcon:S,ref:e,ownerState:g},m,{classes:(0,r.A)({},C,{root:C.switchBase})})),(0,A.jsx)(f,{className:C.track,ownerState:g})]})}))},6494:(t,e,a)=>{a.d(e,{A:()=>g});var o=a(8168),r=a(8587),c=a(5043),n=a(9292),s=a(8610),i=a(4535),l=a(8206),d=a(2532),h=a(2372);function p(t){return(0,h.Ay)("MuiCardContent",t)}(0,d.A)("MuiCardContent",["root"]);var u=a(579);const m=["className","component"],v=(0,i.Ay)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(t,e)=>e.root})((()=>({padding:16,"&:last-child":{paddingBottom:24}}))),g=c.forwardRef((function(t,e){const a=(0,l.b)({props:t,name:"MuiCardContent"}),{className:c,component:i="div"}=a,d=(0,r.A)(a,m),h=(0,o.A)({},a,{component:i}),g=(t=>{const{classes:e}=t;return(0,s.A)({root:["root"]},p,e)})(h);return(0,u.jsx)(v,(0,o.A)({as:i,className:(0,n.A)(g.root,c),ownerState:h,ref:e},d))}))},8004:(t,e,a)=>{var o=a(4994);e.A=void 0;var r=o(a(39)),c=a(579);e.A=(0,r.default)((0,c.jsx)("path",{d:"m18.85 10.39 1.06-1.06c.78-.78.78-2.05 0-2.83L18.5 5.09c-.78-.78-2.05-.78-2.83 0l-1.06 1.06zm-4.24 1.42L7.41 19H6v-1.41l7.19-7.19zm-1.42-4.25L4 16.76V21h4.24l9.19-9.19zM19 17.5c0 2.19-2.54 3.5-5 3.5-.55 0-1-.45-1-1s.45-1 1-1c1.54 0 3-.73 3-1.5 0-.47-.48-.87-1.23-1.2l1.48-1.48c1.07.63 1.75 1.47 1.75 2.68M4.58 13.35C3.61 12.79 3 12.06 3 11c0-1.8 1.89-2.63 3.56-3.36C7.59 7.18 9 6.56 9 6c0-.41-.78-1-2-1-1.26 0-1.8.61-1.83.64-.35.41-.98.46-1.4.12-.41-.34-.49-.95-.15-1.38C3.73 4.24 4.76 3 7 3s4 1.32 4 3c0 1.87-1.93 2.72-3.64 3.47C6.42 9.88 5 10.5 5 11c0 .31.43.6 1.07.86z"}),"DrawOutlined")}}]);
//# sourceMappingURL=521.66cd64fc.chunk.js.map