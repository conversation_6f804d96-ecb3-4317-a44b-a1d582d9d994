"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[969],{7969:(e,a,t)=>{t.r(a),t.d(a,{default:()=>c});var s=t(5043),i=t(9252),o=t(6446),r=t(4379),d=t(579);const c=()=>{const[e,a]=(0,s.useState)(""),[t,c]=(0,s.useState)(!1);return(0,d.jsxs)(i.A,{maxWidth:"sm",className:"qadpt-unistpg",children:[(0,d.jsx)(o.A,{mb:4,className:"qadpt-brand-logo",children:(0,d.jsx)("img",{src:r.bT,alt:"QuickAdopt Logo",className:"qadpt-brand-logo-img"})}),!t&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"qadpt-mgs",children:[(0,d.jsx)("div",{children:"We're sorry to see you go."}),(0,d.jsx)("div",{children:"Before you leave , we'd love to hear your feedback"}),(0,d.jsxs)("div",{children:["Need the extension again ?   ",(0,d.jsx)("img",{src:r.C4,alt:"Refresh img"}),"  ",(0,d.jsx)("a",{href:"https://chromewebstore.google.com/detail/quickadapt/bniabjfpljchdaipkcadcnpjndjdoaom",children:"Re-Install"})," it"]})]}),(0,d.jsxs)("form",{className:"qadpt-feedbkfrm",onSubmit:async e=>{e.preventDefault(),localStorage.clear(),sessionStorage.clear(),document.cookie.split(";").forEach((function(e){document.cookie=e.replace(/^ +/,"").replace(/=.*/,"=;expires="+(new Date).toUTCString()+";path=/")})),c(!0),setTimeout((()=>{window.location.href="".concat({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_ADMIN_API:"https://devadminapi.quickadopt.in/api",REACT_APP_ENABLE_ENCRYPTION:"true",REACT_APP_IDS_API:"https://devapp.quickadopt.in",REACT_APP_PUBLIC_ENCRYPT_KEY:"MIGeMA0GCSqGSIb3DQEBAQUAA4GMADCBiAKBgHPeXxbMNqVNKTZvcbgLqb1itpw7U2wvk9n+qJ4MulRuZXJyVa7BlOboOlE8JZbYi7+i00xLShd7BpHKuRPacUnF2XKVEVbGrFSDKD3oOy9ji36y3HzBfrfiZzK9Q3YtIlVdBBLILd9PXSKQoAlYQqU73N1cKEZf9NOSzyZsnUpJAgMBAAE=",REACT_APP_UER_WEB:"https://devuser.quickadopt.in",REACT_APP_USER_API:"https://devapi.quickadopt.in/api"}.REACT_APP_WEB_API)}),2e3)},children:[(0,d.jsx)("div",{children:"Feedback"}),(0,d.jsx)("textarea",{value:e,onChange:e=>a(e.target.value),placeholder:"Share your feedback",rows:4,cols:50}),(0,d.jsx)("button",{type:"submit",className:"qadpt-btn",disabled:0===e.trim().length,style:{backgroundColor:"#5f9ea0",pointerEvents:0===e.trim().length?"none":"auto",opacity:0===e.trim().length?.5:1},onMouseOver:e=>e.currentTarget.style.backgroundColor="#5f9ea0",onMouseOut:e=>e.currentTarget.style.backgroundColor="#5f9ea0",children:"Submit Feedback"})]})]}),t&&(0,d.jsxs)("div",{className:"qadpt-thkpg",children:[(0,d.jsx)("img",{src:r.Ry,alt:"feedback img"}),(0,d.jsxs)("div",{className:"qadpt-thkmsg",children:[(0,d.jsx)("div",{children:"Thanks for your feedback!"}),(0,d.jsx)("div",{children:"It\u2019s been shared with our team and will be considered for future improvements."})]})]})]})}}}]);
//# sourceMappingURL=969.118b0cee.chunk.js.map