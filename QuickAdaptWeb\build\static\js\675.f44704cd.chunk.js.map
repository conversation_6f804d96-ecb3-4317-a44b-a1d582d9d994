{"version": 3, "file": "static/js/675.f44704cd.chunk.js", "mappings": "iYAEO,SAASA,EAAyBC,GACvC,OAAOC,EAAAA,EAAAA,IAAqB,eAAgBD,EAC9C,EACyBE,EAAAA,EAAAA,GAAuB,eAAgB,CAAC,OAAQ,QAAS,Q,aCDlF,MAAMC,EAAY,CAAC,WAAY,YAAa,YAAa,QAAS,MAAO,SAqBnEC,GAAgBC,EAAAA,EAAAA,IAAO,MAAO,CAClCC,KAAM,eACNN,KAAM,OACNO,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJC,GACEF,GACE,iBACJG,EAAgB,iBAChBC,GACEF,EACJ,MAAO,CAACD,EAAOI,KAAMF,GAAoBF,EAAOK,MAAOF,GAAoBH,EAAOM,IAAI,GAXpEV,EAanBW,IAAA,IAAC,WACFN,GACDM,EAAA,OAAKC,EAAAA,EAAAA,GAAS,CACbC,QAAS,QACTC,eAAgB,QAChBC,iBAAkB,YAClBC,mBAAoB,UACnBX,EAAWC,kBAAoB,CAChCW,MAAO,QACNZ,EAAWE,kBAAoB,CAEhCW,UAAW,SACX,IACIC,EAAmB,CAAC,QAAS,QAAS,UAAW,SAAU,OAC3DC,EAAmB,CAAC,UAAW,OAqFrC,EApF+BC,EAAAA,YAAiB,SAAmBC,EAASC,GAC1E,MAAMpB,GAAQqB,EAAAA,EAAAA,GAAgB,CAC5BrB,MAAOmB,EACPrB,KAAM,kBAEF,SACFwB,EAAQ,UACRC,EAAS,UACTC,EAAY,MAAK,MACjBC,EAAK,IACLC,EAAG,MACHC,GACE3B,EACJ4B,GAAQC,EAAAA,EAAAA,GAA8B7B,EAAOL,GACzCQ,GAA4D,IAAzCa,EAAiBc,QAAQN,GAC5CO,GAAiB5B,GAAoBsB,GAAQhB,EAAAA,EAAAA,GAAS,CAC1DuB,gBAAiB,QAAFC,OAAUR,EAAK,OAC7BE,GAASA,EACNzB,GAAaO,EAAAA,EAAAA,GAAS,CAAC,EAAGT,EAAO,CACrCwB,YACArB,mBACAC,kBAA2D,IAAzCa,EAAiBa,QAAQN,KAEvCU,EA9DkBhC,KACxB,MAAM,QACJgC,EAAO,iBACP/B,EAAgB,iBAChBC,GACEF,EACEiC,EAAQ,CACZ9B,KAAM,CAAC,OAAQF,GAAoB,QAASC,GAAoB,QAElE,OAAOgC,EAAAA,EAAAA,GAAeD,EAAO5C,EAA0B2C,EAAQ,EAqD/CG,CAAkBnC,GAClC,OAAoBoC,EAAAA,EAAAA,KAAK1C,GAAea,EAAAA,EAAAA,GAAS,CAC/Cc,WAAWgB,EAAAA,EAAAA,GAAKL,EAAQ7B,KAAMkB,GAC9BiB,GAAIhB,EACJiB,MAAOtC,GAAoBsB,EAAQ,WAAQiB,EAC3CtB,IAAKA,EACLO,MAAOI,EACP7B,WAAYA,EACZwB,IAAKvB,EAAmBsB,GAASC,OAAMgB,GACtCd,EAAO,CACRN,SAAUA,IAEd,ICtFO,SAASqB,EAA2BnD,GACzC,OAAOC,EAAAA,EAAAA,IAAqB,iBAAkBD,EAChD,EAC2BE,EAAAA,EAAAA,GAAuB,iBAAkB,CAAC,OAAQ,YAA7E,MCDMC,EAAY,CAAC,iBAAkB,aAmB/BiD,GAAkB/C,EAAAA,EAAAA,IAAO,MAAO,CACpCC,KAAM,iBACNN,KAAM,OACNO,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJC,GACEF,EACJ,MAAO,CAACC,EAAOI,MAAOH,EAAW2C,gBAAkB5C,EAAO6C,QAAQ,GAP9CjD,EASrBW,IAAA,IAAC,WACFN,GACDM,EAAA,OAAKC,EAAAA,EAAAA,GAAS,CACbC,QAAS,OACTqC,WAAY,SACZC,QAAS,IACP9C,EAAW2C,gBAAkB,CAC/B,gCAAiC,CAC/BI,WAAY,IAEd,IAgDF,EA/CiC/B,EAAAA,YAAiB,SAAqBC,EAASC,GAC9E,MAAMpB,GAAQqB,EAAAA,EAAAA,GAAgB,CAC5BrB,MAAOmB,EACPrB,KAAM,oBAEF,eACF+C,GAAiB,EAAK,UACtBtB,GACEvB,EACJ4B,GAAQC,EAAAA,EAAAA,GAA8B7B,EAAOL,GACzCO,GAAaO,EAAAA,EAAAA,GAAS,CAAC,EAAGT,EAAO,CACrC6C,mBAEIX,EA3CkBhC,KACxB,MAAM,QACJgC,EAAO,eACPW,GACE3C,EACEiC,EAAQ,CACZ9B,KAAM,CAAC,QAASwC,GAAkB,YAEpC,OAAOT,EAAAA,EAAAA,GAAeD,EAAOQ,EAA4BT,EAAQ,EAmCjDG,CAAkBnC,GAClC,OAAoBoC,EAAAA,EAAAA,KAAKM,GAAiBnC,EAAAA,EAAAA,GAAS,CACjDc,WAAWgB,EAAAA,EAAAA,GAAKL,EAAQ7B,KAAMkB,GAC9BrB,WAAYA,EACZkB,IAAKA,GACJQ,GACL,I,2EC1DesB,EAAAA,EAAAA,IAA4BZ,EAAAA,EAAAA,KAAK,OAAQ,CACtDa,EAAG,6EACD,WCFWD,EAAAA,EAAAA,IAA4BZ,EAAAA,EAAAA,KAAK,OAAQ,CACtDa,EAAG,oHACD,W,kECoDJ,MAskBA,EAtkB2BC,KACvB,MAAOC,EAAQC,IAAaC,EAAAA,EAAAA,UAAuB,KAC5CC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAeC,IAAoBJ,EAAAA,EAAAA,UAAS,KAC5CK,EAAYC,IAAiBN,EAAAA,EAAAA,UAAS,KACtCO,EAAUC,IAAeR,EAAAA,EAAAA,UAA6B,OACtDS,EAAaC,IAAkBV,EAAAA,EAAAA,WAASW,EAAAA,EAAAA,QACxCC,EAAWC,IAAgBb,EAAAA,EAAAA,WAAS,IACpCc,EAAcC,IAAmBf,EAAAA,EAAAA,UAAS,OAC1CgB,EAASC,IAAcjB,EAAAA,EAAAA,WAAS,IAChCkB,EAAeC,IAAoBnB,EAAAA,EAAAA,WAAS,IAC5CoB,EAAiBC,IAAsBrB,EAAAA,EAAAA,WAAS,IAChDsB,EAAaC,IAAkBvB,EAAAA,EAAAA,UAAuB,KACtDwB,EAAcC,IAAmBzB,EAAAA,EAAAA,WAAS,IAC1C0B,EAAiBC,IAAsB3B,EAAAA,EAAAA,UAAS,KAChD4B,EAAkBC,IAAuB7B,EAAAA,EAAAA,UAA8B,YACvE8B,EAAeC,KAAoB/B,EAAAA,EAAAA,UAAS,KAC5CgC,GAASC,KAAcjC,EAAAA,EAAAA,UAAS,KAChCkC,GAAaC,KAAkBnC,EAAAA,EAAAA,aAC/BoC,GAAYC,KAAiBrC,EAAAA,EAAAA,UAAS,IAEvCsC,GAAsBA,KACxBb,GAAgB,EAAM,EAKpBc,GAAeA,KACjBd,GAAgB,GAEhBe,YAAW,KACPf,GAAgB,EAAM,GACvB,IAAK,GAGZgB,EAAAA,EAAAA,YAAU,KAEN,MAAMC,GAAcC,EAAAA,EAAAA,IAAUjC,GAC9B,MAAO,IAAMgC,GAAa,GAC3B,IAEH,MAAME,GAAYC,UACd3C,GAAW,GACK,IACR,MAAM4C,OCnGKD,WAEzB,IAEE,aADuBE,EAAAA,GAAeC,IAAkB,4BACxCC,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,4BAA6BA,GACrCA,CACR,GD2FqCE,GACX,GAAIN,EAAU,CAClB,MAAMO,EAAwBP,EAASQ,KAAKC,IAAQ,CACpDC,QAASD,EAAKE,GACdC,SAAUH,EAAKI,MAAQ,KACvBC,IAAKL,EAAKK,IAAM,eAAgB,IAAIC,MAAOC,WAAY,OAE3D/D,EAAUsD,EACd,CAEJ,CAAE,MAAOH,GACT,CAAC,QACGhD,GAAW,EACf,IAGRuC,EAAAA,EAAAA,YAAU,KAED7B,GACDgC,IACJ,GACD,CAAChC,IAEJ,MA2GMmD,GAAcA,KAChBlD,GAAa,GACbE,EAAgB,MAChBE,GAAW,EAAM,EAiDf+C,GAAenB,UACnB,GAAI/B,EACF,IACEZ,GAAW,QCjRU2C,OACvBoB,EACA/D,EACAW,KAIA,MAAMqD,EAAW,IAAIC,SAErB,IAAK,IAAIZ,KAAQU,EACbC,EAASE,OAAO,OAAQb,GAI5B,IACIrD,GAAW,GAMX,WAJuB6C,EAAAA,GAAesB,KAAK,0BAA2BH,EAAU,CAC5EI,QAAS,CAAE,eAAgB,0BAEDrB,KAE1B,MAAM,IAAIsB,MAAM,+BAGpB1D,GAAa,EAEjB,CAAE,MAAOqC,GAEL,MAAMA,CACV,CAAC,QACGhD,GAAW,EACf,GDkPQsE,CACJ1D,EACAZ,EACAW,EAIJ,CAAE,MAAOqC,GAAQ,IAADuB,EACd,IAAIC,EAAU,mCACd,MAAMC,EAAMzB,EAEL,OAAHyB,QAAG,IAAHA,GAAa,QAAVF,EAAHE,EAAK7B,gBAAQ,IAAA2B,GAAbA,EAAexB,OACjByB,EAC+B,kBAAtBC,EAAI7B,SAASG,KAChB0B,EAAI7B,SAASG,KACb0B,EAAI7B,SAASG,KAAKyB,SAAWA,GAErC/C,EAAmB+C,GACnB7C,EAAoB,SACpBU,IACF,CAAC,QACCrC,GAAW,EACb,CACF,EAoBI0E,GAAsB/B,UACxB,IACI,MAAMgC,OC3RQhC,WACtB,IAWI,aAVuBE,EAAAA,GAAe+B,OAAO,yBAA0B,CACnEC,OAAQ,CACJC,aAQQ/B,IACpB,CAAE,MAAOC,GAEL,MADAC,QAAQD,MAAM,sBAAuBA,GAC/BA,CACV,GD2Q6B+B,CAAW1B,EAAKC,SACjB,GAAhBqB,EAAOK,SACPvD,EAAmB,4BACnBE,EAAoB,WACpBU,KACAK,OAEAjB,EAAmBkD,EAAOM,cAAc,0BACxCtD,EAAoB,SACpBU,KAER,CAAE,MAAOW,GACLC,QAAQiC,IAAIlC,EAEhB,GAGEmC,GAAexC,UAAuD,IAADyC,EACzE,MAAM/B,EAAyB,QAArB+B,EAAGC,EAAMC,OAAOvB,aAAK,IAAAqB,OAAA,EAAlBA,EAAqB,GAElC,GADAnC,QAAQiC,IAAItD,EAAe,sBACvByB,EACF,IACErD,GAAW,QC9RM2C,OACvBmC,EACAzB,EACArD,KACI,MAAMgE,EAAW,IAAIC,SAGzBD,EAASE,OAAO,OAAQb,GAExB,IACIrD,GAAW,GAYP,WAVmB6C,EAAAA,GAAe0C,IAClC,0BACAvB,EACA,CACEa,OAAQ,CAAEC,UACVV,QAAS,CAAE,eAAgB,0BAIHrB,KAEtB,MAAM,IAAIsB,MAAM,8BAIxB,CAAE,MAAOrB,GAEJ,MADDC,QAAQD,MAAM,wBAAyBA,GAChCA,CACX,CAAC,QACGhD,GAAW,EACf,GD+PQwF,CAAY5D,EAAeyB,EAAMrD,GACvCH,EAAU,IAEV6C,IACF,CAAE,MAAOM,GAAQ,IAADyC,EACd,IAAIjB,EAAU,mCACd,MAAMC,EAAMzB,EAEL,OAAHyB,QAAG,IAAHA,GAAa,QAAVgB,EAAHhB,EAAK7B,gBAAQ,IAAA6C,GAAbA,EAAe1C,OACjByB,EAC+B,kBAAtBC,EAAI7B,SAASG,KAChB0B,EAAI7B,SAASG,KACb0B,EAAI7B,SAASG,KAAKyB,SAAWA,GAErC/C,EAAmB+C,GACnB7C,EAAoB,SACpBU,IACF,CAAC,QACCrC,GAAW,EACb,CACF,EASI0F,GAAc/C,UAChBM,QAAQiC,IAAI,mBAEZ,MAAMS,OC1RqBhD,OAC/BmC,IAGA,IAMI,aALuBjC,EAAAA,GAAeC,IAAI,kCAAmC,CACzE+B,OAAQ,CACJC,aAGQ/B,IACpB,CAAE,MAAOC,GAEL,MADAC,QAAQD,MAAM,wBAAyBA,GACjCA,CACV,GD4QwB4C,CAAoBC,EAAMvC,SAC9CnB,GAAcwD,GACd5E,GAAW,GACXkB,GAAe4D,EAAM,GAEjBC,EAAGC,KAAcC,EAAAA,EAAAA,MAEzB,OAEInH,EAAAA,EAAAA,KAAA,OAAKf,UAAS,oBAAuBI,MAAO,CAAEsB,WAAYe,EAAc,QAAU,QAAS1C,UACvFoI,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACC,GAAI,CAAKC,UAAW,aAAcC,SAAU,QAAQxI,SAAA,EACrDoI,EAAAA,EAAAA,MAAA,OAAApI,SAAA,EACAgB,EAAAA,EAAAA,KAAA,MAAAhB,SAAKkI,GAAU,sBACXlH,EAAAA,EAAAA,KAAA,OAAKX,MAAO,CAAEoI,SAAU,WAAYC,IAAK,MAAOC,MAAO,OAAO3I,UAM1DgB,EAAAA,EAAAA,KAAC4H,EAAAA,EAAM,CACPC,QAASA,IAAM/F,GAAa,GAC5B7C,UAAU,aACV6I,WAAW9H,EAAAA,EAAAA,KAAC+H,EAAAA,EAAO,IACnBC,QAAQ,YAAWhJ,UAEpBgB,EAAAA,EAAAA,KAAA,QAAMX,MAAO,CAAEoI,SAAU,YAAazI,SAAC,kBAI1CoI,EAAAA,EAAAA,MAACa,EAAAA,EAAM,CAACC,KAAMrG,EACTsG,QAASnD,GACToD,SAAS,KAETC,WAAY,CACRhJ,MAAO,CACHiJ,UAAW,OACXC,SAAU,SAEhBvJ,SAAA,EAEHgB,EAAAA,EAAAA,KAACwI,EAAAA,EAAW,CAACpK,QAAQ,OACbqK,eAAe,SACnBhI,WAAW,SACXpB,MAAO,CAAEsB,WAAY,SAAU+H,UAAU,QAAQ1J,SACpD,iBACDgB,EAAAA,EAAAA,KAAC2I,EAAAA,EAAa,CAAA3J,UACdgB,EAAAA,EAAAA,KAACqH,EAAAA,EAAG,CACIjJ,QAAQ,OACRqK,eAAe,SACfhI,WAAW,SACX6H,UAAU,OAAOtJ,UAIjBgB,EAAAA,EAAAA,KAAA,SAAO4I,KAAK,OAAOC,SAlKrBrC,IACxBxE,EAAgBwE,EAAMC,OAAOvB,MAAM,EAiKsC4D,OAAO,UAAUC,UAAQ,SAGhF3B,EAAAA,EAAAA,MAAC4B,EAAAA,EAAa,CAAAhK,SAAA,EACdgB,EAAAA,EAAAA,KAAC4H,EAAAA,EAAM,CAACC,QAAS7C,GAAaiE,MAAM,UAASjK,SAAC,YAG9CgB,EAAAA,EAAAA,KAAC4H,EAAAA,EAAM,CACHC,QAAS/D,gBACCmB,KACNnD,GAAa,EAAM,EAEvBmH,MAAM,UACNjK,SACH,oBAMRkC,GACIlB,EAAAA,EAAAA,KAAA,OAAKf,UAAU,eAAcD,UAC1BgB,EAAAA,EAAAA,KAAA,OAClBZ,IAAK8J,EACRC,IAAI,UACJlK,UAAU,2BAIOmI,EAAAA,EAAAA,MAAAgC,EAAAA,SAAA,CAAApK,SAAA,EACAgB,EAAAA,EAAAA,KAACqH,EAAAA,EAAG,CAAEC,GAAI,CAAClJ,QAAQ,OAAOiL,SAAS,QAASrK,SAExC+B,EAAOwD,KAAI,CAACyC,EAAMsC,KAEVlC,EAAAA,EAAAA,MAACmC,EAAAA,EAAI,CAAajC,GAAI,CAClBlJ,QAAS,OAAQoL,cAAe,SAAUC,OAAQ,QAClDhC,SAAU,WAAYiC,OAAQ,OAAQlL,MAAO,QAASmL,aAAc,OACpE,UAAW,CACPC,UAAW,cACXC,UAAW,KAEjB7K,SAAA,EACEoI,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACC,GAAI,CACLG,SAAU,WAEVrJ,QAAS,OAAQqK,eAAgB,SAAUhI,WAAY,SAAUqJ,SAAU,EAAEC,OAAQ,WAGzFC,aAAcA,IAAM9G,GAAW8D,EAAMvC,SACrCwF,aAAcA,IAAM/G,GAAW,IAAIlE,SAAA,EACnCgB,EAAAA,EAAAA,KAACkK,EAAS,CACNhL,UAAU,MACVC,MAAO6H,EAAMnC,KAAK,GACdsE,IAAKnC,EAAMrC,UAAY,QAEvB2C,GAAI,CACAmC,OAAQ,QAERjL,MAAO,QACPC,UAAW,QACX0L,OAAQlH,IAAW+D,EAAMvC,QAAU,YAAc,OACjDkF,aAAc,EACdD,OAAQ,GAEZ7B,QAASA,IAAIhB,GAAYG,GAC7B/H,UAAU,gBAIRgE,IAAW+D,EAAMvC,UACfzE,EAAAA,EAAAA,KAACqH,EAAAA,EAAG,CAACC,GAAI,CACLG,SAAU,WACVrJ,QAAS,OACTqK,eAAgB,SAChBhI,WAAY,UAGhBoH,QAASA,IAAIhB,GAAYG,GAAOhI,UAG5BgB,EAAAA,EAAAA,KAAA,OAAKZ,IAAKgL,EAAAA,EAAcjB,IAAI,qBAQxCnJ,EAAAA,EAAAA,KAACqK,EAAAA,EAAU,CACPxC,QAASA,IAAMhC,GAAoBmB,GACnC3H,MAAO,CAAEoI,SAAU,WAAYC,IAAK,EAAGC,MAAO,EAAGsB,MAAO,OAAQjK,UAEhEgB,EAAAA,EAAAA,KAAA,OAAKZ,IAAKkL,EAAAA,GAAWnB,IAAI,iBAG7B/B,EAAAA,EAAAA,MAACmD,EAAW,CAAClL,MAAO,CAAEjB,QAAS,OAAQqK,eAAgB,gBAAiB/H,QAAS,WAAY1B,SAAA,EACzFgB,EAAAA,EAAAA,KAACwK,EAAAA,EAAU,CAACxC,QAAQ,QAAQyC,QAAM,EAACpL,MAAO,CAAE+I,SAAU,OAAQpJ,SAAEgI,EAAMrC,YACtEyC,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAAArI,SAAA,EACRgB,EAAAA,EAAAA,KAACqK,EAAAA,EAAU,CAACxC,QAASA,IAlOrC/D,WACZ,UACU4G,UAAUC,UAAUC,UAAUC,GACpCjI,EAAmB,0BACnBE,EAAoB,WACpBU,IAKJ,CAAE,MAAOW,GACLC,QAAQiC,IAAIlC,EAChB,GAsNuD2G,CAAQ9D,EAAMnC,KAAK7F,UAC9CgB,EAAAA,EAAAA,KAAA,OAAKZ,IAAK2L,EAAAA,EAAM5B,IAAI,YAEhBnJ,EAAAA,EAAAA,KAACgL,EAAAA,EAAO,CAACC,MAAM,gBAAejM,UAC9BgB,EAAAA,EAAAA,KAACqK,EAAAA,EAAU,CACHxC,QAAUrB,IAAW,IAAD0E,EArKrCC,IAsKuCnE,EAAMvC,QArKpEL,QAAQiC,IAAI8E,EAAI,cAChBnI,GAAiBmI,GAqK4B,OAAL3E,QAAK,IAALA,GAAAA,EAAO4E,kBAC+B,QAAtCF,EAAAG,SAASC,eAAe,sBAAc,IAAAJ,GAAtCA,EAAwCK,OAAO,EACjDvM,UACMgB,EAAAA,EAAAA,KAAA,OAAKZ,IAAKoM,EAAAA,GAASrC,IAAI,iBAK/BnJ,EAAAA,EAAAA,KAAA,SACI4I,KAAK,OACLuC,GAAG,cACH9L,MAAO,CAAEjB,QAAS,QAClB0K,OAAO,UACPD,SAAUvC,aApFfgD,OA8FXrH,IACIjC,EAAAA,EAAAA,KAACyL,EAAAA,EAAK,CACFvD,KAAMjG,EACNkG,QAASA,KACLjG,GAAW,GACXkB,QAAehD,EAAU,EAI7B,kBAAgB,oBAChB,mBAAiB,0BAAyBpB,UAE1CoI,EAAAA,EAAAA,MAACC,EAAAA,EAAG,CAACC,GAAI,CACLG,SAAU,WACVC,IAAK,MACLgE,KAAM,MACN9B,UAAW,wBACX+B,QAAS,mBACT9B,UAAW,GAEXzB,SAAU,QACVb,UAAW,QACXqE,EAAG,GACL5M,SAAA,EAEEgB,EAAAA,EAAAA,KAAA,OAAKZ,KAAgB,OAAX+D,SAAW,IAAXA,QAAW,EAAXA,GAAa0B,MAAM,GAAIsE,IAAI,cAAc9J,MAAO,CAAC+I,SAAS,QAAQb,UAAU,YACtFH,EAAAA,EAAAA,MAACoD,EAAAA,EAAU,CAACW,GAAG,0BAA0B7D,GAAI,CAAEuE,GAAI,GAAI7M,SAAA,CAAC,SAClC,OAAXmE,SAAW,IAAXA,QAAW,EAAXA,GAAawB,aAExByC,EAAAA,EAAAA,MAACoD,EAAAA,EAAU,CAACW,GAAG,0BAA0B7D,GAAI,CAAEuE,GAAI,GAAI7M,SAAA,CAAC,kBAC3B,OAAVqE,SAAU,IAAVA,QAAU,EAAVA,GAAYyI,QAAO,EAAI,4BAA8BzI,YAQhGrD,EAAAA,EAAAA,KAAC+L,EAAAA,EAAQ,CACL7D,KAAMzF,EACNuJ,iBAAkB,IAClB7D,QAAS5E,GACT0I,aAAc,CAAEC,SAAU,MAAOC,WAAY,UAC7C7E,GAAI,CAAE8E,OAAQ,IAAM1D,UAAU,GAAI1J,UAElCgB,EAAAA,EAAAA,KAACqM,EAAAA,EAAK,CACFlE,QAAS5E,GACT+I,SAAUzJ,EACVyE,GAAI,CAAE9I,MAAO,QAASQ,SAErB2D,aAgBX,C,0IE1nBX,SAAS4J,EAAoBrP,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,EACoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,S,aCDvD,MAAMC,EAAY,CAAC,YAAa,UAoB1BmP,GAAWjP,EAAAA,EAAAA,IAAOkP,EAAAA,EAAO,CAC7BjP,KAAM,UACNN,KAAM,OACNO,kBAAmBA,CAACC,EAAOC,IAAWA,EAAOI,MAH9BR,EAId,KACM,CACLiK,SAAU,aAwDd,EArD0B5I,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAMpB,GAAQqB,EAAAA,EAAAA,GAAgB,CAC5BrB,MAAOmB,EACPrB,KAAM,aAEF,UACFyB,EAAS,OACTyN,GAAS,GACPhP,EACJ4B,GAAQC,EAAAA,EAAAA,GAA8B7B,EAAOL,GACzCO,GAAaO,EAAAA,EAAAA,GAAS,CAAC,EAAGT,EAAO,CACrCgP,WAEI9M,EA/BkBhC,KACxB,MAAM,QACJgC,GACEhC,EAIJ,OAAOkC,EAAAA,EAAAA,GAHO,CACZ/B,KAAM,CAAC,SAEoBwO,EAAqB3M,EAAQ,EAwB1CG,CAAkBnC,GAClC,OAAoBoC,EAAAA,EAAAA,KAAKwM,GAAUrO,EAAAA,EAAAA,GAAS,CAC1Cc,WAAWgB,EAAAA,EAAAA,GAAKL,EAAQ7B,KAAMkB,GAC9B0N,UAAWD,EAAS,OAAItM,EACxBtB,IAAKA,EACLlB,WAAYA,GACX0B,GACL,G,qBClDIsN,EAAyBC,EAAQ,MAIrCC,EAAQ,OAAU,EAClB,IAAIC,EAAiBH,EAAuBC,EAAQ,KAChDG,EAAcH,EAAQ,KACXC,EAAQ,GAAU,EAAIC,EAAeE,UAAuB,EAAID,EAAYE,KAAK,OAAQ,CACtGrM,EAAG,sCACD,M,wKCVG,SAASsM,EAAoBjQ,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,CACA,MACA,GADoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,OAAQ,gBAAiB,iBAAkB,kBAAmB,SAAU,iB,wBCHxH,MAAMgQ,EAAuB,CAClCC,QAAS,eACTC,YAAa,eACbC,UAAW,iBACXC,cAAe,iBACfrJ,MAAO,cAiBT,EAZ0BjG,IAGpB,IAHqB,MACzBuP,EAAK,WACL7P,GACDM,EACC,MAAMwP,EAP0BzE,IACzBmE,EAAqBnE,IAAUA,EAMb0E,CAA0B/P,EAAWqL,OACxDA,GAAQ2E,EAAAA,EAAAA,IAAQH,EAAO,WAAF9N,OAAa+N,IAAoB,IAAU9P,EAAWqL,MAC3E4E,GAAeD,EAAAA,EAAAA,IAAQH,EAAO,WAAF9N,OAAa+N,EAAgB,YAC/D,MAAI,SAAUD,GAASI,EACd,QAAPlO,OAAekO,EAAY,YAEtBC,EAAAA,EAAAA,IAAM7E,EAAO,GAAI,E,aClB1B,MAAM5L,EAAY,CAAC,YAAa,QAAS,YAAa,SAAU,UAAW,oBAAqB,YAAa,UAAW,MA2BlH0Q,GAAWxQ,EAAAA,EAAAA,IAAOiN,EAAAA,EAAY,CAClChN,KAAM,UACNN,KAAM,OACNO,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJC,GACEF,EACJ,MAAO,CAACC,EAAOI,KAAMJ,EAAO,YAADgC,QAAaqO,EAAAA,EAAAA,GAAWpQ,EAAWqQ,aAAwC,WAAzBrQ,EAAWsB,WAA0BvB,EAAOuQ,OAAO,GAPnH3Q,EASdW,IAGG,IAHF,MACFuP,EAAK,WACL7P,GACDM,EACC,OAAOC,EAAAA,EAAAA,GAAS,CAAC,EAA4B,SAAzBP,EAAWqQ,WAAwB,CACrDE,eAAgB,QACU,UAAzBvQ,EAAWqQ,WAAyB,CACrCE,eAAgB,OAChB,UAAW,CACTA,eAAgB,cAEQ,WAAzBvQ,EAAWqQ,YAA0B9P,EAAAA,EAAAA,GAAS,CAC/CgQ,eAAgB,aACM,YAArBvQ,EAAWqL,OAAuB,CACnCmF,oBAAqBC,EAAkB,CACrCZ,QACA7P,gBAED,CACD,UAAW,CACTwQ,oBAAqB,aAEI,WAAzBxQ,EAAWsB,WAA0B,CACvCuI,SAAU,WACV6G,wBAAyB,cACzBC,gBAAiB,cAGjBC,QAAS,EACTC,OAAQ,EACR/E,OAAQ,EAERC,aAAc,EACdjJ,QAAS,EAETqJ,OAAQ,UACR2E,WAAY,OACZC,cAAe,SACfC,cAAe,OAEfC,iBAAkB,OAElB,sBAAuB,CACrBC,YAAa,QAEf,CAAC,KAADnP,OAAMoP,EAAYC,eAAiB,CACjCR,QAAS,SAEX,IA0HJ,EAxH0B5P,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAMpB,GAAQqB,EAAAA,EAAAA,GAAgB,CAC5BrB,MAAOmB,EACPrB,KAAM,aAEF,UACFyB,EAAS,MACTgK,EAAQ,UAAS,UACjB/J,EAAY,IAAG,OACf+P,EAAM,QACNC,EAAO,kBACPC,EAAiB,UACjBlB,EAAY,SAAQ,QACpBjG,EAAU,UAAS,GACnBV,GACE5J,EACJ4B,GAAQC,EAAAA,EAAAA,GAA8B7B,EAAOL,IACzC,kBACJ+R,EACAH,OAAQI,EACRH,QAASI,EACTxQ,IAAKyQ,IACHC,EAAAA,EAAAA,MACGR,EAAcS,GAAmB7Q,EAAAA,UAAe,GACjD8Q,GAAaC,EAAAA,EAAAA,GAAW7Q,EAAKyQ,GAmB7B3R,GAAaO,EAAAA,EAAAA,GAAS,CAAC,EAAGT,EAAO,CACrCuL,QACA/J,YACA8P,eACAf,YACAjG,YAEIpI,EAzHkBhC,KACxB,MAAM,QACJgC,EAAO,UACPV,EAAS,aACT8P,EAAY,UACZf,GACErQ,EACEiC,EAAQ,CACZ9B,KAAM,CAAC,OAAQ,YAAF4B,QAAcqO,EAAAA,EAAAA,GAAWC,IAA4B,WAAd/O,GAA0B,SAAU8P,GAAgB,iBAE1G,OAAOlP,EAAAA,EAAAA,GAAeD,EAAOsN,EAAqBvN,EAAQ,EA+G1CG,CAAkBnC,GAClC,OAAoBoC,EAAAA,EAAAA,KAAK+N,GAAU5P,EAAAA,EAAAA,GAAS,CAC1C8K,MAAOA,EACPhK,WAAWgB,EAAAA,EAAAA,GAAKL,EAAQ7B,KAAMkB,GAC9BW,QAASuP,EACTjQ,UAAWA,EACX+P,OA/BiBzI,IACjB6I,EAAkB7I,IACgB,IAA9B4I,EAAkBQ,SACpBH,GAAgB,GAEdR,GACFA,EAAOzI,EACT,EAyBA0I,QAvBkB1I,IAClB8I,EAAmB9I,IACe,IAA9B4I,EAAkBQ,SACpBH,GAAgB,GAEdP,GACFA,EAAQ1I,EACV,EAiBA1H,IAAK4Q,EACL9R,WAAYA,EACZoK,QAASA,EACTV,GAAI,IAAMuI,OAAOC,KAAK1C,GAAsB2C,SAAS9G,GAEhD,GAFyD,CAAC,CAC7DA,aACY+G,MAAMC,QAAQ3I,GAAMA,EAAK,CAACA,KACvChI,GACL,G,qBCxJIsN,EAAyBC,EAAQ,MAIrCC,EAAQ,OAAU,EAClB,IAAIC,EAAiBH,EAAuBC,EAAQ,KAChDG,EAAcH,EAAQ,KACXC,EAAQ,GAAU,EAAIC,EAAeE,UAAuB,EAAID,EAAYE,KAAK,OAAQ,CACtGrM,EAAG,oEACD,Y", "sources": ["../node_modules/@mui/material/CardMedia/cardMediaClasses.js", "../node_modules/@mui/material/CardMedia/CardMedia.js", "../node_modules/@mui/material/CardActions/cardActionsClasses.js", "../node_modules/@mui/material/CardActions/CardActions.js", "../node_modules/@mui/icons-material/esm/Delete.js", "../node_modules/@mui/icons-material/esm/SaveAlt.js", "components/fileManagement/FileList.tsx", "services/FileManagementService.tsx", "../node_modules/@mui/material/Card/cardClasses.js", "../node_modules/@mui/material/Card/Card.js", "../node_modules/@mui/icons-material/Add.js", "../node_modules/@mui/material/Link/linkClasses.js", "../node_modules/@mui/material/Link/getTextDecoration.js", "../node_modules/@mui/material/Link/Link.js", "../node_modules/@mui/icons-material/SwapHoriz.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardMediaUtilityClass(slot) {\n  return generateUtilityClass('MuiCardMedia', slot);\n}\nconst cardMediaClasses = generateUtilityClasses('MuiCardMedia', ['root', 'media', 'img']);\nexport default cardMediaClasses;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"image\", \"src\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getCardMediaUtilityClass } from './cardMediaClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    isMediaComponent,\n    isImageComponent\n  } = ownerState;\n  const slots = {\n    root: ['root', isMediaComponent && 'media', isImageComponent && 'img']\n  };\n  return composeClasses(slots, getCardMediaUtilityClass, classes);\n};\nconst CardMediaRoot = styled('div', {\n  name: 'MuiCardMedia',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      isMediaComponent,\n      isImageComponent\n    } = ownerState;\n    return [styles.root, isMediaComponent && styles.media, isImageComponent && styles.img];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'block',\n  backgroundSize: 'cover',\n  backgroundRepeat: 'no-repeat',\n  backgroundPosition: 'center'\n}, ownerState.isMediaComponent && {\n  width: '100%'\n}, ownerState.isImageComponent && {\n  // ⚠️ object-fit is not supported by IE11.\n  objectFit: 'cover'\n}));\nconst MEDIA_COMPONENTS = ['video', 'audio', 'picture', 'iframe', 'img'];\nconst IMAGE_COMPONENTS = ['picture', 'img'];\nconst CardMedia = /*#__PURE__*/React.forwardRef(function CardMedia(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardMedia'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      image,\n      src,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const isMediaComponent = MEDIA_COMPONENTS.indexOf(component) !== -1;\n  const composedStyle = !isMediaComponent && image ? _extends({\n    backgroundImage: `url(\"${image}\")`\n  }, style) : style;\n  const ownerState = _extends({}, props, {\n    component,\n    isMediaComponent,\n    isImageComponent: IMAGE_COMPONENTS.indexOf(component) !== -1\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardMediaRoot, _extends({\n    className: clsx(classes.root, className),\n    as: component,\n    role: !isMediaComponent && image ? 'img' : undefined,\n    ref: ref,\n    style: composedStyle,\n    ownerState: ownerState,\n    src: isMediaComponent ? image || src : undefined\n  }, other, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardMedia.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    if (!props.children && !props.image && !props.src && !props.component) {\n      return new Error('MUI: Either `children`, `image`, `src` or `component` prop must be specified.');\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Image to be displayed as a background image.\n   * Either `image` or `src` prop must be specified.\n   * Note that caller must specify height otherwise the image will not be visible.\n   */\n  image: PropTypes.string,\n  /**\n   * An alias for `image` property.\n   * Available only with media components.\n   * Media components: `video`, `audio`, `picture`, `iframe`, `img`.\n   */\n  src: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardMedia;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActions', slot);\n}\nconst cardActionsClasses = generateUtilityClasses('MuiCardActions', ['root', 'spacing']);\nexport default cardActionsClasses;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"disableSpacing\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getCardActionsUtilityClass } from './cardActionsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return composeClasses(slots, getCardActionsUtilityClass, classes);\n};\nconst CardActionsRoot = styled('div', {\n  name: 'MuiCardActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8\n}, !ownerState.disableSpacing && {\n  '& > :not(style) ~ :not(style)': {\n    marginLeft: 8\n  }\n}));\nconst CardActions = /*#__PURE__*/React.forwardRef(function CardActions(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActions'\n  });\n  const {\n      disableSpacing = false,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    disableSpacing\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardActionsRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActions;", "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z\"\n}), 'Delete');", "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7zm-6 .67 2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z\"\n}), 'SaveAlt');", "import React, { useState, useEffect } from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport {\r\n    GridColDef,\r\n    GridToolbarContainer,\r\n    GridToolbarColumnsButton,\r\n    GridToolbarFilterButton,\r\n    GridToolbarDensitySelector,\r\n} from \"@mui/x-data-grid\";\r\nimport {\r\n    Button,\r\n    Menu,\r\n    MenuItem,\r\n    FormControlLabel,\r\n    IconButton,\r\n    Link,\r\n    Dialog,\r\n    DialogActions,\r\n    DialogTitle,\r\n    DialogContent,\r\n    Box,\r\n    Card,\r\n    Snackbar,\r\n    Alert,\r\n    Tooltip,\r\n    CardContent,\r\n    CardActions,\r\n    CardMedia,\r\n    Typography,\r\n    Modal\r\n} from \"@mui/material\";\r\nimport {\r\n    Edit as EditIcon,\r\n    Delete as DeleteIcon,\r\n    SaveAlt as SaveAltIcon,\r\n    Opacity,\r\n    FileUpload,\r\n} from \"@mui/icons-material\";\r\nimport SwapHorizIcon from '@mui/icons-material/SwapHoriz';\r\nimport loader from \"../../assets/loader.gif\";\r\nimport CustomGrid from \"../common/Grid\";\r\nimport { getAllFiles,GetGuidesUsedByFile } from \"../../services/FileManagementService\";\r\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport { UploadImage,DeleteFile ,ReplaceFile} from \"../../services/FileManagementService\";\r\nimport { height } from \"@mui/system\";\r\nimport { inherits } from \"util\";\r\nimport LinkIcon from '@mui/icons-material/Link';\r\nimport UpgradeIcon from '@mui/icons-material/Upgrade';\r\nimport { DeleteRed, PreviewImage, Replace, copy } from \"../../assets/icons/icons\";\r\n\r\n\r\ninterface FileUpload {\r\n    ImageId: string;\r\n    FileName: string | null;\r\n    Url: string;\r\n}\r\n\r\nconst FileList: React.FC = () => {\r\n    const [models, setModels] = useState<FileUpload[]>([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [emailiddelete, setemailiddelete] = useState(\"\");\r\n    const [useridedit, setUserIdEdit] = useState(\"\");\r\n    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n    const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\r\n    const [showPopup, setShowPopup] = useState(false);\r\n    const [selectedFile, setSelectedFile] = useState(null);\r\n    const [preview, setPreview] = useState(false);\r\n    const [showEditPopup, setShowEditPopup] = useState(false);\r\n    const [showDeletePopup, setShowDeletePopup] = useState(false);\r\n    const [fileUploads, setFileUploads] = useState<FileUpload[]>([]);\r\n    const [snackbarOpen, setSnackbarOpen] = useState(false);\r\n    const [snackbarMessage, setSnackbarMessage] = useState(\"\");\r\n    const [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n    const [replaceFileId, setReplaceFileId] = useState(\"\");\r\n    const [hoverId, setHoverId] = useState(\"\");\r\n    const [previewMode, setPreviewMode] = useState<FileUpload>();\r\n    const [guideNames, setGuideNames] = useState([]);\r\n    \r\n    const handleSnackbarClose = () => {\r\n        setSnackbarOpen(false);\r\n        \r\n    };\r\n\r\n\r\n    const openSnackbar = () => {\r\n        setSnackbarOpen(true);\r\n        \r\n        setTimeout(() => {\r\n            setSnackbarOpen(false);\r\n        }, 2000);\r\n    }\r\n  \r\n    useEffect(() => {\r\n        //getAllFiles();\r\n        const unsubscribe = subscribe(setSidebarOpen);\r\n        return () => unsubscribe();\r\n    }, []);\r\n\r\n    const fetchData = async () => {\r\n        setLoading(true);\r\n                        try {\r\n                const response = await getAllFiles();\r\n                            if (response) {\r\n                        const uploads: FileUpload[] = response.map((file:any) => ({\r\n                        ImageId: file.Id, \r\n                        FileName: file.Name || null,\r\n                        Url: file.Url + \"?timestamp=\" + new Date().getTime()|| '',\r\n                    }));\r\n                    setModels(uploads);\r\n                } else {\r\n                }\r\n            } catch (error) {\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n    };\r\n\r\n    useEffect(() => {\r\n        \r\n        if (!showPopup) {\r\n            fetchData();\r\n        }\r\n    }, [showPopup]);\r\n    \r\n    const openPopup = () => {\r\n\t\tsetShowPopup(true);\r\n\t};\r\n    const MatEdit = (params: any) => {\r\n        const handleDeleteClick = (emailId: string) => {\r\n            setShowDeletePopup(true);\r\n            setemailiddelete(emailId);\r\n        };\r\n\r\n        const handleEditClick = (userid: string) => {\r\n            setShowEditPopup(true);\r\n            setUserIdEdit(userid);\r\n        };\r\n\r\n        return (\r\n            <div>\r\n                <FormControlLabel\r\n                    control={\r\n                        <IconButton\r\n                            color=\"secondary\"\r\n                            aria-label=\"edit\"\r\n                            onClick={() => handleEditClick(params.userid)}\r\n                        >\r\n                            <EditIcon style={{ color: \"blue\" }} />\r\n                        </IconButton>\r\n                    }\r\n                    label={\"\"}\r\n                />\r\n                 <FormControlLabel\r\n                    control={\r\n                        <IconButton\r\n                            color=\"secondary\"\r\n                            aria-label=\"delete\"\r\n                            onClick={() => handleDeleteClick(params.emailId)}\r\n                        >\r\n                            <SwapHorizIcon style={{ color: \"blue\" }} />\r\n                        </IconButton>\r\n                    }\r\n                    label={\"\"}\r\n                />\r\n                <FormControlLabel\r\n                    control={\r\n                        <IconButton\r\n                            color=\"secondary\"\r\n                            aria-label=\"delete\"\r\n                            onClick={() => handleDeleteClick(params.emailId)}\r\n                        >\r\n                            <DeleteIcon style={{ color: \"blue\" }} />\r\n                        </IconButton>\r\n                    }\r\n                    label={\"\"}\r\n                />  \r\n            </div>\r\n        );\r\n    };\r\n\r\n  // Define columns\r\n    const columns: GridColDef[] = [\r\n        {\r\n            field: \"ImageId\", // Ensure this matches your data field\r\n            headerName: \"ImageId\",\r\n            width: sidebarOpen ? 150 : 170,\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n        },\r\n        {\r\n            field: \"FileName\", \r\n            headerName: \"Name\", \r\n            width: sidebarOpen ? 220 : 250,\r\n            align: 'left',\r\n            headerAlign: 'left',\r\n        },\r\n        {\r\n            field: \"Url\",\r\n            headerName: \"Link\",\r\n            width: sidebarOpen ? 450 : 600,\r\n            renderCell: (params) => (\r\n                <Link href={params.value} target=\"_blank\" rel=\"noopener noreferrer\" color=\"inherit\">\r\n                    {params.value}\r\n                </Link>\r\n            ),\r\n            align: 'left',\r\n            headerAlign: 'left',\r\n        },\r\n        {\r\n            field: \"actions\",\r\n            headerName: \"Actions\",\r\n            sortable: false,\r\n            width: 300,\r\n            renderCell: (params) => (\r\n                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>\r\n                   \r\n                        <MatEdit/>\r\n                   \r\n                </div>\r\n            ),\r\n            align: 'center',\r\n            headerAlign: 'center',\r\n        },\r\n    ];\r\n\r\n// Handle edit action (replace this with your actual implementation)\r\nconst handleEdit = (row:any) => {\r\n    // Implement your edit logic here\r\n};\r\n   \r\n\r\n    const handleClose = () => {\r\n        setShowPopup(false);\r\n        setSelectedFile(null);\r\n        setPreview(false);\r\n    };\r\n\r\n    const CustomToolbar: React.FC = () => {\r\n        const handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {\r\n            setAnchorEl(event.currentTarget);\r\n        };\r\n\r\n        const handleExportMenuClose = () => {\r\n         setAnchorEl(null);\r\n        };\r\n       \r\n        const handleDownloadExcelClick = () => {\r\n            handleExportMenuClose();\r\n        };\r\n\r\n        return (\r\n            <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                <GridToolbarContainer>\r\n                    <GridToolbarColumnsButton />\r\n                    <GridToolbarFilterButton />\r\n                    <GridToolbarDensitySelector />\r\n                </GridToolbarContainer>\r\n                <Button\r\n                    aria-controls=\"export-menu\"\r\n                    aria-haspopup=\"true\"\r\n                    onClick={handleExportMenuClick}\r\n                    style={{ marginLeft: \"10px\" }}\r\n                    startIcon={<SaveAltIcon />}\r\n                >\r\n                    Export\r\n                </Button>\r\n                <Menu\r\n                    id=\"export-menu\"\r\n                    anchorEl={anchorEl}\r\n                    keepMounted\r\n                    open={Boolean(anchorEl)}\r\n                    onClose={handleExportMenuClose}\r\n                >\r\n                    <MenuItem onClick={handleDownloadExcelClick}>Download Excel</MenuItem>\r\n                </Menu>\r\n            </div>\r\n        );\r\n    };\r\n\r\n    const handleFileUpload = (event: any) => {\r\n      setSelectedFile(event.target.files);\r\n    };\r\n\r\n    const handleUpload = async () => {\r\n      if (selectedFile) {\r\n        try {\r\n          setLoading(true);\r\n          await UploadImage(\r\n            selectedFile,\r\n            setLoading,\r\n            setShowPopup,\r\n            setModels,\r\n            setSelectedFile\r\n          );\r\n        } catch (error) {\r\n          let message = \"Upload failed. Please try again.\";\r\n          const err = error as any;\r\n\r\n          if (err?.response?.data) {\r\n            message =\r\n              typeof err.response.data === \"string\"\r\n                ? err.response.data\r\n                : err.response.data.message || message;\r\n          }\r\n          setSnackbarMessage(message);\r\n          setSnackbarSeverity(\"error\");\r\n          openSnackbar();\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    const copyUrl = async (url: string) => {\r\n        try {\r\n            await navigator.clipboard.writeText(url);\r\n            setSnackbarMessage(\"Url Copied Succesfully\");\r\n            setSnackbarSeverity(\"success\");\r\n            openSnackbar();\r\n\r\n            \r\n            \r\n            \r\n        } catch (error) {\r\n            console.log(error);\r\n        }\r\n    }\r\n\r\n\r\n\r\n    const deleteFileIconClick = async (file: any) => {\r\n        try {\r\n            const status = await DeleteFile(file.ImageId);\r\n            if (status.Success!=false) {\r\n                setSnackbarMessage(\"File Deleted Succesfully\");\r\n                setSnackbarSeverity(\"success\");\r\n                openSnackbar();\r\n                fetchData();\r\n            } else {\r\n                setSnackbarMessage(status.ErrorMessage||\"Error in Deleting File\");\r\n                setSnackbarSeverity(\"error\");\r\n                openSnackbar();\r\n            }\r\n        } catch (error) {\r\n            console.log(error);\r\n            \r\n        }\r\n    }\r\n\r\n    const ReplaceImage = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n      const file = event.target.files?.[0];\r\n      console.log(replaceFileId, \"Is replace file id\");\r\n      if (file) {\r\n        try {\r\n          setLoading(true);\r\n          await ReplaceFile(replaceFileId, file, setLoading);\r\n          setModels([]);\r\n\r\n          fetchData();\r\n        } catch (error) {\r\n          let message = \"Upload failed. Please try again.\";\r\n          const err = error as any;\r\n\r\n          if (err?.response?.data) {\r\n            message =\r\n              typeof err.response.data === \"string\"\r\n                ? err.response.data\r\n                : err.response.data.message || message;\r\n          }\r\n          setSnackbarMessage(message);\r\n          setSnackbarSeverity(\"error\");\r\n          openSnackbar();\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    };\r\n\r\n\r\n    const setReplaceFileUrl = (id : any) => {\r\n        console.log(id, \"from click\");\r\n        setReplaceFileId(id);\r\n    }\r\n\r\n    const previewFile = async (model : any) => {\r\n        console.log(\"Preview clicked\");\r\n        \r\n        const names = await GetGuidesUsedByFile(model.ImageId, setLoading);\r\n        setGuideNames(names);\r\n        setPreview(true);\r\n        setPreviewMode(model);\r\n    }\r\n    const { t: translate } = useTranslation();\r\n\r\n    return (\r\n      \r\n        <div className={`smooth-transition`} style={{ marginLeft: sidebarOpen ? \"253px\" : \"23px\" }}>\r\n            <Box sx={{    maxHeight: \"calc(90vh)\", overflow: \"auto\"}}>\r\n                <div>\r\n                <h1>{translate('File Management')}</h1>\r\n                    <div style={{ position: 'absolute', top: '5px', right: '2px'}}>\r\n                    {/* <button onClick={() => openPopup} className=\"userButton\"\r\n                    >  \r\n                        <AddIcon style={{ fontSize: '1.5em' }} />\r\n                        <span style={{ position: 'relative', top: '-7px'}}> Add Field</span>\r\n                    </button> */}\r\n                        <Button\r\n                        onClick={() => setShowPopup(true)}\r\n                        className=\"userButton\"\r\n                        startIcon={<AddIcon />}\r\n                        variant=\"contained\"\r\n                    >\r\n                       <span style={{ position: 'relative' }}>Add File</span>\r\n                    </Button>\r\n                    </div>\r\n      \r\n                    <Dialog open={showPopup}\r\n                         onClose={handleClose}\r\n                         maxWidth=\"md\" // You can change this to 'sm', 'md', 'lg', or 'xl'\r\n                        // fullWidth\r\n                         PaperProps={{\r\n                             style: {\r\n                                 minHeight: '35vh', // Minimum height\r\n                                 minWidth: '30vw',  // Minimum width\r\n                             },\r\n                         }}\r\n                    >\r\n                        <DialogTitle display=\"flex\"\r\n                                justifyContent=\"center\"\r\n                            alignItems=\"center\"\r\n                            style={{ marginLeft: \"-100px\" ,marginTop:\"40px\"}}\r\n                        >Upload File</DialogTitle>\r\n                        <DialogContent>\r\n                        <Box\r\n                                display=\"flex\"\r\n                                justifyContent=\"center\"\r\n                                alignItems=\"center\"\r\n                                minHeight=\"50px\" // Adjust as needed\r\n                            >\r\n                            {/* <input type=\"file\" onChange={handleFileChange} /> */}\r\n\r\n                                <input type=\"file\" onChange={handleFileUpload} accept=\"image/*\" multiple/>\r\n                         </Box>\r\n                        </DialogContent>\r\n                        <DialogActions>\r\n                        <Button onClick={handleClose} color=\"primary\">\r\n                            Cancel\r\n                        </Button>\r\n                        <Button\r\n                            onClick={async () => {\r\n                                await handleUpload();\r\n                                setShowPopup(false);\r\n                            }}\r\n                            color=\"primary\"\r\n                            //disabled={!selectedFile}\r\n                        >\r\n                            Upload\r\n                        </Button>\r\n                        </DialogActions>\r\n                    </Dialog>\r\n                </div>\r\n                {loading ? (\r\n                     <div className=\"Loaderstyles\">\r\n                        <img\r\n      src={loader}\r\n\t\t\talt=\"Spinner\"\r\n\t\t\tclassName=\"LoaderSpinnerStyles\"\r\n      />\r\n                    </div>\r\n            ) : (\r\n                    <>\r\n                    <Box  sx={{display:\"flex\",flexWrap:\"wrap\",}}>\r\n                    {\r\n                        models.map((model,index) => {\r\n                            return (\r\n                                <Card key={index} sx={{\r\n                                    display: \"flex\", flexDirection: \"column\", height: \"223px\",\r\n                                    position: \"relative\", margin: \"10px\", width: \"277px\", borderRadius: \"15px\",\r\n                                    \"&:hover\": {\r\n                                        transform: \"scale(1.05)\", // Slight pop-up effect\r\n                                        boxShadow: 10, // Stronger shadow on hover\r\n                                    }\r\n                                }}>\r\n                                    <Box sx={{\r\n                                        position: \"relative\",\r\n                                        \r\n                                        display: \"flex\", justifyContent: \"center\", alignItems: \"center\", flexGrow: 1,cursor: \"pointer\"\r\n                                        \r\n                                    }}\r\n                                    onMouseEnter={() => setHoverId(model.ImageId)}\r\n                                    onMouseLeave={() => setHoverId(\"\")}>\r\n                                    <CardMedia\r\n                                        component=\"img\"\r\n                                        image={model.Url||\"\"}\r\n                                            alt={model.FileName || \"Image\"}\r\n                                            \r\n                                            sx={{\r\n                                                height: \"162px\",\r\n\r\n                                                width: \"261px\",\r\n                                                objectFit: \"cover\",\r\n                                                filter: hoverId == model.ImageId ? \"blur(1px)\" : \"none\",\r\n                                                borderRadius: 2,\r\n                                                margin: 1\r\n                                            }}\r\n                                            onClick={()=>previewFile(model)}\r\n                                        className=\"image-hover\"\r\n                                        />\r\n                                        \r\n                                        \r\n                                        { hoverId == model.ImageId &&(\r\n                                            <Box sx={{\r\n                                                position: \"absolute\",\r\n                                                display: \"flex\",\r\n                                                justifyContent: \"center\",\r\n                                                alignItems: \"center\",\r\n                                                \r\n                                            }}\r\n                                            onClick={()=>previewFile(model)}\r\n                                            >\r\n                                                {/* <Preview /> */}\r\n                                                <img src={PreviewImage} alt=\"Preview Image\" />\r\n                                                {/* <Typography>Preview</Typography> */}\r\n                                            </Box>)\r\n                                        }\r\n                                        \r\n                                            \r\n                                            \r\n                                    \r\n                                    <IconButton\r\n                                        onClick={() => deleteFileIconClick(model)}\r\n                                        style={{ position: \"absolute\", top: 5, right: 5, color: \"red\" }}\r\n                                    >\r\n                                        <img src={DeleteRed} alt=\"Delete\"/>\r\n                                    </IconButton>\r\n                                    </Box>\r\n                                    <CardActions style={{ display: \"flex\", justifyContent: \"space-between\", padding: \"0px 9px\" }}>\r\n                                        <Typography variant=\"body2\" noWrap style={{ maxWidth: \"50%\" }}>{model.FileName}</Typography>\r\n                                        <Box>\r\n                                    <IconButton onClick={() => copyUrl(model.Url)}>\r\n                                    <img src={copy} alt=\"copy\"/>\r\n                                        </IconButton>\r\n                                        <Tooltip title=\"Replace Image\">\r\n                                        <IconButton \r\n                                                onClick={(event) => {\r\n                                                    setReplaceFileUrl(model.ImageId);\r\n                                                event?.stopPropagation();\r\n                                                document.getElementById(\"file-upload\")?.click();\r\n                                            }}>\r\n                                                    <img src={Replace} alt=\"Replace\"/>\r\n                                                    \r\n                                                </IconButton>\r\n                                                \r\n                                            </Tooltip>\r\n                                            <input\r\n                                                type=\"file\"\r\n                                                id=\"file-upload\"\r\n                                                style={{ display: \"none\" }}\r\n                                                accept=\"image/*\"\r\n                                                onChange={ReplaceImage}\r\n                                            />\r\n                                            </Box>\r\n                                    </CardActions>\r\n                                </Card>\r\n                                );\r\n                        })\r\n                            }\r\n                            </Box>\r\n                            {\r\n                                preview && (\r\n                                    <Modal\r\n                                        open={preview}\r\n                                        onClose={() => {\r\n                                            setPreview(false);\r\n                                            setPreviewMode(undefined);\r\n                                        }\r\n                                            \r\n                                        }\r\n                                        aria-labelledby=\"modal-modal-title\"\r\n                                        aria-describedby=\"modal-modal-description\"\r\n                                        >\r\n                                        <Box sx={{\r\n                                            position: \"absolute\",\r\n                                            top: \"50%\",\r\n                                            left: \"50%\",\r\n                                            transform: \"translate(-50%, -50%)\",\r\n                                            bgcolor: \"background.paper\",\r\n                                            boxShadow: 24,\r\n\r\n                                            maxWidth: \"600px\",\r\n                                            maxHeight: \"600px\",\r\n                                            p: 4,\r\n                                        }}>\r\n                                            \r\n                                            <img src={previewMode?.Url|| \"\"} alt=\"Model Image\" style={{maxWidth:\"500px\",maxHeight:\"500px\"}}/>\r\n                                            <Typography id=\"modal-modal-description\" sx={{ mt: 2 }}>\r\n                                                Name: {previewMode?.FileName}\r\n                                            </Typography>\r\n                                            <Typography id=\"modal-modal-description\" sx={{ mt: 2 }}>\r\n                                                Used Guides : {guideNames?.length<1 ? \"No Guides Used this file \" : guideNames }\r\n                                            </Typography>\r\n\r\n                                        </Box>\r\n                                    </Modal>\r\n                                )\r\n                                \r\n                    }\r\n                    <Snackbar\r\n                        open={snackbarOpen}\r\n                        autoHideDuration={6000}\r\n                        onClose={handleSnackbarClose}\r\n                        anchorOrigin={{ vertical: \"top\", horizontal: \"center\" }}\r\n                        sx={{ zIndex: 10000,marginTop:4}} // Optionally adjust the zIndex if needed\r\n                    >\r\n                        <Alert\r\n                            onClose={handleSnackbarClose}\r\n                            severity={snackbarSeverity}\r\n                            sx={{ width: \"100%\" }}\r\n                        >\r\n                            {snackbarMessage}\r\n                        </Alert>\r\n                    </Snackbar>\r\n                    {/* <CustomGrid\r\n                        rows={models}\r\n                        columns={columns}\r\n                        pageSize={100}\r\n                        totalRows={models.length}   \r\n                        onPageChange={(newPage) => newPage}\r\n                        onPageSizeChange={(newPageSize) => newPageSize}\r\n                        rowsPerPageOptions={[10, 20, 50, 100]}\r\n                        Toolbar={CustomToolbar}\r\n                        /> */}\r\n                        </>\r\n            )}\r\n            </Box>\r\n            </div>\r\n    );\r\n};\r\n\r\nexport default FileList;\r\n", "import { adminApiService, userApiService, adminUrl } from \"./APIService\";\r\nimport { FileUpload } from \"../models/FileUpload\"; \r\n\r\nexport const getAllFiles = async (): Promise<FileUpload[] | null> =>\r\n\t{\r\n\t\ttry {\r\n\t\t  const response = await userApiService.get<FileUpload[]>('/FileUpload/GetAllFiles');\r\n\t\t  return response.data;\r\n\t\t} catch (error) {\r\n\t\t  console.error('Error fetching all users:', error);\r\n\t\t  throw error;\r\n\t\t}\r\n\t};\r\n    \r\n    export const UploadImage = async (\r\n        files: File[],\r\n        setLoading: any,\r\n        setShowPopup: any,\r\n        setModels: any,\r\n        setselectedFile:any\r\n    ) => {\r\n        const formData = new FormData();\r\n\r\n        for (let file of files) {\r\n            formData.append(\"file\", file);  // \"files\" matches backend parameter\r\n        }\r\n        //formData.append('file', files);\r\n    \r\n        try {\r\n            setLoading(true);\r\n    \r\n            const response = await userApiService.post(`/FileUpload/UploadFiles`, formData, {\r\n                headers: { \"Content-Type\": \"multipart/form-data\" }\r\n            });\r\n            const responseData = response.data;\r\n            if (!responseData) {\r\n                throw new Error('Network response was not ok');\r\n            }\r\n    \r\n            setShowPopup(false);\r\n           \r\n        } catch (error) {\r\n\r\n            throw error;\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n};\r\n    \r\n\r\nexport const DeleteFile = async (fileId: any) => {\r\n    try {    \r\n        const response = await userApiService.delete('/FileUpload/DeleteFile', {\r\n            params: {\r\n                fileId\r\n            }\r\n        });\r\n        // if ( response.status == 200) {\r\n        //     return true;  \r\n        // } else {\r\n        //     console.log(response);\r\n        // }\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error deleting file\", error);\r\n        throw error;\r\n    }\r\n}\r\n\r\nexport const ReplaceFile = async (\r\n    fileId: any,\r\n    file: File,\r\n    setLoading: any ) => {\r\n        const formData = new FormData();\r\n\r\n        \r\n    formData.append(\"file\", file); \r\n    \r\n    try {\r\n        setLoading(true);\r\n\r\n        const response = await userApiService.put(\r\n            \"/FileUpload/ReplaceFile\",\r\n            formData,  \r\n            {\r\n              params: { fileId },  \r\n              headers: { \"Content-Type\": \"multipart/form-data\" }\r\n            }\r\n          );\r\n\r\n        const responseData = response.data;\r\n            if (!responseData) {\r\n                throw new Error('Network response was not ok');\r\n            }\r\n    \r\n           \r\n        } catch (error) {\r\n            console.error('Error uploading file:', error);\r\n             throw error;\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    \r\n\r\n}\r\n    \r\nexport const GetGuidesUsedByFile = async(\r\n    fileId:any,\r\n    setLoading: any\r\n)=>{\r\n    try {\r\n        const response = await userApiService.get('/FileUpload/GetGuidesUsedByFile', {\r\n            params: {\r\n                fileId\r\n            }\r\n        });\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error Getting Guides \", error);\r\n        throw error;\r\n    }\r\n}\r\n    ", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root']);\nexport default cardClasses;", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"raised\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Paper from '../Paper';\nimport { getCardUtilityClass } from './cardClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardUtilityClass, classes);\n};\nconst CardRoot = styled(Paper, {\n  name: 'MuiCard',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(() => {\n  return {\n    overflow: 'hidden'\n  };\n});\nconst Card = /*#__PURE__*/React.forwardRef(function Card(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCard'\n  });\n  const {\n      className,\n      raised = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    raised\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardRoot, _extends({\n    className: clsx(classes.root, className),\n    elevation: raised ? 8 : undefined,\n    ref: ref,\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Card.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the card will use raised styling.\n   * @default false\n   */\n  raised: chainPropTypes(PropTypes.bool, props => {\n    if (props.raised && props.variant === 'outlined') {\n      return new Error('MUI: Combining `raised={true}` with `variant=\"outlined\"` has no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Card;", "\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _createSvgIcon = _interopRequireDefault(require(\"./utils/createSvgIcon\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nvar _default = exports.default = (0, _createSvgIcon.default)( /*#__PURE__*/(0, _jsxRuntime.jsx)(\"path\", {\n  d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z\"\n}), 'Add');", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "import { getPath } from '@mui/system';\nimport { alpha } from '@mui/system/colorManipulator';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"onBlur\", \"onFocus\", \"TypographyClasses\", \"underline\", \"variant\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useForkRef from '../utils/useForkRef';\nimport Typography from '../Typography';\nimport linkClasses, { getLinkUtilityClass } from './linkClasses';\nimport getTextDecoration, { colorTransformations } from './getTextDecoration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    component,\n    focusVisible,\n    underline\n  } = ownerState;\n  const slots = {\n    root: ['root', `underline${capitalize(underline)}`, component === 'button' && 'button', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getLinkUtilityClass, classes);\n};\nconst LinkRoot = styled(Typography, {\n  name: 'MuiLink',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`underline${capitalize(ownerState.underline)}`], ownerState.component === 'button' && styles.button];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({}, ownerState.underline === 'none' && {\n    textDecoration: 'none'\n  }, ownerState.underline === 'hover' && {\n    textDecoration: 'none',\n    '&:hover': {\n      textDecoration: 'underline'\n    }\n  }, ownerState.underline === 'always' && _extends({\n    textDecoration: 'underline'\n  }, ownerState.color !== 'inherit' && {\n    textDecorationColor: getTextDecoration({\n      theme,\n      ownerState\n    })\n  }, {\n    '&:hover': {\n      textDecorationColor: 'inherit'\n    }\n  }), ownerState.component === 'button' && {\n    position: 'relative',\n    WebkitTapHighlightColor: 'transparent',\n    backgroundColor: 'transparent',\n    // Reset default value\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    border: 0,\n    margin: 0,\n    // Remove the margin in Safari\n    borderRadius: 0,\n    padding: 0,\n    // Remove the padding in Firefox\n    cursor: 'pointer',\n    userSelect: 'none',\n    verticalAlign: 'middle',\n    MozAppearance: 'none',\n    // Reset\n    WebkitAppearance: 'none',\n    // Reset\n    '&::-moz-focus-inner': {\n      borderStyle: 'none' // Remove Firefox dotted outline.\n    },\n    [`&.${linkClasses.focusVisible}`]: {\n      outline: 'auto'\n    }\n  });\n});\nconst Link = /*#__PURE__*/React.forwardRef(function Link(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiLink'\n  });\n  const {\n      className,\n      color = 'primary',\n      component = 'a',\n      onBlur,\n      onFocus,\n      TypographyClasses,\n      underline = 'always',\n      variant = 'inherit',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const handlerRef = useForkRef(ref, focusVisibleRef);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    focusVisible,\n    underline,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(LinkRoot, _extends({\n    color: color,\n    className: clsx(classes.root, className),\n    classes: TypographyClasses,\n    component: component,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    ref: handlerRef,\n    ownerState: ownerState,\n    variant: variant,\n    sx: [...(!Object.keys(colorTransformations).includes(color) ? [{\n      color\n    }] : []), ...(Array.isArray(sx) ? sx : [sx])]\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Link.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the link.\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Typography`](/material-ui/api/typography/) element.\n   */\n  TypographyClasses: PropTypes.object,\n  /**\n   * Controls when the link should have an underline.\n   * @default 'always'\n   */\n  underline: PropTypes.oneOf(['always', 'hover', 'none']),\n  /**\n   * Applies the theme typography styles.\n   * @default 'inherit'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string])\n} : void 0;\nexport default Link;", "\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _createSvgIcon = _interopRequireDefault(require(\"./utils/createSvgIcon\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nvar _default = exports.default = (0, _createSvgIcon.default)( /*#__PURE__*/(0, _jsxRuntime.jsx)(\"path\", {\n  d: \"M6.99 11 3 15l3.99 4v-3H14v-2H6.99zM21 9l-3.99-4v3H10v2h7.01v3z\"\n}), 'SwapHoriz');"], "names": ["getCardMediaUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "_excluded", "CardMediaRoot", "styled", "name", "overridesResolver", "props", "styles", "ownerState", "isMediaComponent", "isImageComponent", "root", "media", "img", "_ref", "_extends", "display", "backgroundSize", "backgroundRepeat", "backgroundPosition", "width", "objectFit", "MEDIA_COMPONENTS", "IMAGE_COMPONENTS", "React", "inProps", "ref", "useDefaultProps", "children", "className", "component", "image", "src", "style", "other", "_objectWithoutPropertiesLoose", "indexOf", "composed<PERSON><PERSON>le", "backgroundImage", "concat", "classes", "slots", "composeClasses", "useUtilityClasses", "_jsx", "clsx", "as", "role", "undefined", "getCardActionsUtilityClass", "CardActionsRoot", "disableSpacing", "spacing", "alignItems", "padding", "marginLeft", "createSvgIcon", "d", "FileList", "models", "setModels", "useState", "loading", "setLoading", "emailiddelete", "setemailiddelete", "useridedit", "setUserIdEdit", "anchorEl", "setAnchorEl", "sidebarOpen", "setSidebarOpen", "isSidebarOpen", "showPopup", "setShowPopup", "selectedFile", "setSelectedFile", "preview", "setPreview", "showEditPopup", "setShowEditPopup", "showDeletePopup", "setShowDeletePopup", "fileUploads", "setFileUploads", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "replaceFileId", "setReplaceFileId", "hoverId", "setHoverId", "previewMode", "setPreviewMode", "guideNames", "setGuideNames", "handleSnackbarClose", "openSnackbar", "setTimeout", "useEffect", "unsubscribe", "subscribe", "fetchData", "async", "response", "userApiService", "get", "data", "error", "console", "getAllFiles", "uploads", "map", "file", "ImageId", "Id", "FileName", "Name", "Url", "Date", "getTime", "handleClose", "handleUpload", "files", "formData", "FormData", "append", "post", "headers", "Error", "UploadImage", "_err$response", "message", "err", "deleteFileIconClick", "status", "delete", "params", "fileId", "DeleteFile", "Success", "ErrorMessage", "log", "ReplaceImage", "_event$target$files", "event", "target", "put", "ReplaceFile", "_err$response2", "previewFile", "names", "GetGuidesUsedByFile", "model", "t", "translate", "useTranslation", "_jsxs", "Box", "sx", "maxHeight", "overflow", "position", "top", "right", "<PERSON><PERSON>", "onClick", "startIcon", "AddIcon", "variant", "Dialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "PaperProps", "minHeight", "min<PERSON><PERSON><PERSON>", "DialogTitle", "justifyContent", "marginTop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "onChange", "accept", "multiple", "DialogActions", "color", "loader", "alt", "_Fragment", "flexWrap", "index", "Card", "flexDirection", "height", "margin", "borderRadius", "transform", "boxShadow", "flexGrow", "cursor", "onMouseEnter", "onMouseLeave", "CardMedia", "filter", "PreviewImage", "IconButton", "DeleteRed", "CardActions", "Typography", "noWrap", "navigator", "clipboard", "writeText", "url", "copyUrl", "copy", "<PERSON><PERSON><PERSON>", "title", "_document$getElementB", "id", "stopPropagation", "document", "getElementById", "click", "Replace", "Modal", "left", "bgcolor", "p", "mt", "length", "Snackbar", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "zIndex", "<PERSON><PERSON>", "severity", "getCardUtilityClass", "CardRoot", "Paper", "raised", "elevation", "_interopRequireDefault", "require", "exports", "_createSvgIcon", "_jsxRuntime", "default", "jsx", "getLinkUtilityClass", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "theme", "transformedColor", "transformDeprecatedColors", "<PERSON><PERSON><PERSON>", "channelColor", "alpha", "LinkRoot", "capitalize", "underline", "button", "textDecoration", "textDecorationColor", "getTextDecoration", "WebkitTapHighlightColor", "backgroundColor", "outline", "border", "userSelect", "verticalAlign", "MozAppearance", "WebkitAppearance", "borderStyle", "linkClasses", "focusVisible", "onBlur", "onFocus", "TypographyClasses", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "focusVisibleRef", "useIsFocusVisible", "setFocusVisible", "handler<PERSON>ef", "useForkRef", "current", "Object", "keys", "includes", "Array", "isArray"], "sourceRoot": ""}