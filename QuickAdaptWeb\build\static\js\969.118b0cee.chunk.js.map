{"version": 3, "file": "static/js/969.118b0cee.chunk.js", "mappings": "0LAGA,MA4FA,EA5F0BA,KACxB,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAAiBC,IAAsBF,EAAAA,EAAAA,WAAS,GAkBvD,OACMG,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAACC,SAAS,KAAKC,UAAU,gBAAeC,SAAA,EAClDC,EAAAA,EAAAA,KAACC,EAAAA,EAAG,CAACC,GAAI,EAAGJ,UAAU,mBAAkBC,UACpCC,EAAAA,EAAAA,KAAA,OACIG,IAAKC,EAAAA,GACLC,IAAI,kBACJP,UAAU,4BAIhBL,IACAE,EAAAA,EAAAA,MAAAW,EAAAA,SAAA,CAAAP,SAAA,EACEJ,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWC,SAAA,EAC9BC,EAAAA,EAAAA,KAAA,OAAAD,SAAK,gCACLC,EAAAA,EAAAA,KAAA,OAAAD,SAAK,wDACHJ,EAAAA,EAAAA,MAAA,OAAAI,SAAA,CAAK,iCAA6BC,EAAAA,EAAAA,KAAA,OAC1BG,IAAKI,EAAAA,GACLF,IAAI,gBAEN,MAAEL,EAAAA,EAAAA,KAAA,KAAGQ,KAAO,uFAAsFT,SAAC,eAAc,aAEvHJ,EAAAA,EAAAA,MAAA,QAAMG,UAAU,kBACdW,SAtCWC,UACnBC,EAAMC,iBACNC,aAAaC,QACbC,eAAeD,QACfE,SAASC,OAAOC,MAAM,KAAKC,SAAQ,SAASC,GAC1CJ,SAASC,OAASG,EACfC,QAAQ,MAAO,IACfA,QAAQ,MAAO,cAAe,IAAIC,MAAOC,cAAgB,UAC9D,IAEA7B,GAAmB,GACnB8B,YAAW,KACTC,OAAOC,SAASlB,KAAI,GAAAmB,OAAMC,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,oBAAAA,wCAAAA,4BAAAA,OAAAA,kBAAAA,+BAAAA,6BAAAA,2NAAAA,kBAAAA,gCAAAA,mBAAAA,oCAAYC,kBAAmB,GACxD,IAAK,EAyBuB9B,SAAA,EAEvBC,EAAAA,EAAAA,KAAA,OAAAD,SAAK,cACLC,EAAAA,EAAAA,KAAA,YACE8B,MAAOxC,EACPyC,SAAWC,GAAMzC,EAAYyC,EAAEC,OAAOH,OACtCI,YAAY,sBACZC,KAAM,EACNC,KAAM,MAIRpC,EAAAA,EAAAA,KAAA,UACEqC,KAAK,SAELvC,UAAU,YACVwC,SAAqC,IAA3BhD,EAASiD,OAAOC,OAC1BC,MAAO,CACCC,gBAAiB,UACjBC,cAA0C,IAA3BrD,EAASiD,OAAOC,OAAe,OAAS,OACvDI,QAAoC,IAA3BtD,EAASiD,OAAOC,OAAe,GAAM,GAEtDK,YAAcb,GAAOA,EAAEc,cAAcL,MAAMC,gBAAkB,UAC7DK,WAAaf,GAAOA,EAAEc,cAAcL,MAAMC,gBAAkB,UAAW3C,SACxE,0BAOJN,IACCE,EAAAA,EAAAA,MAAA,OAAKG,UAAU,cAAaC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,OACOG,IAAK6C,EAAAA,GACL3C,IAAI,kBAGdV,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,OAAAD,SAAK,+BACLC,EAAAA,EAAAA,KAAA,OAAAD,SAAK,gGAIJ,C", "sources": ["components/settings/UnInstall.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Container, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';\r\nimport { feedbackimg, QuickAdopttext, Refresh } from \"../../assets/icons/icons\";\r\nconst Cleanup: React.FC = () => {\r\n  const [feedback, setFeedback] = useState('');\r\n  const [thankYouMessage, setThankYouMessage] = useState(false); // State to control the thank you message\r\n\r\n  const handleSubmit = async (event: React.FormEvent) => {\r\n    event.preventDefault();\r\n    localStorage.clear();\r\n    sessionStorage.clear();\r\n    document.cookie.split(\";\").forEach(function(c) {\r\n      document.cookie = c\r\n        .replace(/^ +/, \"\")\r\n        .replace(/=.*/, \"=;expires=\" + new Date().toUTCString() + \";path=/\");\r\n    });\r\n\r\n    setThankYouMessage(true);\r\n    setTimeout(() => {\r\n      window.location.href = `${process.env.REACT_APP_WEB_API}`; \r\n    }, 2000);\r\n  };\r\n\r\n  return (  \r\n        <Container maxWidth=\"sm\" className=\"qadpt-unistpg\">\r\n        <Box mb={4} className=\"qadpt-brand-logo\">\r\n            <img \r\n                src={QuickAdopttext} \r\n                alt=\"QuickAdopt Logo\" \r\n                className=\"qadpt-brand-logo-img\"\r\n            />\r\n         </Box>        \r\n     \r\n        {!thankYouMessage && (\r\n          <>\r\n            <div className='qadpt-mgs'>\r\n        <div>We're sorry to see you go.</div>\r\n        <div>Before you leave , we'd love to hear your feedback</div>\r\n          <div>Need the extension again ?   <img \r\n                  src={Refresh} \r\n                  alt=\"Refresh img\" \r\n                  \r\n              />  <a href = \"https://chromewebstore.google.com/detail/quickadapt/bniabjfpljchdaipkcadcnpjndjdoaom\">Re-Install</a> it</div>\r\n          </div>\r\n          <form className='qadpt-feedbkfrm'\r\n            onSubmit={handleSubmit}\r\n                    >\r\n            <div>Feedback</div>\r\n            <textarea\r\n              value={feedback}\r\n              onChange={(e) => setFeedback(e.target.value)}\r\n              placeholder=\"Share your feedback\"\r\n              rows={4}\r\n              cols={50}\r\n            \r\n            />\r\n         \r\n            <button\r\n              type=\"submit\"\r\n              \r\n              className='qadpt-btn'\r\n              disabled={feedback.trim().length === 0}\r\n              style={{\r\n                      backgroundColor: '#5f9ea0',\r\n                      pointerEvents: feedback.trim().length === 0 ? 'none' : 'auto',\r\n                      opacity: feedback.trim().length === 0 ? 0.5 : 1,\r\n                    }}\r\n              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#5f9ea0')}\r\n              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = '#5f9ea0')}\r\n            >\r\n              Submit Feedback\r\n            </button>\r\n          </form>\r\n        </>\r\n      )}\r\n\r\n        {thankYouMessage && (\r\n          <div className='qadpt-thkpg'>\r\n           <img \r\n                  src={feedbackimg} \r\n                  alt=\"feedback img\" \r\n                  \r\n              />\r\n        <div className='qadpt-thkmsg'>\r\n              <div>Thanks for your feedback!</div>\r\n              <div>It’s been shared with our team and will be considered for future improvements.</div>\r\n            </div>\r\n            </div>\r\n      )}\r\n   </Container>\r\n        \r\n \r\n  );\r\n};\r\n\r\nexport default Cleanup;\r\n\r\n\r\n\r\n"], "names": ["Cleanup", "feedback", "setFeedback", "useState", "thankYouMessage", "setThankYouMessage", "_jsxs", "Container", "max<PERSON><PERSON><PERSON>", "className", "children", "_jsx", "Box", "mb", "src", "QuickAdopttext", "alt", "_Fragment", "Refresh", "href", "onSubmit", "async", "event", "preventDefault", "localStorage", "clear", "sessionStorage", "document", "cookie", "split", "for<PERSON>ach", "c", "replace", "Date", "toUTCString", "setTimeout", "window", "location", "concat", "process", "REACT_APP_WEB_API", "value", "onChange", "e", "target", "placeholder", "rows", "cols", "type", "disabled", "trim", "length", "style", "backgroundColor", "pointerEvents", "opacity", "onMouseOver", "currentTarget", "onMouseOut", "feedbackimg"], "sourceRoot": ""}