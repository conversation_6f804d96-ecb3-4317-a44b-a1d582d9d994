{"version": 3, "file": "static/js/599.fd64192a.chunk.js", "mappings": "qRAGA,MAAMA,EAAYC,2NACLC,EAAwBC,IACpC,MAAMC,EAAU,IAAIC,EAAAA,EACpBD,EAAQE,aAAaN,GAAoB,IAEzC,OAD0BI,EAAQA,QAAQD,EAClB,E,2CCGzB,MAoSA,EApSsBI,KAClB,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAcC,IAAmBC,EAAAA,EAAAA,WAAS,IAC1CC,EAAqBC,IAA0BF,EAAAA,EAAAA,WAAS,IACxDT,EAAUY,IAAeH,EAAAA,EAAAA,UAAS,KAClCI,EAAiBC,IAAsBL,EAAAA,EAAAA,UAAS,KAChDM,EAAeC,IAAoBP,EAAAA,EAAAA,UAAS,KAC5CQ,EAAsBC,IAA2BT,EAAAA,EAAAA,UAAS,KAC1DU,EAAqBC,IAA0BX,EAAAA,EAAAA,UAAS,CAC3DY,WAAW,EACXC,cAAc,EACdC,gBAAgB,EAChBC,WAAW,EACXC,UAAU,KAEPC,EAAgBC,IAAqBlB,EAAAA,EAAAA,WAAS,IAC9CmB,EAAmBC,IAAwBpB,EAAAA,EAAAA,WAAS,IAErD,aAAEqB,IAAiBC,EAAAA,EAAAA,KAEnBC,EADW3B,EAAS4B,SACAC,MAAM,KAC1BC,EAAgBH,EAASA,EAASI,OAAS,GAiF3CC,GAAYX,GAAoC,KAAlBX,GAAiD,KAAzBE,EAC5D,OACIqB,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAACC,SAAS,KAAKC,UAAU,sBAAqBC,SAAA,EACpDC,EAAAA,EAAAA,KAACC,EAAAA,EAAG,CAACC,GAAI,EAAGJ,UAAU,mBAAkBC,UACpCC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACC,QAAQ,KAAKN,UAAU,wBAAwBO,cAAY,EAAAN,SAAC,kBAI5EJ,EAAAA,EAAAA,MAACM,EAAAA,EAAG,CAAAF,SAAA,CACEd,GAoJEU,EAAAA,EAAAA,MAACM,EAAAA,EAAG,CAACH,UAAU,oBAAmBC,SAAA,EAC9BC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACC,QAAQ,KAAKN,UAAU,kBAAiBC,SAAC,uBAGrDC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACC,QAAQ,QAAQE,MAAM,gBAAgBR,UAAU,oBAAoBS,GAAG,OAAMR,SAAC,kDAG1FC,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CACHC,KAAK,SACLL,QAAQ,YACRN,UAAU,oBACVY,KAAK,kCAAiCX,SACzC,oCA/JLJ,EAAAA,EAAAA,MAAAgB,EAAAA,SAAA,CAAAZ,SAAA,EACIJ,EAAAA,EAAAA,MAACM,EAAAA,EAAG,CAACH,UAAU,wBAAuBC,SAAA,EAClCC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACC,QAAQ,KAAKN,UAAU,6BAA4BC,SAAC,yBAGhEC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CACPC,QAAQ,QACRE,MAAM,gBACNM,MAAM,SACNV,GAAI,EACJW,GAAI,CACAC,MAAO,QACPC,OAAQ,OACRC,QAAS,MACTC,WAAY,UACZC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,UAAW,SACXf,MAAO,WACTP,SACL,+DAKLJ,EAAAA,EAAAA,MAACM,EAAAA,EAAG,CAACH,UAAU,mBAAkBC,SAAA,EAC7BC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACL,UAAU,mBAAkBC,SAAC,kBACzCC,EAAAA,EAAAA,KAACsB,EAAAA,EAAS,CACNC,OAAO,SACPC,UAAQ,EACRC,WAAS,EACThB,KAAM7C,EAAe,OAAS,WAC9B8D,GAAG,eACHC,KAAK,eACLC,aAAa,eACbC,WAAS,EACTC,MAAOzE,EACP0E,SAhHEC,IAC1B,MAAMC,EAAcD,EAAME,OAAOJ,MACjC7D,EAAYgE,GACZ,MAAME,EAjBgB9E,KACtB,MAAM+E,EAAc,CAChB1D,UAAWrB,EAASoC,QAAU,EAC9Bd,aAAc,QAAQ0D,KAAKhF,GAC3BuB,eAAgB,aAAayD,KAAKhF,GAClCwB,UAAW,KAAKwD,KAAKhF,GACrByB,UAAW,KAAKuD,KAAKhF,IAIzB,OAFAoB,EAAuB2D,GACvBpD,EAAkB3B,IAAaa,GACxBkE,EAAY1D,WAAa0D,EAAYzD,cAAgByD,EAAYxD,gBAAkBwD,EAAYvD,WAAauD,EAAYtD,QAAQ,EAOvHwD,CAAiBL,GAQ7B5D,EAPC8D,EAOgB,GALZ3D,EAAoBM,SAEf,oHADA,4BAMdE,EAAkBiD,IAAgB/D,EAAgB,EAoG1BqE,YAAY,oBACZzC,UAAU,qBACV0C,QAASpE,EACTqE,WAAY,CACRC,cACI1C,EAAAA,EAAAA,KAAC2C,EAAAA,EAAc,CAACC,SAAS,MAAK7C,UAC1BC,EAAAA,EAAAA,KAAC6C,EAAAA,EAAU,CACP,aAAW,6BACXC,QAASA,IAAMjF,GAAiBD,GAChCmF,KAAK,MAAKhD,SAETnC,GAAeoC,EAAAA,EAAAA,KAACgD,EAAAA,EAAa,KAAMhD,EAAAA,EAAAA,KAACiD,EAAAA,EAAU,WAMlE7E,GACO4B,EAAAA,EAAAA,KAACkD,EAAAA,EAAc,CAACrC,GAAI,CAAEP,MAAO,MAAOY,SAAU,QAASnB,SAClD3B,IAGX,IAEF4B,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACL,UAAU,mBAAkBC,SAAC,2BACzCC,EAAAA,EAAAA,KAACsB,EAAAA,EAAS,CACNC,OAAO,SACPC,UAAQ,EACRC,WAAS,EACThB,KAAM1C,EAAsB,OAAS,WACrC2D,GAAG,mBACHC,KAAK,mBACLC,aAAa,mBACbC,WAAS,EACTC,MAAO5D,EACP6D,SAnISC,IACjC,MAAMmB,EAAqBnB,EAAME,OAAOJ,MACxC3D,EAAmBgF,GAEf5E,EADAlB,IAAa8F,EACW,0BAEA,IAE5BnE,EAAkB3B,IAAa8F,GAAsB9F,EAASoC,QAAU,EAAE,EA4HlD8C,YAAY,gBACZzC,UAAU,qBACV0C,QAASlE,EAETmE,WAAY,CACRC,cACI1C,EAAAA,EAAAA,KAAC2C,EAAAA,EAAc,CAACC,SAAS,MAAK7C,UAC1BC,EAAAA,EAAAA,KAAC6C,EAAAA,EAAU,CACP,aAAW,qCACXC,QAASA,IAAM9E,GAAwBD,GACvCgF,KAAK,MAAKhD,SAEThC,GAAsBiC,EAAAA,EAAAA,KAACgD,EAAAA,EAAa,KAAMhD,EAAAA,EAAAA,KAACiD,EAAAA,EAAU,WAOzE3E,GACO0B,EAAAA,EAAAA,KAACkD,EAAAA,EAAc,CAACrC,GAAI,CAAEP,MAAO,MAAOY,SAAU,QAASnB,SAClDzB,IAEX,IAEAjB,GAAYa,KACVyB,EAAAA,EAAAA,MAACM,EAAAA,EAAG,CAACH,UAAU,qBAAoBC,SAAA,EAC/BC,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACL,UAAU,0BAAyBC,SAAC,gCAChDJ,EAAAA,EAAAA,MAACM,EAAAA,EAAG,CAACH,UAAU,+BAA8BC,SAAA,EACzCC,EAAAA,EAAAA,KAACoD,EAAAA,EAAe,CAACtD,UAAWtB,EAAoBE,UAAY,wBAA0B,MACtFsB,EAAAA,EAAAA,KAACG,EAAAA,EAAU,CAACL,UAAU,0BAAyBC,SAAC,iCAiB5DC,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CACHC,KAAK,SACLgB,WAAS,EACTrB,QAAQ,YACR0C,QA5KPO,UACjB,IAAIlB,GAAU,EASd,GARK9E,IACDgB,EAAiB,2BACjB8D,GAAU,GAETjE,IACDK,EAAwB,2BACxB4D,GAAU,GAEVA,GAAWpD,IAAmBX,IAAkBE,EAAsB,CACtE,MAAMgF,EAAoBlG,EAAqBC,GACzCkG,EAA6BnG,EAAqBc,GACxD,GAAIoF,GAAqBC,EACrB,IACI,MAAMC,ODlFGH,OAAOI,EAAuBC,EAAqBC,KAC5E,IACI,MAAMC,EAAG,yCAAAC,OAA4CJ,EAAa,iBAAAI,OAAgBC,mBAAmBJ,GAAY,wBAAAG,OAAuBC,mBAAmBH,IACrJH,QAAiBO,EAAAA,GAAgBC,IAAIJ,GAC3C,OAAwB,MAApBJ,EAASS,QAAkBT,EAASU,KAAKC,QAClCX,EAASU,KAETV,EAASU,KAAKE,YAE7B,CAAE,MAAO5B,GACL,MAAIA,EAAMgB,UACNa,QAAQ7B,MAAM,kBAAmBA,EAAMgB,SAASU,MAC1C,IAAII,MAAM,UAADT,OAAWrB,EAAMgB,SAASS,OAAM,OAAAJ,OAAMrB,EAAMgB,SAASU,QAC7D1B,EAAM+B,SACbF,QAAQ7B,MAAM,wBAAyBA,EAAM+B,SACvC,IAAID,MAAM,0CAEhBD,QAAQ7B,MAAM,uBAAwBA,EAAMgC,SACtC,IAAIF,MAAM,wCAExB,GC8DuCG,CAAcjF,EAAe8D,EAAmBC,GACnEC,GAAYA,EAASW,QACrBjF,GAAqB,GAErBC,EAAaqE,EAAU,QAE/B,CAAE,MAAOhB,GACL6B,QAAQ7B,MAAM,+BAAgCA,EAClD,MAEA6B,QAAQ7B,MAAM,uCAEtB,CACAkC,aAAaC,QACfC,SAASC,OAAOtF,MAAM,KAAKuF,SAAQD,IACjC,MAAOlD,GAAQkD,EAAOtF,MAAM,KAC5BqF,SAASC,OAAM,GAAAhB,OAAMlC,EAAI,oDAAmD,IAE9E+C,aAAaK,QAAQ,eAAgBC,KAAKC,MAAMC,YAC9CC,eAAeR,OAAO,EA2IEjF,UAAWX,GAAoC,KAAlBX,GAAiD,KAAzBE,EACrDwB,UAAWJ,EAAW,qBAAuB,oBAAoBK,SACpE,uBAKLC,EAAAA,EAAAA,KAACC,EAAAA,EAAG,CAACM,GAAI,GAAIT,UAAU,qBAAoBC,UACvCJ,EAAAA,EAAAA,MAACQ,EAAAA,EAAU,CAACC,QAAQ,QAAQN,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAACoF,EAAAA,EAAI,CAAC1E,KAAK,gBAAgBZ,UAAU,oBAAmBC,SAAC,iBAEjD,IAAI,IACV,KACFC,EAAAA,EAAAA,KAACoF,EAAAA,EAAI,CAAC1E,KAAK,kBAAkBZ,UAAU,oBAAmBC,SAAC,4BAwB3EC,EAAAA,EAAAA,KAACC,EAAAA,EAAG,CAACM,GAAI,GAAIT,UAAU,qBAAoBC,UACvCJ,EAAAA,EAAAA,MAACQ,EAAAA,EAAU,CAACC,QAAQ,QAAQN,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAACoF,EAAAA,EAAI,CAAC1E,KAAK,gBAAgBZ,UAAU,oBAAmBC,SAAC,iBAEjD,IAAI,IACV,KACFC,EAAAA,EAAAA,KAACoF,EAAAA,EAAI,CAAC1E,KAAK,kBAAkBZ,UAAU,oBAAmBC,SAAC,6BAM/D,C,wKCzSb,SAASsF,EAAoBC,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,CACA,MACA,GADoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,OAAQ,gBAAiB,iBAAkB,kBAAmB,SAAU,iB,wBCHxH,MAAMC,EAAuB,CAClCC,QAAS,eACTC,YAAa,eACbC,UAAW,iBACXC,cAAe,iBACfrD,MAAO,cAiBT,EAZ0BsD,IAGpB,IAHqB,MACzBC,EAAK,WACLC,GACDF,EACC,MAAMG,EAP0B3F,IACzBmF,EAAqBnF,IAAUA,EAMb4F,CAA0BF,EAAW1F,OACxDA,GAAQ6F,EAAAA,EAAAA,IAAQJ,EAAO,WAAFlC,OAAaoC,IAAoB,IAAUD,EAAW1F,MAC3E8F,GAAeD,EAAAA,EAAAA,IAAQJ,EAAO,WAAFlC,OAAaoC,EAAgB,YAC/D,MAAI,SAAUF,GAASK,EACd,QAAPvC,OAAeuC,EAAY,YAEtBC,EAAAA,EAAAA,IAAM/F,EAAO,GAAI,E,aClB1B,MAAMgG,EAAY,CAAC,YAAa,QAAS,YAAa,SAAU,UAAW,oBAAqB,YAAa,UAAW,MA2BlHC,GAAWC,EAAAA,EAAAA,IAAOrG,EAAAA,EAAY,CAClCwB,KAAM,UACN2D,KAAM,OACNmB,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJX,GACEU,EACJ,MAAO,CAACC,EAAOC,KAAMD,EAAO,YAAD9C,QAAagD,EAAAA,EAAAA,GAAWb,EAAWc,aAAwC,WAAzBd,EAAWe,WAA0BJ,EAAOK,OAAO,GAPnHR,EASdV,IAGG,IAHF,MACFC,EAAK,WACLC,GACDF,EACC,OAAOmB,EAAAA,EAAAA,GAAS,CAAC,EAA4B,SAAzBjB,EAAWc,WAAwB,CACrDI,eAAgB,QACU,UAAzBlB,EAAWc,WAAyB,CACrCI,eAAgB,OAChB,UAAW,CACTA,eAAgB,cAEQ,WAAzBlB,EAAWc,YAA0BG,EAAAA,EAAAA,GAAS,CAC/CC,eAAgB,aACM,YAArBlB,EAAW1F,OAAuB,CACnC6G,oBAAqBC,EAAkB,CACrCrB,QACAC,gBAED,CACD,UAAW,CACTmB,oBAAqB,aAEI,WAAzBnB,EAAWe,WAA0B,CACvCnE,SAAU,WACVyE,wBAAyB,cACzBC,gBAAiB,cAGjBC,QAAS,EACTC,OAAQ,EACRjG,OAAQ,EAERkG,aAAc,EACdC,QAAS,EAETC,OAAQ,UACRC,WAAY,OACZC,cAAe,SACfC,cAAe,OAEfC,iBAAkB,OAElB,sBAAuB,CACrBC,YAAa,QAEf,CAAC,KAADnE,OAAMoE,EAAYC,eAAiB,CACjCX,QAAS,SAEX,IA0HJ,EAxH0BY,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAM3B,GAAQ4B,EAAAA,EAAAA,GAAgB,CAC5B5B,MAAO0B,EACPzG,KAAM,aAEF,UACF7B,EAAS,MACTQ,EAAQ,UAAS,UACjByG,EAAY,IAAG,OACfwB,EAAM,QACNC,EAAO,kBACPC,EAAiB,UACjB3B,EAAY,SAAQ,QACpB1G,EAAU,UAAS,GACnBS,GACE6F,EACJgC,GAAQC,EAAAA,EAAAA,GAA8BjC,EAAOJ,IACzC,kBACJsC,EACAL,OAAQM,EACRL,QAASM,EACTT,IAAKU,IACHC,EAAAA,EAAAA,MACGd,EAAce,GAAmBd,EAAAA,UAAe,GACjDe,GAAaC,EAAAA,EAAAA,GAAWd,EAAKU,GAmB7B/C,GAAaiB,EAAAA,EAAAA,GAAS,CAAC,EAAGP,EAAO,CACrCpG,QACAyG,YACAmB,eACApB,YACA1G,YAEIgJ,EAzHkBpD,KACxB,MAAM,QACJoD,EAAO,UACPrC,EAAS,aACTmB,EAAY,UACZpB,GACEd,EACEqD,EAAQ,CACZzC,KAAM,CAAC,OAAQ,YAAF/C,QAAcgD,EAAAA,EAAAA,GAAWC,IAA4B,WAAdC,GAA0B,SAAUmB,GAAgB,iBAE1G,OAAOoB,EAAAA,EAAAA,GAAeD,EAAOhE,EAAqB+D,EAAQ,EA+G1CG,CAAkBvD,GAClC,OAAoBhG,EAAAA,EAAAA,KAAKuG,GAAUU,EAAAA,EAAAA,GAAS,CAC1C3G,MAAOA,EACPR,WAAW0J,EAAAA,EAAAA,GAAKJ,EAAQxC,KAAM9G,GAC9BsJ,QAASX,EACT1B,UAAWA,EACXwB,OA/BiBvG,IACjB6G,EAAkB7G,IACgB,IAA9B4G,EAAkBa,SACpBR,GAAgB,GAEdV,GACFA,EAAOvG,EACT,EAyBAwG,QAvBkBxG,IAClB8G,EAAmB9G,IACe,IAA9B4G,EAAkBa,SACpBR,GAAgB,GAEdT,GACFA,EAAQxG,EACV,EAiBAqG,IAAKa,EACLlD,WAAYA,EACZ5F,QAASA,EACTS,GAAI,IAAM6I,OAAOC,KAAKlE,GAAsBmE,SAAStJ,GAEhD,GAFyD,CAAC,CAC7DA,aACYuJ,MAAMC,QAAQjJ,GAAMA,EAAK,CAACA,KACvC6H,GACL,G,qBCxJIqB,EAAyBC,EAAQ,MAIrCC,EAAQ,OAAU,EAClB,IAAIC,EAAiBH,EAAuBC,EAAQ,KAChDG,EAAcH,EAAQ,KACXC,EAAQ,GAAU,EAAIC,EAAeE,UAAuB,EAAID,EAAYE,KAAK,OAAQ,CACtGC,EAAG,mHACD,c", "sources": ["services/ResetpasswordService.tsx", "components/login/ResetPassword.tsx", "../node_modules/@mui/material/Link/linkClasses.js", "../node_modules/@mui/material/Link/getTextDecoration.js", "../node_modules/@mui/material/Link/Link.js", "../node_modules/@mui/icons-material/CheckCircle.js"], "sourcesContent": ["import { ResetPassword } from \"../models/Resetpassword\";\r\nimport { adminApiService, userUrl } from \"./APIService\";\r\nimport { JSEncrypt } from 'jsencrypt';\r\nconst publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY; \r\nexport const encryptResetPassword = (password: string) => {\r\n\tconst encrypt = new JSEncrypt();\r\n\tencrypt.setPublicKey(publicKey?publicKey:\"\");\r\n\tconst encryptedPassword = encrypt.encrypt(password);\r\n\treturn encryptedPassword;\r\n};\r\nexport const Resetpassword = async (PasswordLogId: string, NewPassword: string, ReEnterNewPassword: string) => {\r\n    try {\r\n        const url = `/User/ResetUserPassword?PasswordLogId=${PasswordLogId}&NewPassword=${encodeURIComponent(NewPassword)}&ReEnterNewPassword=${encodeURIComponent(ReEnterNewPassword)}`;\r\n        const response = await adminApiService.get(url);\r\n        if (response.status === 200 && response.data.Success) {\r\n            return response.data; \r\n        } else {\r\n            return response.data.ErrorMessage ; \r\n        }\r\n    } catch (error: any) {\r\n        if (error.response) {\r\n            console.error(\"Error Response:\", error.response.data);\r\n            throw new Error(`Error: ${error.response.status} - ${error.response.data}`);\r\n        } else if (error.request) {\r\n            console.error(\"No response received:\", error.request);\r\n            throw new Error('No response received from the server');\r\n        } else {\r\n            console.error(\"Request setup error:\", error.message);\r\n            throw new Error('Error sending password reset request');\r\n        }\r\n    }\r\n};", "import React, { useState } from 'react';\r\nimport { Con<PERSON><PERSON>, TextField, Button, Typography, Box, Link, IconButton, InputAdornment } from '@mui/material';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\r\nimport { Resetpassword, encryptResetPassword } from '../../services/ResetpasswordService';\r\nimport { useLocation } from 'react-router-dom';\r\nimport { useSnackbar } from \"../../SnackbarContext\";\r\nimport FormHelperText from '@mui/material/FormHelperText';\r\n\r\n\r\nconst ResetPassword = () => {\r\n    const location = useLocation();\r\n    const [showPassword, setShowPassword] = useState(false);\r\n    const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n    const [password, setPassword] = useState('');\r\n    const [confirmPassword, setConfirmPassword] = useState('');\r\n    const [passwordError, setPasswordError] = useState('');\r\n    const [confirmPasswordError, setConfirmPasswordError] = useState('');\r\n    const [passwordValidations, setPasswordValidations] = useState({\r\n        minLength: false,\r\n        hasUppercase: false,\r\n        hasSpecialChar: false,\r\n        hasNumber: false,\r\n        noSpaces: true,\r\n    });\r\n    const [passwordsMatch, setPasswordsMatch] = useState(false);\r\n    const [isPasswordChanged, setIsPasswordChanged] = useState(false);\r\n\r\n    const { openSnackbar } = useSnackbar();\r\n    const pathname = location.pathname;\r\n    const segments = pathname.split('/');\r\n    const passwordLogId = segments[segments.length - 1];\r\n\r\n    // Validate password criteria\r\n    const validatePassword = (password: string) => {\r\n        const validations = {\r\n            minLength: password.length >= 8,\r\n            hasUppercase: /[A-Z]/.test(password),\r\n            hasSpecialChar: /[!@#$%^&*]/.test(password),\r\n            hasNumber: /\\d/.test(password),\r\n            noSpaces: !/\\s/.test(password),\r\n        };\r\n        setPasswordValidations(validations);\r\n        setPasswordsMatch(password === confirmPassword);\r\n        return validations.minLength && validations.hasUppercase && validations.hasSpecialChar && validations.hasNumber && validations.noSpaces;\r\n    };\r\n\r\n    // Password change handler with validation\r\n    const handlePasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newPassword = event.target.value;\r\n        setPassword(newPassword);\r\n        const isValid = validatePassword(newPassword);\r\n        if (!isValid) {\r\n            setPasswordError(\r\n                !passwordValidations.noSpaces\r\n                    ? \"Spaces are not accepted.\"\r\n                    : \"Password must be at least 8 characters, contain 1 uppercase letter, 1 special character, 1 number, and no spaces.\"\r\n            );\r\n        } else {\r\n            setPasswordError('');\r\n        }\r\n        setPasswordsMatch(newPassword === confirmPassword);\r\n    };\r\n\r\n    // Confirm password change handler with validation\r\n    const handleConfirmPasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newConfirmPassword = event.target.value;\r\n        setConfirmPassword(newConfirmPassword);\r\n        if (password !== newConfirmPassword) {\r\n            setConfirmPasswordError(\"Passwords do not match.\");\r\n        } else {\r\n            setConfirmPasswordError('');\r\n        }\r\n        setPasswordsMatch(password === newConfirmPassword && password.length >= 8);\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        let isValid = true;\r\n        if (!password) {\r\n            setPasswordError('This field is required.');\r\n            isValid = false;\r\n        }\r\n        if (!confirmPassword) {\r\n            setConfirmPasswordError('This field is required.');\r\n            isValid = false;\r\n        }\r\n        if (isValid && passwordsMatch && !passwordError && !confirmPasswordError) {\r\n            const encryptedPassword = encryptResetPassword(password);\r\n            const encryptedReenteredPassword = encryptResetPassword(confirmPassword);\r\n            if (encryptedPassword && encryptedReenteredPassword) {\r\n                try {\r\n                    const response = await Resetpassword(passwordLogId, encryptedPassword, encryptedReenteredPassword);\r\n                    if (response && response.Success) {\r\n                        setIsPasswordChanged(true);\r\n                    } else {\r\n                        openSnackbar(response, \"error\");\r\n                    }\r\n                } catch (error) {\r\n                    console.error('Error during password reset:', error);\r\n                }\r\n            } else {\r\n                console.error('Encryption failed. Please try again.');\r\n            }\r\n        }\r\n        localStorage.clear();\r\n      document.cookie.split(\";\").forEach(cookie => {\r\n        const [name] = cookie.split(\"=\");\r\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\r\n      });\r\n      localStorage.setItem('logout-event', Date.now().toString());\r\n        sessionStorage.clear();\r\n    };\r\n    const disabled = !passwordsMatch || passwordError !== '' || confirmPasswordError !== '';\r\n    return (\r\n        <Container maxWidth=\"xs\" className=\"qadpt-resetpassword\">\r\n            <Box mb={4} className=\"qadpt-brand-logo\">\r\n                <Typography variant=\"h3\" className=\"qadpt-brand-logo-text\" gutterBottom>\r\n                    QuickAdopt\r\n                </Typography>\r\n            </Box>\r\n            <Box>\r\n                {!isPasswordChanged ? (\r\n                    <>\r\n                        <Box className=\"qadpt-welcome-message\">\r\n                            <Typography variant=\"h4\" className=\"qadpt-welcome-message-text\">\r\n                                Reset your password\r\n                            </Typography>\r\n                            <Typography\r\n                                variant=\"body2\"\r\n                                color=\"textSecondary\"\r\n                                align=\"center\"\r\n                                mb={2}\r\n                                sx={{\r\n                                    width: \"325px\",\r\n                                    height: \"38px\",\r\n                                    opacity: \"0.8\",\r\n                                    fontFamily: \"Poppins\",\r\n                                    fontSize: \"14px\",\r\n                                    fontWeight: 400,\r\n                                    lineHeight: \"19px\",\r\n                                    textAlign: \"center\",\r\n                                    color: \"#222222\",\r\n                                }}\r\n                            >\r\n                                Enter your new password below to change your password.\r\n                            </Typography>\r\n                        </Box>\r\n\r\n                        <Box className=\"qadpt-login-form\">\r\n                            <Typography className=\"qadpt-form-label\">New Password</Typography>\r\n                            <TextField\r\n                                margin=\"normal\"\r\n                                required\r\n                                fullWidth\r\n                                type={showPassword ? \"text\" : \"password\"}\r\n                                id=\"new-password\"\r\n                                name=\"new-password\"\r\n                                autoComplete=\"new-password\"\r\n                                autoFocus\r\n                                value={password}\r\n                                onChange={handlePasswordChange}\r\n                                placeholder=\"min. 8 characters\"\r\n                                className=\"qadpt-custom-input\"\r\n                                error={!!passwordError}\r\n                                InputProps={{\r\n                                    endAdornment: (\r\n                                        <InputAdornment position=\"end\">\r\n                                            <IconButton\r\n                                                aria-label=\"toggle password visibility\"\r\n                                                onClick={() => setShowPassword(!showPassword)}\r\n                                                edge=\"end\"\r\n                                            >\r\n                                                {showPassword ? <VisibilityOff /> : <Visibility />}\r\n                                            </IconButton>\r\n                                        </InputAdornment>\r\n                                    ),\r\n                                }}\r\n                            />\r\n                            {passwordError ? (\r\n                                    <FormHelperText sx={{ color: 'red', fontSize: '12px' }}>\r\n                                        {passwordError}\r\n                                       \r\n                                    </FormHelperText>\r\n                            ):\"\"}\r\n\r\n                            <Typography className=\"qadpt-form-label\">Re-enter New Password</Typography>\r\n                            <TextField\r\n                                margin=\"normal\"\r\n                                required\r\n                                fullWidth\r\n                                type={showConfirmPassword ? \"text\" : \"password\"}\r\n                                id=\"confirm-password\"\r\n                                name=\"confirm-password\"\r\n                                autoComplete=\"confirm-password\"\r\n                                autoFocus\r\n                                value={confirmPassword}\r\n                                onChange={handleConfirmPasswordChange}\r\n                                placeholder=\"Same as above\"\r\n                                className=\"qadpt-custom-input\"\r\n                                error={!!confirmPasswordError}\r\n                                //helperText={confirmPasswordError}\r\n                                InputProps={{\r\n                                    endAdornment: (\r\n                                        <InputAdornment position=\"end\">\r\n                                            <IconButton\r\n                                                aria-label=\"toggle confirm password visibility\"\r\n                                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                                                edge=\"end\"\r\n                                            >\r\n                                                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}\r\n                                            </IconButton>\r\n                                        </InputAdornment>\r\n                                    ),\r\n                                }}\r\n                                  \r\n                            />\r\n                            {confirmPasswordError ? (\r\n                                    <FormHelperText sx={{ color: 'red', fontSize: '12px' }}>\r\n                                        {confirmPasswordError}\r\n                                    </FormHelperText>\r\n                            ):\"\"}\r\n\r\n                            {(password || confirmPassword) && (\r\n                                <Box className=\"qadpt-passwordhint\">\r\n                                    <Typography className=\"qadpt-passwordhint-text\">Your password must contain</Typography>\r\n                                    <Box className=\"qadpt-passwordhint-container\">\r\n                                        <CheckCircleIcon className={passwordValidations.minLength ? \"qadpt-checkicon-valid\" : \"\"} />\r\n                                        <Typography className=\"qadpt-passwordhint-item\">At least 8 characters</Typography>\r\n                                    </Box>\r\n                                    {/* <Box className=\"qadpt-passwordhint-container\">\r\n                                        <CheckCircleIcon className={passwordValidations.hasUppercase ? \"qadpt-checkicon-valid\" : \"\"} />\r\n                                        <Typography className=\"qadpt-passwordhint-item\">1 capital letter</Typography>\r\n                                    </Box>\r\n                                    <Box className=\"qadpt-passwordhint-container\">\r\n                                        <CheckCircleIcon className={passwordValidations.hasSpecialChar ? \"qadpt-checkicon-valid\" : \"\"} />\r\n                                        <Typography className=\"qadpt-passwordhint-item\">1 special character</Typography>\r\n                                    </Box>\r\n                                    <Box className=\"qadpt-passwordhint-container\">\r\n                                        <CheckCircleIcon className={passwordValidations.hasNumber ? \"qadpt-checkicon-valid\" : \"\"} />\r\n                                        <Typography className=\"qadpt-passwordhint-item\">1 number</Typography>\r\n                                    </Box> */}\r\n                                </Box>\r\n                            )}\r\n\r\n                            <Button\r\n                                type=\"button\"\r\n                                fullWidth\r\n                                variant=\"contained\"\r\n                                onClick={handleSubmit}\r\n                                disabled={!passwordsMatch || passwordError !== '' || confirmPasswordError !== ''}\r\n                                className={disabled ? \"qadpt-btn-disabled\" : \"qadpt-btn-default\"}\r\n                            >\r\n                                Reset password\r\n                            </Button>\r\n                        </Box>\r\n\r\n                        <Box mt={12} className=\"qadpt-login-footer\">\r\n                            <Typography variant=\"body2\" className=\"qadpt-footer-text\">\r\n                                <Link href=\"/terms-of-use\" className=\"qadpt-footer-link\">\r\n                                    Terms of use\r\n                                </Link>{\" \"}\r\n                                |{\" \"}\r\n                                <Link href=\"/privacy-policy\" className=\"qadpt-footer-link\">\r\n                                    Privacy Policy\r\n                                </Link>\r\n                            </Typography>\r\n                        </Box>\r\n                    </>\r\n                ) : (\r\n                    <Box className=\"qadpt-pwd-changed\">\r\n                        <Typography variant=\"h5\" className=\"qadpt-pwd-title\">\r\n                            Password Changed!\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\" className=\"qadpt-changed-msg\" mt=\"10px\">\r\n                            Your password has been changed successfully.\r\n                        </Typography>\r\n                        <Button\r\n                            type=\"button\"\r\n                            variant=\"contained\"\r\n                            className=\"qadpt-btn-default\"\r\n                            href=\"https://app.quickadopt.in/Login\"\r\n                        >\r\n                            Back to QuickAdopt Platform\r\n                        </Button>\r\n                    </Box>\r\n                )}\r\n                <Box mt={12} className=\"qadpt-login-footer\">\r\n                    <Typography variant=\"body2\" className=\"qadpt-footer-text\">\r\n                        <Link href=\"/terms-of-use\" className=\"qadpt-footer-link\">\r\n                            Terms of use\r\n                        </Link>{\" \"}\r\n                        |{\" \"}\r\n                        <Link href=\"/privacy-policy\" className=\"qadpt-footer-link\">\r\n                            Privacy Policy\r\n                        </Link>\r\n                    </Typography>\r\n                </Box>\r\n            </Box>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default ResetPassword;\r\n", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "import { getPath } from '@mui/system';\nimport { alpha } from '@mui/system/colorManipulator';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"onBlur\", \"onFocus\", \"TypographyClasses\", \"underline\", \"variant\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useForkRef from '../utils/useForkRef';\nimport Typography from '../Typography';\nimport linkClasses, { getLinkUtilityClass } from './linkClasses';\nimport getTextDecoration, { colorTransformations } from './getTextDecoration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    component,\n    focusVisible,\n    underline\n  } = ownerState;\n  const slots = {\n    root: ['root', `underline${capitalize(underline)}`, component === 'button' && 'button', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getLinkUtilityClass, classes);\n};\nconst LinkRoot = styled(Typography, {\n  name: 'MuiLink',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`underline${capitalize(ownerState.underline)}`], ownerState.component === 'button' && styles.button];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({}, ownerState.underline === 'none' && {\n    textDecoration: 'none'\n  }, ownerState.underline === 'hover' && {\n    textDecoration: 'none',\n    '&:hover': {\n      textDecoration: 'underline'\n    }\n  }, ownerState.underline === 'always' && _extends({\n    textDecoration: 'underline'\n  }, ownerState.color !== 'inherit' && {\n    textDecorationColor: getTextDecoration({\n      theme,\n      ownerState\n    })\n  }, {\n    '&:hover': {\n      textDecorationColor: 'inherit'\n    }\n  }), ownerState.component === 'button' && {\n    position: 'relative',\n    WebkitTapHighlightColor: 'transparent',\n    backgroundColor: 'transparent',\n    // Reset default value\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    border: 0,\n    margin: 0,\n    // Remove the margin in Safari\n    borderRadius: 0,\n    padding: 0,\n    // Remove the padding in Firefox\n    cursor: 'pointer',\n    userSelect: 'none',\n    verticalAlign: 'middle',\n    MozAppearance: 'none',\n    // Reset\n    WebkitAppearance: 'none',\n    // Reset\n    '&::-moz-focus-inner': {\n      borderStyle: 'none' // Remove Firefox dotted outline.\n    },\n    [`&.${linkClasses.focusVisible}`]: {\n      outline: 'auto'\n    }\n  });\n});\nconst Link = /*#__PURE__*/React.forwardRef(function Link(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiLink'\n  });\n  const {\n      className,\n      color = 'primary',\n      component = 'a',\n      onBlur,\n      onFocus,\n      TypographyClasses,\n      underline = 'always',\n      variant = 'inherit',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const handlerRef = useForkRef(ref, focusVisibleRef);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    focusVisible,\n    underline,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(LinkRoot, _extends({\n    color: color,\n    className: clsx(classes.root, className),\n    classes: TypographyClasses,\n    component: component,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    ref: handlerRef,\n    ownerState: ownerState,\n    variant: variant,\n    sx: [...(!Object.keys(colorTransformations).includes(color) ? [{\n      color\n    }] : []), ...(Array.isArray(sx) ? sx : [sx])]\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Link.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the link.\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Typography`](/material-ui/api/typography/) element.\n   */\n  TypographyClasses: PropTypes.object,\n  /**\n   * Controls when the link should have an underline.\n   * @default 'always'\n   */\n  underline: PropTypes.oneOf(['always', 'hover', 'none']),\n  /**\n   * Applies the theme typography styles.\n   * @default 'inherit'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string])\n} : void 0;\nexport default Link;", "\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _createSvgIcon = _interopRequireDefault(require(\"./utils/createSvgIcon\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nvar _default = exports.default = (0, _createSvgIcon.default)( /*#__PURE__*/(0, _jsxRuntime.jsx)(\"path\", {\n  d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z\"\n}), 'CheckCircle');"], "names": ["public<PERSON>ey", "process", "encryptResetPassword", "password", "encrypt", "JSEncrypt", "setPublicKey", "ResetPassword", "location", "useLocation", "showPassword", "setShowPassword", "useState", "showConfirmPassword", "setShowConfirmPassword", "setPassword", "confirmPassword", "setConfirmPassword", "passwordError", "setPasswordError", "confirmPasswordError", "setConfirmPasswordError", "passwordValidations", "setPasswordValidations", "<PERSON><PERSON><PERSON><PERSON>", "hasUppercase", "hasSpecialChar", "hasNumber", "noSpaces", "passwordsMatch", "setPasswordsMatch", "isPasswordChanged", "setIsPasswordChanged", "openSnackbar", "useSnackbar", "segments", "pathname", "split", "passwordLogId", "length", "disabled", "_jsxs", "Container", "max<PERSON><PERSON><PERSON>", "className", "children", "_jsx", "Box", "mb", "Typography", "variant", "gutterBottom", "color", "mt", "<PERSON><PERSON>", "type", "href", "_Fragment", "align", "sx", "width", "height", "opacity", "fontFamily", "fontSize", "fontWeight", "lineHeight", "textAlign", "TextField", "margin", "required", "fullWidth", "id", "name", "autoComplete", "autoFocus", "value", "onChange", "event", "newPassword", "target", "<PERSON><PERSON><PERSON><PERSON>", "validations", "test", "validatePassword", "placeholder", "error", "InputProps", "endAdornment", "InputAdornment", "position", "IconButton", "onClick", "edge", "VisibilityOff", "Visibility", "FormHelperText", "newConfirmPassword", "CheckCircleIcon", "async", "encryptedPassword", "encryptedReenteredPassword", "response", "PasswordLogId", "NewPassword", "ReEnterNewPassword", "url", "concat", "encodeURIComponent", "adminApiService", "get", "status", "data", "Success", "ErrorMessage", "console", "Error", "request", "message", "Resetpassword", "localStorage", "clear", "document", "cookie", "for<PERSON>ach", "setItem", "Date", "now", "toString", "sessionStorage", "Link", "getLinkUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "_ref", "theme", "ownerState", "transformedColor", "transformDeprecatedColors", "<PERSON><PERSON><PERSON>", "channelColor", "alpha", "_excluded", "LinkRoot", "styled", "overridesResolver", "props", "styles", "root", "capitalize", "underline", "component", "button", "_extends", "textDecoration", "textDecorationColor", "getTextDecoration", "WebkitTapHighlightColor", "backgroundColor", "outline", "border", "borderRadius", "padding", "cursor", "userSelect", "verticalAlign", "MozAppearance", "WebkitAppearance", "borderStyle", "linkClasses", "focusVisible", "React", "inProps", "ref", "useDefaultProps", "onBlur", "onFocus", "TypographyClasses", "other", "_objectWithoutPropertiesLoose", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "focusVisibleRef", "useIsFocusVisible", "setFocusVisible", "handler<PERSON>ef", "useForkRef", "classes", "slots", "composeClasses", "useUtilityClasses", "clsx", "current", "Object", "keys", "includes", "Array", "isArray", "_interopRequireDefault", "require", "exports", "_createSvgIcon", "_jsxRuntime", "default", "jsx", "d"], "sourceRoot": ""}