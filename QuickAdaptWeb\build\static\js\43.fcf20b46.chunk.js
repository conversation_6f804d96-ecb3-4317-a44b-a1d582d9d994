"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[43],{4043:(e,t,a)=>{a.r(t),a.d(t,{default:()=>B});var s=a(9379),i=a(5043),l=a(9252),n=a(8903),d=a(7392),c=a(5865),r=a(6446),o=a(7784),u=a(7739),h=a(2518),p=a(2110),g=a(6494),x=a(1787),v=a(3193),m=a(2221),j=a(2143),A=a(4598),y=a(4605),b=a(7972),f=a(9120),S=a(3216),q=a(5540);const I=a.p+"static/media/OpenNewWindow.059466fa7af6ab96a666d8d50091ed20.svg";const D=a.p+"static/media/PublishIcon.6a1093cdcc5e542585986fcb8cacb3d0.svg";const G=a.p+"static/media/check_small.cb756a537b1bd4966a6b125d7f380848.svg";const N=a.p+"static/media/SaveIcon.f6c223459d0635e3565f4cef408c20fa.svg";const C=a.p+"static/media/Warning.e0885f593d31d785ec668e8106ec19b4.svg";const T=a.p+"static/media/ShareIcon.31ff833787dcfd0d114788c6c5dfadfc.svg";const P=a.p+"static/media/Targetdelete.5730a2469df52a795fa013c4e36773b7.svg";var w=a(8004),O=a(4256),k=a(4535),E=a(7236),M=a(6180),U=a(1227),R=a(4379),W=a(2944),z=a(579);const B=()=>{var e,t,a,B,V,F,L,Y,H,Q,Z,J,K,X,_,$;const ee=(new Date).toISOString().slice(0,16),te=new Date,[ae,se]=(0,i.useState)(!1),[ie,le]=(0,i.useState)(null),[ne,de]=(0,i.useState)(!1),[ce,re]=(0,i.useState)(!1),oe=(0,S.zy)(),ue=(0,S.Zp)(),{openSnackbar:he}=(0,M.d)(),[pe,ge]=(0,i.useState)((null===ie||void 0===ie||null===(e=ie.GuideDetails)||void 0===e?void 0:e.Name)||" "),{guideId:xe}=(0,S.g)(),[ve,me]=(0,i.useState)(xe||""),[je,Ae]=(0,i.useState)((null===ie||void 0===ie?void 0:ie.GuideDetails.OrganizationId)||""),[ye,be]=(0,i.useState)((null===ie||void 0===ie||null===(t=ie.GuideDetails)||void 0===t?void 0:t.AccountId)||""),[fe,Se]=(0,i.useState)((null===ie||void 0===ie||null===(a=ie.GuideDetails)||void 0===a?void 0:a.GuideStatus)||""),[qe,Ie]=(0,i.useState)(!1),[De,Ge]=(0,i.useState)((null===ie||void 0===ie||null===(B=ie.GuideDetails)||void 0===B?void 0:B.Frequency)||"onceInSession"),[Ne,Ce]=(0,i.useState)((null===ie||void 0===ie||null===(V=ie.GuideDetails)||void 0===V?void 0:V.GuideType)||""),[Te,Pe]=(0,i.useState)(null===ie||void 0===ie||null===(F=ie.GuideDetails)||void 0===F?void 0:F.UpdatedBy),[we,Oe]=(0,i.useState)(null===ie||void 0===ie||null===(L=ie.GuideDetails)||void 0===L?void 0:L.CreatedBy),[ke,Ee]=(0,i.useState)(null===ie||void 0===ie||null===(Y=ie.GuideDetails)||void 0===Y?void 0:Y.AutoTrigger),[Me,Ue]=(0,i.useState)((null===ie||void 0===ie||null===(H=ie.GuideDetails)||void 0===H?void 0:H.PublishType)||"immediately"),[Re,We]=(0,i.useState)((null===ie||void 0===ie||null===(Q=ie.GuideDetails)||void 0===Q?void 0:Q.UnPublishType)||"Manually"),[ze,Be]=(0,i.useState)(null===ie||void 0===ie||null===(Z=ie.GuideDetails)||void 0===Z?void 0:Z.Description),[Ve,Fe]=(0,i.useState)((null===ie||void 0===ie||null===(J=ie.GuideDetails)||void 0===J?void 0:J.TargetUrl)||""),[Le,Ye]=(0,i.useState)(null===ie||void 0===ie||null===(K=ie.GuideDetails)||void 0===K?void 0:K.CreatedDate),[He,Qe]=(0,i.useState)((null===ie||void 0===ie||null===(X=ie.GuideDetails)||void 0===X?void 0:X.PublishDate)||te),[Ze,Je]=(0,i.useState)((null===ie||void 0===ie||null===(_=ie.GuideDetails)||void 0===_?void 0:_.UnPublishDate)||te),[Ke,Xe]=(0,i.useState)(""),[_e,$e]=(0,i.useState)(!1),[et,tt]=(0,i.useState)(!1),[at,st]=(0,i.useState)(!1),[it,lt]=(0,i.useState)(null),[nt,dt]=(0,i.useState)([{pageRule:"Equals",url:(null===ie||void 0===ie||null===($=ie.GuideDetails)||void 0===$?void 0:$.TargetUrl)||"",logicalOperator:"",PageTargetId:""}]),[ct,rt]=(0,i.useState)([]),[ot,ut]=(0,i.useState)(!1),[ht,pt]=(0,i.useState)({GuideId:"",GuideType:"",Name:"",OrganizationId:"",CreatedBy:"",UpdatedBy:"",Frequency:"",AccountId:"",GuideStatus:"",AutoTrigger:!1,Publish:!1,UnPublish:!1,Description:"",TargetUrl:""}),[gt,xt]=(0,i.useState)(!1),[vt,mt]=(0,i.useState)("2024-09-26T07:00"),[jt,At]=(0,i.useState)("2024-09-26T07:00"),[yt,bt]=(0,i.useState)(Array.isArray(null===ie||void 0===ie?void 0:ie.GuideDetails.GuideStep)?null===ie||void 0===ie?void 0:ie.GuideDetails.GuideStep:[]);(0,k.Ay)("div")((e=>{let{theme:t}=e;return{display:"flex",alignItems:"center",textAlign:"center","&:before, &:after":{content:'""',margin:"0 8px"}}}));(0,i.useEffect)((()=>{(async()=>{const e=await(0,E.kh)(ve);e&&(le(e),Se(e.GuideDetails.GuideStatus),$e("Active"===e.GuideDetails.GuideStatus),tt("InActive"===e.GuideDetails.GuideStatus),Ae(e.GuideDetails.OrganizationId),Ce(e.GuideDetails.GuideType),ge(e.GuideDetails.Name),be(e.GuideDetails.AccountId),Pe(e.GuideDetails.UpdatedBy),Oe(e.GuideDetails.CreatedBy),bt(e.GuideDetails.GuideStep),Be(e.GuideDetails.Description),Ge(e.GuideDetails.Frequency),Ee(e.GuideDetails.AutoTrigger),Ue(e.GuideDetails.publishOption),We(e.GuideDetails.unpublishOption),Fe(e.GuideDetails.TargetUrl),Ye(e.GuideDetails.CreatedDate),Qe(e.GuideDetails.PublishDate),Je(e.GuideDetails.UnpublishDate),ue(oe.pathname,{state:{response:e},replace:!0}))})()}),[ve,fe]);const ft=async()=>{try{if(await kt(),_e){const e=await(0,E.JV)(ve);if(e.Success){he("".concat(pe," ").concat(Ne," Unpublished Successfully"),"success"),Se("InActive"),$e(!1),tt(!0);const e=await(0,E.kh)(ve);e&&(le(e),Se(e.GuideDetails.GuideStatus),$e("Active"===e.GuideDetails.GuideStatus),tt("InActive"===e.GuideDetails.GuideStatus))}else he(e.SuccessMessage,"error")}else{const e=await(0,E.IL)(ve);if(e.Success){he("".concat(pe," ").concat(Ne," Published Successfully"),"success"),Se("Active"),$e(!0),tt(!1);const e=await(0,E.kh)(ve);e&&(le(e),Se(e.GuideDetails.GuideStatus),$e("Active"===e.GuideDetails.GuideStatus),tt("InActive"===e.GuideDetails.GuideStatus))}else he(e.SuccessMessage,"error")}}catch(e){console.error("Error updating guide status:",e)}},St=()=>{const e="".concat(Ne.toLowerCase(),"s");ue("/".concat(e))},qt=async()=>{Ie(!1);try{const e=await(0,E.kA)(ve,je,pe,ye,Ne);!0===e.Success?he(e.SuccessMessage,"success"):he(e.ErrorMessage,"error")}catch(e){console.error("Error updating guide name:",e)}};(0,i.useEffect)((()=>{if(null!==ie&&void 0!==ie&&ie.PageTargets){const e=null===ie||void 0===ie?void 0:ie.PageTargets.map((e=>({pageRule:e.Condition,url:e.Value,logicalOperator:e.Operator,PageTargetId:e.PageTargetId})));dt(e)}}),[null===ie||void 0===ie?void 0:ie.PageTargets]),(0,i.useEffect)((()=>{0===nt.length&&dt([{pageRule:"Equals",url:(null===ie||void 0===ie?void 0:ie.GuideDetails.TargetUrl)||"",PageTargetId:"",logicalOperator:""}])}),[nt]);const It=(e,t,a)=>{const s=[],i=[...nt];i[e][t]=a;let l="";a.length<1&&"url"===t?l="Minimun Length : 1 Character":a.length>500?l="Maximum Length : 500 characters.":/\s/.test(a)&&"url"===t?l="Restriction : Spaces are not allowed ":dt(i),l.length>0&&(s[e]=l),rt(s),se(!0)};(0,i.useEffect)((()=>{var e;const t=null===ie||void 0===ie||null===(e=ie.GuideDetails)||void 0===e?void 0:e.Frequency;if("onceInSession"!==t){const{frequencyBase:e,dropdownValue:a}=(e=>{if(null!==e&&void 0!==e&&e.startsWith("onceIn")&&"onceInSession"!==e){const t=e.split("-");return{frequencyBase:t[0],dropdownValue:t.length>1?t[1]:""}}return{frequencyBase:e,dropdownValue:""}})(t);Ge(e),Xe(a)}}),[oe.state]);const[Dt,Gt]=(0,i.useState)(""),[Nt,Ct]=(0,i.useState)({});(0,i.useEffect)((()=>{ke&&Ge("onceInSession")}),[ke]);const Tt=e=>{Ge("onceIn"===e?"onceIn".concat(Ke):e),"onceIn"!==e&&Xe("")},[Pt,wt]=(0,i.useState)((null===ie||void 0===ie?void 0:ie.PageTargets)||[]);(0,i.useEffect)((()=>{null!==ie&&void 0!==ie&&ie.PageTargets&&(null===ie||void 0===ie?void 0:ie.PageTargets.length)>0&&wt(null===ie||void 0===ie?void 0:ie.PageTargets)}),[null===ie||void 0===ie?void 0:ie.PageTargets]);const Ot=async()=>{se(!1);const e=nt.filter((e=>!e.PageTargetId)),t=nt.filter((e=>e.PageTargetId));if(e.length>0){const t=e.map((e=>({PageTargetId:"",Condition:e.pageRule,Operator:(null===e||void 0===e?void 0:e.logicalOperator)||"OR",Value:e.url,GuideId:ve,OrganizationId:""})));try{const e=await(0,E.yh)(t);e.Success?(he(e.SuccessMessage,"success"),de(!0),dt((e=>e.filter((e=>!!e.PageTargetId)))),re(!1)):he(e.ErrorMessage,"error")}catch(s){console.error("Error saving new page targets:",s)}}const a=t.filter((e=>{const t=Pt.find((t=>t.PageTargetId===e.PageTargetId));return t&&(t.Condition!==e.pageRule||t.Operator!==e.logicalOperator||t.Value!==e.url)}));if(a.length>0){const e=a.map((e=>({PageTargetId:e.PageTargetId,Condition:e.pageRule,Operator:(null===e||void 0===e?void 0:e.logicalOperator)||"OR",Value:e.url,GuideId:ve,OrganizationId:""})));try{const t=await(0,E.k6)(e);t.Success?(he(t.SuccessMessage,"success"),de(!0),re(!1)):he(t.ErrorMessage,"error")}catch(s){console.error("Error updating existing page targets:",s)}}try{const e=await(0,E.s2)(ve);if(e){const t=[...e.map((e=>({pageRule:e.Condition,url:e.Value,logicalOperator:e.Operator,PageTargetId:e.PageTargetId})))];wt(e),dt(t)}else he(e.ErrorMessage,"error")}catch(s){console.error("Error fetching page targets:",s)}},kt=async()=>{var e;let t=fe;t=et||_e&&(ae||ce||qe||ze!==(null===ie||void 0===ie||null===(e=ie.GuideDetails)||void 0===e?void 0:e.Description))?"Draft":_e?"Active":et?"InActive":"Draft",Se(t);const a=(0,s.A)((0,s.A)({GuideId:ve,GuideType:Ne,Name:pe.trim(),Content:"".concat(Ne," content"),OrganizationId:je,CreatedDate:Le,UpdatedDate:(new Date).toISOString(),CreatedBy:we,UpdatedBy:Te,Frequency:"onceInSession",Segment:"All users",AccountId:ye,GuideStep:yt,GuideStatus:t,AutoTrigger:!0,PublishType:Me,UnPublishType:Re,Description:ze,TargetUrl:Ve},("Draft"===t||"InActive"===t)&&{PublishDate:"custom"===Me?He:te,UnPublishDate:"custom"===Re?Ze:null}),_e&&{UnpublishDate:"custom"===Re?Ze:null,PublishDate:"custom"===Me?He:te});!ne&&ce&&await Ot();const i=await(0,E.jZ)(a);!0===i.Success?(he("".concat(pe," ").concat(Ne," updated Successfully"),"success"),de(!1),re(!1),tt("InActive"===t)):he(i.ErrorMessage,"error"),Ie(!1)};return(0,i.useEffect)((()=>{if("Active"===(null===ie||void 0===ie?void 0:ie.GuideDetails.GuideStatus)&&"custom"===(null===ie||void 0===ie?void 0:ie.GuideDetails.UnPublishType)&&null!==ie&&void 0!==ie&&ie.GuideDetails.UnPublishDate){const e=setInterval((async()=>{const t=new Date,a=ie.GuideDetails.UnPublishDate?new Date(ie.GuideDetails.UnPublishDate):null;if(a&&t>=a){await ft();const t=await(0,E.kh)(ve);t&&(le(t),Se(t.GuideDetails.GuideStatus),$e("Active"===t.GuideDetails.GuideStatus),tt("InActive"===t.GuideDetails.GuideStatus)),clearInterval(e)}}),10);return()=>clearInterval(e)}}),[ie]),(0,z.jsx)(l.A,{maxWidth:"xl",children:(0,z.jsx)("div",{className:"qadpt-web",children:(0,z.jsxs)("div",{className:"qadpt-webcontent",children:[(0,z.jsxs)("div",{className:"qadpt-setting-title",children:[(0,z.jsx)("div",{className:"qadpt-backbtn",children:(0,z.jsxs)(n.Ay,{container:!0,alignItems:"center",className:"qadpt-titsec-grid",children:[(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(d.A,{onClick:St,children:(0,z.jsx)(f.A,{})})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsxs)(c.A,{variant:"body1",className:"qadpt-back-text",onClick:St,children:["Back to ","".concat(Ne,"s")]})})]})}),(0,z.jsx)("div",{children:(0,z.jsxs)(n.Ay,{container:!0,className:"qadpt-titsec",children:[(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsxs)(r.A,{className:"qadpt-name-box",children:[qe?(0,z.jsx)(o.A,{name:"GuideName",variant:"outlined",value:pe,onChange:e=>{const{name:t,value:a}=e.target;let i="";"GuideName"===t&&a.trim().length<3&&(i="Guide Name must be at least 3 characters."),ge(e.target.value),Ct((e=>(0,s.A)((0,s.A)({},e),{},{[t]:i})))},onKeyDown:e=>{"Enter"===e.key?qt():"Escape"===e.key&&(ge(Dt),Ie(!1))},helperText:Nt.GuideName,error:!!Nt.GuideName,sx:{marginRight:"16px",width:"300px"}}):(0,z.jsx)(c.A,{variant:"h5",fontWeight:"bold",className:"qadpt-name-text",children:pe}),(0,z.jsx)(d.A,{onClick:qe?qt:()=>{Gt(pe),Ie(!0)},disabled:qe&&!!Nt.GuideName,children:qe?(0,z.jsx)(u.A,{arrow:!0,title:"Save",children:(0,z.jsx)("img",{src:N,alt:"saveicon"})}):(0,z.jsx)(u.A,{arrow:!0,title:"Edit",children:(0,z.jsx)(q.A,{})})})]})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsxs)(n.Ay,{container:!0,spacing:1,children:[(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsxs)(h.A,{variant:"outlined",color:"primary",className:"qadpt-action-btn",startIcon:(0,z.jsx)("img",{src:I,alt:"openinnewwindow"}),onClick:()=>(0,W.X)(Ve,ve),children:["Edit ",Ne]})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(h.A,{variant:"outlined",onClick:kt,className:"qadpt-action-btn",startIcon:(0,z.jsx)("img",{src:N,alt:"saveicon"}),children:"Save"})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(h.A,{variant:"contained",onClick:ft,className:"qadpt-action-btn qadpt-action-btn-primary ".concat(_e?"qadpt-unpublish":""),startIcon:(0,z.jsx)("img",{src:_e?R.Pm:D,alt:_e?"cloudoff":"PublishIcon"}),children:_e?"UnPublish":"Publish"})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(h.A,{variant:"outlined",disabled:!0,className:"qadpt-action-btn qadpt-share",startIcon:(0,z.jsx)("img",{src:T,alt:"Shareicon"})})})]})})]})})]}),(0,z.jsxs)("div",{className:"qadpt-content",children:[(0,z.jsxs)("div",{className:"qadpt-set-left",children:[(0,z.jsx)("div",{className:"qadpt-description",children:(0,z.jsx)(n.Ay,{item:!0,xs:12,md:6,children:(0,z.jsx)(p.A,{className:"qadpt-card",children:(0,z.jsxs)(g.A,{children:[(0,z.jsx)("label",{htmlFor:"description",className:"qadpt-label",children:"Description"}),(0,z.jsx)("textarea",{id:"description",value:ze,maxLength:500,placeholder:"Enter your description here (max 500 characters)",onChange:e=>Be(e.target.value)})]})})})}),(0,z.jsx)("div",{className:"qadpt-buildsteps",children:(0,z.jsx)(n.Ay,{item:!0,xs:12,md:6,children:(0,z.jsx)(p.A,{className:"qadpt-card qadpt-buildcard",children:(0,z.jsxs)(g.A,{children:[(0,z.jsx)("div",{className:"qadpt-label",children:"Build Steps"}),yt.length>0&&(0,z.jsxs)(c.A,{variant:"body1",sx:{marginTop:"10",fontSize:"1rem",color:"#555"},children:[yt.length," step",yt.length>1?"s":""]}),(0,z.jsx)("div",{className:"qadpt-build-content",children:yt.length>0?yt.map(((e,t)=>(0,z.jsx)(r.A,{className:"qadpt-steps",children:(0,z.jsx)(o.A,{variant:"outlined",multiline:!0,minRows:1,maxRows:1,fullWidth:!0,tabIndex:-1,inputProps:{tabIndex:-1},sx:{borderRadius:"8px","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:"transparent"},"&:hover fieldset":{borderColor:"transparent"},"&.Mui-focused fieldset":{borderColor:"transparent"}},"& textarea":{overflow:"hidden",resize:"none",paddingTop:"4px",lineHeight:"1.2"}},InputProps:{endAdornment:(0,z.jsx)(x.A,{position:"end",children:(0,z.jsxs)(d.A,{tabIndex:-1,children:[" ",(0,z.jsx)(w.A,{})]})}),readOnly:!0},defaultValue:"".concat((null===e||void 0===e?void 0:e.StepTitle)||"Step-".concat(t+1),"\n").concat(Ve)})},e.StepId||t))):(0,z.jsx)(c.A,{variant:"body1",sx:{marginTop:"16px",color:"#888"},children:"No steps available. Please check back later."})})]})})})})]}),(0,z.jsxs)("div",{className:"qadpt-set-right",children:[(0,z.jsx)("div",{className:"qadpt-page-target",children:(0,z.jsx)(n.Ay,{item:!0,xs:12,md:6,children:(0,z.jsx)(p.A,{className:"qadpt-card qadpt-target",children:(0,z.jsxs)(g.A,{children:[(0,z.jsxs)("div",{className:"qadpt-header-container",children:[(0,z.jsxs)("div",{className:"qadpt-label-sublabel",children:[(0,z.jsx)("div",{className:"qadpt-label",children:"Page Targeting"}),(0,z.jsx)("div",{className:"qadpt-sublabel",children:"Which pages should the guide be visible?"})]}),(0,z.jsxs)("div",{children:[(0,z.jsx)(h.A,{variant:"outlined",className:"qadpt-add-target-btn",onClick:()=>{nt.length<5&&(dt([...nt,{pageRule:"Equals",url:"",PageTargetId:"",logicalOperator:"OR"}]),de(!1),re(!0),rt([]))},disabled:nt.length>=5,startIcon:(0,z.jsx)(U.A,{}),children:"Add Target"}),(0,z.jsx)(d.A,{onClick:Ot,disabled:!ae||ct.length>0,className:"qadpt-add-target-btn save",style:{opacity:!ae||ct.length>0?.5:1},children:(0,z.jsx)("img",{src:N,alt:"saveicon",style:{opacity:!ae||ct.length>0?.5:1}})})]})]}),(0,z.jsxs)("div",{className:"qadpt-conditions",children:[nt.length>1&&(0,z.jsxs)(v.A,{className:"qadpt-operator",children:[(0,z.jsx)("span",{children:"Condition"}),(0,z.jsxs)(m.A,{value:"OR",disabled:!0,onChange:e=>((e,t)=>{const a=[...nt];e<a.length-1&&(a[e+1].logicalOperator=t),dt(a)})(0,e.target.value),children:[(0,z.jsx)(j.A,{value:"AND",children:"AND"}),(0,z.jsx)(j.A,{value:"OR",children:"OR"})]})]}),nt.map(((e,t)=>(0,z.jsxs)(n.Ay,{container:!0,spacing:2,children:[(0,z.jsx)(n.Ay,{item:!0,xs:6,children:(0,z.jsxs)("div",{children:[(0,z.jsx)("label",{className:"qadpt-field-label",children:"Page Rule"}),(0,z.jsx)(v.A,{fullWidth:!0,children:(0,z.jsxs)(m.A,{value:e.pageRule,onChange:e=>It(t,"pageRule",e.target.value),renderValue:e=>e,sx:{"&:hover .MuiOutlinedInput-notchedOutline":{border:"1px solid #ccc"},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{border:"1px solid #ccc"},"&.MuiOutlinedInput-notchedOutline":{height:"40px !important"}},children:[(0,z.jsxs)(j.A,{sx:{fontSize:"14px",display:"flex",justifyContent:"space-between"},value:"Equals",children:["Equals",(0,z.jsx)("img",{src:G,alt:"check"})]}),(0,z.jsxs)(j.A,{sx:{fontSize:"14px",display:"flex",justifyContent:"space-between"},value:"Not Equals",children:["Not Equals  ",(0,z.jsx)("img",{src:G,alt:"check"})]}),(0,z.jsxs)(j.A,{sx:{fontSize:"14px",display:"flex",justifyContent:"space-between"},value:"Starts With",children:["Starts With  ",(0,z.jsx)("img",{src:G,alt:"check"})]}),(0,z.jsxs)(j.A,{sx:{fontSize:"14px",display:"flex",justifyContent:"space-between"},value:"Ends With",children:["Ends With  ",(0,z.jsx)("img",{src:G,alt:"check"})]}),(0,z.jsxs)(j.A,{sx:{fontSize:"14px",display:"flex",justifyContent:"space-between"},value:"Contains",children:["Contains  ",(0,z.jsx)("img",{src:G,alt:"check"})]}),(0,z.jsxs)(j.A,{sx:{fontSize:"14px",display:"flex",justifyContent:"space-between"},value:"Not Contains",children:["Does Not Contain  ",(0,z.jsx)("img",{src:G,alt:"check"})]})]})})]})}),(0,z.jsx)(n.Ay,{item:!0,xs:5,children:(0,z.jsxs)("div",{children:[(0,z.jsx)("label",{className:"qadpt-field-label",children:"URL"}),(0,z.jsx)(o.A,{fullWidth:!0,value:e.url,onChange:e=>It(t,"url",e.target.value),error:!!ct[t],helperText:!!ct[t]&&(0,z.jsxs)("span",{style:{display:"flex",alignItems:"center",gap:"5px"},children:[(0,z.jsx)("img",{src:C,alt:"error-icon"}),ct[t]]}),InputProps:{sx:{"&:hover .MuiOutlinedInput-notchedOutline":{border:"1px solid #ccc"},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{border:"1px solid #ccc"},"&.MuiOutlinedInput-notchedOutline":{height:"40px !important"},"&.Mui-error .MuiOutlinedInput-notchedOutline":{borderColor:"#e6a957 !important"}}}})]})}),(0,z.jsx)(n.Ay,{item:!0,xs:1,className:"qadpt-btngrid ".concat(ct[t]?"qadpt-error-btn":""),style:{opacity:1===nt.length?.5:1},children:(0,z.jsx)(d.A,{onClick:()=>{lt(t),st(!0)},disabled:1===nt.length,children:(0,z.jsx)("img",{src:P,alt:"tardelete",style:{opacity:1===nt.length?.5:1}})})})]},t))),at&&(0,z.jsx)("div",{className:"qadpt-modal-overlay",children:(0,z.jsxs)("div",{className:"qadpt-usrconfirm-popup qadpt-danger",children:[(0,z.jsx)("div",{children:(0,z.jsx)("div",{className:"qadpt-icon",children:(0,z.jsx)(d.A,{className:"qadpt-svg",children:(0,z.jsx)("i",{className:"fal fa-trash-alt"})})})}),(0,z.jsx)("div",{className:"qadpt-popup-title",children:"Delete Trigger"}),(0,z.jsx)("div",{className:"qadpt-warning",children:"Are you sure you want to delete this trigger?"}),(0,z.jsxs)("div",{className:"qadpt-buttons",children:[(0,z.jsx)("button",{onClick:()=>st(!1),className:"qadpt-cancel-button",children:"Cancel"}),(0,z.jsx)("button",{onClick:async()=>{if(null===it)return;const e=nt[it];try{if(e.PageTargetId){const t=e.PageTargetId,a={currentGuideId:ve,pageTargetId:t},s=await(0,E.ov)(a);if(s.Success){he(s.SuccessMessage,"success");const e=nt.filter(((e,t)=>t!==it));dt(e)}else he(s.ErrorMessage,"error")}else if(nt.length>1){const e=nt.filter(((e,t)=>t!==it));dt(e)}}catch(t){console.error("Error deleting page target:",t),he("Failed to delete the trigger. Please try again.","error")}finally{lt(null),st(!1)}},className:"qadpt-conform-button",type:"button",children:"Confirm"})]})]})})]})]})})})}),(0,z.jsx)("div",{className:"qadpt-auto-trigger",children:(0,z.jsx)(n.Ay,{item:!0,xs:12,md:6,children:(0,z.jsx)(p.A,{className:"qadpt-card qadpt-trigger",children:(0,z.jsxs)(g.A,{children:[(0,z.jsxs)(n.Ay,{container:!0,children:[(0,z.jsx)("div",{className:"qadpt-label",children:"Auto Trigger"}),(0,z.jsx)(A.A,{checked:!0,onChange:e=>Ee(e.target.checked),disabled:!0})]}),(0,z.jsxs)("div",{className:"qadpt-sublabel",style:{lineHeight:1},children:["Enable this to automatically display the ",Ne.toLowerCase()," on target pages, or trigger manually via checklists."]})]})})})}),(0,z.jsx)("div",{className:"qadpt-frequency",children:(0,z.jsx)(n.Ay,{item:!0,xs:12,md:6,children:(0,z.jsx)(p.A,{className:"qadpt-card",children:(0,z.jsxs)(g.A,{children:[(0,z.jsx)("div",{className:"qadpt-label",children:"Frequency"}),(0,z.jsx)("div",{className:"qadpt-sublabel",children:"How often do you want it to trigger"}),(0,z.jsx)(v.A,{component:"fieldset",disabled:!ke,children:(0,z.jsxs)(n.Ay,{container:!0,spacing:1,alignItems:"center",wrap:"nowrap",children:[(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(y.A,{control:(0,z.jsx)(O.A,{checked:"onlyOnce"===De,onChange:()=>Tt("onlyOnce"),disabled:!0}),label:(0,z.jsx)("span",{className:"qadpt-freqselect",children:"Only Once"})})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(y.A,{control:(0,z.jsx)(O.A,{checked:!0,onChange:()=>Tt("onceInSession")}),label:(0,z.jsx)("span",{className:"qadpt-freqselect",children:"Once in a session"})})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(y.A,{control:(0,z.jsx)(O.A,{checked:"onceADay"===De,onChange:()=>Tt("onceADay"),disabled:!0}),label:(0,z.jsx)("span",{className:"qadpt-freqselect",children:"Once a day"})})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(y.A,{control:(0,z.jsx)(O.A,{checked:(null===De||void 0===De?void 0:De.startsWith("onceIn"))&&"onceInSession"!==De,onChange:()=>Tt("onceIn"),disabled:!0}),label:(0,z.jsx)("span",{className:"qadpt-freqselect",children:"Once in"})})}),(0,z.jsx)(n.Ay,{item:!0,children:(0,z.jsx)(v.A,{sx:{marginLeft:"8px",minWidth:"80px"},disabled:!ke||!(null!==De&&void 0!==De&&De.startsWith("onceIn")),children:(0,z.jsxs)(m.A,{labelId:"frequency-select-label",value:Ke,defaultValue:"2days",onChange:e=>{const t=e.target.value;null!==De&&void 0!==De&&De.startsWith("onceIn")&&"onceInSession"!==De&&(Xe(t),Ge("onceIn-".concat(t)))},disabled:!0,children:[(0,z.jsx)(j.A,{value:"2days",children:"2 days"}),(0,z.jsx)(j.A,{value:"3days",children:"3 days"}),(0,z.jsx)(j.A,{value:"Week",children:"Week"}),(0,z.jsx)(j.A,{value:"month",children:"Month"}),(0,z.jsx)(j.A,{value:"Quarter",children:"Quarter"}),(0,z.jsx)(j.A,{value:"Semi-Yearly",children:"Semi-Yearly"}),(0,z.jsx)(j.A,{value:"Yearly",children:"Yearly"})]})})})]})})]})})})}),(0,z.jsx)("div",{className:"qadpt-rev-publish",children:(0,z.jsx)(n.Ay,{item:!0,xs:12,md:6,children:(0,z.jsx)(p.A,{className:"qadpt-card qadpt-rev",children:(0,z.jsxs)(g.A,{children:[(0,z.jsx)("div",{className:"qadpt-label",children:"Review & Publish"}),(0,z.jsxs)(n.Ay,{container:!0,spacing:2,children:[(0,z.jsxs)(n.Ay,{item:!0,xs:6,className:"qadpt-gridleft",children:[(0,z.jsxs)(c.A,{children:["Publish ","".concat(Ne),":"]}),(0,z.jsx)(v.A,{component:"fieldset",children:(0,z.jsxs)(b.A,{value:Me,onChange:e=>Ue(e.target.value),children:[(0,z.jsx)(y.A,{value:"immediately",control:(0,z.jsx)(O.A,{}),label:"Immediately"}),(0,z.jsx)(y.A,{value:"custom",control:(0,z.jsx)(O.A,{}),label:"Custom date"})]})}),"custom"===Me&&(0,z.jsx)(o.A,{type:"datetime-local",value:He,onChange:e=>{Qe(e.target.value)},fullWidth:!0,sx:{marginTop:"8px"},inputProps:{min:ee}})]}),(0,z.jsxs)(n.Ay,{item:!0,xs:6,className:"qadpt-gridright",children:[(0,z.jsxs)(c.A,{children:["Unpublish ","".concat(Ne),":"]}),(0,z.jsx)(v.A,{component:"fieldset",children:(0,z.jsxs)(b.A,{value:Re,onChange:e=>We(e.target.value),children:[(0,z.jsx)(y.A,{value:"Manually",control:(0,z.jsx)(O.A,{}),label:"Manually"}),(0,z.jsx)(y.A,{value:"custom",control:(0,z.jsx)(O.A,{}),label:"Custom date"})]})}),"custom"===Re&&(0,z.jsx)(o.A,{type:"datetime-local",value:Ze,onChange:e=>{Je(e.target.value)},fullWidth:!0,sx:{marginTop:"8px"},inputProps:{min:ee}})]})]})]})})})})]})]})]})})})}}}]);
//# sourceMappingURL=43.fcf20b46.chunk.js.map