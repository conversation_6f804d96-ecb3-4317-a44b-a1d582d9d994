"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[599],{5599:(e,r,t)=>{t.r(r),t.d(r,{default:()=>S});var s=t(5043),a=t(9252),o=t(6446),n=t(5865),c=t(7784),i=t(1787),l=t(7392),d=t(2518),p=t(8446),u=t(7260),h=t(2819),m=t(9778),x=t(5335),A=t(2446);const w="MIGeMA0GCSqGSIb3DQEBAQUAA4GMADCBiAKBgHPeXxbMNqVNKTZvcbgLqb1itpw7U2wvk9n+qJ4MulRuZXJyVa7BlOboOlE8JZbYi7+i00xLShd7BpHKuRPacUnF2XKVEVbGrFSDKD3oOy9ji36y3HzBfrfiZzK9Q3YtIlVdBBLILd9PXSKQoAlYQqU73N1cKEZf9NOSzyZsnUpJAgMBAAE=",g=e=>{const r=new A.v;r.set<PERSON>ublic<PERSON>ey(w||"");return r.encrypt(e)};var f=t(3216),b=t(6180),y=t(1673),v=t(579);const S=()=>{const e=(0,f.zy)(),[r,t]=(0,s.useState)(!1),[A,w]=(0,s.useState)(!1),[S,j]=(0,s.useState)(""),[q,N]=(0,s.useState)(""),[k,C]=(0,s.useState)(""),[P,E]=(0,s.useState)(""),[L,R]=(0,s.useState)({minLength:!1,hasUppercase:!1,hasSpecialChar:!1,hasNumber:!1,noSpaces:!0}),[B,M]=(0,s.useState)(!1),[U,D]=(0,s.useState)(!1),{openSnackbar:F}=(0,b.d)(),I=e.pathname.split("/"),V=I[I.length-1],z=!B||""!==k||""!==P;return(0,v.jsxs)(a.A,{maxWidth:"xs",className:"qadpt-resetpassword",children:[(0,v.jsx)(o.A,{mb:4,className:"qadpt-brand-logo",children:(0,v.jsx)(n.A,{variant:"h3",className:"qadpt-brand-logo-text",gutterBottom:!0,children:"QuickAdopt"})}),(0,v.jsxs)(o.A,{children:[U?(0,v.jsxs)(o.A,{className:"qadpt-pwd-changed",children:[(0,v.jsx)(n.A,{variant:"h5",className:"qadpt-pwd-title",children:"Password Changed!"}),(0,v.jsx)(n.A,{variant:"body2",color:"textSecondary",className:"qadpt-changed-msg",mt:"10px",children:"Your password has been changed successfully."}),(0,v.jsx)(d.A,{type:"button",variant:"contained",className:"qadpt-btn-default",href:"https://app.quickadopt.in/Login",children:"Back to QuickAdopt Platform"})]}):(0,v.jsxs)(v.Fragment,{children:[(0,v.jsxs)(o.A,{className:"qadpt-welcome-message",children:[(0,v.jsx)(n.A,{variant:"h4",className:"qadpt-welcome-message-text",children:"Reset your password"}),(0,v.jsx)(n.A,{variant:"body2",color:"textSecondary",align:"center",mb:2,sx:{width:"325px",height:"38px",opacity:"0.8",fontFamily:"Poppins",fontSize:"14px",fontWeight:400,lineHeight:"19px",textAlign:"center",color:"#222222"},children:"Enter your new password below to change your password."})]}),(0,v.jsxs)(o.A,{className:"qadpt-login-form",children:[(0,v.jsx)(n.A,{className:"qadpt-form-label",children:"New Password"}),(0,v.jsx)(c.A,{margin:"normal",required:!0,fullWidth:!0,type:r?"text":"password",id:"new-password",name:"new-password",autoComplete:"new-password",autoFocus:!0,value:S,onChange:e=>{const r=e.target.value;j(r);const t=(e=>{const r={minLength:e.length>=8,hasUppercase:/[A-Z]/.test(e),hasSpecialChar:/[!@#$%^&*]/.test(e),hasNumber:/\d/.test(e),noSpaces:!/\s/.test(e)};return R(r),M(e===q),r.minLength&&r.hasUppercase&&r.hasSpecialChar&&r.hasNumber&&r.noSpaces})(r);C(t?"":L.noSpaces?"Password must be at least 8 characters, contain 1 uppercase letter, 1 special character, 1 number, and no spaces.":"Spaces are not accepted."),M(r===q)},placeholder:"min. 8 characters",className:"qadpt-custom-input",error:!!k,InputProps:{endAdornment:(0,v.jsx)(i.A,{position:"end",children:(0,v.jsx)(l.A,{"aria-label":"toggle password visibility",onClick:()=>t(!r),edge:"end",children:r?(0,v.jsx)(h.A,{}):(0,v.jsx)(u.A,{})})})}}),k?(0,v.jsx)(y.A,{sx:{color:"red",fontSize:"12px"},children:k}):"",(0,v.jsx)(n.A,{className:"qadpt-form-label",children:"Re-enter New Password"}),(0,v.jsx)(c.A,{margin:"normal",required:!0,fullWidth:!0,type:A?"text":"password",id:"confirm-password",name:"confirm-password",autoComplete:"confirm-password",autoFocus:!0,value:q,onChange:e=>{const r=e.target.value;N(r),E(S!==r?"Passwords do not match.":""),M(S===r&&S.length>=8)},placeholder:"Same as above",className:"qadpt-custom-input",error:!!P,InputProps:{endAdornment:(0,v.jsx)(i.A,{position:"end",children:(0,v.jsx)(l.A,{"aria-label":"toggle confirm password visibility",onClick:()=>w(!A),edge:"end",children:A?(0,v.jsx)(h.A,{}):(0,v.jsx)(u.A,{})})})}}),P?(0,v.jsx)(y.A,{sx:{color:"red",fontSize:"12px"},children:P}):"",(S||q)&&(0,v.jsxs)(o.A,{className:"qadpt-passwordhint",children:[(0,v.jsx)(n.A,{className:"qadpt-passwordhint-text",children:"Your password must contain"}),(0,v.jsxs)(o.A,{className:"qadpt-passwordhint-container",children:[(0,v.jsx)(m.A,{className:L.minLength?"qadpt-checkicon-valid":""}),(0,v.jsx)(n.A,{className:"qadpt-passwordhint-item",children:"At least 8 characters"})]})]}),(0,v.jsx)(d.A,{type:"button",fullWidth:!0,variant:"contained",onClick:async()=>{let e=!0;if(S||(C("This field is required."),e=!1),q||(E("This field is required."),e=!1),e&&B&&!k&&!P){const e=g(S),t=g(q);if(e&&t)try{const r=await(async(e,r,t)=>{try{const s="/User/ResetUserPassword?PasswordLogId=".concat(e,"&NewPassword=").concat(encodeURIComponent(r),"&ReEnterNewPassword=").concat(encodeURIComponent(t)),a=await x.XI.get(s);return 200===a.status&&a.data.Success?a.data:a.data.ErrorMessage}catch(s){throw s.response?(console.error("Error Response:",s.response.data),new Error("Error: ".concat(s.response.status," - ").concat(s.response.data))):s.request?(console.error("No response received:",s.request),new Error("No response received from the server")):(console.error("Request setup error:",s.message),new Error("Error sending password reset request"))}})(V,e,t);r&&r.Success?D(!0):F(r,"error")}catch(r){console.error("Error during password reset:",r)}else console.error("Encryption failed. Please try again.")}localStorage.clear(),document.cookie.split(";").forEach((e=>{const[r]=e.split("=");document.cookie="".concat(r,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;")})),localStorage.setItem("logout-event",Date.now().toString()),sessionStorage.clear()},disabled:!B||""!==k||""!==P,className:z?"qadpt-btn-disabled":"qadpt-btn-default",children:"Reset password"})]}),(0,v.jsx)(o.A,{mt:12,className:"qadpt-login-footer",children:(0,v.jsxs)(n.A,{variant:"body2",className:"qadpt-footer-text",children:[(0,v.jsx)(p.A,{href:"/terms-of-use",className:"qadpt-footer-link",children:"Terms of use"})," ","|"," ",(0,v.jsx)(p.A,{href:"/privacy-policy",className:"qadpt-footer-link",children:"Privacy Policy"})]})})]}),(0,v.jsx)(o.A,{mt:12,className:"qadpt-login-footer",children:(0,v.jsxs)(n.A,{variant:"body2",className:"qadpt-footer-text",children:[(0,v.jsx)(p.A,{href:"/terms-of-use",className:"qadpt-footer-link",children:"Terms of use"})," ","|"," ",(0,v.jsx)(p.A,{href:"/privacy-policy",className:"qadpt-footer-link",children:"Privacy Policy"})]})})]})]})}},8446:(e,r,t)=>{t.d(r,{A:()=>q});var s=t(8587),a=t(8168),o=t(5043),n=t(9292),c=t(8610),i=t(6803),l=t(4535),d=t(8206),p=t(3574),u=t(5849),h=t(5865),m=t(2532),x=t(2372);function A(e){return(0,x.Ay)("MuiLink",e)}const w=(0,m.A)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]);var g=t(7162),f=t(7266);const b={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},y=e=>{let{theme:r,ownerState:t}=e;const s=(e=>b[e]||e)(t.color),a=(0,g.Yn)(r,"palette.".concat(s),!1)||t.color,o=(0,g.Yn)(r,"palette.".concat(s,"Channel"));return"vars"in r&&o?"rgba(".concat(o," / 0.4)"):(0,f.X4)(a,.4)};var v=t(579);const S=["className","color","component","onBlur","onFocus","TypographyClasses","underline","variant","sx"],j=(0,l.Ay)(h.A,{name:"MuiLink",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:t}=e;return[r.root,r["underline".concat((0,i.A)(t.underline))],"button"===t.component&&r.button]}})((e=>{let{theme:r,ownerState:t}=e;return(0,a.A)({},"none"===t.underline&&{textDecoration:"none"},"hover"===t.underline&&{textDecoration:"none","&:hover":{textDecoration:"underline"}},"always"===t.underline&&(0,a.A)({textDecoration:"underline"},"inherit"!==t.color&&{textDecorationColor:y({theme:r,ownerState:t})},{"&:hover":{textDecorationColor:"inherit"}}),"button"===t.component&&{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(w.focusVisible)]:{outline:"auto"}})})),q=o.forwardRef((function(e,r){const t=(0,d.b)({props:e,name:"MuiLink"}),{className:l,color:h="primary",component:m="a",onBlur:x,onFocus:w,TypographyClasses:g,underline:f="always",variant:y="inherit",sx:q}=t,N=(0,s.A)(t,S),{isFocusVisibleRef:k,onBlur:C,onFocus:P,ref:E}=(0,p.A)(),[L,R]=o.useState(!1),B=(0,u.A)(r,E),M=(0,a.A)({},t,{color:h,component:m,focusVisible:L,underline:f,variant:y}),U=(e=>{const{classes:r,component:t,focusVisible:s,underline:a}=e,o={root:["root","underline".concat((0,i.A)(a)),"button"===t&&"button",s&&"focusVisible"]};return(0,c.A)(o,A,r)})(M);return(0,v.jsx)(j,(0,a.A)({color:h,className:(0,n.A)(U.root,l),classes:g,component:m,onBlur:e=>{C(e),!1===k.current&&R(!1),x&&x(e)},onFocus:e=>{P(e),!0===k.current&&R(!0),w&&w(e)},ref:B,ownerState:M,variant:y,sx:[...Object.keys(b).includes(h)?[]:[{color:h}],...Array.isArray(q)?q:[q]]},N))}))},9778:(e,r,t)=>{var s=t(4994);r.A=void 0;var a=s(t(39)),o=t(579);r.A=(0,a.default)((0,o.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"}),"CheckCircle")}}]);
//# sourceMappingURL=599.fd64192a.chunk.js.map