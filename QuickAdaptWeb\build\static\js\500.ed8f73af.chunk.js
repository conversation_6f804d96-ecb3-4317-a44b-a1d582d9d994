"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[500],{2029:(e,t,o)=>{o.d(t,{H:()=>n});const n=async function(e,t,o,n){let a=arguments.length>5?arguments[5]:void 0,r=arguments.length>6?arguments[6]:void 0;try{const o="https://devapp.quickadopt.in/connect/token",n=new URLSearchParams({grant_type:"password",client_id:"dap_extension",client_secret:"user_interaction",scope:"openid profile api1",username:e,password:t,authType:a,tenantid:r}),s={"Content-Type":"application/x-www-form-urlencoded"},i=await fetch(o,{method:"POST",body:n,headers:s});if(200===i.status){return await i.json()}return await i.json()}catch(s){throw console.error("An error occurred:",s),s}}},8446:(e,t,o)=>{o.d(t,{A:()=>j});var n=o(8587),a=o(8168),r=o(5043),s=o(9292),i=o(8610),l=o(6803),c=o(4535),d=o(8206),u=o(3574),p=o(5849),m=o(5865),v=o(2532),g=o(2372);function f(e){return(0,g.Ay)("MuiLink",e)}const h=(0,v.A)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]);var y=o(7162),A=o(7266);const x={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},b=e=>{let{theme:t,ownerState:o}=e;const n=(e=>x[e]||e)(o.color),a=(0,y.Yn)(t,"palette.".concat(n),!1)||o.color,r=(0,y.Yn)(t,"palette.".concat(n,"Channel"));return"vars"in t&&r?"rgba(".concat(r," / 0.4)"):(0,A.X4)(a,.4)};var N=o(579);const w=["className","color","component","onBlur","onFocus","TypographyClasses","underline","variant","sx"],S=(0,c.Ay)(m.A,{name:"MuiLink",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,t["underline".concat((0,l.A)(o.underline))],"button"===o.component&&t.button]}})((e=>{let{theme:t,ownerState:o}=e;return(0,a.A)({},"none"===o.underline&&{textDecoration:"none"},"hover"===o.underline&&{textDecoration:"none","&:hover":{textDecoration:"underline"}},"always"===o.underline&&(0,a.A)({textDecoration:"underline"},"inherit"!==o.color&&{textDecorationColor:b({theme:t,ownerState:o})},{"&:hover":{textDecorationColor:"inherit"}}),"button"===o.component&&{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(h.focusVisible)]:{outline:"auto"}})})),j=r.forwardRef((function(e,t){const o=(0,d.b)({props:e,name:"MuiLink"}),{className:c,color:m="primary",component:v="a",onBlur:g,onFocus:h,TypographyClasses:y,underline:A="always",variant:b="inherit",sx:j}=o,k=(0,n.A)(o,w),{isFocusVisibleRef:C,onBlur:q,onFocus:I,ref:F}=(0,u.A)(),[L,O]=r.useState(!1),B=(0,p.A)(t,F),E=(0,a.A)({},o,{color:m,component:v,focusVisible:L,underline:A,variant:b}),T=(e=>{const{classes:t,component:o,focusVisible:n,underline:a}=e,r={root:["root","underline".concat((0,l.A)(a)),"button"===o&&"button",n&&"focusVisible"]};return(0,i.A)(r,f,t)})(E);return(0,N.jsx)(S,(0,a.A)({color:m,className:(0,s.A)(T.root,c),classes:y,component:v,onBlur:e=>{q(e),!1===C.current&&O(!1),g&&g(e)},onFocus:e=>{I(e),!0===C.current&&O(!0),h&&h(e)},ref:B,ownerState:E,variant:b,sx:[...Object.keys(x).includes(m)?[]:[{color:m}],...Array.isArray(j)?j:[j]]},k))}))},9500:(e,t,o)=>{o.r(t),o.d(t,{default:()=>C});var n=o(5043),a=o(9252),r=o(6446),s=o(5865),i=o(7784),l=o(1787),c=o(7392),d=o(2518),u=o(8446),p=o(5677),m=o(2029),v=o(9393),g=o(2446),f=o(3216),h=o(7070),y=o(1673),A=o(4353),x=o(4379),b=o(5335),N=o(7254),w=o(579);let S,j,k={};const C=()=>{const{user:e}=(0,p.As)();let t;const[o,C]=(0,n.useState)(!1);(0,n.useEffect)((()=>{if((0,b.oI)()){C(!0);const e=setTimeout((()=>{C(!1)}),5e3);return()=>clearTimeout(e)}}),[]);const[q,I]=(0,n.useState)(!1),[F,L]=(0,n.useState)(""),[O,B]=(0,n.useState)(""),[E,T]=(0,n.useState)(null),[U,P]=(0,n.useState)(null),[D,_]=(0,n.useState)(void 0),[M,V]=(0,n.useState)(""),[J,K]=(0,n.useState)(""),[R,z]=(0,n.useState)(null),[H,W]=(0,n.useState)(null),{signOut:Y,loggedOut:Z}=(0,p.As)(),Q=(0,f.Zp)();(0,n.useEffect)((()=>{const t=(0,b.oI)(),o=localStorage.getItem("access_token");e&&o&&!t&&Q("/",{replace:!0})}),[e]);return(0,n.useEffect)((()=>{var e,t,o,n,a,r;const s=null!==(e=j)&&void 0!==e&&e.FirstName&&null!==(t=j)&&void 0!==t&&t.FirstName?null===(o=j)||void 0===o?void 0:o.FirstName.substring(0,1).toUpperCase():"",i=null!==(n=j)&&void 0!==n&&n.LastName&&null!==(a=j)&&void 0!==a&&a.LastName?null===(r=j)||void 0===r?void 0:r.LastName.substring(0,1).toUpperCase():"";S=s+i}),[j]),(0,n.useEffect)((()=>{let e=localStorage.getItem("access_token");const o=JSON.parse(localStorage.getItem("userInfo")||"{}");if(o["oidc-info"]&&o.user&&(j=JSON.parse(o.user),e=o["oidc-info"].access_token),e)try{const o=(0,h.A)(e);_(o),async function(e){try{var t;const o=null!==e&&void 0!==e&&e.FirstName&&null!==e&&void 0!==e&&e.FirstName?null===e||void 0===e?void 0:e.FirstName.substring(0,1).toUpperCase():"",n=e&&null!==e&&void 0!==e&&e.LastName?null===e||void 0===e?void 0:e.LastName.substring(0,1).toUpperCase():"";S=o+n,localStorage.setItem("userType",null!==(t=null===e||void 0===e?void 0:e.UserType)&&void 0!==t?t:"")}catch(U){console.error("Error fetching user or organization details",U)}}(j),t=o.UserId}catch(U){Y()}else Y()}),[e]),(0,w.jsxs)(a.A,{maxWidth:"sm",className:"qadpt-superadminlogin",children:[(0,w.jsx)(r.A,{mb:4,className:"qadpt-brand-logo",children:(0,w.jsx)("img",{src:x.bT,alt:"QuickAdopt Logo",className:"qadpt-brand-logo-img"})}),o&&(0,w.jsx)(N.A,{severity:"error",sx:{marginBottom:-5,width:"50%",position:"relative",alignContent:"center",textAlign:"center",display:"flex",justifyContent:"center"},children:"Your session has expired. Please log in again."}),o&&(0,w.jsx)(N.A,{severity:"error",sx:{marginBottom:-5,width:"50%",position:"relative",alignContent:"center",textAlign:"center",display:"flex",justifyContent:"center"},children:"Your session has expired. Please log in again."}),(0,w.jsx)(r.A,{className:"qadpt-welcome-message",children:(0,w.jsx)(s.A,{variant:"h4",className:"qadpt-welcome-message-text",children:"Welcome back"})}),(0,w.jsxs)(r.A,{className:"qadpt-login-form",children:[(0,w.jsx)(s.A,{className:"qadpt-form-label",children:"Email"}),(0,w.jsx)(i.A,{required:!0,fullWidth:!0,type:"email",id:"email",name:"Email",autoComplete:"Email",autoFocus:!0,value:F,onChange:e=>{L(e.target.value)},placeholder:"eg, <EMAIL>",className:"qadpt-custom-input"}),(0,w.jsx)(s.A,{className:"qadpt-form-label",children:"Password"}),(0,w.jsx)(i.A,{required:!0,fullWidth:!0,type:q?"text":"password",id:"password",name:"password",autoComplete:"password",value:O,onChange:e=>{B(e.target.value)},placeholder:"Enter your password",className:"qadpt-custom-input",InputProps:{endAdornment:(0,w.jsx)(l.A,{position:"end",children:(0,w.jsx)(c.A,{"aria-label":"toggle password visibility",onClick:()=>{I(!q)},edge:"end",children:(0,w.jsx)("i",{className:"fal ".concat(q?"fa-eye-slash":"fa-eye")})})})}}),U&&(0,w.jsx)(y.A,{error:!0,className:"qadpt-text-danger",children:U}),(0,w.jsx)("div",{className:"qadpt-form-label",children:(0,w.jsx)("span",{onClick:()=>window.open("/forgotpassword",""),children:"Forgot password?"})}),(0,w.jsx)(d.A,{type:"button",fullWidth:!0,variant:"contained",className:"qadpt-btn-default",onClick:async()=>{try{const a="1",r=!0,s="",i="admin",l="web";if(""===O||null==O)P("password should not be empty");else if(""===F||null==F)P("email should not be empty");else{const c=!0,d="MIGeMA0GCSqGSIb3DQEBAQUAA4GMADCBiAKBgHPeXxbMNqVNKTZvcbgLqb1itpw7U2wvk9n+qJ4MulRuZXJyVa7BlOboOlE8JZbYi7+i00xLShd7BpHKuRPacUnF2XKVEVbGrFSDKD3oOy9ji36y3HzBfrfiZzK9Q3YtIlVdBBLILd9PXSKQoAlYQqU73N1cKEZf9NOSzyZsnUpJAgMBAAE=",u=new g.v;u.setPublicKey(d);const p=(new Date).toISOString(),f=u.encrypt(O+"|"+p.trim()).toString();if(!f)return void console.error("Encryption failed");const h=await(0,m.H)(F,c?f:O,a,r,s,i,l);if(h.access_token){var e,t,o,n;V(h),k["oidc-info"]=JSON.stringify(h),localStorage.setItem("access_token",h.access_token);const a=await(0,v.wO)();W(a?a.data:null);const r=null!==a&&void 0!==a&&a.data.FirstName&&null!==a&&void 0!==a&&a.data.FirstName?null===a||void 0===a?void 0:a.data.FirstName.substring(0,1).toUpperCase():"",s=null!==a&&void 0!==a&&a.data&&null!==a&&void 0!==a&&a.data.LastName?null===a||void 0===a?void 0:a.data.LastName.substring(0,1).toUpperCase():"";S=r+s,localStorage.setItem("userType",null!==(e=null===a||void 0===a||null===(t=a.data)||void 0===t?void 0:t.UserType)&&void 0!==e?e:""),k.user=JSON.stringify(null===a||void 0===a?void 0:a.data);const i=await(0,A.Sw)(null!==(o=null===a||void 0===a||null===(n=a.data)||void 0===n?void 0:n.OrganizationId)&&void 0!==o?o:"");k.orgDetails=JSON.stringify(i),localStorage.setItem("userInfo",JSON.stringify(k)),Q("/")}else P(h.error_description)}}catch(U){console.error("Login failed:"),P("An unexpected error occurred.")}},children:"Continue"})]}),(0,w.jsx)(r.A,{mt:12,className:"qadpt-login-footer",children:(0,w.jsxs)(s.A,{variant:"body2",className:"qadpt-footer-text",children:[(0,w.jsx)(u.A,{href:"/terms-of-use",className:"qadpt-footer-link",children:"Terms of use"})," |",(0,w.jsx)(u.A,{href:"/privacy-policy",className:"qadpt-footer-link",children:"Privacy Policy"})]})})]})}}}]);
//# sourceMappingURL=500.ed8f73af.chunk.js.map