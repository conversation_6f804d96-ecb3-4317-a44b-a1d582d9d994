{"version": 3, "file": "static/js/500.ed8f73af.chunk.js", "mappings": "wHAGO,MAAMA,EAAeC,eAC1BC,EACAC,EACAC,EACAC,GAIkB,IAFlBC,EAAgBC,UAAAC,OAAA,EAAAD,UAAA,QAAAE,EAChBC,EAAeH,UAAAC,OAAA,EAAAD,UAAA,QAAAE,EAEf,IACE,MAAME,EAAaC,6CACbC,EAAO,IAAIC,gBAAgB,CAC/BC,WAAY,WACZC,UAAW,gBACXC,cAAe,mBACfC,MAAO,sBACPC,SAAUjB,EACVC,SAAUA,EACVG,SAAUA,EACVI,SAASA,IAGLU,EAAU,CACd,eAAgB,qCAGZC,QAAiBC,MAAMX,EAAY,CACvCY,OAAQ,OACRC,KAAMX,EACNO,QAASA,IAGX,GAAwB,MAApBC,EAASI,OAAgB,CAE3B,aAD2BJ,EAASK,MAEtC,CAEE,aAD4BL,EAASK,MAGzC,CACA,MAAOC,GAEL,MADAC,QAAQD,MAAM,qBAAsBA,GAC9BA,CACR,CACF,C,wKC7CO,SAASE,EAAoBC,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,CACA,MACA,GADoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,OAAQ,gBAAiB,iBAAkB,kBAAmB,SAAU,iB,wBCHxH,MAAMC,EAAuB,CAClCC,QAAS,eACTC,YAAa,eACbC,UAAW,iBACXC,cAAe,iBACfV,MAAO,cAiBT,EAZ0BW,IAGpB,IAHqB,MACzBC,EAAK,WACLC,GACDF,EACC,MAAMG,EAP0BC,IACzBT,EAAqBS,IAAUA,EAMbC,CAA0BH,EAAWE,OACxDA,GAAQE,EAAAA,EAAAA,IAAQL,EAAO,WAAFM,OAAaJ,IAAoB,IAAUD,EAAWE,MAC3EI,GAAeF,EAAAA,EAAAA,IAAQL,EAAO,WAAFM,OAAaJ,EAAgB,YAC/D,MAAI,SAAUF,GAASO,EACd,QAAPD,OAAeC,EAAY,YAEtBC,EAAAA,EAAAA,IAAML,EAAO,GAAI,E,aClB1B,MAAMM,EAAY,CAAC,YAAa,QAAS,YAAa,SAAU,UAAW,oBAAqB,YAAa,UAAW,MA2BlHC,GAAWC,EAAAA,EAAAA,IAAOC,EAAAA,EAAY,CAClCC,KAAM,UACNtB,KAAM,OACNuB,kBAAmBA,CAACC,EAAOC,KACzB,MAAM,WACJf,GACEc,EACJ,MAAO,CAACC,EAAOC,KAAMD,EAAO,YAADV,QAAaY,EAAAA,EAAAA,GAAWjB,EAAWkB,aAAwC,WAAzBlB,EAAWmB,WAA0BJ,EAAOK,OAAO,GAPnHV,EASdZ,IAGG,IAHF,MACFC,EAAK,WACLC,GACDF,EACC,OAAOuB,EAAAA,EAAAA,GAAS,CAAC,EAA4B,SAAzBrB,EAAWkB,WAAwB,CACrDI,eAAgB,QACU,UAAzBtB,EAAWkB,WAAyB,CACrCI,eAAgB,OAChB,UAAW,CACTA,eAAgB,cAEQ,WAAzBtB,EAAWkB,YAA0BG,EAAAA,EAAAA,GAAS,CAC/CC,eAAgB,aACM,YAArBtB,EAAWE,OAAuB,CACnCqB,oBAAqBC,EAAkB,CACrCzB,QACAC,gBAED,CACD,UAAW,CACTuB,oBAAqB,aAEI,WAAzBvB,EAAWmB,WAA0B,CACvCM,SAAU,WACVC,wBAAyB,cACzBC,gBAAiB,cAGjBC,QAAS,EACTC,OAAQ,EACRC,OAAQ,EAERC,aAAc,EACdC,QAAS,EAETC,OAAQ,UACRC,WAAY,OACZC,cAAe,SACfC,cAAe,OAEfC,iBAAkB,OAElB,sBAAuB,CACrBC,YAAa,QAEf,CAAC,KAADjC,OAAMkC,EAAYC,eAAiB,CACjCZ,QAAS,SAEX,IA0HJ,EAxH0Ba,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAM7B,GAAQ8B,EAAAA,EAAAA,GAAgB,CAC5B9B,MAAO4B,EACP9B,KAAM,aAEF,UACFiC,EAAS,MACT3C,EAAQ,UAAS,UACjBiB,EAAY,IAAG,OACf2B,EAAM,QACNC,EAAO,kBACPC,EAAiB,UACjB9B,EAAY,SAAQ,QACpB+B,EAAU,UAAS,GACnBC,GACEpC,EACJqC,GAAQC,EAAAA,EAAAA,GAA8BtC,EAAON,IACzC,kBACJ6C,EACAP,OAAQQ,EACRP,QAASQ,EACTZ,IAAKa,IACHC,EAAAA,EAAAA,MACGjB,EAAckB,GAAmBjB,EAAAA,UAAe,GACjDkB,GAAaC,EAAAA,EAAAA,GAAWjB,EAAKa,GAmB7BxD,GAAaqB,EAAAA,EAAAA,GAAS,CAAC,EAAGP,EAAO,CACrCZ,QACAiB,YACAqB,eACAtB,YACA+B,YAEIY,EAzHkB7D,KACxB,MAAM,QACJ6D,EAAO,UACP1C,EAAS,aACTqB,EAAY,UACZtB,GACElB,EACE8D,EAAQ,CACZ9C,KAAM,CAAC,OAAQ,YAAFX,QAAcY,EAAAA,EAAAA,GAAWC,IAA4B,WAAdC,GAA0B,SAAUqB,GAAgB,iBAE1G,OAAOuB,EAAAA,EAAAA,GAAeD,EAAOzE,EAAqBwE,EAAQ,EA+G1CG,CAAkBhE,GAClC,OAAoBiE,EAAAA,EAAAA,KAAKxD,GAAUY,EAAAA,EAAAA,GAAS,CAC1CnB,MAAOA,EACP2C,WAAWqB,EAAAA,EAAAA,GAAKL,EAAQ7C,KAAM6B,GAC9BgB,QAASb,EACT7B,UAAWA,EACX2B,OA/BiBqB,IACjBb,EAAkBa,IACgB,IAA9Bd,EAAkBe,SACpBV,GAAgB,GAEdZ,GACFA,EAAOqB,EACT,EAyBApB,QAvBkBoB,IAClBZ,EAAmBY,IACe,IAA9Bd,EAAkBe,SACpBV,GAAgB,GAEdX,GACFA,EAAQoB,EACV,EAiBAxB,IAAKgB,EACL3D,WAAYA,EACZiD,QAASA,EACTC,GAAI,IAAMmB,OAAOC,KAAK7E,GAAsB8E,SAASrE,GAEhD,GAFyD,CAAC,CAC7DA,aACYsE,MAAMC,QAAQvB,GAAMA,EAAK,CAACA,KACvCC,GACL,G,oQCrIA,IACIuB,EACAC,EAFAC,EAAwC,CAAC,EAG7C,MA4RA,EA5RwBC,KAEtB,MAAM,KAAEC,IAASC,EAAAA,EAAAA,MACf,IAAIC,EAEJ,MAAOC,EAAyBC,IAA8BC,EAAAA,EAAAA,WAAS,IAEvEC,EAAAA,EAAAA,YAAU,KAGN,IADuBC,EAAAA,EAAAA,MACH,CAClBH,GAA2B,GAG3B,MAAMI,EAAQC,YAAW,KACvBL,GAA2B,EAAM,GAChC,KAEH,MAAO,IAAMM,aAAaF,EAC5B,IACD,IAEH,MAAOG,EAAcC,IAAmBP,EAAAA,EAAAA,WAAS,IAC1CQ,EAAOC,IAAYT,EAAAA,EAAAA,UAAS,KAC5BxH,EAAUkI,IAAeV,EAAAA,EAAAA,UAAS,KAClCW,EAAOC,IAAWZ,EAAAA,EAAAA,UAAuB,OACzChG,EAAO6G,IAAYb,EAAAA,EAAAA,UAAwB,OAC3Cc,EAAeC,IAAoBf,EAAAA,EAAAA,eAAoClH,IACvEY,EAAUsH,IAAehB,EAAAA,EAAAA,UAAS,KAClCiB,EAASC,IAAalB,EAAAA,EAAAA,UAAS,KAC/BmB,EAAqBC,IAA0BpB,EAAAA,EAAAA,UAA8B,OAC7EqB,EAAkBC,IAAkBtB,EAAAA,EAAAA,UAAsB,OAI3D,QAAEuB,EAAO,UAAEC,IAAc5B,EAAAA,EAAAA,MASzB6B,GAAWC,EAAAA,EAAAA,OAErBzB,EAAAA,EAAAA,YAAU,KACR,MAAM0B,GAAoBzB,EAAAA,EAAAA,MACpB0B,EAAQC,aAAaC,QAAQ,gBAE/BnC,GAAQiC,IAAUD,GACpBF,EAAS,IAAK,CAAEM,SAAS,GAC3B,GACC,CAACpC,IA+FA,OA1CAM,EAAAA,EAAAA,YAAU,KACT,IAAD+B,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACI,MAAMC,EAA+B,QAAXN,EAAAxC,SAAW,IAAAwC,GAAXA,EAAaO,WAAyB,QAAhBN,EAAKzC,SAAW,IAAAyC,GAAXA,EAAaM,UAAuB,QAAdL,EAAG1C,SAAW,IAAA0C,OAAA,EAAXA,EAAaK,UAAUC,UAAU,EAAG,GAAGC,cAAgB,GAC/HC,EAA+B,QAAXP,EAAA3C,SAAW,IAAA2C,GAAXA,EAAaQ,UAAwB,QAAhBP,EAAK5C,SAAW,IAAA4C,GAAXA,EAAaO,SAAsB,QAAdN,EAAG7C,SAAW,IAAA6C,OAAA,EAAXA,EAAaM,SAASH,UAAU,EAAG,GAAGC,cAAgB,GAElIlD,EADkB+C,EAAoBI,CACZ,GAC3B,CAAClD,KAaJS,EAAAA,EAAAA,YAAU,KACN,IAAI2B,EAAQC,aAAaC,QAAQ,gBACvC,MAAMc,EAAWC,KAAKC,MAAMjB,aAAaC,QAAQ,aAAe,MAK1D,GAJFc,EAAS,cAAgBA,EAAe,OAC3CpD,EAAcqD,KAAKC,MAAMF,EAAe,MACxChB,EAAQgB,EAAS,aAAaG,cAErBnB,EACA,IACI,MAAMoB,GAAmBC,EAAAA,EAAAA,GAA0BrB,GACnDb,EAAiBiC,GArB7B1K,eAAgC4K,GAC5B,IAAK,IAADC,EACA,MAAMb,EAAiC,OAAZY,QAAY,IAAZA,GAAAA,EAAcX,WAA0B,OAAZW,QAAY,IAAZA,GAAAA,EAAcX,UAAwB,OAAZW,QAAY,IAAZA,OAAY,EAAZA,EAAcX,UAAUC,UAAU,EAAG,GAAGC,cAAgB,GACnIC,EAAoBQ,GAA6B,OAAZA,QAAY,IAAZA,GAAAA,EAAcP,SAAuB,OAAZO,QAAY,IAAZA,OAAY,EAAZA,EAAcP,SAASH,UAAU,EAAG,GAAGC,cAAgB,GAE3HlD,EADkB+C,EAAoBI,EAEtCb,aAAauB,QAAQ,WAAkC,QAAxBD,EAAc,OAAZD,QAAY,IAAZA,OAAY,EAAZA,EAAcG,gBAAQ,IAAAF,EAAAA,EAAI,GAC/D,CAAE,MAAOnJ,GACLC,QAAQD,MAAM,8CAA+CA,EACjE,CACJ,CAYYsJ,CAAiB9D,GACjBK,EAASmD,EAAiBnD,MAC9B,CAAE,MAAO7F,GACLuH,GACJ,MAGAA,GACJ,GAED,CAAC5B,KAKA4D,EAAAA,EAAAA,MAACC,EAAAA,EAAS,CAACC,SAAS,KAAK/F,UAAU,wBAAuBgG,SAAA,EACtD5E,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAACC,GAAI,EAAGlG,UAAU,mBAAkBgG,UAChD5E,EAAAA,EAAAA,KAAA,OACI+E,IAAKC,EAAAA,GACLC,IAAI,kBACJrG,UAAU,2BAILoC,IACHhB,EAAAA,EAAAA,KAACkF,EAAAA,EAAK,CACJC,SAAS,QACTlG,GAAI,CACFmG,cAAe,EACbC,MAAO,MACP7H,SAAU,WACV8H,aAAc,SACdC,UAAW,SACXC,QAAS,OACTC,eAAgB,UAElBb,SACH,mDAKE5D,IACHhB,EAAAA,EAAAA,KAACkF,EAAAA,EAAK,CACJC,SAAS,QACTlG,GAAI,CACFmG,cAAe,EACbC,MAAO,MACP7H,SAAU,WACV8H,aAAc,SACdC,UAAW,SACXC,QAAS,OACTC,eAAgB,UAElBb,SACH,oDAKC5E,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAACjG,UAAU,wBAAuBgG,UAClC5E,EAAAA,EAAAA,KAACtD,EAAAA,EAAU,CAACsC,QAAQ,KAAKJ,UAAU,6BAA4BgG,SAAC,oBAKpEH,EAAAA,EAAAA,MAACI,EAAAA,EAAG,CAACjG,UAAU,mBAAkBgG,SAAA,EAC7B5E,EAAAA,EAAAA,KAACtD,EAAAA,EAAU,CAACkC,UAAU,mBAAkBgG,SAAC,WAGzC5E,EAAAA,EAAAA,KAAC0F,EAAAA,EAAS,CACNC,UAAQ,EACRC,WAAS,EACTC,KAAK,QACLC,GAAG,QACHnJ,KAAK,QACLoJ,aAAa,QACbC,WAAS,EACTC,MAAOvE,EACPwE,SAlLWhG,IACvByB,EAASzB,EAAMiG,OAAOF,MAAM,EAkLhBG,YAAY,uBACZxH,UAAU,wBAGdoB,EAAAA,EAAAA,KAACtD,EAAAA,EAAU,CAACkC,UAAU,mBAAkBgG,SAAC,cAGzC5E,EAAAA,EAAAA,KAAC0F,EAAAA,EAAS,CACNC,UAAQ,EACRC,WAAS,EACTC,KAAMrE,EAAe,OAAS,WAC9BsE,GAAG,WACHnJ,KAAK,WACLoJ,aAAa,WACbE,MAAOvM,EACPwM,SA9LchG,IAC1B0B,EAAY1B,EAAMiG,OAAOF,MAAM,EA8LnBG,YAAY,sBACZxH,UAAU,qBACVyH,WAAY,CACRC,cACItG,EAAAA,EAAAA,KAACuG,EAAAA,EAAc,CAAC/I,SAAS,MAAKoH,UAC1B5E,EAAAA,EAAAA,KAACwG,EAAAA,EAAU,CACP,aAAW,6BACXC,QA/MAC,KAC5BjF,GAAiBD,EAAa,EA+MFmF,KAAK,MAAK/B,UAGV5E,EAAAA,EAAAA,KAAA,KAAGpB,UAAS,OAAAxC,OAASoF,EAAe,eAAiB,mBAOvEtG,IACE8E,EAAAA,EAAAA,KAAC4G,EAAAA,EAAc,CAAC1L,OAAK,EAAC0D,UAAU,oBAAmBgG,SAC9C1J,KAIT8E,EAAAA,EAAAA,KAAA,OAAKpB,UAAU,mBAAkBgG,UAC7B5E,EAAAA,EAAAA,KAAA,QAAMyG,QAASA,IAAMI,OAAOC,KAAK,kBAAmB,IAAIlC,SAAC,wBAI7D5E,EAAAA,EAAAA,KAAC+G,EAAAA,EAAM,CACHlB,KAAK,SACLD,WAAS,EACT5G,QAAQ,YACRJ,UAAU,oBACV6H,QApNKjN,UACjB,IACI,MAAMG,EAAiB,IACjBC,GAAgB,EAChBoN,EAAY,GACZnN,EAAW,QACXoN,EAAW,MACjB,GAAiB,KAAbvN,GAA+B,MAAZA,EACnBqI,EAAS,qCAER,GAAc,KAAVL,GAAyB,MAATA,EACrBK,EAAS,iCAER,CACD,MAAMmF,GAAsB/M,EACtBgN,EAAYhN,2NACZiN,EAAY,IAAIC,EAAAA,EACtBD,EAAUE,aAAaH,GACvB,MAAMI,GAAM,IAAIC,MAAOC,cACjBC,EAAoBN,EAAUO,QAAQjO,EAAW,IAAM6N,EAAIK,QAAQC,WACzE,IAAKH,EAEH,YADAvM,QAAQD,MAAM,qBAGhB,MAAMN,QAAiBrB,EAAAA,EAAAA,GAAamI,EAAOwF,EAAsBQ,EAAoBhO,EAAUC,EAAgBC,EAAeoN,EAAWnN,EAAUoN,GACnJ,GAAIrM,EAASqJ,aAAc,CAAC,IAAD6D,EAAAC,EAAAC,EAAAC,EACvB/F,EAAYtH,GACZ+F,EAAc,aAAeoD,KAAKmE,UAAUtN,GAC5CmI,aAAauB,QAAQ,eAAgB1J,EAASqJ,cAC9C,MAAMG,QAAqB+D,EAAAA,EAAAA,MAC3B3F,EAAe4B,EAAeA,EAAahK,KAAO,MAClD,MAAMoJ,EAAgC,OAAZY,QAAY,IAAZA,GAAAA,EAAchK,KAAKqJ,WAAyB,OAAZW,QAAY,IAAZA,GAAAA,EAAchK,KAAKqJ,UAAwB,OAAZW,QAAY,IAAZA,OAAY,EAAZA,EAAchK,KAAKqJ,UAAUC,UAAU,EAAG,GAAGC,cAAgB,GAChJC,EAA+B,OAAZQ,QAAY,IAAZA,GAAAA,EAAchK,MAAoB,OAAZgK,QAAY,IAAZA,GAAAA,EAAchK,KAAKyJ,SAAuB,OAAZO,QAAY,IAAZA,OAAY,EAAZA,EAAchK,KAAKyJ,SAASH,UAAU,EAAG,GAAGC,cAAgB,GAEzIlD,EADkB+C,EAAoBI,EAEtCb,aAAauB,QAAQ,WAAwC,QAA9BwD,EAAc,OAAZ1D,QAAY,IAAZA,GAAkB,QAAN2D,EAAZ3D,EAAchK,YAAI,IAAA2N,OAAN,EAAZA,EAAoBxD,gBAAQ,IAAAuD,EAAAA,EAAI,IACjEnH,EAAoB,KAAIoD,KAAKmE,UAAsB,OAAZ9D,QAAY,IAAZA,OAAY,EAAZA,EAAchK,MACrD,MAAMgO,QAAmBC,EAAAA,EAAAA,IAAsD,QAAnCL,EAAa,OAAZ5D,QAAY,IAAZA,GAAkB,QAAN6D,EAAZ7D,EAAchK,YAAI,IAAA6N,OAAN,EAAZA,EAAoBK,sBAAc,IAAAN,EAAAA,EAAI,IACnFrH,EAA0B,WAAIoD,KAAKmE,UAAUE,GAE7CrF,aAAauB,QAAQ,WAAYP,KAAKmE,UAAUvH,IAChDgC,EAAS,IACb,MACIZ,EAASnH,EAAS2N,kBAE1B,CACJ,CACA,MAAOrN,GACHC,QAAQD,MAAM,iBACd6G,EAAS,gCACb,GAkKkC6C,SACzB,iBAKL5E,EAAAA,EAAAA,KAAC6E,EAAAA,EAAG,CAAC2D,GAAI,GAAI5J,UAAU,qBAAoBgG,UACvCH,EAAAA,EAAAA,MAAC/H,EAAAA,EAAU,CAACsC,QAAQ,QAAQJ,UAAU,oBAAmBgG,SAAA,EACrD5E,EAAAA,EAAAA,KAACyI,EAAAA,EAAI,CAACC,KAAK,gBAAgB9J,UAAU,oBAAmBgG,SAAC,iBAAmB,MAC5E5E,EAAAA,EAAAA,KAACyI,EAAAA,EAAI,CAACC,KAAK,kBAAkB9J,UAAU,oBAAmBgG,SAAC,0BAGvD,C", "sources": ["services/LoginService.tsx", "../node_modules/@mui/material/Link/linkClasses.js", "../node_modules/@mui/material/Link/getTextDecoration.js", "../node_modules/@mui/material/Link/Link.js", "components/login/login.tsx"], "sourcesContent": ["import JSEncrypt from 'jsencrypt';\r\nimport { adminApiService, idsApiService } from \"./APIService\";\r\n\r\nexport const LoginService = async (\r\n  emailId: string,\r\n  password: string,\r\n  organizationId: string,\r\n  rememberLogin: boolean,\r\n  returnUrl: string = \"\",\r\n  authType: string,\r\n  tenantid:string\r\n): Promise<any> => {\r\n  try {\r\n    const requestUrl = process.env.REACT_APP_IDS_API + `/connect/token`;\r\n    const data = new URLSearchParams({\r\n      grant_type: 'password',\r\n      client_id: 'dap_extension',\r\n      client_secret: 'user_interaction',\r\n      scope: 'openid profile api1',\r\n      username: emailId,\r\n      password: password,\r\n      authType: authType,\r\n      tenantid:tenantid\r\n    });\r\n\r\n    const headers = {\r\n      'Content-Type': 'application/x-www-form-urlencoded',\r\n    };\r\n\r\n    const response = await fetch(requestUrl, {\r\n      method: 'POST',\r\n      body: data,\r\n      headers: headers,\r\n    });\r\n\r\n    if (response.status === 200) {\r\n      const jsonResponse = await response.json(); \r\n      return jsonResponse; \r\n    } else {\r\n      const errorResponse = await response.json(); \r\n      return errorResponse; \r\n    }\r\n  }\r\n  catch (error) {\r\n    console.error(\"An error occurred:\", error);\r\n    throw error; \r\n  }\r\n};\r\n", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "import { getPath } from '@mui/system';\nimport { alpha } from '@mui/system/colorManipulator';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"onBlur\", \"onFocus\", \"TypographyClasses\", \"underline\", \"variant\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useForkRef from '../utils/useForkRef';\nimport Typography from '../Typography';\nimport linkClasses, { getLinkUtilityClass } from './linkClasses';\nimport getTextDecoration, { colorTransformations } from './getTextDecoration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    component,\n    focusVisible,\n    underline\n  } = ownerState;\n  const slots = {\n    root: ['root', `underline${capitalize(underline)}`, component === 'button' && 'button', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getLinkUtilityClass, classes);\n};\nconst LinkRoot = styled(Typography, {\n  name: 'MuiLink',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`underline${capitalize(ownerState.underline)}`], ownerState.component === 'button' && styles.button];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({}, ownerState.underline === 'none' && {\n    textDecoration: 'none'\n  }, ownerState.underline === 'hover' && {\n    textDecoration: 'none',\n    '&:hover': {\n      textDecoration: 'underline'\n    }\n  }, ownerState.underline === 'always' && _extends({\n    textDecoration: 'underline'\n  }, ownerState.color !== 'inherit' && {\n    textDecorationColor: getTextDecoration({\n      theme,\n      ownerState\n    })\n  }, {\n    '&:hover': {\n      textDecorationColor: 'inherit'\n    }\n  }), ownerState.component === 'button' && {\n    position: 'relative',\n    WebkitTapHighlightColor: 'transparent',\n    backgroundColor: 'transparent',\n    // Reset default value\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    border: 0,\n    margin: 0,\n    // Remove the margin in Safari\n    borderRadius: 0,\n    padding: 0,\n    // Remove the padding in Firefox\n    cursor: 'pointer',\n    userSelect: 'none',\n    verticalAlign: 'middle',\n    MozAppearance: 'none',\n    // Reset\n    WebkitAppearance: 'none',\n    // Reset\n    '&::-moz-focus-inner': {\n      borderStyle: 'none' // Remove Firefox dotted outline.\n    },\n    [`&.${linkClasses.focusVisible}`]: {\n      outline: 'auto'\n    }\n  });\n});\nconst Link = /*#__PURE__*/React.forwardRef(function Link(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiLink'\n  });\n  const {\n      className,\n      color = 'primary',\n      component = 'a',\n      onBlur,\n      onFocus,\n      TypographyClasses,\n      underline = 'always',\n      variant = 'inherit',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const handlerRef = useForkRef(ref, focusVisibleRef);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    focusVisible,\n    underline,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(LinkRoot, _extends({\n    color: color,\n    className: clsx(classes.root, className),\n    classes: TypographyClasses,\n    component: component,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    ref: handlerRef,\n    ownerState: ownerState,\n    variant: variant,\n    sx: [...(!Object.keys(colorTransformations).includes(color) ? [{\n      color\n    }] : []), ...(Array.isArray(sx) ? sx : [sx])]\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Link.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the link.\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Typography`](/material-ui/api/typography/) element.\n   */\n  TypographyClasses: PropTypes.object,\n  /**\n   * Controls when the link should have an underline.\n   * @default 'always'\n   */\n  underline: PropTypes.oneOf(['always', 'hover', 'none']),\n  /**\n   * Applies the theme typography styles.\n   * @default 'inherit'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string])\n} : void 0;\nexport default Link;", "import React, { useState, useEffect } from 'react';\r\nimport { Con<PERSON>er, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport { useAuth } from '../auth/AuthProvider';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport { LoginService } from \"../../services/LoginService\";\r\nimport { GetUserDetails, encryptPassword } from '../../services/UserService';\r\nimport { JSEncrypt } from 'jsencrypt';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { GetUserDetailsById } from '../../services/UserService';\r\nimport { LoginUserInfo } from '../../models/LoginUserInfo';\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { getAllUsers } from '../../services/UserService';\r\nimport { Organization } from \"../../models/Organization\";\r\nimport { User } from \"../../models/User\";\r\nimport { User as Users, UserManager } from 'oidc-client-ts';\r\nimport { FormHelperText } from '@mui/material';\r\nimport { getOrganizationById } from '../../services/OrganizationService';\r\nimport { QuickAdopttext } from \"../../assets/icons/icons\";\r\nimport { checkSessionExpired } from '../../services/APIService';\r\nimport { Alert } from '@mui/material';\r\n//import { useAuth } from '../auth/AuthProvider';\r\nlet userLocalData: { [key: string]: any } = {}\r\nlet SAinitialsData: string;\r\nlet userDetails: User;\r\nconst Login: React.FC = () => {\r\n\r\n  const { user } = useAuth();\r\n    let UserId: string;\r\n    let OrganizationId: string;\r\n    const [showSessionExpiredAlert, setShowSessionExpiredAlert] = useState(false);\r\n  \r\n    useEffect(() => {\r\n        // Check if user was redirected due to session expiration\r\n        const sessionExpired = checkSessionExpired();\r\n        if (sessionExpired) {\r\n          setShowSessionExpiredAlert(true);\r\n          \r\n          // Auto-hide the alert after 5 seconds\r\n          const timer = setTimeout(() => {\r\n            setShowSessionExpiredAlert(false);\r\n          }, 5000);\r\n          \r\n          return () => clearTimeout(timer);\r\n        }\r\n    }, []);\r\n    \r\n    const [showPassword, setShowPassword] = useState(false);\r\n    const [email, setEmail] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [users, setUser] = useState<Users | null>(null);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);\r\n    const [response, setresponse] = useState('');\r\n    const [userIds, setuserId] = useState(\"\");\r\n    const [organizationDetails, setOrganizationDetails] = useState<Organization | null>(null);\r\n    const [loginUserDetails, setUserDetails] = useState<User | null>(null);\r\n    const handleClickShowPassword = () => {\r\n        setShowPassword(!showPassword);\r\n    };\r\n    const { signOut, loggedOut } = useAuth();\r\n\r\n    const handleEmailChange = (event: any) => {\r\n        setEmail(event.target.value);\r\n    };\r\n\r\n    const handlePasswordChange = (event: any) => {\r\n        setPassword(event.target.value);\r\n    };\r\n    const navigate = useNavigate();\r\n\r\nuseEffect(() => {\r\n  const hasSessionExpired = checkSessionExpired();\r\n  const token = localStorage.getItem(\"access_token\");\r\n\r\n  if (user && token && !hasSessionExpired) {\r\n    navigate(\"/\", { replace: true });\r\n  }\r\n}, [user]);\r\n    const handleSubmit = async () => {\r\n        try {\r\n            const organizationId = \"1\";\r\n            const rememberLogin = true;\r\n            const returnUrl = \"\"\r\n            const authType = \"admin\"\r\n            const tenantId = \"web\"\r\n            if (password === '' || password == null) {\r\n                setError('password should not be empty');\r\n            }\r\n            else if (email === '' || email == null) {\r\n                setError('email should not be empty');\r\n            }\r\n            else {\r\n                const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\r\n                const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\r\n                const encryptor = new JSEncrypt();\r\n                encryptor.setPublicKey(publicKey);\r\n                const now = new Date().toISOString();\r\n                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\r\n                if (!encryptedPassword) {\r\n                  console.error(\"Encryption failed\");\r\n                  return; \r\n                }\r\n                const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);\r\n                if (response.access_token) {\r\n                    setresponse(response);\r\n                    userLocalData[\"oidc-info\"] = JSON.stringify(response)\r\n                    localStorage.setItem(\"access_token\", response.access_token)\r\n                    const userResponse = await GetUserDetails();\r\n                    setUserDetails(userResponse ? userResponse.data : null);\r\n                    const firstNameInitials = userResponse?.data.FirstName && userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';\r\n                    const lastNameinitials = userResponse?.data && userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';\r\n                    const finalData = firstNameInitials + lastNameinitials;\r\n                    SAinitialsData = finalData;\r\n                    localStorage.setItem(\"userType\", userResponse?.data?.UserType ?? \"\");\r\n                    userLocalData[\"user\"] = JSON.stringify(userResponse?.data);\r\n                    const orgDetails = await getOrganizationById(userResponse?.data?.OrganizationId ?? \"\");\r\n                    userLocalData[\"orgDetails\"] = JSON.stringify(orgDetails);\r\n\r\n                    localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData))\r\n                    navigate(\"/\");\r\n                } else {\r\n                    setError(response.error_description);\r\n                }\r\n            }\r\n        }\r\n        catch (error) {\r\n            console.error('Login failed:');\r\n            setError('An unexpected error occurred.'); // Handle unexpected errors\r\n        }\r\n    };\r\n    useEffect(() =>\r\n    {\r\n        const firstNameInitials = userDetails?.FirstName &&  userDetails?.FirstName ? userDetails?.FirstName.substring(0, 1).toUpperCase() : '';\r\n        const lastNameinitials =  userDetails?.LastName &&  userDetails?.LastName ? userDetails?.LastName.substring(0, 1).toUpperCase() : '';\r\n        const finalData = firstNameInitials + lastNameinitials;\r\n        SAinitialsData = finalData;\r\n    }, [userDetails])\r\n    \r\n    async function GetLoginUserInfo(userResponse : User) {\r\n        try {\r\n            const firstNameInitials =  userResponse?.FirstName &&  userResponse?.FirstName ? userResponse?.FirstName.substring(0, 1).toUpperCase() : '';\r\n            const lastNameinitials =  userResponse &&  userResponse?.LastName ? userResponse?.LastName.substring(0, 1).toUpperCase() : '';\r\n            const finalData = firstNameInitials + lastNameinitials;\r\n            SAinitialsData = finalData;\r\n            localStorage.setItem(\"userType\", userResponse?.UserType ?? \"\");\r\n        } catch (error) {\r\n            console.error('Error fetching user or organization details', error);\r\n        }\r\n    }\r\n    useEffect(() => {\r\n        let token = localStorage.getItem(\"access_token\");\r\n\t\tconst userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n\t\tif (userInfo['oidc-info'] && userInfo['user']) {\r\n\t\t\tuserDetails = JSON.parse(userInfo['user'])\r\n\t\t\ttoken = userInfo['oidc-info'].access_token;\r\n\t\t}\r\n        if (token) {\r\n            try {\r\n                const loggedinUserInfo = jwt_decode<LoginUserInfo>(token);\r\n                setLoginUserInfo(loggedinUserInfo);\r\n                GetLoginUserInfo(userDetails);\r\n                UserId = loggedinUserInfo.UserId;\r\n            } catch (error) {\r\n                signOut();\r\n            }\r\n        }\r\n        else {\r\n            signOut();\r\n        }\r\n\r\n    }, [user]);\r\n\r\n    return (\r\n    \r\n\r\n        <Container maxWidth=\"sm\" className=\"qadpt-superadminlogin\">\r\n            <Box mb={4} className=\"qadpt-brand-logo\">\r\n    <img \r\n        src={QuickAdopttext} \r\n        alt=\"QuickAdopt Logo\" \r\n        className=\"qadpt-brand-logo-img\"\r\n    />\r\n            </Box>\r\n\r\n            {showSessionExpiredAlert && (\r\n          <Alert \r\n            severity=\"error\" \r\n            sx={{ \r\n              marginBottom: -5,\r\n                width: '50%',\r\n                position: 'relative',\r\n                alignContent: 'center',\r\n                textAlign: 'center', // centers the text\r\n                display: 'flex',\r\n                justifyContent: 'center',\r\n              \r\n            }}\r\n          >\r\n            Your session has expired. Please log in again.\r\n          </Alert>\r\n        )}\r\n\r\n            {showSessionExpiredAlert && (\r\n          <Alert \r\n            severity=\"error\" \r\n            sx={{ \r\n              marginBottom: -5,\r\n                width: '50%',\r\n                position: 'relative',\r\n                alignContent: 'center',\r\n                textAlign: 'center', // centers the text\r\n                display: 'flex',\r\n                justifyContent: 'center',\r\n              \r\n            }}\r\n          >\r\n            Your session has expired. Please log in again.\r\n          </Alert>\r\n        )}\r\n\r\n            <Box className=\"qadpt-welcome-message\">\r\n                <Typography variant=\"h4\" className=\"qadpt-welcome-message-text\">\r\n                    Welcome back\r\n                </Typography>\r\n            </Box>\r\n\r\n            <Box className=\"qadpt-login-form\">\r\n                <Typography className=\"qadpt-form-label\">\r\n                    Email\r\n                </Typography>\r\n                <TextField\r\n                    required\r\n                    fullWidth\r\n                    type=\"email\"\r\n                    id=\"email\"\r\n                    name=\"Email\"\r\n                    autoComplete=\"Email\"\r\n                    autoFocus\r\n                    value={email}\r\n                    onChange={handleEmailChange}\r\n                    placeholder=\"eg, <EMAIL>\"\r\n                    className=\"qadpt-custom-input\"\r\n                />\r\n\r\n                <Typography className=\"qadpt-form-label\">\r\n                    Password\r\n                </Typography>\r\n                <TextField\r\n                    required\r\n                    fullWidth\r\n                    type={showPassword ? \"text\" : \"password\"}\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    autoComplete=\"password\"                    \r\n                    value={password}\r\n                    onChange={handlePasswordChange}\r\n                    placeholder=\"Enter your password\"\r\n                    className=\"qadpt-custom-input\"\r\n                    InputProps={{\r\n                        endAdornment: (\r\n                            <InputAdornment position=\"end\">\r\n                                <IconButton\r\n                                    aria-label=\"toggle password visibility\"\r\n                                    onClick={handleClickShowPassword}\r\n                                    edge=\"end\"\r\n                                >\r\n                                    {/* {showPassword ? <VisibilityOff /> : <Visibility />} */}\r\n                                    <i className={`fal ${showPassword ? \"fa-eye-slash\" : \"fa-eye\"}`}></i>\r\n                                </IconButton>\r\n                            </InputAdornment>\r\n                        ),\r\n                    }}\r\n                   \r\n                />\r\n                 {error && (\r\n                    <FormHelperText error className=\"qadpt-text-danger\">\r\n                        {error}\r\n                    </FormHelperText>\r\n                )}\r\n\r\n                <div className=\"qadpt-form-label\">\r\n                    <span onClick={() => window.open(`/forgotpassword`, '')}>\r\n                        Forgot password?\r\n                    </span>\r\n                </div>\r\n                <Button\r\n                    type=\"button\"\r\n                    fullWidth\r\n                    variant=\"contained\"\r\n                    className=\"qadpt-btn-default\"\r\n                    onClick={handleSubmit}\r\n                > \r\n                        Continue\r\n                </Button>\r\n            </Box>\r\n\r\n            <Box mt={12} className=\"qadpt-login-footer\">\r\n                <Typography variant=\"body2\" className=\"qadpt-footer-text\">\r\n                    <Link href=\"/terms-of-use\" className=\"qadpt-footer-link\">Terms of use</Link> | \r\n                    <Link href=\"/privacy-policy\" className=\"qadpt-footer-link\">Privacy Policy</Link>\r\n                </Typography>\r\n            </Box>\r\n            </Container>\r\n      \r\n    );\r\n\r\n\r\n}\r\nexport default Login;\r\n\r\n\r\n\r\n\r\n\r\n\r\n//Code based login changes\r\n\r\n//   const { signIn ,signOut,loggedOut} = useAuth();\r\n//   useEffect(() => {\r\n//     // Get the user from userManager\r\n//     userManager.getUser().then(user => {\r\n//       if (!user || user.expired) {\r\n//         // If the user is not authenticated or the token is expired, redirect to the identity server login page\r\n//         loggedOut ? signOut() :userManager.signinRedirect() ;        \r\n//       }\r\n//       else {        \r\n//         userManager.signinRedirect();\r\n//       }\r\n//     });\r\n//   }, []);\r\n\r\n//   return null;\r\n// };"], "names": ["LoginService", "async", "emailId", "password", "organizationId", "<PERSON><PERSON><PERSON><PERSON>", "authType", "arguments", "length", "undefined", "tenantid", "requestUrl", "process", "data", "URLSearchParams", "grant_type", "client_id", "client_secret", "scope", "username", "headers", "response", "fetch", "method", "body", "status", "json", "error", "console", "getLinkUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "_ref", "theme", "ownerState", "transformedColor", "color", "transformDeprecatedColors", "<PERSON><PERSON><PERSON>", "concat", "channelColor", "alpha", "_excluded", "LinkRoot", "styled", "Typography", "name", "overridesResolver", "props", "styles", "root", "capitalize", "underline", "component", "button", "_extends", "textDecoration", "textDecorationColor", "getTextDecoration", "position", "WebkitTapHighlightColor", "backgroundColor", "outline", "border", "margin", "borderRadius", "padding", "cursor", "userSelect", "verticalAlign", "MozAppearance", "WebkitAppearance", "borderStyle", "linkClasses", "focusVisible", "React", "inProps", "ref", "useDefaultProps", "className", "onBlur", "onFocus", "TypographyClasses", "variant", "sx", "other", "_objectWithoutPropertiesLoose", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "focusVisibleRef", "useIsFocusVisible", "setFocusVisible", "handler<PERSON>ef", "useForkRef", "classes", "slots", "composeClasses", "useUtilityClasses", "_jsx", "clsx", "event", "current", "Object", "keys", "includes", "Array", "isArray", "SAinitialsData", "userDetails", "userLocalData", "<PERSON><PERSON>", "user", "useAuth", "UserId", "showSessionExpiredAlert", "setShowSessionExpiredAlert", "useState", "useEffect", "checkSessionExpired", "timer", "setTimeout", "clearTimeout", "showPassword", "setShowPassword", "email", "setEmail", "setPassword", "users", "setUser", "setError", "loginUserInfo", "setLoginUserInfo", "setresponse", "userIds", "setuserId", "organizationDetails", "setOrganizationDetails", "loginUserDetails", "setUserDetails", "signOut", "loggedOut", "navigate", "useNavigate", "hasSessionExpired", "token", "localStorage", "getItem", "replace", "_userDetails", "_userDetails2", "_userDetails3", "_userDetails4", "_userDetails5", "_userDetails6", "firstNameInitials", "FirstName", "substring", "toUpperCase", "lastNameinitials", "LastName", "userInfo", "JSON", "parse", "access_token", "loggedinUserInfo", "jwt_decode", "userResponse", "_userResponse$UserTyp", "setItem", "UserType", "GetLoginUserInfo", "_jsxs", "Container", "max<PERSON><PERSON><PERSON>", "children", "Box", "mb", "src", "QuickAdopttext", "alt", "<PERSON><PERSON>", "severity", "marginBottom", "width", "align<PERSON><PERSON><PERSON>", "textAlign", "display", "justifyContent", "TextField", "required", "fullWidth", "type", "id", "autoComplete", "autoFocus", "value", "onChange", "target", "placeholder", "InputProps", "endAdornment", "InputAdornment", "IconButton", "onClick", "handleClickShowPassword", "edge", "FormHelperText", "window", "open", "<PERSON><PERSON>", "returnUrl", "tenantId", "isEncryptionEnabled", "public<PERSON>ey", "encryptor", "JSEncrypt", "setPublicKey", "now", "Date", "toISOString", "encryptedPassword", "encrypt", "trim", "toString", "_userResponse$data$Us", "_userResponse$data", "_userResponse$data$Or", "_userResponse$data2", "stringify", "GetUserDetails", "orgDetails", "getOrganizationById", "OrganizationId", "error_description", "mt", "Link", "href"], "sourceRoot": ""}