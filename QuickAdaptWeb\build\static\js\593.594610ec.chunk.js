"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[593],{8446:(e,n,t)=>{t.d(n,{A:()=>w});var o=t(8587),i=t(8168),r=t(5043),a=t(9292),l=t(8610),p=t(6803),s=t(4535),c=t(8206),x=t(3574),d=t(5849),u=t(5865),h=t(2532),g=t(2372);function m(e){return(0,g.Ay)("MuiLink",e)}const f=(0,h.A)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]);var y=t(7162),A=t(7266);const b={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},v=e=>{let{theme:n,ownerState:t}=e;const o=(e=>b[e]||e)(t.color),i=(0,y.Yn)(n,"palette.".concat(o),!1)||t.color,r=(0,y.Yn)(n,"palette.".concat(o,"Channel"));return"vars"in n&&r?"rgba(".concat(r," / 0.4)"):(0,A.X4)(i,.4)};var S=t(579);const F=["className","color","component","onBlur","onFocus","TypographyClasses","underline","variant","sx"],j=(0,s.Ay)(u.A,{name:"MuiLink",slot:"Root",overridesResolver:(e,n)=>{const{ownerState:t}=e;return[n.root,n["underline".concat((0,p.A)(t.underline))],"button"===t.component&&n.button]}})((e=>{let{theme:n,ownerState:t}=e;return(0,i.A)({},"none"===t.underline&&{textDecoration:"none"},"hover"===t.underline&&{textDecoration:"none","&:hover":{textDecoration:"underline"}},"always"===t.underline&&(0,i.A)({textDecoration:"underline"},"inherit"!==t.color&&{textDecorationColor:v({theme:n,ownerState:t})},{"&:hover":{textDecorationColor:"inherit"}}),"button"===t.component&&{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(f.focusVisible)]:{outline:"auto"}})})),w=r.forwardRef((function(e,n){const t=(0,c.b)({props:e,name:"MuiLink"}),{className:s,color:u="primary",component:h="a",onBlur:g,onFocus:f,TypographyClasses:y,underline:A="always",variant:v="inherit",sx:w}=t,k=(0,o.A)(t,F),{isFocusVisibleRef:H,onBlur:T,onFocus:W,ref:B}=(0,x.A)(),[P,C]=r.useState(!1),D=(0,d.A)(n,B),z=(0,i.A)({},t,{color:u,component:h,focusVisible:P,underline:A,variant:v}),R=(e=>{const{classes:n,component:t,focusVisible:o,underline:i}=e,r={root:["root","underline".concat((0,p.A)(i)),"button"===t&&"button",o&&"focusVisible"]};return(0,l.A)(r,m,n)})(z);return(0,S.jsx)(j,(0,i.A)({color:u,className:(0,a.A)(R.root,s),classes:y,component:h,onBlur:e=>{T(e),!1===H.current&&C(!1),g&&g(e)},onFocus:e=>{W(e),!0===H.current&&C(!0),f&&f(e)},ref:D,ownerState:z,variant:v,sx:[...Object.keys(b).includes(u)?[]:[{color:u}],...Array.isArray(w)?w:[w]]},k))}))},8593:(e,n,t)=>{t.r(n),t.d(n,{default:()=>d});var o=t(5043),i=t(9252),r=t(5865),a=t(6446),l=t(7784),p=t(2518),s=t(8446),c=t(7094),x=t(579);const d=function(){const[e,n]=(0,o.useState)(""),[t,d]=(0,o.useState)(!1),[u,h]=(0,o.useState)(" "),g=async n=>{n.preventDefault();try{const n=await(0,c.b)(e);n.Success?d(!0):h(n.ErrorMessage)}catch(t){console.error("Error sending email",t)}};return(0,x.jsx)(i.A,{maxWidth:"xs",sx:{marginTop:"60px"},children:t?(0,x.jsx)(i.A,{maxWidth:"xs",sx:{marginTop:"135px"},children:(0,x.jsxs)(a.A,{sx:{marginTop:8,display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,x.jsx)(r.A,{variant:"h5",sx:{fontFamily:"Poppins",fontSize:"24px",fontWeight:700,lineHeight:"36px",letterSpacing:"0.30000001192092896px",textAlign:"center"},children:"Check your email"}),(0,x.jsxs)(r.A,{variant:"body2",color:"textSecondary",align:"center",sx:{fontFamily:"Poppins",fontSize:"14px",fontWeight:400,lineHeight:"19px",letterSpacing:"0.30000001192092896px",textAlign:"center"},children:["Please check your email address ",e," for instructions to reset your password."]}),(0,x.jsx)(p.A,{fullWidth:!0,variant:"contained",sx:{width:"Fill (325px)px",height:"Hug (44px)px",padding:"10px 12px 10px 12px",gap:"4px",borderRadius:"15px",background:"#5F9EA0",marginTop:3},onClick:g,children:"Resend Email"}),(0,x.jsx)(a.A,{mt:5,children:(0,x.jsxs)(r.A,{variant:"body2",color:"textSecondary",align:"center",sx:{marginTop:9},children:[(0,x.jsx)(s.A,{sx:{width:"Fill (325px)px",height:"Hug (24px)px",gap:"10px",opacity:"0px",textDecoration:"none",color:"#6BB2A1",marginTop:9,fontFamily:"Poppins",fontSize:"16px",fontWeight:400,lineHeight:"273px",textAlign:"center"},href:"/terms-of-use",children:"Terms of use"})," | ",(0,x.jsx)(s.A,{sx:{width:"Fill (325px)px",height:"Hug (24px)px",gap:"10px",opacity:"0px",textDecoration:"none",color:"#6BB2A1",marginTop:9,fontFamily:"Poppins",fontSize:"16px",fontWeight:400,lineHeight:"273px",textAlign:"center"},href:"/privacy-policy",children:"Privacy Policy"})]})})]})}):(0,x.jsxs)("form",{onSubmit:g,children:[(0,x.jsx)(r.A,{variant:"h5",gutterBottom:!0,sx:{fontFamily:"Syncopate",fontSize:"19px",fontWeight:700,lineHeight:"19.78px",letterSpacing:"0.3px",textAlign:"center",color:"#5F9EA0"},children:"QuickAdopt"}),(0,x.jsxs)(a.A,{sx:{marginTop:8,display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,x.jsx)(r.A,{variant:"h5",gutterBottom:!0,sx:{fontFamily:"Poppins",fontSize:"24px",fontWeight:"700",lineHeight:"36px",letterSpacing:"0.30000001192092896px",textAlign:"center"},children:"Reset your password"}),(0,x.jsx)(r.A,{variant:"body2",color:"textSecondary",align:"center",sx:{fontFamily:"Poppins",fontSize:"14px",fontWeight:400,lineHeight:"19px",letterSpacing:"0.30000001192092896px",textAlign:"center",color:"#222222"},children:"Enter your email address and we\u2019ll send you instructions to reset your password"}),(0,x.jsxs)(a.A,{sx:{width:"100%",mt:3},children:[(0,x.jsx)(r.A,{variant:"body2",sx:{fontFamily:"Poppins",fontSize:"16px",fontWeight:400,lineHeight:"23px",color:"#444444",mb:-3,textAlign:"left",marginLeft:"12px"},children:"Email"}),(0,x.jsx)(l.A,{margin:"normal",required:!0,fullWidth:!0,id:"email",name:"email",autoComplete:"email",value:e,onChange:e=>{n(e.target.value),h("")},autoFocus:!0,error:!!u,helperText:u,sx:{width:"Fill (325px)px",height:"Hug (48px)px",padding:"12px 0px 0px 0px",borderRadius:"6px 0px 0px 0px",border:"1px 0px 0px 0px",opacity:"0px","& .MuiOutlinedInput-notchedOutline":{borderRadius:"15px"}}})]}),(0,x.jsx)(p.A,{type:"submit",fullWidth:!0,variant:"contained",sx:{width:"Fill (325px)px",height:"Hug (44px)px",padding:"10px 12px 10px 12px",gap:"4px",borderRadius:"15px",opacity:"0px",background:"#5F9EA0"},children:"Continue"}),(0,x.jsx)(s.A,{href:"https://devapp.quickadopt.in",variant:"body2",align:"center",sx:{marginTop:4,color:"#6BB2A1",textDecoration:"none",fontFamily:"Poppins",fontSize:"16px",fontWeight:400,lineHeight:"24px",textAlign:"center"},children:"Back to login"}),(0,x.jsx)(a.A,{mt:5,children:(0,x.jsxs)(r.A,{variant:"body2",color:"textSecondary",align:"center",sx:{marginTop:9},children:[(0,x.jsx)(s.A,{sx:{width:"Fill (325px)px",height:"Hug (24px)px",gap:"10px",opacity:"0px",textDecoration:"none",color:"#6BB2A1",marginTop:9,fontFamily:"Poppins",fontSize:"16px",fontWeight:400,lineHeight:"24px",textAlign:"center"},href:"/terms-of-use",children:"Terms of use"})," | ",(0,x.jsx)(s.A,{sx:{width:"Fill (325px)px",height:"Hug (24px)px",gap:"10px",opacity:"0px",textDecoration:"none",color:"#6BB2A1",marginTop:9,fontFamily:"Poppins",fontSize:"16px",fontWeight:400,lineHeight:"24px",textAlign:"center"},href:"/privacy-policy",children:"Privacy Policy"})]})})]})]})})}}}]);
//# sourceMappingURL=593.594610ec.chunk.js.map