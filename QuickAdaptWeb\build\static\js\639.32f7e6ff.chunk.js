"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[639],{2029:(e,t,a)=>{a.d(t,{H:()=>n});const n=async function(e,t,a,n){let o=arguments.length>5?arguments[5]:void 0,r=arguments.length>6?arguments[6]:void 0;try{const a="https://devapp.quickadopt.in/connect/token",n=new URLSearchParams({grant_type:"password",client_id:"dap_extension",client_secret:"user_interaction",scope:"openid profile api1",username:e,password:t,authType:o,tenantid:r}),s={"Content-Type":"application/x-www-form-urlencoded"},i=await fetch(a,{method:"POST",body:n,headers:s});if(200===i.status){return await i.json()}return await i.json()}catch(s){throw console.error("An error occurred:",s),s}}},2639:(e,t,a)=>{a.r(t),a.d(t,{SAinitialsData:()=>g,default:()=>x});var n=a(5043),o=a(9252),r=a(6446),s=a(5865),i=a(7784),l=a(1787),c=a(7392),d=a(2518),u=a(8446),p=a(5677),m=a(9393),h=a(2446),A=a(3216),y=a(1673),v=a(2029),f=a(579);let g,b={};function x(){const{user:e,signOut:t,loggedOut:a}=(0,p.As)();const[x,w]=(0,n.useState)(!1),[S,N]=(0,n.useState)(""),[q,k]=(0,n.useState)(""),[j,C]=(0,n.useState)(null),[D,O]=(0,n.useState)(null),[B,F]=(0,n.useState)(void 0),[I,L]=(0,n.useState)(""),[P,M]=(0,n.useState)(""),[T,V]=(0,n.useState)(null),[E,K]=(0,n.useState)(null),U=(0,A.Zp)();return(0,f.jsxs)(o.A,{maxWidth:"sm",className:"qadpt-superadminlogin",children:[(0,f.jsx)(r.A,{mb:4,className:"qadpt-brand-logo",children:(0,f.jsx)(s.A,{variant:"h3",className:"qadpt-brand-logo-text",children:"QUICKADOPT"})}),(0,f.jsx)(r.A,{className:"qadpt-welcome-message",children:(0,f.jsx)(s.A,{variant:"h4",className:"qadpt-welcome-message-text",children:"Welcome back"})}),(0,f.jsxs)(r.A,{className:"qadpt-login-form",children:[(0,f.jsx)(s.A,{className:"qadpt-form-label",children:"Email"}),(0,f.jsx)(i.A,{required:!0,fullWidth:!0,type:"email",id:"email",name:"Email",autoComplete:"Email",autoFocus:!0,value:S,onChange:e=>{N(e.target.value)},placeholder:"eg, <EMAIL>",className:"qadpt-custom-input"}),(0,f.jsx)(s.A,{className:"qadpt-form-label",children:"Password"}),(0,f.jsx)(i.A,{required:!0,fullWidth:!0,type:x?"text":"password",id:"password",name:"password",autoComplete:"password",autoFocus:!0,value:q,onChange:e=>{k(e.target.value)},placeholder:"Enter your password",className:"qadpt-custom-input",InputProps:{endAdornment:(0,f.jsx)(l.A,{position:"end",children:(0,f.jsx)(c.A,{"aria-label":"toggle password visibility",onClick:()=>{w(!x)},edge:"end",children:(0,f.jsx)("i",{className:"fal ".concat(x?"fa-eye-slash":"fa-eye")})})})}}),D&&(0,f.jsx)(y.A,{error:!0,className:"qadpt-text-danger",children:D}),(0,f.jsx)(d.A,{type:"button",fullWidth:!0,variant:"contained",className:"qadpt-btn-default",onClick:async()=>{try{if(""===q||null==q)O("password should not be empty");else if(""===S||null==S)O("email should not be empty");else{const a=!0,n="MIGeMA0GCSqGSIb3DQEBAQUAA4GMADCBiAKBgHPeXxbMNqVNKTZvcbgLqb1itpw7U2wvk9n+qJ4MulRuZXJyVa7BlOboOlE8JZbYi7+i00xLShd7BpHKuRPacUnF2XKVEVbGrFSDKD3oOy9ji36y3HzBfrfiZzK9Q3YtIlVdBBLILd9PXSKQoAlYQqU73N1cKEZf9NOSzyZsnUpJAgMBAAE=",o=new h.v;o.setPublicKey(n);const r=(new Date).toISOString(),s=o.encrypt(q+"|"+r.trim()).toString();s||O("Enter correct password");const i="1",l=!0,c="",d=await(0,v.H)(S,a?s:q,i,l,c,"super","admin");if(d.access_token){var e,t;b["oidc-info"]=JSON.stringify(d),localStorage.setItem("access_token",d.access_token);const a=await(0,m.wO)();K(a?a.data:null);const n=null!==a&&void 0!==a&&a.data.FirstName&&null!==a&&void 0!==a&&a.data.FirstName?null===a||void 0===a?void 0:a.data.FirstName.substring(0,1).toUpperCase():"",o=null!==a&&void 0!==a&&a.data&&null!==a&&void 0!==a&&a.data.LastName?null===a||void 0===a?void 0:a.data.LastName.substring(0,1).toUpperCase():"";g=n+o,localStorage.setItem("userType",null!==(e=null===a||void 0===a||null===(t=a.data)||void 0===t?void 0:t.UserType)&&void 0!==e?e:""),b.user=JSON.stringify(null===a||void 0===a?void 0:a.data),localStorage.setItem("userInfo",JSON.stringify(b)),U("/superadmin/organizations",{state:{userDetail:null===a||void 0===a?void 0:a.data,organizationDetails:null===a||void 0===a?void 0:a.data.OrganizationId}})}else O(d.error_description)}}catch(D){console.error("Login failed:"),O("An unexpected error occurred.")}},children:"Continue"})]}),(0,f.jsx)(r.A,{mt:12,className:"qadpt-login-footer",children:(0,f.jsxs)(s.A,{variant:"body2",className:"qadpt-footer-text",children:[(0,f.jsx)(u.A,{href:"/terms-of-use",className:"qadpt-footer-link",children:"Terms of use"})," |",(0,f.jsx)(u.A,{href:"/privacy-policy",className:"qadpt-footer-link",children:"Privacy Policy"})]})})]})}},8446:(e,t,a)=>{a.d(t,{A:()=>q});var n=a(8587),o=a(8168),r=a(5043),s=a(9292),i=a(8610),l=a(6803),c=a(4535),d=a(8206),u=a(3574),p=a(5849),m=a(5865),h=a(2532),A=a(2372);function y(e){return(0,A.Ay)("MuiLink",e)}const v=(0,h.A)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]);var f=a(7162),g=a(7266);const b={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},x=e=>{let{theme:t,ownerState:a}=e;const n=(e=>b[e]||e)(a.color),o=(0,f.Yn)(t,"palette.".concat(n),!1)||a.color,r=(0,f.Yn)(t,"palette.".concat(n,"Channel"));return"vars"in t&&r?"rgba(".concat(r," / 0.4)"):(0,g.X4)(o,.4)};var w=a(579);const S=["className","color","component","onBlur","onFocus","TypographyClasses","underline","variant","sx"],N=(0,c.Ay)(m.A,{name:"MuiLink",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,t["underline".concat((0,l.A)(a.underline))],"button"===a.component&&t.button]}})((e=>{let{theme:t,ownerState:a}=e;return(0,o.A)({},"none"===a.underline&&{textDecoration:"none"},"hover"===a.underline&&{textDecoration:"none","&:hover":{textDecoration:"underline"}},"always"===a.underline&&(0,o.A)({textDecoration:"underline"},"inherit"!==a.color&&{textDecorationColor:x({theme:t,ownerState:a})},{"&:hover":{textDecorationColor:"inherit"}}),"button"===a.component&&{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(v.focusVisible)]:{outline:"auto"}})})),q=r.forwardRef((function(e,t){const a=(0,d.b)({props:e,name:"MuiLink"}),{className:c,color:m="primary",component:h="a",onBlur:A,onFocus:v,TypographyClasses:f,underline:g="always",variant:x="inherit",sx:q}=a,k=(0,n.A)(a,S),{isFocusVisibleRef:j,onBlur:C,onFocus:D,ref:O}=(0,u.A)(),[B,F]=r.useState(!1),I=(0,p.A)(t,O),L=(0,o.A)({},a,{color:m,component:h,focusVisible:B,underline:g,variant:x}),P=(e=>{const{classes:t,component:a,focusVisible:n,underline:o}=e,r={root:["root","underline".concat((0,l.A)(o)),"button"===a&&"button",n&&"focusVisible"]};return(0,i.A)(r,y,t)})(L);return(0,w.jsx)(N,(0,o.A)({color:m,className:(0,s.A)(P.root,c),classes:f,component:h,onBlur:e=>{C(e),!1===j.current&&F(!1),A&&A(e)},onFocus:e=>{D(e),!0===j.current&&F(!0),v&&v(e)},ref:I,ownerState:L,variant:x,sx:[...Object.keys(b).includes(m)?[]:[{color:m}],...Array.isArray(q)?q:[q]]},k))}))}}]);
//# sourceMappingURL=639.32f7e6ff.chunk.js.map