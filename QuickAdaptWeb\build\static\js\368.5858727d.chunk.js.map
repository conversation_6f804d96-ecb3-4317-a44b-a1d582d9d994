{"version": 3, "file": "static/js/368.5858727d.chunk.js", "mappings": "gLAOA,MAcA,EAdkBA,KAChB,MAAOC,EAAaC,IAAkBC,EAAAA,EAAAA,WAASC,EAAAA,EAAAA,OAC/C,OACEC,EAAAA,EAAAA,KAACC,EAAAA,EAAS,CAACC,SAAS,KAAIC,UAErBH,EAAAA,EAAAA,KAAA,OAAKI,UAAS,oBAAuBC,MAAO,CAACC,WAAWV,EAAY,QAAQ,IAAIO,UAC/EH,EAAAA,EAAAA,KAAA,UAAAG,UAAQH,EAAAA,EAAAA,KAAA,MAAAG,SAAI,mBAGF,C", "sources": ["components/dashboard/Dashboard.tsx"], "sourcesContent": ["  \r\n  \r\nimport React, { useState } from 'react';\r\n\r\nimport { isSidebarOpen } from \"../adminMenu/sidemenustate\";\r\nimport { Container } from '@mui/material';\r\n\r\nconst Dashboard = () => {\r\n  const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\r\n  return (\r\n    <Container maxWidth=\"xl\">\r\n   \r\n       <div className={`smooth-transition`} style={{marginLeft:sidebarOpen?\"170px\":\"\"}}>\r\n        <center><h1>Dashboard</h1></center>\r\n      </div>\r\n   \r\n      </Container>\r\n    );\r\n  };\r\n  \r\n\r\nexport default Dashboard"], "names": ["Dashboard", "sidebarOpen", "setSidebarOpen", "useState", "isSidebarOpen", "_jsx", "Container", "max<PERSON><PERSON><PERSON>", "children", "className", "style", "marginLeft"], "sourceRoot": ""}