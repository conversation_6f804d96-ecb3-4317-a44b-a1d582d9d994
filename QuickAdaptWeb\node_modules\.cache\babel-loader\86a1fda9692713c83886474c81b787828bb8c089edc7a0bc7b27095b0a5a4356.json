{"ast": null, "code": "import _objectSpread from\"E:/Code/Qadpt/quickadapt/QuickAdaptWeb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from\"react\";import{Dashboard,Audience,Tours,Announcements,Tooltips,webbanner,Checklist,Surveys,Hotspots,Settings,Filemanagement}from\"../../assets/icons/icons\";import{useNavigate,useLocation}from\"react-router-dom\";import{ThemeProvider,createTheme}from\"@mui/material/styles\";import CssBaseline from\"@mui/material/CssBaseline\";import Drawer from\"@mui/material/Drawer\";import List from\"@mui/material/List\";import ListItemIcon from\"@mui/material/ListItemIcon\";import Box from\"@mui/material/Box\";import Typography from\"@mui/material/Typography\";import Popup from\"../common/Popup\";import ButtonBase from'@mui/material/ButtonBase';import{isSidebarOpen,setSidebarOpen,subscribe}from\"../adminMenu/sidemenustate\";// import { useRtl } from \"../../RtlContext\";\nimport PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';import{useAuth}from\"../auth/AuthProvider\";import{useTranslation}from\"react-i18next\";import{useRtl}from\"../../RtlContext\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CursorTooltip=_ref=>{let{title,children}=_ref;const{isRtl}=useRtl();const[visible,setVisible]=useState(false);const[position,setPosition]=useState({x:0,y:0});const handleMouseEnter=()=>{setVisible(true);document.addEventListener('mousemove',handleMouseMove);};const handleMouseLeave=()=>{setVisible(false);document.removeEventListener('mousemove',handleMouseMove);};const handleMouseMove=e=>{const gap=15;setPosition({x:e.clientX+gap,y:e.clientY+gap});};return/*#__PURE__*/_jsxs(\"span\",{onMouseEnter:handleMouseEnter,onMouseLeave:handleMouseLeave,children:[children,visible&&/*#__PURE__*/_jsx(\"div\",{style:_objectSpread(_objectSpread({position:'fixed'},isRtl?{right:\"\".concat(window.innerWidth-position.x,\"px\")}:{left:\"\".concat(position.x,\"px\")}),{},{top:\"\".concat(position.y,\"px\"),boxShadow:'0 2px 4px rgba(0,0,0,0.1)',backgroundColor:'#fff',color:'#000',fontSize:'12px',width:'248px',borderRadius:'8px',padding:'16px',borderTop:'2px solid #5c80ac',zIndex:9999,pointerEvents:'none'}),children:title})]});};const SideMenu=_ref2=>{var _userDetails$UserType;let{selectedLanguageProp}=_ref2;const{t:translate}=useTranslation();const[activeItem,setActiveItem]=useState(\"\");const[openPopup,setOpenPopup]=useState(false);const[sidebarOpen,setLocalSidebarOpen]=useState(isSidebarOpen());const[translatedTitles,setTranslatedTitles]=useState({});const{signOut,userDetails}=useAuth();const[OrganizationId,setOrganizationId]=useState(userDetails===null||userDetails===void 0?void 0:userDetails.OrganizationId);const[user,setUser]=useState(null);const navigate=useNavigate();const location=useLocation();const[userType,setUserType]=useState((_userDetails$UserType=userDetails===null||userDetails===void 0?void 0:userDetails.UserType)!==null&&_userDetails$UserType!==void 0?_userDetails$UserType:\"\");const{isRtl}=useRtl();const themeWithDirection=createTheme({direction:isRtl?\"rtl\":\"ltr\"});useEffect(()=>{const userInfoString=localStorage.getItem(\"userInfo\");if(userInfoString){try{const userInfo=JSON.parse(userInfoString);if(userInfo['user']){const parsedUser=JSON.parse(userInfo['user']);setUser(parsedUser);if(parsedUser){var _parsedUser$Organizat;const OrgId=(_parsedUser$Organizat=parsedUser.OrganizationId)!==null&&_parsedUser$Organizat!==void 0?_parsedUser$Organizat:'';setOrganizationId(OrgId);}}}catch(error){console.error(\"Error parsing userInfo: \",error);}}else if(userDetails){setUser(userDetails);if(userDetails){var _userDetails$Organiza;const OrgId=(_userDetails$Organiza=userDetails.OrganizationId)!==null&&_userDetails$Organiza!==void 0?_userDetails$Organiza:'';setOrganizationId(OrgId);}}},[]);useEffect(()=>{const unsubscribe=subscribe(setLocalSidebarOpen);return()=>unsubscribe();},[]);const MenuClick=key=>{if(!OrganizationId){const userInfoString=localStorage.getItem(\"userInfo\");if(userInfoString){try{const userInfo=JSON.parse(userInfoString);if(userInfo['user']){const parsedUser=JSON.parse(userInfo['user']);setUser(parsedUser);if(parsedUser){var _parsedUser$Organizat2;const OrgId=(_parsedUser$Organizat2=parsedUser.OrganizationId)!==null&&_parsedUser$Organizat2!==void 0?_parsedUser$Organizat2:'';setOrganizationId(OrgId);}}}catch(error){console.error(\"Error parsing userInfo: \",error);}}}setActiveItem(key);switch(key.toLowerCase()){case\"dashboard\":return navigate(\"/modern-dashboard\");case\"audience\":return navigate(\"/audience\");case\"tours\":return navigate(\"/tours\");case\"announcements\":return navigate(\"/announcements\");case\"tooltips\":return navigate(\"/tooltips\");case\"banners\":return navigate(\"/banners\");case\"checklists\":return navigate(\"/checklists\");case\"surveys\":return navigate(\"/surveys\");case\"hotspots\":return navigate(\"/hotspots\");case\"settings\":if(userType.toLocaleLowerCase()==\"admin\")return navigate(\"\".concat(OrganizationId,\"/team\"));else return navigate(\"settings/multilingual\");case'filemanagement':return navigate(\"/filelist\");default:return navigate(\"/\");}};const toggleSidebar=()=>{setLocalSidebarOpen(!sidebarOpen);setSidebarOpen(!sidebarOpen);};if(location.pathname===\"/Builder\"){return null;}const menuItems=[{key:\"Dashboard\",icon:Dashboard,title:translate('Dashboard'),disabled:false,tooltip:'Get a unified view of insights and actions in one place.',access:['admin,user']},{key:\"Announcements\",icon:Announcements,title:translate('Announcements'),tooltip:'Helps to communicate important updates, notifications, or messages.',access:['admin,user']},{key:\"Banners\",icon:webbanner,title:translate('Banners'),tooltip:'Displays notifications at the top of the screen.',access:['admin,user']},{key:\"Tooltips\",icon:Tooltips,title:translate('Tooltips'),tooltip:'Provide quick explanations, tips, or instructions of the tools,',access:['admin,user']},{key:\"Hotspots\",icon:Hotspots,title:translate('Hotspots'),tooltip:'Interactive areas to draw attention to important features, actions, or guidance.',access:['admin,user']},{key:\"Tours\",icon:Tours,title:translate('Product Tour'),tooltip:'Step-by-step guides to navigate and understand key features.',access:['admin,user']},{key:\"Checklists\",icon:Checklist,title:translate('Checklists'),tooltip:'Task lists that guide users through a series of steps or actions.',access:['admin,user']},{key:\"Filemanagement\",icon:Filemanagement,title:translate('File Management'),tooltip:'Store and reuse media assets across multiple interactions.',access:['user']},{key:\"Settings\",icon:Settings,title:translate('Settings'),tooltip:'Configure application settings and preferences.',access:['admin,user']},{key:\"Surveys\",icon:Surveys,title:translate('Surveys'),disabled:true,tooltip:'Interactive forms or questionnaires designed to collect feedback, insights, or opinions',access:['admin,user']},{key:\"Audience\",icon:Audience,title:translate('Audience'),disabled:true,tooltip:'Categorize users into segments for precise targeting.',access:['admin,user']}];return/*#__PURE__*/_jsxs(ThemeProvider,{theme:themeWithDirection,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-side-menu\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\"},children:[/*#__PURE__*/_jsx(Drawer,{variant:\"persistent\",anchor:isRtl?\"right\":\"left\",open:sidebarOpen,className:\"qadpt-smenu\",sx:{\"& .MuiDrawer-paper\":{direction:isRtl?\"rtl\":\"ltr\"}},children:/*#__PURE__*/_jsx(PerfectScrollbar,{children:/*#__PURE__*/_jsxs(List,{className:\"qadpt-smenu-list\",children:[menuItems.map((_ref3,index)=>{let{key,icon,title,disabled,tooltip,access}=_ref3;return!disabled&&!access.includes(userType.toLocaleLowerCase())&&/*#__PURE__*/_jsx(React.Fragment,{children:/*#__PURE__*/_jsx(CursorTooltip,{title:tooltip,children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(ButtonBase,{className:\"qadpt-sm-item \".concat(activeItem===key?'active':'inactive',\" \").concat(disabled?'disabled':''),onClick:()=>MenuClick(key),disabled:disabled,disableRipple:true,children:/*#__PURE__*/_jsx(Box,{sx:{textAlign:'left',width:'100%'},children:!disabled&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',opacity:disabled?0.5:1,'& p':{fontSize:'14px'}},children:[/*#__PURE__*/_jsx(ListItemIcon,{className:\"qadpt-sm-icon \".concat(activeItem===key?'active':'inactive'),sx:_objectSpread({},key==='Banners'&&{marginLeft:'-3px'}),children:/*#__PURE__*/_jsx(\"img\",{src:icon,alt:key})}),/*#__PURE__*/_jsx(Typography,{children:translatedTitles[title]||title})]})})})})})},key);}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-cmgitems\",children:[/*#__PURE__*/_jsx(Typography,{className:\"cmg-soon\",sx:{textAlign:'start',opacity:1},children:\"Coming soon\"}),menuItems.map((_ref4,index)=>{let{key,icon,title,disabled,tooltip}=_ref4;return disabled&&/*#__PURE__*/_jsx(React.Fragment,{children:/*#__PURE__*/_jsx(CursorTooltip,{title:tooltip,children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(ButtonBase,{className:\"qadpt-sm-item \".concat(activeItem===key?'active':'inactive',\" \").concat(disabled?'disabled':''),onClick:()=>MenuClick(key),disabled:disabled,disableRipple:true,children:/*#__PURE__*/_jsx(Box,{sx:{textAlign:'left',width:'100%'},children:disabled&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',opacity:disabled?0.5:1,'& p':{fontSize:'14px'}},children:[/*#__PURE__*/_jsx(ListItemIcon,{className:\"qadpt-sm-icon \".concat(activeItem===key?'active':'inactive'),sx:_objectSpread({},key==='Banners'&&{marginLeft:'-3px'}),children:/*#__PURE__*/_jsx(\"img\",{src:icon,alt:key})}),/*#__PURE__*/_jsx(Typography,{children:translatedTitles[title]||title})]})})})})})},key);})]})]})})}),openPopup&&/*#__PURE__*/_jsx(Popup,{onClose:()=>setOpenPopup(false),onOk:()=>setOpenPopup(false),type:\"Apply\",title:\"Log out from QuickAdopt\",description:\"Do you really want to logout?\",button1:\"Cancel\",button2:\"Logout\"})]})})]});};export default SideMenu;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dashboard", "Audience", "Tours", "Announcements", "Tooltips", "webbanner", "Checklist", "Surveys", "Hotspots", "Settings", "Filemanagement", "useNavigate", "useLocation", "ThemeProvider", "createTheme", "CssBaseline", "Drawer", "List", "ListItemIcon", "Box", "Typography", "Popup", "ButtonBase", "isSidebarOpen", "setSidebarOpen", "subscribe", "PerfectScrollbar", "useAuth", "useTranslation", "useRtl", "jsx", "_jsx", "jsxs", "_jsxs", "CursorTooltip", "_ref", "title", "children", "isRtl", "visible", "setVisible", "position", "setPosition", "x", "y", "handleMouseEnter", "document", "addEventListener", "handleMouseMove", "handleMouseLeave", "removeEventListener", "e", "gap", "clientX", "clientY", "onMouseEnter", "onMouseLeave", "style", "_objectSpread", "right", "concat", "window", "innerWidth", "left", "top", "boxShadow", "backgroundColor", "color", "fontSize", "width", "borderRadius", "padding", "borderTop", "zIndex", "pointerEvents", "SideMenu", "_ref2", "_userDetails$UserType", "selectedLanguageProp", "t", "translate", "activeItem", "setActiveItem", "openPopup", "set<PERSON>penPop<PERSON>", "sidebarOpen", "setLocalSidebarOpen", "translated<PERSON><PERSON><PERSON>", "setTranslatedTitles", "signOut", "userDetails", "OrganizationId", "setOrganizationId", "user", "setUser", "navigate", "location", "userType", "setUserType", "UserType", "themeWithDirection", "direction", "userInfoString", "localStorage", "getItem", "userInfo", "JSON", "parse", "parsedUser", "_parsedUser$Organizat", "OrgId", "error", "console", "_userDetails$Organiza", "unsubscribe", "MenuClick", "key", "_parsedUser$Organizat2", "toLowerCase", "toLocaleLowerCase", "toggleSidebar", "pathname", "menuItems", "icon", "disabled", "tooltip", "access", "theme", "className", "sx", "display", "variant", "anchor", "open", "map", "_ref3", "index", "includes", "Fragment", "onClick", "disable<PERSON><PERSON><PERSON>", "textAlign", "alignItems", "opacity", "marginLeft", "src", "alt", "_ref4", "onClose", "onOk", "type", "description", "button1", "button2"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/adminMenu/sideMenu.tsx"], "sourcesContent": ["import React, { useState, useEffect, useTransition, ReactNode } from \"react\";\r\nimport {\r\n  Dashboard,\r\n  Audience,\r\n  Tours,\r\n  Announcements,\r\n  Tooltips,\r\n  Banners,\r\n  webbanner,\r\n  Checklist,\r\n  Surveys,\r\n  Hotspots,\r\n  Settings,\r\n  Auditlog,\r\n  Filemanagement,\r\n  Cursor\r\n} from \"../../assets/icons/icons\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { useTheme, ThemeProvider, createTheme } from \"@mui/material/styles\";\r\nimport CssBaseline from \"@mui/material/CssBaseline\";\r\nimport Drawer from \"@mui/material/Drawer\";\r\nimport List from \"@mui/material/List\";\r\nimport ListItemIcon from \"@mui/material/ListItemIcon\";\r\nimport IconButton from \"@mui/material/IconButton\";\r\nimport Switch from \"@mui/material/Switch\";\r\nimport Box from \"@mui/material/Box\";\r\nimport MenuIcon from \"@mui/icons-material/Menu\";\r\nimport Typography from \"@mui/material/Typography\";\r\nimport Popup from \"../common/Popup\";\r\nimport ButtonBase from '@mui/material/ButtonBase';\r\nimport { isSidebarOpen, setSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\n// import { useRtl } from \"../../RtlContext\";\r\nimport { Divider, Tooltip as MuiTooltip } from \"@mui/material\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport { User } from \"../../models/User\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { useRtl } from \"../../RtlContext\";\r\n\r\n\r\ninterface CursorTooltipProps {\r\n  title: string;\r\n  children: ReactNode;\r\n}\r\n\r\nconst CursorTooltip: React.FC<CursorTooltipProps> = ({ title, children }) => {\r\n   const { isRtl } = useRtl();\r\n  const [visible, setVisible] = useState<boolean>(false);\r\n  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });\r\n\r\n  const handleMouseEnter = (): void => {\r\n    setVisible(true);\r\n    document.addEventListener('mousemove', handleMouseMove);\r\n  };\r\n\r\n  const handleMouseLeave = (): void => {\r\n    setVisible(false);\r\n    document.removeEventListener('mousemove', handleMouseMove);\r\n  };\r\n\r\n  const handleMouseMove = (e: MouseEvent): void => {\r\n    const gap = 15; \r\n    setPosition({\r\n      x: e.clientX + gap,\r\n      y: e.clientY + gap\r\n    });\r\n  };\r\n\r\n  return (\r\n    <span onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>\r\n      {children}\r\n      {visible && (\r\n        <div\r\n          style={{\r\n            position: 'fixed',\r\n            // left: `${position.x}px`,\r\n            // [isRtl ? 'right' : 'left']: `${position.x}px`,   ---- need to enable for RTL once preoperty values came\r\n               ...(isRtl ? { right: `${window.innerWidth - position.x}px` } : { left: `${position.x}px` }),\r\n            top: `${position.y}px`,\r\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n            backgroundColor: '#fff',\r\n            color: '#000',\r\n            fontSize: '12px',\r\n            width: '248px',\r\n            borderRadius: '8px',\r\n            padding: '16px',\r\n            borderTop: '2px solid #5c80ac',\r\n            zIndex: 9999,\r\n            pointerEvents: 'none'\r\n          }}\r\n        >\r\n          {title}\r\n        </div>\r\n      )}\r\n    </span>\r\n  );\r\n};\r\n\r\ninterface SideMenuProps {\r\n  selectedLanguageProp: string;\r\n}\r\n\r\nconst SideMenu: React.FC<SideMenuProps> = ({ selectedLanguageProp }) => {\r\n  const { t: translate } = useTranslation();\r\n  const [activeItem, setActiveItem] = useState<string>(\"\");\r\n  const [openPopup, setOpenPopup] = useState(false);\r\n  const [sidebarOpen, setLocalSidebarOpen] = useState(isSidebarOpen());\r\n  const [translatedTitles, setTranslatedTitles] = useState<{ [key: string]: string }>({});\r\n  const { signOut, userDetails } = useAuth();\r\n  const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId);\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [userType, setUserType] = useState(userDetails?.UserType ?? \"\");\r\n\r\n  const { isRtl } = useRtl();\r\n\r\n  const themeWithDirection = createTheme({\r\n    direction: isRtl ? \"rtl\" : \"ltr\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    const userInfoString = localStorage.getItem(\"userInfo\");  \r\n    if (userInfoString) { \r\n      try {\r\n        const userInfo = JSON.parse(userInfoString);  \r\n        if (userInfo['user']) {\r\n          const parsedUser = JSON.parse(userInfo['user']);\r\n          setUser(parsedUser);  \r\n          if (parsedUser) {\r\n            const OrgId = parsedUser.OrganizationId ?? '';\r\n            setOrganizationId(OrgId);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error parsing userInfo: \", error);\r\n      }\r\n    }\r\n    else if (userDetails) {\r\n      setUser(userDetails);  \r\n      if (userDetails) {\r\n        const OrgId = userDetails.OrganizationId ?? '';\r\n        setOrganizationId(OrgId);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const unsubscribe = subscribe(setLocalSidebarOpen);\r\n    return () => unsubscribe();\r\n  }, []);\r\n\r\n  const MenuClick = (key: string) => {    \r\n    if (!OrganizationId)\r\n    {\r\n      const userInfoString = localStorage.getItem(\"userInfo\");  \r\n      if (userInfoString) { \r\n        try {\r\n          const userInfo = JSON.parse(userInfoString);  \r\n          if (userInfo['user']) {\r\n            const parsedUser = JSON.parse(userInfo['user']);\r\n            setUser(parsedUser);  \r\n            if (parsedUser) {\r\n              const OrgId = parsedUser.OrganizationId ?? '';\r\n              setOrganizationId(OrgId);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error parsing userInfo: \", error);\r\n        }\r\n      }\r\n    }\r\n    \r\n    setActiveItem(key);\r\n    switch (key.toLowerCase()) {\r\n      case \"dashboard\":\r\n            return navigate(\"/modern-dashboard\");\r\n      case \"audience\":\r\n        return navigate(\"/audience\");\r\n      case \"tours\":\r\n        return navigate(\"/tours\");\r\n      case \"announcements\":\r\n        return navigate(\"/announcements\");\r\n      case \"tooltips\":\r\n        return navigate(\"/tooltips\");\r\n      case \"banners\":\r\n        return navigate(\"/banners\");\r\n      case \"checklists\":\r\n        return navigate(\"/checklists\");\r\n      case \"surveys\":\r\n        return navigate(\"/surveys\");\r\n      case \"hotspots\":\r\n        return navigate(\"/hotspots\");\r\n      case \"settings\":\r\n        if(userType.toLocaleLowerCase() == \"admin\")\r\n          return navigate(`${OrganizationId}/team`); \r\n        else\r\n          return navigate(`settings/multilingual`); \r\n      case 'filemanagement':\r\n        return navigate(\"/filelist\");\r\n      default:\r\n        return navigate(\"/\");\r\n    }\r\n  };\r\n\r\n  const toggleSidebar = () => {\r\n    setLocalSidebarOpen(!sidebarOpen);\r\n    setSidebarOpen(!sidebarOpen);\r\n  };\r\n\r\n  if (location.pathname === \"/Builder\") {\r\n    return null;\r\n  }\r\n\r\n  const menuItems = [\r\n    { key: \"Dashboard\", icon: Dashboard, title: translate('Dashboard'), disabled: false, tooltip: 'Get a unified view of insights and actions in one place.'  , access: ['admin,user'] },\r\n    { key: \"Announcements\", icon: Announcements, title: translate('Announcements'), tooltip: 'Helps to communicate important updates, notifications, or messages.' , access: ['admin,user'] },\r\n    { key: \"Banners\", icon: webbanner, title: translate('Banners'), tooltip: 'Displays notifications at the top of the screen.'  , access: ['admin,user'] },\r\n    { key: \"Tooltips\", icon: Tooltips, title: translate('Tooltips'), tooltip: 'Provide quick explanations, tips, or instructions of the tools,'  , access: ['admin,user'] },\r\n    { key: \"Hotspots\", icon: Hotspots, title: translate('Hotspots'), tooltip: 'Interactive areas to draw attention to important features, actions, or guidance.'  , access: ['admin,user'] },\r\n    { key: \"Tours\", icon: Tours, title: translate('Product Tour'), tooltip: 'Step-by-step guides to navigate and understand key features.'  , access: ['admin,user'] },\r\n    { key: \"Checklists\", icon: Checklist, title: translate('Checklists'), tooltip: 'Task lists that guide users through a series of steps or actions.'  , access: ['admin,user'] },\r\n    { key: \"Filemanagement\", icon: Filemanagement, title: translate('File Management'), tooltip: 'Store and reuse media assets across multiple interactions.'  , access: ['user'] },\r\n    { key: \"Settings\", icon: Settings, title: translate('Settings'), tooltip: 'Configure application settings and preferences.'  , access: ['admin,user'] },\r\n    { key: \"Surveys\", icon: Surveys, title: translate('Surveys'), disabled: true, tooltip: 'Interactive forms or questionnaires designed to collect feedback, insights, or opinions'  , access: ['admin,user'] },\r\n    { key: \"Audience\", icon: Audience, title: translate('Audience'), disabled: true, tooltip: 'Categorize users into segments for precise targeting.'  , access: ['admin,user'] },\r\n  ];\r\n  \r\n  return (\r\n    <ThemeProvider theme={themeWithDirection}>\r\n      <CssBaseline />\r\n      <div className=\"qadpt-side-menu\">\r\n      <Box sx={{ display: \"flex\" }}>\r\n        <Drawer\r\n          variant=\"persistent\"\r\n          anchor={isRtl ? \"right\" : \"left\"}\r\n          open={sidebarOpen}\r\n          className=\"qadpt-smenu\"\r\n          sx={{\r\n            \"& .MuiDrawer-paper\": {\r\n              direction: isRtl ? \"rtl\" : \"ltr\",\r\n            },\r\n          }}\r\n        >\r\n          <PerfectScrollbar>\r\n              <List className=\"qadpt-smenu-list\">\r\n              \r\n                {menuItems.map(({ key, icon, title, disabled, tooltip,access }, index) => (\r\n              !disabled && !access.includes(userType.toLocaleLowerCase()) &&\r\n              <React.Fragment key={key}>\r\n                <CursorTooltip title={tooltip}>\r\n                  <span>\r\n                    <ButtonBase \r\n                      className={`qadpt-sm-item ${activeItem === key ? 'active' : 'inactive'} ${disabled ? 'disabled' : ''}`}\r\n                      onClick={() => MenuClick(key)}\r\n\t\t\t\t\t\t\t\tdisabled={disabled}\r\n\t\t\t\t\t\t\t\tdisableRipple\r\n                    >\r\n                      <Box sx={{ textAlign: 'left', width: '100%' }}>\r\n                        {!disabled&&(\r\n                        <Box\r\n                          sx={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            opacity: disabled ? 0.5 : 1,\r\n                            '& p': {\r\n                              fontSize: '14px', \r\n                            },    \r\n                          }}\r\n                        >\r\n                          <ListItemIcon\r\n                            className={`qadpt-sm-icon ${activeItem === key ? 'active' : 'inactive'}`}\r\n                            sx={{\r\n                              ...(key === 'Banners' && { marginLeft: '-3px' }),\r\n                            }}\r\n                          >\r\n                            <img src={icon} alt={key} />\r\n                          </ListItemIcon>\r\n\r\n                          <Typography>\r\n                            {translatedTitles[title] || title}\r\n                          </Typography>\r\n                        </Box>)}\r\n                      </Box>\r\n                    </ButtonBase>\r\n                  </span>\r\n                </CursorTooltip>\r\n              </React.Fragment>\r\n                ))}\r\n                <Box className = \"qadpt-cmgitems\">\r\n                <Typography\r\n                            className=\"cmg-soon\"\r\n                            sx={{\r\n                              textAlign: 'start',\r\n                              opacity: 1, \r\n                            }}\r\n                          >\r\n                            Coming soon\r\n                  </Typography>\r\n                {menuItems.map(({ key, icon, title, disabled, tooltip }, index) => (\r\n              disabled &&\r\n              <React.Fragment key={key}>\r\n                <CursorTooltip title={tooltip}>\r\n                  <span>\r\n                    <ButtonBase \r\n                      className={`qadpt-sm-item ${activeItem === key ? 'active' : 'inactive'} ${disabled ? 'disabled' : ''}`}\r\n                      onClick={() => MenuClick(key)}\r\n\t\t\t\t\t\t\t\tdisabled={disabled}\r\n\t\t\t\t\t\t\t\tdisableRipple\r\n                    >\r\n                      <Box sx={{ textAlign: 'left', width: '100%' }}>\r\n                        {disabled && (\r\n                        <Box\r\n                          sx={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            opacity: disabled ? 0.5 : 1,\r\n                            '& p': {\r\n                              fontSize: '14px', \r\n                            },    \r\n                          }}\r\n                        >\r\n                          <ListItemIcon\r\n                            className={`qadpt-sm-icon ${activeItem === key ? 'active' : 'inactive'}`}\r\n                            sx={{\r\n                              ...(key === 'Banners' && { marginLeft: '-3px' }),\r\n                            }}\r\n                          >\r\n                            <img src={icon} alt={key} />\r\n                          </ListItemIcon>\r\n\r\n                          <Typography>\r\n                            {translatedTitles[title] || title}\r\n                          </Typography>\r\n                        </Box>)}\r\n                      </Box>\r\n                    </ButtonBase>\r\n                  </span>\r\n                </CursorTooltip>\r\n              </React.Fragment>\r\n                ))}\r\n                </Box>\r\n          </List>\r\n          </PerfectScrollbar>  \r\n        </Drawer>\r\n        \r\n        {openPopup && (\r\n          <Popup\r\n            onClose={() => setOpenPopup(false)}\r\n            onOk={() => setOpenPopup(false)}\r\n            type=\"Apply\"\r\n            title=\"Log out from QuickAdopt\"\r\n            description=\"Do you really want to logout?\"\r\n            button1=\"Cancel\"\r\n            button2=\"Logout\"\r\n          />\r\n        )}\r\n      </Box>\r\n      </div>\r\n    </ThemeProvider>\r\n  );\r\n};\r\n\r\nexport default SideMenu;"], "mappings": "2HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAkC,OAAO,CAC5E,OACEC,SAAS,CACTC,QAAQ,CACRC,KAAK,CACLC,aAAa,CACbC,QAAQ,CAERC,SAAS,CACTC,SAAS,CACTC,OAAO,CACPC,QAAQ,CACRC,QAAQ,CAERC,cAAc,KAET,0BAA0B,CACjC,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAAmBC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CAC3E,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,IAAI,KAAM,oBAAoB,CACrC,MAAO,CAAAC,YAAY,KAAM,4BAA4B,CAGrD,MAAO,CAAAC,GAAG,KAAM,mBAAmB,CAEnC,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,MAAO,CAAAC,KAAK,KAAM,iBAAiB,CACnC,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,OAASC,aAAa,CAAEC,cAAc,CAAEC,SAAS,KAAQ,4BAA4B,CACrF;AAEA,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CACpD,OAASC,OAAO,KAAQ,sBAAsB,CAE9C,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,MAAM,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ1C,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAAyB,IAAxB,CAAEC,KAAK,CAAEC,QAAS,CAAC,CAAAF,IAAA,CACrE,KAAM,CAAEG,KAAM,CAAC,CAAGT,MAAM,CAAC,CAAC,CAC3B,KAAM,CAACU,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAU,KAAK,CAAC,CACtD,KAAM,CAAC2C,QAAQ,CAAEC,WAAW,CAAC,CAAG5C,QAAQ,CAA2B,CAAE6C,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAElF,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAY,CACnCL,UAAU,CAAC,IAAI,CAAC,CAChBM,QAAQ,CAACC,gBAAgB,CAAC,WAAW,CAAEC,eAAe,CAAC,CACzD,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAY,CACnCT,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACI,mBAAmB,CAAC,WAAW,CAAEF,eAAe,CAAC,CAC5D,CAAC,CAED,KAAM,CAAAA,eAAe,CAAIG,CAAa,EAAW,CAC/C,KAAM,CAAAC,GAAG,CAAG,EAAE,CACdV,WAAW,CAAC,CACVC,CAAC,CAAEQ,CAAC,CAACE,OAAO,CAAGD,GAAG,CAClBR,CAAC,CAAEO,CAAC,CAACG,OAAO,CAAGF,GACjB,CAAC,CAAC,CACJ,CAAC,CAED,mBACEnB,KAAA,SAAMsB,YAAY,CAAEV,gBAAiB,CAACW,YAAY,CAAEP,gBAAiB,CAAAZ,QAAA,EAClEA,QAAQ,CACRE,OAAO,eACNR,IAAA,QACE0B,KAAK,CAAAC,aAAA,CAAAA,aAAA,EACHjB,QAAQ,CAAE,OAAO,EAGVH,KAAK,CAAG,CAAEqB,KAAK,IAAAC,MAAA,CAAKC,MAAM,CAACC,UAAU,CAAGrB,QAAQ,CAACE,CAAC,MAAK,CAAC,CAAG,CAAEoB,IAAI,IAAAH,MAAA,CAAKnB,QAAQ,CAACE,CAAC,MAAK,CAAC,MAC7FqB,GAAG,IAAAJ,MAAA,CAAKnB,QAAQ,CAACG,CAAC,MAAI,CACtBqB,SAAS,CAAE,2BAA2B,CACtCC,eAAe,CAAE,MAAM,CACvBC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,OAAO,CACdC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfC,SAAS,CAAE,mBAAmB,CAC9BC,MAAM,CAAE,IAAI,CACZC,aAAa,CAAE,MAAM,EACrB,CAAArC,QAAA,CAEDD,KAAK,CACH,CACN,EACG,CAAC,CAEX,CAAC,CAMD,KAAM,CAAAuC,QAAiC,CAAGC,KAAA,EAA8B,KAAAC,qBAAA,IAA7B,CAAEC,oBAAqB,CAAC,CAAAF,KAAA,CACjE,KAAM,CAAEG,CAAC,CAAEC,SAAU,CAAC,CAAGpD,cAAc,CAAC,CAAC,CACzC,KAAM,CAACqD,UAAU,CAAEC,aAAa,CAAC,CAAGpF,QAAQ,CAAS,EAAE,CAAC,CACxD,KAAM,CAACqF,SAAS,CAAEC,YAAY,CAAC,CAAGtF,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACuF,WAAW,CAAEC,mBAAmB,CAAC,CAAGxF,QAAQ,CAACyB,aAAa,CAAC,CAAC,CAAC,CACpE,KAAM,CAACgE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1F,QAAQ,CAA4B,CAAC,CAAC,CAAC,CACvF,KAAM,CAAE2F,OAAO,CAAEC,WAAY,CAAC,CAAG/D,OAAO,CAAC,CAAC,CAC1C,KAAM,CAACgE,cAAc,CAAEC,iBAAiB,CAAC,CAAG9F,QAAQ,CAAC4F,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEC,cAAc,CAAC,CACjF,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGhG,QAAQ,CAAc,IAAI,CAAC,CACnD,KAAM,CAAAiG,QAAQ,CAAGpF,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqF,QAAQ,CAAGpF,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACqF,QAAQ,CAAEC,WAAW,CAAC,CAAGpG,QAAQ,EAAA+E,qBAAA,CAACa,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAES,QAAQ,UAAAtB,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAErE,KAAM,CAAEvC,KAAM,CAAC,CAAGT,MAAM,CAAC,CAAC,CAE1B,KAAM,CAAAuE,kBAAkB,CAAGtF,WAAW,CAAC,CACrCuF,SAAS,CAAE/D,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAC,CAAC,CAEFvC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuG,cAAc,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CACvD,GAAIF,cAAc,CAAE,CAClB,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC,CAC3C,GAAIG,QAAQ,CAAC,MAAM,CAAC,CAAE,CACpB,KAAM,CAAAG,UAAU,CAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC,CAC/CX,OAAO,CAACc,UAAU,CAAC,CACnB,GAAIA,UAAU,CAAE,KAAAC,qBAAA,CACd,KAAM,CAAAC,KAAK,EAAAD,qBAAA,CAAGD,UAAU,CAACjB,cAAc,UAAAkB,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC7CjB,iBAAiB,CAACkB,KAAK,CAAC,CAC1B,CACF,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CACF,CAAC,IACI,IAAIrB,WAAW,CAAE,CACpBI,OAAO,CAACJ,WAAW,CAAC,CACpB,GAAIA,WAAW,CAAE,KAAAuB,qBAAA,CACf,KAAM,CAAAH,KAAK,EAAAG,qBAAA,CAAGvB,WAAW,CAACC,cAAc,UAAAsB,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC9CrB,iBAAiB,CAACkB,KAAK,CAAC,CAC1B,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN/G,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmH,WAAW,CAAGzF,SAAS,CAAC6D,mBAAmB,CAAC,CAClD,MAAO,IAAM4B,WAAW,CAAC,CAAC,CAC5B,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,SAAS,CAAIC,GAAW,EAAK,CACjC,GAAI,CAACzB,cAAc,CACnB,CACE,KAAM,CAAAW,cAAc,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CACvD,GAAIF,cAAc,CAAE,CAClB,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC,CAC3C,GAAIG,QAAQ,CAAC,MAAM,CAAC,CAAE,CACpB,KAAM,CAAAG,UAAU,CAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC,CAC/CX,OAAO,CAACc,UAAU,CAAC,CACnB,GAAIA,UAAU,CAAE,KAAAS,sBAAA,CACd,KAAM,CAAAP,KAAK,EAAAO,sBAAA,CAAGT,UAAU,CAACjB,cAAc,UAAA0B,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC7CzB,iBAAiB,CAACkB,KAAK,CAAC,CAC1B,CACF,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CACF,CACF,CAEA7B,aAAa,CAACkC,GAAG,CAAC,CAClB,OAAQA,GAAG,CAACE,WAAW,CAAC,CAAC,EACvB,IAAK,WAAW,CACV,MAAO,CAAAvB,QAAQ,CAAC,mBAAmB,CAAC,CAC1C,IAAK,UAAU,CACb,MAAO,CAAAA,QAAQ,CAAC,WAAW,CAAC,CAC9B,IAAK,OAAO,CACV,MAAO,CAAAA,QAAQ,CAAC,QAAQ,CAAC,CAC3B,IAAK,eAAe,CAClB,MAAO,CAAAA,QAAQ,CAAC,gBAAgB,CAAC,CACnC,IAAK,UAAU,CACb,MAAO,CAAAA,QAAQ,CAAC,WAAW,CAAC,CAC9B,IAAK,SAAS,CACZ,MAAO,CAAAA,QAAQ,CAAC,UAAU,CAAC,CAC7B,IAAK,YAAY,CACf,MAAO,CAAAA,QAAQ,CAAC,aAAa,CAAC,CAChC,IAAK,SAAS,CACZ,MAAO,CAAAA,QAAQ,CAAC,UAAU,CAAC,CAC7B,IAAK,UAAU,CACb,MAAO,CAAAA,QAAQ,CAAC,WAAW,CAAC,CAC9B,IAAK,UAAU,CACb,GAAGE,QAAQ,CAACsB,iBAAiB,CAAC,CAAC,EAAI,OAAO,CACxC,MAAO,CAAAxB,QAAQ,IAAAnC,MAAA,CAAI+B,cAAc,SAAO,CAAC,CAAC,IAE1C,OAAO,CAAAI,QAAQ,wBAAwB,CAAC,CAC5C,IAAK,gBAAgB,CACnB,MAAO,CAAAA,QAAQ,CAAC,WAAW,CAAC,CAC9B,QACE,MAAO,CAAAA,QAAQ,CAAC,GAAG,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAAyB,aAAa,CAAGA,CAAA,GAAM,CAC1BlC,mBAAmB,CAAC,CAACD,WAAW,CAAC,CACjC7D,cAAc,CAAC,CAAC6D,WAAW,CAAC,CAC9B,CAAC,CAED,GAAIW,QAAQ,CAACyB,QAAQ,GAAK,UAAU,CAAE,CACpC,MAAO,KAAI,CACb,CAEA,KAAM,CAAAC,SAAS,CAAG,CAChB,CAAEN,GAAG,CAAE,WAAW,CAAEO,IAAI,CAAE3H,SAAS,CAAEoC,KAAK,CAAE4C,SAAS,CAAC,WAAW,CAAC,CAAE4C,QAAQ,CAAE,KAAK,CAAEC,OAAO,CAAE,0DAA0D,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CACpL,CAAEV,GAAG,CAAE,eAAe,CAAEO,IAAI,CAAExH,aAAa,CAAEiC,KAAK,CAAE4C,SAAS,CAAC,eAAe,CAAC,CAAE6C,OAAO,CAAE,qEAAqE,CAAGC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CACzL,CAAEV,GAAG,CAAE,SAAS,CAAEO,IAAI,CAAEtH,SAAS,CAAE+B,KAAK,CAAE4C,SAAS,CAAC,SAAS,CAAC,CAAE6C,OAAO,CAAE,kDAAkD,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CACvJ,CAAEV,GAAG,CAAE,UAAU,CAAEO,IAAI,CAAEvH,QAAQ,CAAEgC,KAAK,CAAE4C,SAAS,CAAC,UAAU,CAAC,CAAE6C,OAAO,CAAE,iEAAiE,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CACvK,CAAEV,GAAG,CAAE,UAAU,CAAEO,IAAI,CAAEnH,QAAQ,CAAE4B,KAAK,CAAE4C,SAAS,CAAC,UAAU,CAAC,CAAE6C,OAAO,CAAE,kFAAkF,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CACxL,CAAEV,GAAG,CAAE,OAAO,CAAEO,IAAI,CAAEzH,KAAK,CAAEkC,KAAK,CAAE4C,SAAS,CAAC,cAAc,CAAC,CAAE6C,OAAO,CAAE,8DAA8D,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CAClK,CAAEV,GAAG,CAAE,YAAY,CAAEO,IAAI,CAAErH,SAAS,CAAE8B,KAAK,CAAE4C,SAAS,CAAC,YAAY,CAAC,CAAE6C,OAAO,CAAE,mEAAmE,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CAC9K,CAAEV,GAAG,CAAE,gBAAgB,CAAEO,IAAI,CAAEjH,cAAc,CAAE0B,KAAK,CAAE4C,SAAS,CAAC,iBAAiB,CAAC,CAAE6C,OAAO,CAAE,4DAA4D,CAAIC,MAAM,CAAE,CAAC,MAAM,CAAE,CAAC,CAC/K,CAAEV,GAAG,CAAE,UAAU,CAAEO,IAAI,CAAElH,QAAQ,CAAE2B,KAAK,CAAE4C,SAAS,CAAC,UAAU,CAAC,CAAE6C,OAAO,CAAE,iDAAiD,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CACvJ,CAAEV,GAAG,CAAE,SAAS,CAAEO,IAAI,CAAEpH,OAAO,CAAE6B,KAAK,CAAE4C,SAAS,CAAC,SAAS,CAAC,CAAE4C,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,yFAAyF,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CAC5M,CAAEV,GAAG,CAAE,UAAU,CAAEO,IAAI,CAAE1H,QAAQ,CAAEmC,KAAK,CAAE4C,SAAS,CAAC,UAAU,CAAC,CAAE4C,QAAQ,CAAE,IAAI,CAAEC,OAAO,CAAE,uDAAuD,CAAIC,MAAM,CAAE,CAAC,YAAY,CAAE,CAAC,CAC9K,CAED,mBACE7F,KAAA,CAACpB,aAAa,EAACkH,KAAK,CAAE3B,kBAAmB,CAAA/D,QAAA,eACvCN,IAAA,CAAChB,WAAW,GAAE,CAAC,cACfgB,IAAA,QAAKiG,SAAS,CAAC,iBAAiB,CAAA3F,QAAA,cAChCJ,KAAA,CAACd,GAAG,EAAC8G,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAA7F,QAAA,eAC3BN,IAAA,CAACf,MAAM,EACLmH,OAAO,CAAC,YAAY,CACpBC,MAAM,CAAE9F,KAAK,CAAG,OAAO,CAAG,MAAO,CACjC+F,IAAI,CAAEhD,WAAY,CAClB2C,SAAS,CAAC,aAAa,CACvBC,EAAE,CAAE,CACF,oBAAoB,CAAE,CACpB5B,SAAS,CAAE/D,KAAK,CAAG,KAAK,CAAG,KAC7B,CACF,CAAE,CAAAD,QAAA,cAEFN,IAAA,CAACL,gBAAgB,EAAAW,QAAA,cACbJ,KAAA,CAAChB,IAAI,EAAC+G,SAAS,CAAC,kBAAkB,CAAA3F,QAAA,EAE/BqF,SAAS,CAACY,GAAG,CAAC,CAAAC,KAAA,CAAiDC,KAAK,OAArD,CAAEpB,GAAG,CAAEO,IAAI,CAAEvF,KAAK,CAAEwF,QAAQ,CAAEC,OAAO,CAACC,MAAO,CAAC,CAAAS,KAAA,OAChE,CAACX,QAAQ,EAAI,CAACE,MAAM,CAACW,QAAQ,CAACxC,QAAQ,CAACsB,iBAAiB,CAAC,CAAC,CAAC,eAC3DxF,IAAA,CAAClC,KAAK,CAAC6I,QAAQ,EAAArG,QAAA,cACbN,IAAA,CAACG,aAAa,EAACE,KAAK,CAAEyF,OAAQ,CAAAxF,QAAA,cAC5BN,IAAA,SAAAM,QAAA,cACEN,IAAA,CAACT,UAAU,EACT0G,SAAS,kBAAApE,MAAA,CAAmBqB,UAAU,GAAKmC,GAAG,CAAG,QAAQ,CAAG,UAAU,MAAAxD,MAAA,CAAIgE,QAAQ,CAAG,UAAU,CAAG,EAAE,CAAG,CACvGe,OAAO,CAAEA,CAAA,GAAMxB,SAAS,CAACC,GAAG,CAAE,CAC5CQ,QAAQ,CAAEA,QAAS,CACnBgB,aAAa,MAAAvG,QAAA,cAECN,IAAA,CAACZ,GAAG,EAAC8G,EAAE,CAAE,CAAEY,SAAS,CAAE,MAAM,CAAExE,KAAK,CAAE,MAAO,CAAE,CAAAhC,QAAA,CAC3C,CAACuF,QAAQ,eACV3F,KAAA,CAACd,GAAG,EACF8G,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfY,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAEnB,QAAQ,CAAG,GAAG,CAAG,CAAC,CAC3B,KAAK,CAAE,CACLxD,QAAQ,CAAE,MACZ,CACF,CAAE,CAAA/B,QAAA,eAEFN,IAAA,CAACb,YAAY,EACX8G,SAAS,kBAAApE,MAAA,CAAmBqB,UAAU,GAAKmC,GAAG,CAAG,QAAQ,CAAG,UAAU,CAAG,CACzEa,EAAE,CAAAvE,aAAA,IACI0D,GAAG,GAAK,SAAS,EAAI,CAAE4B,UAAU,CAAE,MAAO,CAAC,CAC/C,CAAA3G,QAAA,cAEFN,IAAA,QAAKkH,GAAG,CAAEtB,IAAK,CAACuB,GAAG,CAAE9B,GAAI,CAAE,CAAC,CAChB,CAAC,cAEfrF,IAAA,CAACX,UAAU,EAAAiB,QAAA,CACRkD,gBAAgB,CAACnD,KAAK,CAAC,EAAIA,KAAK,CACvB,CAAC,EACV,CAAE,CACJ,CAAC,CACI,CAAC,CACT,CAAC,CACM,CAAC,EArCGgF,GAsCL,CAAC,EACd,CAAC,cACFnF,KAAA,CAACd,GAAG,EAAC6G,SAAS,CAAG,gBAAgB,CAAA3F,QAAA,eACjCN,IAAA,CAACX,UAAU,EACC4G,SAAS,CAAC,UAAU,CACpBC,EAAE,CAAE,CACFY,SAAS,CAAE,OAAO,CAClBE,OAAO,CAAE,CACX,CAAE,CAAA1G,QAAA,CACH,aAET,CAAY,CAAC,CACdqF,SAAS,CAACY,GAAG,CAAC,CAAAa,KAAA,CAA0CX,KAAK,OAA9C,CAAEpB,GAAG,CAAEO,IAAI,CAAEvF,KAAK,CAAEwF,QAAQ,CAAEC,OAAQ,CAAC,CAAAsB,KAAA,OACzD,CAAAvB,QAAQ,eACR7F,IAAA,CAAClC,KAAK,CAAC6I,QAAQ,EAAArG,QAAA,cACbN,IAAA,CAACG,aAAa,EAACE,KAAK,CAAEyF,OAAQ,CAAAxF,QAAA,cAC5BN,IAAA,SAAAM,QAAA,cACEN,IAAA,CAACT,UAAU,EACT0G,SAAS,kBAAApE,MAAA,CAAmBqB,UAAU,GAAKmC,GAAG,CAAG,QAAQ,CAAG,UAAU,MAAAxD,MAAA,CAAIgE,QAAQ,CAAG,UAAU,CAAG,EAAE,CAAG,CACvGe,OAAO,CAAEA,CAAA,GAAMxB,SAAS,CAACC,GAAG,CAAE,CAC5CQ,QAAQ,CAAEA,QAAS,CACnBgB,aAAa,MAAAvG,QAAA,cAECN,IAAA,CAACZ,GAAG,EAAC8G,EAAE,CAAE,CAAEY,SAAS,CAAE,MAAM,CAAExE,KAAK,CAAE,MAAO,CAAE,CAAAhC,QAAA,CAC3CuF,QAAQ,eACT3F,KAAA,CAACd,GAAG,EACF8G,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfY,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAEnB,QAAQ,CAAG,GAAG,CAAG,CAAC,CAC3B,KAAK,CAAE,CACLxD,QAAQ,CAAE,MACZ,CACF,CAAE,CAAA/B,QAAA,eAEFN,IAAA,CAACb,YAAY,EACX8G,SAAS,kBAAApE,MAAA,CAAmBqB,UAAU,GAAKmC,GAAG,CAAG,QAAQ,CAAG,UAAU,CAAG,CACzEa,EAAE,CAAAvE,aAAA,IACI0D,GAAG,GAAK,SAAS,EAAI,CAAE4B,UAAU,CAAE,MAAO,CAAC,CAC/C,CAAA3G,QAAA,cAEFN,IAAA,QAAKkH,GAAG,CAAEtB,IAAK,CAACuB,GAAG,CAAE9B,GAAI,CAAE,CAAC,CAChB,CAAC,cAEfrF,IAAA,CAACX,UAAU,EAAAiB,QAAA,CACRkD,gBAAgB,CAACnD,KAAK,CAAC,EAAIA,KAAK,CACvB,CAAC,EACV,CAAE,CACJ,CAAC,CACI,CAAC,CACT,CAAC,CACM,CAAC,EArCGgF,GAsCL,CAAC,EACd,CAAC,EACG,CAAC,EACN,CAAC,CACW,CAAC,CACb,CAAC,CAERjC,SAAS,eACRpD,IAAA,CAACV,KAAK,EACJ+H,OAAO,CAAEA,CAAA,GAAMhE,YAAY,CAAC,KAAK,CAAE,CACnCiE,IAAI,CAAEA,CAAA,GAAMjE,YAAY,CAAC,KAAK,CAAE,CAChCkE,IAAI,CAAC,OAAO,CACZlH,KAAK,CAAC,yBAAyB,CAC/BmH,WAAW,CAAC,+BAA+B,CAC3CC,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAC,QAAQ,CACjB,CACF,EACE,CAAC,CACD,CAAC,EACO,CAAC,CAEpB,CAAC,CAED,cAAe,CAAA9E,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}