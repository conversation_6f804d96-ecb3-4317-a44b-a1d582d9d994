{"version": 3, "file": "static/js/702.755c4c04.chunk.js", "mappings": "0LAIA,SAAeA,EAAAA,EAAAA,IAA4BC,EAAAA,EAAAA,KAAK,OAAQ,CACtDC,EAAG,wCACD,Q,cCGJ,MAuBA,EAvB2BC,KACzB,MAAMC,GAAWC,EAAAA,EAAAA,OACX,KAAEC,IAASC,EAAAA,EAAAA,OACVC,EAAYC,IAAiBC,EAAAA,EAAAA,WAAS,GAW7C,OATAC,EAAAA,EAAAA,YAAU,KACRC,EAAAA,EAAYC,yBAAyBC,MAAMR,IACzCG,GAAc,GACdL,EAAS,IAAI,IACZW,OAAOC,IACRJ,EAAAA,EAAYK,iBAAiB,GAC7B,GACD,IAECT,GACKP,EAAAA,EAAAA,KAACiB,EAAI,KAIPjB,EAAAA,EAAAA,KAAA,OAAAkB,SAAK,cAAgB,C", "sources": ["../node_modules/@mui/icons-material/esm/Home.js", "services/Callback.tsx"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"\n}), 'Home');", "import React, { useEffect, useState } from 'react';\r\nimport { Navigate, useNavigate } from 'react-router-dom';\r\nimport userManager from '../components/auth/UseAuth';\r\nimport Login from \"../components/login/login\";\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { LoginUserInfo } from '../models/LoginUserInfo';\r\nimport { Home } from '@mui/icons-material';\r\nimport { useAuth } from '../components/auth/AuthProvider';\r\n\r\nconst Callback: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const { user } = useAuth();\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n\r\n  useEffect(() => {\r\n    userManager.signinRedirectCallback().then((user) => {\r\n      setIsLoggedIn(true);  \r\n      navigate(\"/\")\r\n    }).catch((err) => {\r\n      userManager.signoutRedirect();\r\n    });\r\n  }, []);\r\n  \r\n  if (isLoggedIn) {    \r\n    return <Home/>;  \r\n  }\r\n  \r\n  \r\n  return <div>Loading...</div>;\r\n \r\n};\r\n\r\nexport default Callback;\r\n"], "names": ["createSvgIcon", "_jsx", "d", "Callback", "navigate", "useNavigate", "user", "useAuth", "isLoggedIn", "setIsLoggedIn", "useState", "useEffect", "userManager", "signinRedirectCallback", "then", "catch", "err", "signoutRedirect", "Home", "children"], "sourceRoot": ""}