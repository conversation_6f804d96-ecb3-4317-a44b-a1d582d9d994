{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\dashboard\\\\ModernDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Container, Tabs, Tab } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { TrendingUp, TrendingDown, People, CheckCircle, Star, Schedule } from '@mui/icons-material';\nimport Card from '../common/Card';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DashboardWrapper = styled('div')({\n  backgroundColor: '#f6f9ff',\n  minHeight: '100vh'\n});\n_c = DashboardWrapper;\nconst HeaderSection = styled(Box)({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  marginBottom: 'var(--spacing-4)'\n});\nconst StyledTabs = styled(Tabs)({\n  marginBottom: 'var(--spacing-4)',\n  '& .MuiTabs-indicator': {\n    backgroundColor: 'var(--color-primary-600)'\n  },\n  '& .MuiTab-root': {\n    fontSize: 'var(--font-size-sm)',\n    fontWeight: 'var(--font-weight-medium)',\n    textTransform: 'none',\n    color: 'var(--color-gray-600)',\n    '&.Mui-selected': {\n      color: 'var(--color-primary-600)',\n      fontWeight: 'var(--font-weight-semibold)'\n    }\n  }\n});\n_c2 = StyledTabs;\nconst FilterSection = styled(Box)({\n  display: 'flex',\n  gap: 'var(--spacing-3)',\n  alignItems: 'center'\n});\nconst MetricsGrid = styled(Box)({\n  display: 'grid',\n  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n  gap: 'var(--spacing-4)',\n  marginBottom: 'var(--spacing-6)'\n});\n_c3 = MetricsGrid;\nconst MetricCardContainer = styled(Card)({\n  padding: 'var(--spacing-5)',\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-4)'\n});\n_c4 = MetricCardContainer;\nconst MetricIcon = styled(Box)(({\n  color\n}) => ({\n  width: '48px',\n  height: '48px',\n  borderRadius: 'var(--radius-lg)',\n  backgroundColor: `${color}15`,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  '& svg': {\n    color: color,\n    fontSize: '24px'\n  }\n}));\n_c5 = MetricIcon;\nconst MetricContent = styled(Box)({\n  flex: 1\n});\n_c6 = MetricContent;\nconst MetricTitle = styled(Typography)({\n  fontSize: 'var(--font-size-sm)',\n  color: 'var(--color-gray-600)',\n  marginBottom: 'var(--spacing-1)'\n});\n_c7 = MetricTitle;\nconst MetricValue = styled(Typography)({\n  fontSize: 'var(--font-size-2xl)',\n  fontWeight: 'var(--font-weight-bold)',\n  color: 'var(--color-gray-900)',\n  marginBottom: 'var(--spacing-1)'\n});\n_c8 = MetricValue;\nconst MetricChange = styled(Box)({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)'\n});\n_c9 = MetricChange;\nconst ChangeIndicator = styled(Box)(({\n  trend\n}) => ({\n  display: 'flex',\n  alignItems: 'center',\n  gap: 'var(--spacing-1)',\n  fontSize: 'var(--font-size-xs)',\n  fontWeight: 'var(--font-weight-medium)',\n  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',\n  '& svg': {\n    fontSize: '16px'\n  }\n}));\n_c0 = ChangeIndicator;\nconst MetricCard = ({\n  title,\n  value,\n  change,\n  changeValue,\n  trend,\n  icon,\n  color\n}) => {\n  return /*#__PURE__*/_jsxDEV(MetricCardContainer, {\n    shadow: \"sm\",\n    hover: true,\n    children: [/*#__PURE__*/_jsxDEV(MetricIcon, {\n      color: color,\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MetricContent, {\n      children: [/*#__PURE__*/_jsxDEV(MetricTitle, {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricValue, {\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MetricChange, {\n        children: [/*#__PURE__*/_jsxDEV(ChangeIndicator, {\n          trend: trend,\n          children: [trend === 'up' ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 48\n          }, this), change]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: changeValue\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_c1 = MetricCard;\nconst ModernDashboard = () => {\n  _s();\n  const [selectedTab, setSelectedTab] = useState(0);\n  const [selectedGuideType, setSelectedGuideType] = useState(null);\n  const [selectedGuideFromList, setSelectedGuideFromList] = useState(null);\n  const [tooltip, setTooltip] = useState({\n    visible: false,\n    x: 0,\n    y: 0,\n    content: '',\n    title: ''\n  });\n\n  // Mock data for guide types and guides\n  const guideTypes = [{\n    id: 'tours',\n    name: 'Tours',\n    count: 8,\n    completionRate: 85,\n    color: '#8b5cf6'\n  }, {\n    id: 'banners',\n    name: 'Banners',\n    count: 12,\n    completionRate: 92,\n    color: '#06b6d4'\n  }, {\n    id: 'announcements',\n    name: 'Announcements',\n    count: 6,\n    completionRate: 78,\n    color: '#f59e0b'\n  }, {\n    id: 'tooltips',\n    name: 'Tooltips',\n    count: 15,\n    completionRate: 88,\n    color: '#10b981'\n  }, {\n    id: 'announcement',\n    name: 'Announcement',\n    count: 4,\n    completionRate: 73,\n    color: '#ef4444'\n  }, {\n    id: 'hotspots',\n    name: 'Hotspots',\n    count: 9,\n    completionRate: 81,\n    color: '#f97316'\n  }];\n  const guidesByType = {\n    tours: [{\n      id: 'tour-1',\n      name: 'Product Onboarding',\n      views: 1400,\n      completed: 1247,\n      dropOff: 11,\n      status: 'excellent',\n      lastUpdated: '2 days ago'\n    }, {\n      id: 'tour-2',\n      name: 'Feature Discovery',\n      views: 1174,\n      completed: 892,\n      dropOff: 24,\n      status: 'good',\n      lastUpdated: '1 day ago'\n    }, {\n      id: 'tour-3',\n      name: 'Advanced Settings',\n      views: 962,\n      completed: 634,\n      dropOff: 32,\n      status: 'needs attention',\n      lastUpdated: '5 days ago'\n    }],\n    banners: [{\n      id: 'banner-1',\n      name: 'Welcome Banner',\n      views: 2100,\n      completed: 1932,\n      dropOff: 8,\n      status: 'excellent',\n      lastUpdated: '1 day ago'\n    }, {\n      id: 'banner-2',\n      name: 'Feature Announcement',\n      views: 1850,\n      completed: 1665,\n      dropOff: 10,\n      status: 'excellent',\n      lastUpdated: '3 days ago'\n    }],\n    announcements: [{\n      id: 'announcement-1',\n      name: 'New Feature Release',\n      views: 980,\n      completed: 764,\n      dropOff: 22,\n      status: 'good',\n      lastUpdated: '1 week ago'\n    }, {\n      id: 'announcement-2',\n      name: 'Maintenance Notice',\n      views: 1200,\n      completed: 936,\n      dropOff: 22,\n      status: 'good',\n      lastUpdated: '2 days ago'\n    }],\n    tooltips: [{\n      id: 'tooltip-1',\n      name: 'Button Helper',\n      views: 3200,\n      completed: 2816,\n      dropOff: 12,\n      status: 'excellent',\n      lastUpdated: '1 day ago'\n    }, {\n      id: 'tooltip-2',\n      name: 'Form Guidance',\n      views: 2800,\n      completed: 2464,\n      dropOff: 12,\n      status: 'excellent',\n      lastUpdated: '2 days ago'\n    }],\n    announcement: [{\n      id: 'modal-1',\n      name: 'Confirmation Dialog',\n      views: 1500,\n      completed: 1095,\n      dropOff: 27,\n      status: 'needs attention',\n      lastUpdated: '4 days ago'\n    }],\n    hotspots: [{\n      id: 'hotspot-1',\n      name: 'Navigation Helper',\n      views: 1100,\n      completed: 891,\n      dropOff: 19,\n      status: 'good',\n      lastUpdated: '3 days ago'\n    }]\n  };\n  const showTooltip = (event, title, content) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n    setTooltip({\n      visible: true,\n      x: rect.left + scrollX + rect.width / 2,\n      y: rect.top + scrollY - 10,\n      content,\n      title\n    });\n  };\n  const hideTooltip = () => {\n    setTooltip(prev => ({\n      ...prev,\n      visible: false\n    }));\n  };\n  const overviewMetrics = [{\n    title: 'Completion Rate',\n    value: '87.3%',\n    change: '****%',\n    changeValue: '+2.8pp',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'User Satisfaction',\n    value: '4.6',\n    change: '+0.2',\n    changeValue: 'out of 5.0',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Hours Saved',\n    value: '2,847',\n    change: '+18.7%',\n    changeValue: '+447h',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }];\n  const analyticsMetrics = [{\n    title: 'Active Users',\n    value: '12,847',\n    change: '+12.5%',\n    changeValue: '+1,432',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }, {\n    title: 'Completion Rate',\n    value: '87.3%',\n    change: '****%',\n    changeValue: '+2.8pp',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'User Satisfaction',\n    value: '4.6',\n    change: '+0.2',\n    changeValue: 'out of 5.0',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Hours Saved',\n    value: '2,847',\n    change: '+18.7%',\n    changeValue: '+447h',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }];\n  const aiPerformanceMetrics = [{\n    title: 'AI Response Time',\n    value: '1.2s',\n    change: '-15%',\n    changeValue: 'faster',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-success-600)'\n  }, {\n    title: 'AI Accuracy',\n    value: '94.8%',\n    change: '+2.1%',\n    changeValue: 'improved',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-primary-600)'\n  }, {\n    title: 'Model Confidence',\n    value: '89.3%',\n    change: '+1.8%',\n    changeValue: 'higher',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-warning-600)'\n  }, {\n    title: 'Processing Load',\n    value: '67%',\n    change: '+5%',\n    changeValue: 'capacity',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 13\n    }, this),\n    color: 'var(--color-error-500)'\n  }];\n  const handleTabChange = (event, newValue) => {\n    setSelectedTab(newValue);\n  };\n  const renderTabContent = () => {\n    var _guidesByType$selecte;\n    switch (selectedTab) {\n      case 0:\n        // Overview\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(MetricsGrid, {\n            children: analyticsMetrics.map((metric, index) => /*#__PURE__*/_jsxDEV(MetricCard, {\n              ...metric\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDCC8 User Activity Trends\",\n              subtitle: \"Active, retained, and total users over time\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  position: 'relative',\n                  backgroundColor: 'white',\n                  borderRadius: 'var(--radius-md)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"100%\",\n                  height: \"280\",\n                  viewBox: \"0 0 450 250\",\n                  style: {\n                    overflow: 'visible'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n                    children: /*#__PURE__*/_jsxDEV(\"pattern\", {\n                      id: \"activityGrid\",\n                      width: \"200\",\n                      height: \"50\",\n                      patternUnits: \"userSpaceOnUse\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M 200 0 L 0 0 0 50\",\n                        fill: \"none\",\n                        stroke: \"#f1f5f9\",\n                        strokeWidth: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 370,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    width: \"100%\",\n                    height: \"200\",\n                    fill: \"url(#activityGrid)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"50\",\n                    y1: \"20\",\n                    x2: \"50\",\n                    y2: \"200\",\n                    stroke: \"#e2e8f0\",\n                    strokeWidth: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"50\",\n                    y1: \"200\",\n                    x2: \"400\",\n                    y2: \"200\",\n                    stroke: \"#e2e8f0\",\n                    strokeWidth: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"25\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"60000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"65\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"45000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"105\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"30000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"145\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"15000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"40\",\n                    y: \"205\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"end\",\n                    children: \"0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"125\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Week 1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"325\",\n                    y: \"220\",\n                    fontSize: \"11\",\n                    fill: \"#64748b\",\n                    textAnchor: \"middle\",\n                    children: \"Week 2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 125 50 L 325 45\",\n                    fill: \"none\",\n                    stroke: \"#8b5cf6\",\n                    strokeWidth: \"3\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"125\",\n                    cy: \"50\",\n                    r: \"5\",\n                    fill: \"#8b5cf6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"325\",\n                    cy: \"45\",\n                    r: \"5\",\n                    fill: \"#8b5cf6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 125 80 L 325 75\",\n                    fill: \"none\",\n                    stroke: \"#3b82f6\",\n                    strokeWidth: \"3\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"125\",\n                    cy: \"80\",\n                    r: \"5\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"325\",\n                    cy: \"75\",\n                    r: \"5\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 125 110 L 325 105\",\n                    fill: \"none\",\n                    stroke: \"#10b981\",\n                    strokeWidth: \"3\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"125\",\n                    cy: \"110\",\n                    r: \"5\",\n                    fill: \"#10b981\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"325\",\n                    cy: \"105\",\n                    r: \"5\",\n                    fill: \"#10b981\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"125\",\n                    y: \"40\",\n                    fontSize: \"10\",\n                    fill: \"#8b5cf6\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"36000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"325\",\n                    y: \"35\",\n                    fontSize: \"10\",\n                    fill: \"#8b5cf6\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"37000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"125\",\n                    y: \"70\",\n                    fontSize: \"10\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"30000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"325\",\n                    y: \"65\",\n                    fontSize: \"10\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"31000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"125\",\n                    y: \"100\",\n                    fontSize: \"10\",\n                    fill: \"#10b981\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"24000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"325\",\n                    y: \"95\",\n                    fontSize: \"10\",\n                    fill: \"#10b981\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"600\",\n                    children: \"25000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    position: 'absolute',\n                    bottom: 10,\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    display: 'flex',\n                    gap: 3,\n                    backgroundColor: 'rgba(255,255,255,0.9)',\n                    padding: '8px 16px',\n                    borderRadius: '8px',\n                    border: '1px solid #e2e8f0'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 12,\n                        height: 12,\n                        backgroundColor: '#3b82f6',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b'\n                      },\n                      children: \"Active\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 12,\n                        height: 12,\n                        backgroundColor: '#10b981',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b'\n                      },\n                      children: \"Retained\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 12,\n                        height: 12,\n                        backgroundColor: '#8b5cf6',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b'\n                      },\n                      children: \"Total\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uFFFD Feature Adoption Distribution\",\n              subtitle: \"Interactive feature usage metrics\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center',\n                  alignItems: 'center',\n                  backgroundColor: 'white',\n                  borderRadius: 'var(--radius-md)',\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"180\",\n                  height: \"180\",\n                  viewBox: \"0 0 180 180\",\n                  style: {\n                    marginBottom: '20px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#3b82f6\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"94 377\",\n                    strokeDashoffset: \"0\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#f97316\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"68 377\",\n                    strokeDashoffset: \"-94\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#14b8a6\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"45 377\",\n                    strokeDashoffset: \"-162\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#10b981\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"75 377\",\n                    strokeDashoffset: \"-207\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#8b5cf6\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"71 377\",\n                    strokeDashoffset: \"-282\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"60\",\n                    fill: \"none\",\n                    stroke: \"#ef4444\",\n                    strokeWidth: \"25\",\n                    strokeDasharray: \"23 377\",\n                    strokeDashoffset: \"-353\",\n                    transform: \"rotate(-90 90 90)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"90\",\n                    cy: \"90\",\n                    r: \"35\",\n                    fill: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"90\",\n                    y: \"85\",\n                    fontSize: \"16\",\n                    fill: \"#1f2937\",\n                    textAnchor: \"middle\",\n                    fontWeight: \"bold\",\n                    children: \"100%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"90\",\n                    y: \"100\",\n                    fontSize: \"11\",\n                    fill: \"#6b7280\",\n                    textAnchor: \"middle\",\n                    children: \"Total\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(3, 1fr)',\n                    gap: 2,\n                    width: '100%',\n                    maxWidth: '400px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#3b82f6',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Tours\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 504,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"25%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#10b981',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Tooltips\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 512,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"20%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#8b5cf6',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 519,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Announcement\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 520,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"19%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#f97316',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Banners\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 528,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"18%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#14b8a6',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 535,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Hotspots\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 536,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"12%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 12,\n                          height: 12,\n                          backgroundColor: '#ef4444',\n                          borderRadius: '50%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 543,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontSize: '11px',\n                          color: '#64748b'\n                        },\n                        children: \"Checklists\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '11px',\n                        color: '#64748b',\n                        fontWeight: 600\n                      },\n                      children: \"6%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u2B50 User Satisfaction Ratings\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 'var(--spacing-4)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#10b981',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Excellent (5\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '65%',\n                        height: '100%',\n                        backgroundColor: '#10b981',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 577,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"1,247\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#84cc16',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Good (4\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '45%',\n                        height: '100%',\n                        backgroundColor: '#84cc16',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 605,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"892\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#f59e0b',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Average (3\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '22%',\n                        height: '100%',\n                        backgroundColor: '#f59e0b',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"434\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#f97316',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Poor (2\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '8%',\n                        height: '100%',\n                        backgroundColor: '#f97316',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 661,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"123\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1,\n                      minWidth: 120\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        backgroundColor: '#ef4444',\n                        borderRadius: '50%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"medium\",\n                      children: \"Very Poor (1\\u2605)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1,\n                      height: 8,\n                      backgroundColor: 'var(--color-gray-200)',\n                      borderRadius: 'var(--radius-full)',\n                      overflow: 'hidden',\n                      mr: 2\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '4%',\n                        height: '100%',\n                        backgroundColor: '#ef4444',\n                        borderRadius: 'var(--radius-full)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      minWidth: 40,\n                      textAlign: 'right'\n                    },\n                    children: \"57\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(3, 1fr)',\n                    gap: 2,\n                    mt: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#f0fdf4',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#16a34a\",\n                      children: \"77%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#16a34a\",\n                      children: \"Positive\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#fffbeb',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#d97706\",\n                      children: \"16%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#d97706\",\n                      children: \"Neutral\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 2,\n                      backgroundColor: '#fef2f2',\n                      borderRadius: 'var(--radius-md)',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      fontWeight: \"bold\",\n                      color: \"#dc2626\",\n                      children: \"7%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"#dc2626\",\n                      children: \"Negative\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDCC8 Satisfaction Trend\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '300px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center',\n                  alignItems: 'center',\n                  backgroundColor: '#f8fafc',\n                  borderRadius: 'var(--radius-md)',\n                  position: 'relative'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"100%\",\n                  height: \"250\",\n                  viewBox: \"0 0 400 200\",\n                  style: {\n                    overflow: 'visible'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n                    children: /*#__PURE__*/_jsxDEV(\"pattern\", {\n                      id: \"feedbackGrid\",\n                      width: \"66.67\",\n                      height: \"40\",\n                      patternUnits: \"userSpaceOnUse\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M 66.67 0 L 0 0 0 40\",\n                        fill: \"none\",\n                        stroke: \"#e2e8f0\",\n                        strokeWidth: \"0.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    width: \"100%\",\n                    height: \"100%\",\n                    fill: \"url(#feedbackGrid)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 757,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"20\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"60\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.75\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"100\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"140\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4.25\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"10\",\n                    y: \"180\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"50\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Jan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"110\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Feb\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"170\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Mar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"230\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Apr\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"290\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"May\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 771,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"350\",\n                    y: \"215\",\n                    fontSize: \"10\",\n                    fill: \"#64748b\",\n                    children: \"Jun\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88\",\n                    fill: \"none\",\n                    stroke: \"#3b82f6\",\n                    strokeWidth: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"168\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"110\",\n                    cy: \"152\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"170\",\n                    cy: \"136\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"230\",\n                    cy: \"120\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"290\",\n                    cy: \"104\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"350\",\n                    cy: \"88\",\n                    r: \"4\",\n                    fill: \"#3b82f6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"50\",\n                    y: \"160\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"110\",\n                    y: \"144\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"170\",\n                    y: \"128\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"230\",\n                    y: \"112\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 789,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"290\",\n                    y: \"96\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 790,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                    x: \"350\",\n                    y: \"80\",\n                    fontSize: \"9\",\n                    fill: \"#3b82f6\",\n                    textAnchor: \"middle\",\n                    children: \"4.7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 791,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\uD83D\\uDCCA Feedback Summary\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(4, 1fr)',\n                gap: 'var(--spacing-6)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f8fafc',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"text.primary\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"2,238\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Feedback\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0fdf4',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#16a34a\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"85.8%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Positive Sentiment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#eff6ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#2563eb\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"4.6/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Average Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#fdf4ff',\n                  borderRadius: 'var(--radius-md)',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"#9333ea\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"+12%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 852,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"vs Last Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 1:\n        // Analytics\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            title: \"Analytics Overview\",\n            subtitle: \"Select a guide type to view detailed analytics\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n                gap: 'var(--spacing-4)',\n                mb: 4\n              },\n              children: guideTypes.map(guideType => /*#__PURE__*/_jsxDEV(Box, {\n                onClick: () => {\n                  setSelectedGuideType(guideType.id);\n                  setSelectedGuideFromList(null); // Clear guide selection when changing type\n                },\n                sx: {\n                  p: 3,\n                  border: selectedGuideType === guideType.id ? `2px solid ${guideType.color}` : '1px solid var(--color-gray-200)',\n                  borderRadius: 'var(--radius-md)',\n                  backgroundColor: selectedGuideType === guideType.id ? `${guideType.color}08` : 'white',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  '&:hover': {\n                    backgroundColor: selectedGuideType === guideType.id ? `${guideType.color}12` : 'var(--color-gray-50)',\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    fontWeight: \"semibold\",\n                    sx: {\n                      color: selectedGuideType === guideType.id ? guideType.color : 'var(--color-gray-900)'\n                    },\n                    children: guideType.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 12,\n                      height: 12,\n                      borderRadius: '50%',\n                      backgroundColor: guideType.color\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [guideType.count, \" guides\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 905,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h5\",\n                    fontWeight: \"bold\",\n                    sx: {\n                      color: guideType.color\n                    },\n                    children: [guideType.completionRate, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100%',\n                    height: 6,\n                    backgroundColor: 'var(--color-gray-200)',\n                    borderRadius: 'var(--radius-full)',\n                    overflow: 'hidden'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: `${guideType.completionRate}%`,\n                      height: '100%',\n                      backgroundColor: guideType.color,\n                      borderRadius: 'var(--radius-full)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 21\n                }, this)]\n              }, guideType.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 13\n          }, this), selectedGuideType && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: 'var(--spacing-6)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDCCA Top Guides Performance\",\n              subtitle: \"Comprehensive guide analytics with completion and engagement metrics\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 'var(--spacing-4)'\n                },\n                children: (_guidesByType$selecte = guidesByType[selectedGuideType]) === null || _guidesByType$selecte === void 0 ? void 0 : _guidesByType$selecte.map((guide, index) => {\n                  var _guideTypes$find;\n                  const completionRate = Math.round(guide.completed / guide.views * 100);\n                  const guideColor = ((_guideTypes$find = guideTypes.find(gt => gt.id === selectedGuideType)) === null || _guideTypes$find === void 0 ? void 0 : _guideTypes$find.color) || '#8b5cf6';\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    onClick: () => setSelectedGuideFromList(guide.id),\n                    sx: {\n                      p: 3,\n                      border: selectedGuideFromList === guide.id ? `2px solid ${guideColor}` : '1px solid var(--color-gray-200)',\n                      borderRadius: 'var(--radius-lg)',\n                      backgroundColor: selectedGuideFromList === guide.id ? `${guideColor}08` : 'white',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      '&:hover': {\n                        backgroundColor: selectedGuideFromList === guide.id ? `${guideColor}12` : 'var(--color-gray-50)',\n                        transform: 'translateY(-2px)',\n                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        mb: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            width: 8,\n                            height: 8,\n                            borderRadius: '50%',\n                            backgroundColor: guideColor\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 962,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h6\",\n                          fontWeight: \"semibold\",\n                          children: guide.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 968,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 961,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h5\",\n                          fontWeight: \"bold\",\n                          sx: {\n                            color: guideColor\n                          },\n                          children: [completionRate, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 973,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            px: 1.5,\n                            py: 0.5,\n                            backgroundColor: guide.status === 'excellent' ? '#e8f5e9' : guide.status === 'good' ? '#e3f2fd' : '#fff3e0',\n                            color: guide.status === 'excellent' ? '#2e7d32' : guide.status === 'good' ? '#1976d2' : '#f57c00',\n                            borderRadius: 'var(--radius-sm)',\n                            fontSize: '11px',\n                            fontWeight: 'medium'\n                          },\n                          children: guide.status\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 976,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 972,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 960,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        mb: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100%',\n                          height: 8,\n                          backgroundColor: 'var(--color-gray-200)',\n                          borderRadius: 'var(--radius-full)',\n                          overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            width: `${completionRate}%`,\n                            height: '100%',\n                            backgroundColor: guideColor,\n                            borderRadius: 'var(--radius-full)'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 999,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 992,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 991,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          textAlign: 'center'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h6\",\n                          fontWeight: \"bold\",\n                          sx: {\n                            color: '#3b82f6'\n                          },\n                          children: guide.views.toLocaleString()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1011,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: \"\\uD83D\\uDC41 Views\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1014,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1010,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          textAlign: 'center'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h6\",\n                          fontWeight: \"bold\",\n                          sx: {\n                            color: '#10b981'\n                          },\n                          children: guide.completed.toLocaleString()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1019,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: \"\\u2705 Completed\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1022,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1018,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          textAlign: 'center'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h6\",\n                          fontWeight: \"bold\",\n                          sx: {\n                            color: guide.dropOff < 15 ? '#10b981' : guide.dropOff < 25 ? '#f59e0b' : '#ef4444'\n                          },\n                          children: [guide.dropOff, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1027,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: \"\\uD83D\\uDCC9 Drop-off\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1032,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1026,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1009,\n                      columnNumber: 27\n                    }, this)]\n                  }, guide.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\uD83D\\uDD04 Interactive Guide Funnel\",\n              subtitle: \"Step-by-step user journey with conversion and drop-off analysis\",\n              padding: \"lg\",\n              children: selectedGuideFromList ? (_guidesByType$selecte2 => {\n                const selectedGuide = (_guidesByType$selecte2 = guidesByType[selectedGuideType]) === null || _guidesByType$selecte2 === void 0 ? void 0 : _guidesByType$selecte2.find(g => g.id === selectedGuideFromList);\n                if (!selectedGuide) return null;\n\n                // Mock funnel data for the selected guide\n                const funnelSteps = [{\n                  name: 'Guide Started',\n                  users: selectedGuide.views,\n                  rate: 100,\n                  color: '#10b981',\n                  dropOff: 0\n                }, {\n                  name: 'Step 1: Welcome',\n                  users: Math.round(selectedGuide.views * 0.85),\n                  rate: 85,\n                  color: '#3b82f6',\n                  dropOff: 15\n                }, {\n                  name: 'Step 2: Setup',\n                  users: Math.round(selectedGuide.views * 0.72),\n                  rate: 72,\n                  color: '#f59e0b',\n                  dropOff: 13\n                }, {\n                  name: 'Step 3: Configuration',\n                  users: Math.round(selectedGuide.views * 0.65),\n                  rate: 65,\n                  color: '#8b5cf6',\n                  dropOff: 7\n                }, {\n                  name: 'Guide Completed',\n                  users: selectedGuide.completed,\n                  rate: Math.round(selectedGuide.completed / selectedGuide.views * 100),\n                  color: '#ef4444',\n                  dropOff: Math.round((selectedGuide.views * 0.65 - selectedGuide.completed) / (selectedGuide.views * 0.65) * 100)\n                }];\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: 'var(--spacing-3)'\n                  },\n                  children: [funnelSteps.map((step, index) => /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      p: 3,\n                      backgroundColor: `${step.color}08`,\n                      border: `1px solid ${step.color}20`,\n                      borderRadius: 'var(--radius-lg)',\n                      position: 'relative'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        mb: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            width: 8,\n                            height: 8,\n                            borderRadius: '50%',\n                            backgroundColor: step.color\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1105,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body1\",\n                          fontWeight: \"semibold\",\n                          children: step.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1111,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1104,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h5\",\n                        fontWeight: \"bold\",\n                        sx: {\n                          color: step.color\n                        },\n                        children: [step.rate, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1115,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1103,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h4\",\n                        fontWeight: \"bold\",\n                        sx: {\n                          color: step.color\n                        },\n                        children: step.users.toLocaleString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1122,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Users\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1125,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1121,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      sx: {\n                        mb: 2\n                      },\n                      children: \"Avg Step: 2m 15s\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1130,\n                      columnNumber: 31\n                    }, this), step.dropOff > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        position: 'absolute',\n                        top: -8,\n                        right: 16,\n                        px: 2,\n                        py: 0.5,\n                        backgroundColor: '#fef2f2',\n                        color: '#dc2626',\n                        borderRadius: 'var(--radius-full)',\n                        fontSize: '12px',\n                        fontWeight: 'medium',\n                        border: '1px solid #fecaca'\n                      },\n                      children: [\"-\", step.dropOff, \"% users (\", Math.round(selectedGuide.views * (step.dropOff / 100)).toLocaleString(), \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1136,\n                      columnNumber: 33\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1092,\n                    columnNumber: 29\n                  }, this)), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      mt: 3,\n                      pt: 3,\n                      borderTop: '1px solid var(--color-gray-200)'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h3\",\n                        fontWeight: \"bold\",\n                        sx: {\n                          color: '#10b981'\n                        },\n                        children: [Math.round(selectedGuide.completed / selectedGuide.views * 100), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1165,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Overall Conversion\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1168,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1164,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h3\",\n                        fontWeight: \"bold\",\n                        sx: {\n                          color: '#ef4444'\n                        },\n                        children: [selectedGuide.dropOff, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1173,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Total Drop-off\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1176,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1172,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1156,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1090,\n                  columnNumber: 25\n                }, this);\n              })() : /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  py: 8,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: \"Select a guide from the left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1193,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Choose a guide to view its step-by-step funnel analysis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1196,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1185,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true);\n      case 2:\n        // AI Performance\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(3, 1fr)',\n              gap: 'var(--spacing-6)',\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#3b82f6\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Total Interactions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e3f2fd',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#3b82f6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1235,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1226,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"2,847\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"+12% from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#10b981\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Success Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#e8f5e9',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#10b981',\n                      borderRadius: '50%',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1281,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"91%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"+3% improvement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1300,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 4,\n                backgroundColor: 'white',\n                borderRadius: 'var(--radius-lg)',\n                border: '1px solid var(--color-gray-200)',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"#8b5cf6\",\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: \"Avg Response Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1315,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    backgroundColor: '#f3e8ff',\n                    borderRadius: 'var(--radius-md)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 24,\n                      height: 24,\n                      backgroundColor: '#8b5cf6',\n                      borderRadius: 'var(--radius-sm)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: 'white',\n                      fontSize: '14px',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\u26A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1327,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1318,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"text.primary\",\n                sx: {\n                  mb: 1,\n                  fontSize: '2rem'\n                },\n                children: \"1.9s\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"#10b981\",\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: \"-0.3s faster\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1346,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"AI Task Performance\",\n              padding: \"lg\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 'var(--spacing-4)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Password Reset\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1371,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e8f5e9',\n                          color: '#2e7d32',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"96%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1374,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"342 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1385,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1370,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 1.2s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1390,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-success-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"+2% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1393,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1389,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1369,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"96%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1399,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '96%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1409,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1402,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1398,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1357,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Account Setup\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1434,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e3f2fd',\n                          color: '#1976d2',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"89%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1437,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"198 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1448,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1433,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 1.4s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1453,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-5% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1456,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1452,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1432,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"89%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1462,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '89%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1472,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1465,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1461,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1420,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Feature Explanation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1497,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#e3f2fd',\n                          color: '#1976d2',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"90%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1500,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"267 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1511,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1496,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 2.1s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1516,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-1% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1519,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1515,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1495,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"90%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1525,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '90%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1535,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1528,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1524,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1483,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Troubleshooting\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1560,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#fff3e0',\n                          color: '#f57c00',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"88%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1563,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"156 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1574,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1559,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 3.1s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1579,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-error-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"-3% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1582,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1578,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1558,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"88%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1588,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '88%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1598,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1591,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1587,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1546,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    p: 3,\n                    border: '1px solid var(--color-gray-200)',\n                    borderRadius: 'var(--radius-md)',\n                    '&:hover': {\n                      backgroundColor: 'var(--color-gray-50)',\n                      cursor: 'pointer'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        fontWeight: \"semibold\",\n                        children: \"Integration Help\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1623,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          px: 1.5,\n                          py: 0.5,\n                          backgroundColor: '#fff3e0',\n                          color: '#f57c00',\n                          borderRadius: 'var(--radius-sm)',\n                          fontSize: '12px',\n                          fontWeight: 'medium'\n                        },\n                        children: \"87%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1626,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"123 interactions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1637,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1622,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 2,\n                        color: 'var(--color-gray-600)',\n                        fontSize: '14px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Avg time: 2.5s\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1642,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          color: 'var(--color-success-600)',\n                          fontWeight: 'medium',\n                          fontSize: '12px'\n                        },\n                        children: \"+1% trend\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1645,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1641,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1621,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 2\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      fontWeight: \"bold\",\n                      children: \"87%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1651,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 80,\n                        height: 8,\n                        backgroundColor: 'var(--color-gray-200)',\n                        borderRadius: 'var(--radius-full)',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '87%',\n                          height: '100%',\n                          backgroundColor: 'var(--color-gray-800)',\n                          borderRadius: 'var(--radius-full)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1661,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1654,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1650,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1609,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1354,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"AI Insights & Recommendations\",\n            padding: \"lg\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 'var(--spacing-3)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#fffbeb',\n                  border: '1px solid #fbbf24',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#f59e0b',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1687,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Optimize Workflow\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1695,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Consider optimizing your workflow to reduce response time by 15%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1698,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1694,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1678,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1714,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Excluded Performance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1722,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Excluded tasks are performing well with 94% accuracy rate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1725,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1721,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1705,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  backgroundColor: '#f0f9ff',\n                  border: '1px solid #3b82f6',\n                  borderRadius: 'var(--radius-md)',\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 6,\n                    height: 6,\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1741,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: \"Personalized Suggestions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1749,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"AI suggests implementing advanced filtering for better user experience\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1752,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1748,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1732,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1676,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1675,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-web\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-webcontent\",\n      children: [/*#__PURE__*/_jsxDEV(DashboardWrapper, {\n        children: /*#__PURE__*/_jsxDEV(Container, {\n          maxWidth: \"xl\",\n          sx: {\n            py: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              mb: 4,\n              pb: 3,\n              borderBottom: '1px solid #e2e8f0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                fontWeight: \"bold\",\n                sx: {\n                  color: '#1e293b',\n                  mb: 0.5\n                },\n                children: \"Digital Adoption Platform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1785,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#64748b'\n                },\n                children: \"Admin Dashboard - Guide Creation & Analytics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1788,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1784,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search guides, users...\",\n                  style: {\n                    width: '240px',\n                    height: '36px',\n                    padding: '0 12px 0 36px',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '6px',\n                    fontSize: '14px',\n                    backgroundColor: 'white',\n                    outline: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1797,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    position: 'absolute',\n                    left: '12px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af',\n                    fontSize: '16px'\n                  },\n                  children: \"\\uD83D\\uDD0D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1811,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1796,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  px: 2,\n                  py: 1,\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  backgroundColor: 'white',\n                  cursor: 'pointer',\n                  fontSize: '14px',\n                  color: '#374151',\n                  '&:hover': {\n                    backgroundColor: '#f9fafb'\n                  }\n                },\n                children: \"\\uD83D\\uDCC5 30 days \\u25BC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1824,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  width: '36px',\n                  height: '36px',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  backgroundColor: 'white',\n                  cursor: 'pointer',\n                  fontSize: '16px',\n                  '&:hover': {\n                    backgroundColor: '#f9fafb'\n                  }\n                },\n                children: \"\\uD83D\\uDD04\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1844,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#3b82f6',\n                  color: 'white',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  fontSize: '14px',\n                  fontWeight: 'medium',\n                  '&:hover': {\n                    backgroundColor: '#2563eb'\n                  }\n                },\n                children: \"\\uD83D\\uDCE4 Export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1863,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1794,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1775,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StyledTabs, {\n            value: selectedTab,\n            onChange: handleTabChange,\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1886,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1887,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"AI Performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1888,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1885,\n            columnNumber: 13\n          }, this), renderTabContent()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1773,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1772,\n        columnNumber: 9\n      }, this), tooltip.visible && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          left: tooltip.x,\n          top: tooltip.y,\n          transform: 'translate(-50%, -100%)',\n          backgroundColor: 'white',\n          border: '1px solid #d1d5db',\n          borderRadius: '6px',\n          padding: '6px 10px',\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n          zIndex: 10000,\n          pointerEvents: 'none',\n          fontSize: '11px',\n          minWidth: '70px',\n          textAlign: 'center',\n          fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontSize: '10px',\n            color: '#6b7280',\n            mb: 0.2,\n            lineHeight: 1.2\n          },\n          children: tooltip.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1917,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontSize: '11px',\n            color: '#111827',\n            fontWeight: '600',\n            lineHeight: 1.2\n          },\n          children: tooltip.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1920,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1898,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1771,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1770,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernDashboard, \"1JmCDWGblFA/3wVdJ/vZ4uKnqII=\");\n_c10 = ModernDashboard;\nexport default ModernDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"DashboardWrapper\");\n$RefreshReg$(_c2, \"StyledTabs\");\n$RefreshReg$(_c3, \"MetricsGrid\");\n$RefreshReg$(_c4, \"MetricCardContainer\");\n$RefreshReg$(_c5, \"MetricIcon\");\n$RefreshReg$(_c6, \"MetricContent\");\n$RefreshReg$(_c7, \"MetricTitle\");\n$RefreshReg$(_c8, \"MetricValue\");\n$RefreshReg$(_c9, \"MetricChange\");\n$RefreshReg$(_c0, \"ChangeIndicator\");\n$RefreshReg$(_c1, \"MetricCard\");\n$RefreshReg$(_c10, \"ModernDashboard\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Container", "Tabs", "Tab", "styled", "TrendingUp", "TrendingDown", "People", "CheckCircle", "Star", "Schedule", "Card", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DashboardWrapper", "backgroundColor", "minHeight", "_c", "HeaderSection", "display", "justifyContent", "alignItems", "marginBottom", "StyledTabs", "fontSize", "fontWeight", "textTransform", "color", "_c2", "FilterSection", "gap", "MetricsGrid", "gridTemplateColumns", "_c3", "MetricCardContainer", "padding", "_c4", "MetricIcon", "width", "height", "borderRadius", "_c5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flex", "_c6", "MetricTitle", "_c7", "MetricValue", "_c8", "MetricChange", "_c9", "ChangeIndicator", "trend", "_c0", "MetricCard", "title", "value", "change", "changeValue", "icon", "shadow", "hover", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "_c1", "ModernDashboard", "_s", "selectedTab", "setSelectedTab", "selectedGuideType", "setSelectedGuideType", "selectedGuideFromList", "setSelectedGuideFromList", "tooltip", "setTooltip", "visible", "x", "y", "content", "guideTypes", "id", "name", "count", "completionRate", "guidesByType", "tours", "views", "completed", "dropOff", "status", "lastUpdated", "banners", "announcements", "tooltips", "announcement", "hotspots", "showTooltip", "event", "rect", "currentTarget", "getBoundingClientRect", "scrollX", "window", "pageXOffset", "document", "documentElement", "scrollLeft", "scrollY", "pageYOffset", "scrollTop", "left", "top", "hideTooltip", "prev", "overviewMetrics", "analyticsMetrics", "aiPerformanceMetrics", "handleTabChange", "newValue", "renderTabContent", "_guidesByType$selecte", "map", "metric", "index", "sx", "mb", "subtitle", "position", "viewBox", "style", "overflow", "patternUnits", "d", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "textAnchor", "strokeLinecap", "cx", "cy", "r", "bottom", "transform", "border", "flexDirection", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "mr", "textAlign", "mt", "p", "guideType", "onClick", "cursor", "transition", "boxShadow", "guide", "_guideTypes$find", "Math", "round", "guideColor", "find", "gt", "px", "py", "toLocaleString", "_guidesByType$selecte2", "<PERSON><PERSON><PERSON><PERSON>", "g", "funnelSteps", "users", "rate", "step", "right", "pt", "borderTop", "className", "pb", "borderBottom", "type", "placeholder", "outline", "onChange", "label", "zIndex", "pointerEvents", "fontFamily", "lineHeight", "_c10", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/dashboard/ModernDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Box, Typography, IconButton, Container, Tabs, Tab } from '@mui/material';\r\nimport { styled } from '@mui/material/styles';\r\nimport {\r\n  TrendingUp,\r\n  TrendingDown,\r\n  People,\r\n  CheckCircle,\r\n  Star,\r\n  Schedule,\r\n  FilterList,\r\n  CalendarToday\r\n} from '@mui/icons-material';\r\nimport Card from '../common/Card';\r\nimport ModernButton from '../common/ModernButton';\r\nimport ChartPlaceholder from './ChartPlaceholder';\r\n\r\ninterface MetricCardProps {\r\n  title: string;\r\n  value: string;\r\n  change: string;\r\n  changeValue: string;\r\n  trend: 'up' | 'down';\r\n  icon: React.ReactNode;\r\n  color: string;\r\n}\r\n\r\nconst DashboardWrapper = styled('div')({\r\n  backgroundColor: '#f6f9ff',\r\n  minHeight: '100vh',\r\n});\r\n\r\nconst HeaderSection = styled(Box)({\r\n  display: 'flex',\r\n  justifyContent: 'space-between',\r\n  alignItems: 'center',\r\n  marginBottom: 'var(--spacing-4)',\r\n});\r\n\r\nconst StyledTabs = styled(Tabs)({\r\n  marginBottom: 'var(--spacing-4)',\r\n  '& .MuiTabs-indicator': {\r\n    backgroundColor: 'var(--color-primary-600)',\r\n  },\r\n  '& .MuiTab-root': {\r\n    fontSize: 'var(--font-size-sm)',\r\n    fontWeight: 'var(--font-weight-medium)',\r\n    textTransform: 'none',\r\n    color: 'var(--color-gray-600)',\r\n    '&.Mui-selected': {\r\n      color: 'var(--color-primary-600)',\r\n      fontWeight: 'var(--font-weight-semibold)',\r\n    },\r\n  },\r\n});\r\n\r\nconst FilterSection = styled(Box)({\r\n  display: 'flex',\r\n  gap: 'var(--spacing-3)',\r\n  alignItems: 'center',\r\n});\r\n\r\nconst MetricsGrid = styled(Box)({\r\n  display: 'grid',\r\n  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\r\n  gap: 'var(--spacing-4)',\r\n  marginBottom: 'var(--spacing-6)',\r\n});\r\n\r\nconst MetricCardContainer = styled(Card)({\r\n  padding: 'var(--spacing-5)',\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  gap: 'var(--spacing-4)',\r\n});\r\n\r\nconst MetricIcon = styled(Box)<{ color: string }>(({ color }) => ({\r\n  width: '48px',\r\n  height: '48px',\r\n  borderRadius: 'var(--radius-lg)',\r\n  backgroundColor: `${color}15`,\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  justifyContent: 'center',\r\n  \r\n  '& svg': {\r\n    color: color,\r\n    fontSize: '24px',\r\n  },\r\n}));\r\n\r\nconst MetricContent = styled(Box)({\r\n  flex: 1,\r\n});\r\n\r\nconst MetricTitle = styled(Typography)({\r\n  fontSize: 'var(--font-size-sm)',\r\n  color: 'var(--color-gray-600)',\r\n  marginBottom: 'var(--spacing-1)',\r\n});\r\n\r\nconst MetricValue = styled(Typography)({\r\n  fontSize: 'var(--font-size-2xl)',\r\n  fontWeight: 'var(--font-weight-bold)',\r\n  color: 'var(--color-gray-900)',\r\n  marginBottom: 'var(--spacing-1)',\r\n});\r\n\r\nconst MetricChange = styled(Box)({\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  gap: 'var(--spacing-1)',\r\n});\r\n\r\nconst ChangeIndicator = styled(Box)<{ trend: 'up' | 'down' }>(({ trend }) => ({\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  gap: 'var(--spacing-1)',\r\n  fontSize: 'var(--font-size-xs)',\r\n  fontWeight: 'var(--font-weight-medium)',\r\n  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',\r\n  \r\n  '& svg': {\r\n    fontSize: '16px',\r\n  },\r\n}));\r\n\r\nconst MetricCard: React.FC<MetricCardProps> = ({\r\n  title,\r\n  value,\r\n  change,\r\n  changeValue,\r\n  trend,\r\n  icon,\r\n  color,\r\n}) => {\r\n  return (\r\n    <MetricCardContainer shadow=\"sm\" hover>\r\n      <MetricIcon color={color}>\r\n        {icon}\r\n      </MetricIcon>\r\n      <MetricContent>\r\n        <MetricTitle>{title}</MetricTitle>\r\n        <MetricValue>{value}</MetricValue>\r\n        <MetricChange>\r\n          <ChangeIndicator trend={trend}>\r\n            {trend === 'up' ? <TrendingUp /> : <TrendingDown />}\r\n            {change}\r\n          </ChangeIndicator>\r\n          <Typography variant=\"caption\" color=\"text.secondary\">\r\n            {changeValue}\r\n          </Typography>\r\n        </MetricChange>\r\n      </MetricContent>\r\n    </MetricCardContainer>\r\n  );\r\n};\r\n\r\nconst ModernDashboard: React.FC = () => {\r\n  const [selectedTab, setSelectedTab] = useState(0);\r\n  const [selectedGuideType, setSelectedGuideType] = useState<string | null>(null);\r\n  const [selectedGuideFromList, setSelectedGuideFromList] = useState<string | null>(null);\r\n  const [tooltip, setTooltip] = useState<{\r\n    visible: boolean;\r\n    x: number;\r\n    y: number;\r\n    content: string;\r\n    title: string;\r\n  }>({\r\n    visible: false,\r\n    x: 0,\r\n    y: 0,\r\n    content: '',\r\n    title: ''\r\n  });\r\n\r\n  // Mock data for guide types and guides\r\n  const guideTypes = [\r\n    { id: 'tours', name: 'Tours', count: 8, completionRate: 85, color: '#8b5cf6' },\r\n    { id: 'banners', name: 'Banners', count: 12, completionRate: 92, color: '#06b6d4' },\r\n    { id: 'announcements', name: 'Announcements', count: 6, completionRate: 78, color: '#f59e0b' },\r\n    { id: 'tooltips', name: 'Tooltips', count: 15, completionRate: 88, color: '#10b981' },\r\n    { id: 'announcement', name: 'Announcement', count: 4, completionRate: 73, color: '#ef4444' },\r\n    { id: 'hotspots', name: 'Hotspots', count: 9, completionRate: 81, color: '#f97316' }\r\n  ];\r\n\r\n  const guidesByType: Record<string, any[]> = {\r\n    tours: [\r\n      { id: 'tour-1', name: 'Product Onboarding', views: 1400, completed: 1247, dropOff: 11, status: 'excellent', lastUpdated: '2 days ago' },\r\n      { id: 'tour-2', name: 'Feature Discovery', views: 1174, completed: 892, dropOff: 24, status: 'good', lastUpdated: '1 day ago' },\r\n      { id: 'tour-3', name: 'Advanced Settings', views: 962, completed: 634, dropOff: 32, status: 'needs attention', lastUpdated: '5 days ago' }\r\n    ],\r\n    banners: [\r\n      { id: 'banner-1', name: 'Welcome Banner', views: 2100, completed: 1932, dropOff: 8, status: 'excellent', lastUpdated: '1 day ago' },\r\n      { id: 'banner-2', name: 'Feature Announcement', views: 1850, completed: 1665, dropOff: 10, status: 'excellent', lastUpdated: '3 days ago' }\r\n    ],\r\n    announcements: [\r\n      { id: 'announcement-1', name: 'New Feature Release', views: 980, completed: 764, dropOff: 22, status: 'good', lastUpdated: '1 week ago' },\r\n      { id: 'announcement-2', name: 'Maintenance Notice', views: 1200, completed: 936, dropOff: 22, status: 'good', lastUpdated: '2 days ago' }\r\n    ],\r\n    tooltips: [\r\n      { id: 'tooltip-1', name: 'Button Helper', views: 3200, completed: 2816, dropOff: 12, status: 'excellent', lastUpdated: '1 day ago' },\r\n      { id: 'tooltip-2', name: 'Form Guidance', views: 2800, completed: 2464, dropOff: 12, status: 'excellent', lastUpdated: '2 days ago' }\r\n    ],\r\n    announcement: [\r\n      { id: 'modal-1', name: 'Confirmation Dialog', views: 1500, completed: 1095, dropOff: 27, status: 'needs attention', lastUpdated: '4 days ago' }\r\n    ],\r\n    hotspots: [\r\n      { id: 'hotspot-1', name: 'Navigation Helper', views: 1100, completed: 891, dropOff: 19, status: 'good', lastUpdated: '3 days ago' }\r\n    ]\r\n  };\r\n\r\n  const showTooltip = (event: React.MouseEvent, title: string, content: string) => {\r\n    const rect = event.currentTarget.getBoundingClientRect();\r\n    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\r\n    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\r\n\r\n    setTooltip({\r\n      visible: true,\r\n      x: rect.left + scrollX + rect.width / 2,\r\n      y: rect.top + scrollY - 10,\r\n      content,\r\n      title\r\n    });\r\n  };\r\n\r\n  const hideTooltip = () => {\r\n    setTooltip(prev => ({ ...prev, visible: false }));\r\n  };\r\n\r\n  const overviewMetrics = [\r\n    {\r\n      title: 'Completion Rate',\r\n      value: '87.3%',\r\n      change: '****%',\r\n      changeValue: '+2.8pp',\r\n      trend: 'up' as const,\r\n      icon: <CheckCircle />,\r\n      color: 'var(--color-success-600)',\r\n    },\r\n    {\r\n      title: 'User Satisfaction',\r\n      value: '4.6',\r\n      change: '+0.2',\r\n      changeValue: 'out of 5.0',\r\n      trend: 'up' as const,\r\n      icon: <Star />,\r\n      color: 'var(--color-warning-600)',\r\n    },\r\n    {\r\n      title: 'Hours Saved',\r\n      value: '2,847',\r\n      change: '+18.7%',\r\n      changeValue: '+447h',\r\n      trend: 'up' as const,\r\n      icon: <Schedule />,\r\n      color: 'var(--color-primary-600)',\r\n    },\r\n  ];\r\n\r\n  const analyticsMetrics = [\r\n    {\r\n      title: 'Active Users',\r\n      value: '12,847',\r\n      change: '+12.5%',\r\n      changeValue: '+1,432',\r\n      trend: 'up' as const,\r\n      icon: <People />,\r\n      color: 'var(--color-primary-600)',\r\n    },\r\n    {\r\n      title: 'Completion Rate',\r\n      value: '87.3%',\r\n      change: '****%',\r\n      changeValue: '+2.8pp',\r\n      trend: 'up' as const,\r\n      icon: <CheckCircle />,\r\n      color: 'var(--color-success-600)',\r\n    },\r\n    {\r\n      title: 'User Satisfaction',\r\n      value: '4.6',\r\n      change: '+0.2',\r\n      changeValue: 'out of 5.0',\r\n      trend: 'up' as const,\r\n      icon: <Star />,\r\n      color: 'var(--color-warning-600)',\r\n    },\r\n    {\r\n      title: 'Hours Saved',\r\n      value: '2,847',\r\n      change: '+18.7%',\r\n      changeValue: '+447h',\r\n      trend: 'up' as const,\r\n      icon: <Schedule />,\r\n      color: 'var(--color-primary-600)',\r\n    },\r\n  ];\r\n\r\n  const aiPerformanceMetrics = [\r\n    {\r\n      title: 'AI Response Time',\r\n      value: '1.2s',\r\n      change: '-15%',\r\n      changeValue: 'faster',\r\n      trend: 'up' as const,\r\n      icon: <Schedule />,\r\n      color: 'var(--color-success-600)',\r\n    },\r\n    {\r\n      title: 'AI Accuracy',\r\n      value: '94.8%',\r\n      change: '+2.1%',\r\n      changeValue: 'improved',\r\n      trend: 'up' as const,\r\n      icon: <CheckCircle />,\r\n      color: 'var(--color-primary-600)',\r\n    },\r\n    {\r\n      title: 'Model Confidence',\r\n      value: '89.3%',\r\n      change: '+1.8%',\r\n      changeValue: 'higher',\r\n      trend: 'up' as const,\r\n      icon: <Star />,\r\n      color: 'var(--color-warning-600)',\r\n    },\r\n    {\r\n      title: 'Processing Load',\r\n      value: '67%',\r\n      change: '+5%',\r\n      changeValue: 'capacity',\r\n      trend: 'up' as const,\r\n      icon: <People />,\r\n      color: 'var(--color-error-500)',\r\n    },\r\n  ];\r\n\r\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\r\n    setSelectedTab(newValue);\r\n  };\r\n\r\n  const renderTabContent = () => {\r\n    switch (selectedTab) {\r\n      case 0: // Overview\r\n        return (\r\n          <>\r\n           \r\n\r\n            {/* Overview Metrics Grid (4 cards like Analytics) */}\r\n            <MetricsGrid>\r\n              {analyticsMetrics.map((metric, index) => (\r\n                <MetricCard key={index} {...metric} />\r\n              ))}\r\n            </MetricsGrid>\r\n\r\n            {/* Overview Charts - Growth Trends and User Satisfaction */}\r\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>\r\n              {/* User Activity Trends Chart */}\r\n              <Card title=\"📈 User Activity Trends\" subtitle=\"Active, retained, and total users over time\" padding=\"lg\">\r\n                <Box sx={{ height: '300px', position: 'relative', backgroundColor: 'white', borderRadius: 'var(--radius-md)' }}>\r\n\r\n                  \r\n\r\n                  {/* Multi-line Chart */}\r\n                  <svg width=\"100%\" height=\"280\" viewBox=\"0 0 450 250\" style={{ overflow: 'visible' }}>\r\n                    {/* Grid lines */}\r\n                    <defs>\r\n                      <pattern id=\"activityGrid\" width=\"200\" height=\"50\" patternUnits=\"userSpaceOnUse\">\r\n                        <path d=\"M 200 0 L 0 0 0 50\" fill=\"none\" stroke=\"#f1f5f9\" strokeWidth=\"1\"/>\r\n                      </pattern>\r\n                    </defs>\r\n                    <rect width=\"100%\" height=\"200\" fill=\"url(#activityGrid)\" />\r\n\r\n                    {/* Y-axis */}\r\n                    <line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"200\" stroke=\"#e2e8f0\" strokeWidth=\"1\"/>\r\n                    {/* X-axis */}\r\n                    <line x1=\"50\" y1=\"200\" x2=\"400\" y2=\"200\" stroke=\"#e2e8f0\" strokeWidth=\"1\"/>\r\n\r\n                    {/* Y-axis labels */}\r\n                    <text x=\"40\" y=\"25\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">60000</text>\r\n                    <text x=\"40\" y=\"65\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">45000</text>\r\n                    <text x=\"40\" y=\"105\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">30000</text>\r\n                    <text x=\"40\" y=\"145\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">15000</text>\r\n                    <text x=\"40\" y=\"205\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"end\">0</text>\r\n\r\n                    {/* X-axis labels */}\r\n                    <text x=\"125\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Week 1</text>\r\n                    <text x=\"325\" y=\"220\" fontSize=\"11\" fill=\"#64748b\" textAnchor=\"middle\">Week 2</text>\r\n\r\n                    {/* Total Users Line (Purple) */}\r\n                    <path d=\"M 125 50 L 325 45\" fill=\"none\" stroke=\"#8b5cf6\" strokeWidth=\"3\" strokeLinecap=\"round\"/>\r\n                    <circle cx=\"125\" cy=\"50\" r=\"5\" fill=\"#8b5cf6\"/>\r\n                    <circle cx=\"325\" cy=\"45\" r=\"5\" fill=\"#8b5cf6\"/>\r\n\r\n                    {/* Active Users Line (Blue) */}\r\n                    <path d=\"M 125 80 L 325 75\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"3\" strokeLinecap=\"round\"/>\r\n                    <circle cx=\"125\" cy=\"80\" r=\"5\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"325\" cy=\"75\" r=\"5\" fill=\"#3b82f6\"/>\r\n\r\n                    {/* Retained Users Line (Green) */}\r\n                    <path d=\"M 125 110 L 325 105\" fill=\"none\" stroke=\"#10b981\" strokeWidth=\"3\" strokeLinecap=\"round\"/>\r\n                    <circle cx=\"125\" cy=\"110\" r=\"5\" fill=\"#10b981\"/>\r\n                    <circle cx=\"325\" cy=\"105\" r=\"5\" fill=\"#10b981\"/>\r\n\r\n                    {/* Data point values */}\r\n                    <text x=\"125\" y=\"40\" fontSize=\"10\" fill=\"#8b5cf6\" textAnchor=\"middle\" fontWeight=\"600\">36000</text>\r\n                    <text x=\"325\" y=\"35\" fontSize=\"10\" fill=\"#8b5cf6\" textAnchor=\"middle\" fontWeight=\"600\">37000</text>\r\n\r\n                    <text x=\"125\" y=\"70\" fontSize=\"10\" fill=\"#3b82f6\" textAnchor=\"middle\" fontWeight=\"600\">30000</text>\r\n                    <text x=\"325\" y=\"65\" fontSize=\"10\" fill=\"#3b82f6\" textAnchor=\"middle\" fontWeight=\"600\">31000</text>\r\n\r\n                    <text x=\"125\" y=\"100\" fontSize=\"10\" fill=\"#10b981\" textAnchor=\"middle\" fontWeight=\"600\">24000</text>\r\n                    <text x=\"325\" y=\"95\" fontSize=\"10\" fill=\"#10b981\" textAnchor=\"middle\" fontWeight=\"600\">25000</text>\r\n                  </svg>\r\n\r\n                  {/* Legend */}\r\n                  <Box sx={{\r\n                    position: 'absolute',\r\n                    bottom: 10,\r\n                    left: '50%',\r\n                    transform: 'translateX(-50%)',\r\n                    display: 'flex',\r\n                    gap: 3,\r\n                    backgroundColor: 'rgba(255,255,255,0.9)',\r\n                    padding: '8px 16px',\r\n                    borderRadius: '8px',\r\n                    border: '1px solid #e2e8f0'\r\n                  }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                      <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Active</Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                      <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Retained</Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                      <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Total</Typography>\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Card>\r\n\r\n              {/* Feature Adoption Distribution Chart */}\r\n              <Card title=\"� Feature Adoption Distribution\" subtitle=\"Interactive feature usage metrics\" padding=\"lg\">\r\n                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', borderRadius: 'var(--radius-md)', position: 'relative' }}>\r\n                  {/* Donut Chart */}\r\n                  <svg width=\"180\" height=\"180\" viewBox=\"0 0 180 180\" style={{ marginBottom: '20px' }}>\r\n                    {/* Tours - 25% - Blue */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"94 377\" strokeDashoffset=\"0\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Banners - 18% - Orange */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#f97316\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"68 377\" strokeDashoffset=\"-94\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Hotspots - 12% - Teal */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#14b8a6\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"45 377\" strokeDashoffset=\"-162\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Tooltips - 20% - Green */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#10b981\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"75 377\" strokeDashoffset=\"-207\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* AI Assistant - 19% - Purple */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#8b5cf6\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"71 377\" strokeDashoffset=\"-282\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Checklists - 6% - Red */}\r\n                    <circle\r\n                      cx=\"90\" cy=\"90\" r=\"60\" fill=\"none\" stroke=\"#ef4444\" strokeWidth=\"25\"\r\n                      strokeDasharray=\"23 377\" strokeDashoffset=\"-353\" transform=\"rotate(-90 90 90)\"\r\n                    />\r\n\r\n                    {/* Center circle for donut effect with 100% Total */}\r\n                    <circle cx=\"90\" cy=\"90\" r=\"35\" fill=\"white\"/>\r\n                    <text x=\"90\" y=\"85\" fontSize=\"16\" fill=\"#1f2937\" textAnchor=\"middle\" fontWeight=\"bold\">100%</text>\r\n                    <text x=\"90\" y=\"100\" fontSize=\"11\" fill=\"#6b7280\" textAnchor=\"middle\">Total</text>\r\n                  </svg>\r\n\r\n                  {/* Legend */}\r\n                  <Box sx={{\r\n                    display: 'grid',\r\n                    gridTemplateColumns: 'repeat(3, 1fr)',\r\n                    gap: 2,\r\n                    width: '100%',\r\n                    maxWidth: '400px'\r\n                  }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Tours</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>25%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Tooltips</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>20%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Announcement</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>19%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#f97316', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Banners</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>18%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#14b8a6', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Hotspots</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>12%</Typography>\r\n                    </Box>\r\n\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                        <Box sx={{ width: 12, height: 12, backgroundColor: '#ef4444', borderRadius: '50%' }} />\r\n                        <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b' }}>Checklists</Typography>\r\n                      </Box>\r\n                      <Typography variant=\"caption\" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>6%</Typography>\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Card>\r\n            </Box>\r\n\r\n            {/* Quick Actions and Recent Activity - Commented out for now */}\r\n\r\n            {/* User Feedback & Satisfaction Section */}\r\n            {/* User Satisfaction Ratings and Satisfaction Trend */}\r\n            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>\r\n              {/* User Satisfaction Ratings */}\r\n              <Card title=\"⭐ User Satisfaction Ratings\" padding=\"lg\">\r\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\r\n                  {/* Excellent (5★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Excellent (5★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '65%',\r\n                        height: '100%',\r\n                        backgroundColor: '#10b981',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      1,247\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Good (4★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Good (4★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '45%',\r\n                        height: '100%',\r\n                        backgroundColor: '#84cc16',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      892\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Average (3★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Average (3★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '22%',\r\n                        height: '100%',\r\n                        backgroundColor: '#f59e0b',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      434\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Poor (2★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Poor (2★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '8%',\r\n                        height: '100%',\r\n                        backgroundColor: '#f97316',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      123\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Very Poor (1★) */}\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>\r\n                      <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />\r\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\r\n                        Very Poor (1★)\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      flex: 1,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden',\r\n                      mr: 2\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '4%',\r\n                        height: '100%',\r\n                        backgroundColor: '#ef4444',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\" sx={{ minWidth: 40, textAlign: 'right' }}>\r\n                      57\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Summary Cards */}\r\n                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>\r\n                    <Box sx={{\r\n                      p: 2,\r\n                      backgroundColor: '#f0fdf4',\r\n                      borderRadius: 'var(--radius-md)',\r\n                      textAlign: 'center'\r\n                    }}>\r\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#16a34a\">\r\n                        77%\r\n                      </Typography>\r\n                      <Typography variant=\"caption\" color=\"#16a34a\">\r\n                        Positive\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      p: 2,\r\n                      backgroundColor: '#fffbeb',\r\n                      borderRadius: 'var(--radius-md)',\r\n                      textAlign: 'center'\r\n                    }}>\r\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#d97706\">\r\n                        16%\r\n                      </Typography>\r\n                      <Typography variant=\"caption\" color=\"#d97706\">\r\n                        Neutral\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      p: 2,\r\n                      backgroundColor: '#fef2f2',\r\n                      borderRadius: 'var(--radius-md)',\r\n                      textAlign: 'center'\r\n                    }}>\r\n                      <Typography variant=\"h5\" fontWeight=\"bold\" color=\"#dc2626\">\r\n                        7%\r\n                      </Typography>\r\n                      <Typography variant=\"caption\" color=\"#dc2626\">\r\n                        Negative\r\n                      </Typography>\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Card>\r\n\r\n              {/* Satisfaction Trend */}\r\n              <Card title=\"📈 Satisfaction Trend\" padding=\"lg\">\r\n                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8fafc', borderRadius: 'var(--radius-md)', position: 'relative' }}>\r\n                  {/* Satisfaction Trend Line Chart */}\r\n                  <svg width=\"100%\" height=\"250\" viewBox=\"0 0 400 200\" style={{ overflow: 'visible' }}>\r\n                    {/* Grid lines */}\r\n                    <defs>\r\n                      <pattern id=\"feedbackGrid\" width=\"66.67\" height=\"40\" patternUnits=\"userSpaceOnUse\">\r\n                        <path d=\"M 66.67 0 L 0 0 0 40\" fill=\"none\" stroke=\"#e2e8f0\" strokeWidth=\"0.5\"/>\r\n                      </pattern>\r\n                    </defs>\r\n                    <rect width=\"100%\" height=\"100%\" fill=\"url(#feedbackGrid)\" />\r\n\r\n                    {/* Y-axis labels */}\r\n                    <text x=\"10\" y=\"20\" fontSize=\"10\" fill=\"#64748b\">5</text>\r\n                    <text x=\"10\" y=\"60\" fontSize=\"10\" fill=\"#64748b\">4.75</text>\r\n                    <text x=\"10\" y=\"100\" fontSize=\"10\" fill=\"#64748b\">4.5</text>\r\n                    <text x=\"10\" y=\"140\" fontSize=\"10\" fill=\"#64748b\">4.25</text>\r\n                    <text x=\"10\" y=\"180\" fontSize=\"10\" fill=\"#64748b\">4</text>\r\n\r\n                    {/* X-axis labels */}\r\n                    <text x=\"50\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Jan</text>\r\n                    <text x=\"110\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Feb</text>\r\n                    <text x=\"170\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Mar</text>\r\n                    <text x=\"230\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Apr</text>\r\n                    <text x=\"290\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">May</text>\r\n                    <text x=\"350\" y=\"215\" fontSize=\"10\" fill=\"#64748b\">Jun</text>\r\n\r\n                    {/* Line showing satisfaction trend from 4.2 to 4.7 */}\r\n                    <path d=\"M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88\" fill=\"none\" stroke=\"#3b82f6\" strokeWidth=\"3\"/>\r\n\r\n                    {/* Data points */}\r\n                    <circle cx=\"50\" cy=\"168\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"110\" cy=\"152\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"170\" cy=\"136\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"230\" cy=\"120\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"290\" cy=\"104\" r=\"4\" fill=\"#3b82f6\"/>\r\n                    <circle cx=\"350\" cy=\"88\" r=\"4\" fill=\"#3b82f6\"/>\r\n\r\n                    {/* Value labels on data points */}\r\n                    <text x=\"50\" y=\"160\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.2</text>\r\n                    <text x=\"110\" y=\"144\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.3</text>\r\n                    <text x=\"170\" y=\"128\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.4</text>\r\n                    <text x=\"230\" y=\"112\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.5</text>\r\n                    <text x=\"290\" y=\"96\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.6</text>\r\n                    <text x=\"350\" y=\"80\" fontSize=\"9\" fill=\"#3b82f6\" textAnchor=\"middle\">4.7</text>\r\n                  </svg>\r\n                </Box>\r\n              </Card>\r\n            </Box>\r\n\r\n            {/* Feedback Summary */}\r\n            <Card title=\"📊 Feedback Summary\" padding=\"lg\">\r\n              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 'var(--spacing-6)' }}>\r\n                {/* Total Feedback */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#f8fafc',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1 }}>\r\n                    2,238\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Total Feedback\r\n                  </Typography>\r\n                </Box>\r\n\r\n                {/* Positive Sentiment */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#f0fdf4',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#16a34a\" sx={{ mb: 1 }}>\r\n                    85.8%\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Positive Sentiment\r\n                  </Typography>\r\n                </Box>\r\n\r\n                {/* Average Rating */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#eff6ff',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#2563eb\" sx={{ mb: 1 }}>\r\n                    4.6/5\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Average Rating\r\n                  </Typography>\r\n                </Box>\r\n\r\n                {/* Growth vs Last Month */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#fdf4ff',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  textAlign: 'center'\r\n                }}>\r\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"#9333ea\" sx={{ mb: 1 }}>\r\n                    +12%\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    vs Last Month\r\n                  </Typography>\r\n                </Box>\r\n              </Box>\r\n            </Card>\r\n\r\n            {/* Recent Feedback - Commented out for now */}\r\n          </>\r\n        );\r\n\r\n      case 1: // Analytics\r\n        return (\r\n          <>\r\n            {/* Level 1 - Guide Type Cards */}\r\n            <Card title=\"Analytics Overview\" subtitle=\"Select a guide type to view detailed analytics\" padding=\"lg\">\r\n              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: 'var(--spacing-4)', mb: 4 }}>\r\n                {guideTypes.map((guideType) => (\r\n                  <Box\r\n                    key={guideType.id}\r\n                    onClick={() => {\r\n                      setSelectedGuideType(guideType.id);\r\n                      setSelectedGuideFromList(null); // Clear guide selection when changing type\r\n                    }}\r\n                    sx={{\r\n                      p: 3,\r\n                      border: selectedGuideType === guideType.id ? `2px solid ${guideType.color}` : '1px solid var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-md)',\r\n                      backgroundColor: selectedGuideType === guideType.id ? `${guideType.color}08` : 'white',\r\n                      cursor: 'pointer',\r\n                      transition: 'all 0.2s ease',\r\n                      '&:hover': {\r\n                        backgroundColor: selectedGuideType === guideType.id ? `${guideType.color}12` : 'var(--color-gray-50)',\r\n                        transform: 'translateY(-2px)',\r\n                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\r\n                      }\r\n                    }}\r\n                  >\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\" sx={{ color: selectedGuideType === guideType.id ? guideType.color : 'var(--color-gray-900)' }}>\r\n                        {guideType.name}\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        width: 12,\r\n                        height: 12,\r\n                        borderRadius: '50%',\r\n                        backgroundColor: guideType.color\r\n                      }} />\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>\r\n                      <Typography variant=\"body2\" color=\"text.secondary\">\r\n                        {guideType.count} guides\r\n                      </Typography>\r\n                      <Typography variant=\"h5\" fontWeight=\"bold\" sx={{ color: guideType.color }}>\r\n                        {guideType.completionRate}%\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{\r\n                      width: '100%',\r\n                      height: 6,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: `${guideType.completionRate}%`,\r\n                        height: '100%',\r\n                        backgroundColor: guideType.color,\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                ))}\r\n              </Box>\r\n            </Card>\r\n\r\n            {/* Level 2 & 3 - Top Guides Performance and Interactive Guide Funnel */}\r\n            {selectedGuideType && (\r\n              <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)' }}>\r\n                {/* Left Column - Top Guides Performance */}\r\n                <Card title=\"📊 Top Guides Performance\" subtitle=\"Comprehensive guide analytics with completion and engagement metrics\" padding=\"lg\">\r\n                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\r\n                    {guidesByType[selectedGuideType]?.map((guide, index) => {\r\n                      const completionRate = Math.round((guide.completed / guide.views) * 100);\r\n                      const guideColor = guideTypes.find(gt => gt.id === selectedGuideType)?.color || '#8b5cf6';\r\n\r\n                      return (\r\n                        <Box\r\n                          key={guide.id}\r\n                          onClick={() => setSelectedGuideFromList(guide.id)}\r\n                          sx={{\r\n                            p: 3,\r\n                            border: selectedGuideFromList === guide.id ? `2px solid ${guideColor}` : '1px solid var(--color-gray-200)',\r\n                            borderRadius: 'var(--radius-lg)',\r\n                            backgroundColor: selectedGuideFromList === guide.id ? `${guideColor}08` : 'white',\r\n                            cursor: 'pointer',\r\n                            transition: 'all 0.2s ease',\r\n                            '&:hover': {\r\n                              backgroundColor: selectedGuideFromList === guide.id ? `${guideColor}12` : 'var(--color-gray-50)',\r\n                              transform: 'translateY(-2px)',\r\n                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          {/* Guide Header */}\r\n                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                              <Box sx={{\r\n                                width: 8,\r\n                                height: 8,\r\n                                borderRadius: '50%',\r\n                                backgroundColor: guideColor\r\n                              }} />\r\n                              <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                                {guide.name}\r\n                              </Typography>\r\n                            </Box>\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                              <Typography variant=\"h5\" fontWeight=\"bold\" sx={{ color: guideColor }}>\r\n                                {completionRate}%\r\n                              </Typography>\r\n                              <Box sx={{\r\n                                px: 1.5,\r\n                                py: 0.5,\r\n                                backgroundColor: guide.status === 'excellent' ? '#e8f5e9' : guide.status === 'good' ? '#e3f2fd' : '#fff3e0',\r\n                                color: guide.status === 'excellent' ? '#2e7d32' : guide.status === 'good' ? '#1976d2' : '#f57c00',\r\n                                borderRadius: 'var(--radius-sm)',\r\n                                fontSize: '11px',\r\n                                fontWeight: 'medium'\r\n                              }}>\r\n                                {guide.status}\r\n                              </Box>\r\n                            </Box>\r\n                          </Box>\r\n\r\n                          {/* Progress Bar */}\r\n                          <Box sx={{ mb: 2 }}>\r\n                            <Box sx={{\r\n                              width: '100%',\r\n                              height: 8,\r\n                              backgroundColor: 'var(--color-gray-200)',\r\n                              borderRadius: 'var(--radius-full)',\r\n                              overflow: 'hidden'\r\n                            }}>\r\n                              <Box sx={{\r\n                                width: `${completionRate}%`,\r\n                                height: '100%',\r\n                                backgroundColor: guideColor,\r\n                                borderRadius: 'var(--radius-full)'\r\n                              }} />\r\n                            </Box>\r\n                          </Box>\r\n\r\n                          {/* Metrics Row */}\r\n                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                            <Box sx={{ textAlign: 'center' }}>\r\n                              <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ color: '#3b82f6' }}>\r\n                                {guide.views.toLocaleString()}\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                👁 Views\r\n                              </Typography>\r\n                            </Box>\r\n                            <Box sx={{ textAlign: 'center' }}>\r\n                              <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ color: '#10b981' }}>\r\n                                {guide.completed.toLocaleString()}\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                ✅ Completed\r\n                              </Typography>\r\n                            </Box>\r\n                            <Box sx={{ textAlign: 'center' }}>\r\n                              <Typography variant=\"h6\" fontWeight=\"bold\" sx={{\r\n                                color: guide.dropOff < 15 ? '#10b981' : guide.dropOff < 25 ? '#f59e0b' : '#ef4444'\r\n                              }}>\r\n                                {guide.dropOff}%\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                📉 Drop-off\r\n                              </Typography>\r\n                            </Box>\r\n                          </Box>\r\n                        </Box>\r\n                      );\r\n                    })}\r\n                  </Box>\r\n                </Card>\r\n\r\n                {/* Right Column - Interactive Guide Funnel */}\r\n                <Card title=\"🔄 Interactive Guide Funnel\" subtitle=\"Step-by-step user journey with conversion and drop-off analysis\" padding=\"lg\">\r\n                  {selectedGuideFromList ? (\r\n                    (() => {\r\n                      const selectedGuide = guidesByType[selectedGuideType]?.find(g => g.id === selectedGuideFromList);\r\n                      if (!selectedGuide) return null;\r\n\r\n                      // Mock funnel data for the selected guide\r\n                      const funnelSteps = [\r\n                        {\r\n                          name: 'Guide Started',\r\n                          users: selectedGuide.views,\r\n                          rate: 100,\r\n                          color: '#10b981',\r\n                          dropOff: 0\r\n                        },\r\n                        {\r\n                          name: 'Step 1: Welcome',\r\n                          users: Math.round(selectedGuide.views * 0.85),\r\n                          rate: 85,\r\n                          color: '#3b82f6',\r\n                          dropOff: 15\r\n                        },\r\n                        {\r\n                          name: 'Step 2: Setup',\r\n                          users: Math.round(selectedGuide.views * 0.72),\r\n                          rate: 72,\r\n                          color: '#f59e0b',\r\n                          dropOff: 13\r\n                        },\r\n                        {\r\n                          name: 'Step 3: Configuration',\r\n                          users: Math.round(selectedGuide.views * 0.65),\r\n                          rate: 65,\r\n                          color: '#8b5cf6',\r\n                          dropOff: 7\r\n                        },\r\n                        {\r\n                          name: 'Guide Completed',\r\n                          users: selectedGuide.completed,\r\n                          rate: Math.round((selectedGuide.completed / selectedGuide.views) * 100),\r\n                          color: '#ef4444',\r\n                          dropOff: Math.round(((selectedGuide.views * 0.65) - selectedGuide.completed) / (selectedGuide.views * 0.65) * 100)\r\n                        }\r\n                      ];\r\n\r\n                      return (\r\n                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>\r\n                          {funnelSteps.map((step, index) => (\r\n                            <Box\r\n                              key={index}\r\n                              sx={{\r\n                                p: 3,\r\n                                backgroundColor: `${step.color}08`,\r\n                                border: `1px solid ${step.color}20`,\r\n                                borderRadius: 'var(--radius-lg)',\r\n                                position: 'relative'\r\n                              }}\r\n                            >\r\n                              {/* Step Header */}\r\n                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>\r\n                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                  <Box sx={{\r\n                                    width: 8,\r\n                                    height: 8,\r\n                                    borderRadius: '50%',\r\n                                    backgroundColor: step.color\r\n                                  }} />\r\n                                  <Typography variant=\"body1\" fontWeight=\"semibold\">\r\n                                    {step.name}\r\n                                  </Typography>\r\n                                </Box>\r\n                                <Typography variant=\"h5\" fontWeight=\"bold\" sx={{ color: step.color }}>\r\n                                  {step.rate}%\r\n                                </Typography>\r\n                              </Box>\r\n\r\n                              {/* Metrics */}\r\n                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>\r\n                                <Typography variant=\"h4\" fontWeight=\"bold\" sx={{ color: step.color }}>\r\n                                  {step.users.toLocaleString()}\r\n                                </Typography>\r\n                                <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                  Users\r\n                                </Typography>\r\n                              </Box>\r\n\r\n                              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\r\n                                Avg Step: 2m 15s\r\n                              </Typography>\r\n\r\n                              {/* Drop-off indicator */}\r\n                              {step.dropOff > 0 && (\r\n                                <Box sx={{\r\n                                  position: 'absolute',\r\n                                  top: -8,\r\n                                  right: 16,\r\n                                  px: 2,\r\n                                  py: 0.5,\r\n                                  backgroundColor: '#fef2f2',\r\n                                  color: '#dc2626',\r\n                                  borderRadius: 'var(--radius-full)',\r\n                                  fontSize: '12px',\r\n                                  fontWeight: 'medium',\r\n                                  border: '1px solid #fecaca'\r\n                                }}>\r\n                                  -{step.dropOff}% users ({Math.round(selectedGuide.views * (step.dropOff / 100)).toLocaleString()})\r\n                                </Box>\r\n                              )}\r\n                            </Box>\r\n                          ))}\r\n\r\n                          {/* Overall Conversion Summary */}\r\n                          <Box sx={{\r\n                            display: 'flex',\r\n                            justifyContent: 'space-between',\r\n                            alignItems: 'center',\r\n                            mt: 3,\r\n                            pt: 3,\r\n                            borderTop: '1px solid var(--color-gray-200)'\r\n                          }}>\r\n                            <Box sx={{ textAlign: 'center' }}>\r\n                              <Typography variant=\"h3\" fontWeight=\"bold\" sx={{ color: '#10b981' }}>\r\n                                {Math.round((selectedGuide.completed / selectedGuide.views) * 100)}%\r\n                              </Typography>\r\n                              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                Overall Conversion\r\n                              </Typography>\r\n                            </Box>\r\n                            <Box sx={{ textAlign: 'center' }}>\r\n                              <Typography variant=\"h3\" fontWeight=\"bold\" sx={{ color: '#ef4444' }}>\r\n                                {selectedGuide.dropOff}%\r\n                              </Typography>\r\n                              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                Total Drop-off\r\n                              </Typography>\r\n                            </Box>\r\n                          </Box>\r\n                        </Box>\r\n                      );\r\n                    })()\r\n                  ) : (\r\n                    <Box sx={{\r\n                      display: 'flex',\r\n                      flexDirection: 'column',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      py: 8,\r\n                      textAlign: 'center'\r\n                    }}>\r\n                      <Typography variant=\"h6\" color=\"text.secondary\" sx={{ mb: 1 }}>\r\n                        Select a guide from the left\r\n                      </Typography>\r\n                      <Typography variant=\"body2\" color=\"text.secondary\">\r\n                        Choose a guide to view its step-by-step funnel analysis\r\n                      </Typography>\r\n                    </Box>\r\n                  )}\r\n                </Card>\r\n              </Box>\r\n            )}\r\n          </>\r\n        );\r\n\r\n      case 2: // AI Performance\r\n        return (\r\n          <>\r\n            \r\n             {/* Bottom Metrics Cards */}\r\n            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 'var(--spacing-6)', mb: 4 }}>\r\n              {/* Total Interactions Card */}\r\n              <Box sx={{\r\n                p: 4,\r\n                backgroundColor: 'white',\r\n                borderRadius: 'var(--radius-lg)',\r\n                border: '1px solid var(--color-gray-200)',\r\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\r\n                position: 'relative'\r\n              }}>\r\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\r\n                  <Typography variant=\"caption\" color=\"#3b82f6\" sx={{ fontWeight: 'medium' }}>\r\n                    Total Interactions\r\n                  </Typography>\r\n                  <Box sx={{\r\n                    width: 40,\r\n                    height: 40,\r\n                    backgroundColor: '#e3f2fd',\r\n                    borderRadius: 'var(--radius-md)',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Box sx={{\r\n                      width: 24,\r\n                      height: 24,\r\n                      backgroundColor: '#3b82f6',\r\n                      borderRadius: 'var(--radius-sm)',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      color: 'white',\r\n                      fontSize: '14px',\r\n                      fontWeight: 'bold'\r\n                    }}>\r\n                      💬\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\r\n                  2,847\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\r\n                  +12% from last month\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Success Rate Card */}\r\n              <Box sx={{\r\n                p: 4,\r\n                backgroundColor: 'white',\r\n                borderRadius: 'var(--radius-lg)',\r\n                border: '1px solid var(--color-gray-200)',\r\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\r\n                position: 'relative'\r\n              }}>\r\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\r\n                  <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\r\n                    Success Rate\r\n                  </Typography>\r\n                  <Box sx={{\r\n                    width: 40,\r\n                    height: 40,\r\n                    backgroundColor: '#e8f5e9',\r\n                    borderRadius: 'var(--radius-md)',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Box sx={{\r\n                      width: 24,\r\n                      height: 24,\r\n                      backgroundColor: '#10b981',\r\n                      borderRadius: '50%',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      color: 'white',\r\n                      fontSize: '14px',\r\n                      fontWeight: 'bold'\r\n                    }}>\r\n                      ✓\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\r\n                  91%\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\r\n                  +3% improvement\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Avg Response Time Card */}\r\n              <Box sx={{\r\n                p: 4,\r\n                backgroundColor: 'white',\r\n                borderRadius: 'var(--radius-lg)',\r\n                border: '1px solid var(--color-gray-200)',\r\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\r\n                position: 'relative'\r\n              }}>\r\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\r\n                  <Typography variant=\"caption\" color=\"#8b5cf6\" sx={{ fontWeight: 'medium' }}>\r\n                    Avg Response Time\r\n                  </Typography>\r\n                  <Box sx={{\r\n                    width: 40,\r\n                    height: 40,\r\n                    backgroundColor: '#f3e8ff',\r\n                    borderRadius: 'var(--radius-md)',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Box sx={{\r\n                      width: 24,\r\n                      height: 24,\r\n                      backgroundColor: '#8b5cf6',\r\n                      borderRadius: 'var(--radius-sm)',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      color: 'white',\r\n                      fontSize: '14px',\r\n                      fontWeight: 'bold'\r\n                    }}>\r\n                      ⚡\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"text.primary\" sx={{ mb: 1, fontSize: '2rem' }}>\r\n                  1.9s\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"#10b981\" sx={{ fontWeight: 'medium' }}>\r\n                  -0.3s faster\r\n                </Typography>\r\n              </Box>\r\n            </Box>\r\n\r\n            {/* AI Task Performance Section */}\r\n            <Box sx={{ mb: 4 }}>\r\n              <Card title=\"AI Task Performance\" padding=\"lg\">\r\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>\r\n                {/* Password Reset */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Password Reset\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#e8f5e9',\r\n                        color: '#2e7d32',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        96%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        342 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 1.2s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        +2% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      96%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '96%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Account Setup */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Account Setup\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#e3f2fd',\r\n                        color: '#1976d2',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        89%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        198 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 1.4s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        -5% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      89%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '89%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Feature Explanation */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Feature Explanation\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#e3f2fd',\r\n                        color: '#1976d2',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        90%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        267 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 2.1s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        -1% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      90%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '90%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Troubleshooting */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Troubleshooting\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#fff3e0',\r\n                        color: '#f57c00',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        88%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        156 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 3.1s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        -3% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      88%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '88%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Integration Help */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'space-between',\r\n                  p: 3,\r\n                  border: '1px solid var(--color-gray-200)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  '&:hover': {\r\n                    backgroundColor: 'var(--color-gray-50)',\r\n                    cursor: 'pointer'\r\n                  }\r\n                }}>\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\r\n                      <Typography variant=\"h6\" fontWeight=\"semibold\">\r\n                        Integration Help\r\n                      </Typography>\r\n                      <Box sx={{\r\n                        px: 1.5,\r\n                        py: 0.5,\r\n                        backgroundColor: '#fff3e0',\r\n                        color: '#f57c00',\r\n                        borderRadius: 'var(--radius-sm)',\r\n                        fontSize: '12px',\r\n                        fontWeight: 'medium'\r\n                      }}>\r\n                        87%\r\n                      </Box>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        123 interactions\r\n                      </Typography>\r\n                    </Box>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        Avg time: 2.5s\r\n                      </Typography>\r\n                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>\r\n                        +1% trend\r\n                      </Box>\r\n                    </Box>\r\n                  </Box>\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                    <Typography variant=\"h4\" fontWeight=\"bold\">\r\n                      87%\r\n                    </Typography>\r\n                    <Box sx={{\r\n                      width: 80,\r\n                      height: 8,\r\n                      backgroundColor: 'var(--color-gray-200)',\r\n                      borderRadius: 'var(--radius-full)',\r\n                      overflow: 'hidden'\r\n                    }}>\r\n                      <Box sx={{\r\n                        width: '87%',\r\n                        height: '100%',\r\n                        backgroundColor: 'var(--color-gray-800)',\r\n                        borderRadius: 'var(--radius-full)'\r\n                      }} />\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            </Card>\r\n            </Box>\r\n\r\n            {/* AI Insights & Recommendations */}\r\n            <Card title=\"AI Insights & Recommendations\" padding=\"lg\">\r\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>\r\n                {/* Optimize Workflow */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#fffbeb',\r\n                  border: '1px solid #fbbf24',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  display: 'flex',\r\n                  alignItems: 'flex-start',\r\n                  gap: 2\r\n                }}>\r\n                  <Box sx={{\r\n                    width: 6,\r\n                    height: 6,\r\n                    backgroundColor: '#f59e0b',\r\n                    borderRadius: '50%',\r\n                    mt: 1\r\n                  }} />\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\r\n                      Optimize Workflow\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      Consider optimizing your workflow to reduce response time by 15%\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Excluded Performance */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#f0f9ff',\r\n                  border: '1px solid #3b82f6',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  display: 'flex',\r\n                  alignItems: 'flex-start',\r\n                  gap: 2\r\n                }}>\r\n                  <Box sx={{\r\n                    width: 6,\r\n                    height: 6,\r\n                    backgroundColor: '#3b82f6',\r\n                    borderRadius: '50%',\r\n                    mt: 1\r\n                  }} />\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\r\n                      Excluded Performance\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      Excluded tasks are performing well with 94% accuracy rate\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Personalized Suggestions */}\r\n                <Box sx={{\r\n                  p: 3,\r\n                  backgroundColor: '#f0f9ff',\r\n                  border: '1px solid #3b82f6',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  display: 'flex',\r\n                  alignItems: 'flex-start',\r\n                  gap: 2\r\n                }}>\r\n                  <Box sx={{\r\n                    width: 6,\r\n                    height: 6,\r\n                    backgroundColor: '#3b82f6',\r\n                    borderRadius: '50%',\r\n                    mt: 1\r\n                  }} />\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ mb: 0.5 }}>\r\n                      Personalized Suggestions\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      AI suggests implementing advanced filtering for better user experience\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            </Card>\r\n          </>\r\n        );\r\n\r\n\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className='qadpt-web'>\r\n      <div className='qadpt-webcontent'>\r\n        <DashboardWrapper>\r\n          <Container maxWidth=\"xl\" sx={{ py: 3 }}>\r\n            {/* Header Section */}\r\n            <Box sx={{\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'space-between',\r\n              mb: 4,\r\n              pb: 3,\r\n              borderBottom: '1px solid #e2e8f0'\r\n            }}>\r\n              {/* Left Side - Title and Subtitle */}\r\n              <Box>\r\n                <Typography variant=\"h4\" fontWeight=\"bold\" sx={{ color: '#1e293b', mb: 0.5 }}>\r\n                  Digital Adoption Platform\r\n                </Typography>\r\n                <Typography variant=\"body2\" sx={{ color: '#64748b' }}>\r\n                  Admin Dashboard - Guide Creation & Analytics\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Right Side - Action Buttons */}\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                {/* Search Input */}\r\n                <Box sx={{ position: 'relative' }}>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"Search guides, users...\"\r\n                    style={{\r\n                      width: '240px',\r\n                      height: '36px',\r\n                      padding: '0 12px 0 36px',\r\n                      border: '1px solid #d1d5db',\r\n                      borderRadius: '6px',\r\n                      fontSize: '14px',\r\n                      backgroundColor: 'white',\r\n                      outline: 'none'\r\n                    }}\r\n                  />\r\n                  <Box sx={{\r\n                    position: 'absolute',\r\n                    left: '12px',\r\n                    top: '50%',\r\n                    transform: 'translateY(-50%)',\r\n                    color: '#9ca3af',\r\n                    fontSize: '16px'\r\n                  }}>\r\n                    🔍\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Time Filter Dropdown */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: 1,\r\n                  px: 2,\r\n                  py: 1,\r\n                  border: '1px solid #d1d5db',\r\n                  borderRadius: '6px',\r\n                  backgroundColor: 'white',\r\n                  cursor: 'pointer',\r\n                  fontSize: '14px',\r\n                  color: '#374151',\r\n                  '&:hover': {\r\n                    backgroundColor: '#f9fafb'\r\n                  }\r\n                }}>\r\n                  📅 30 days ▼\r\n                </Box>\r\n\r\n                {/* Refresh Button */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  width: '36px',\r\n                  height: '36px',\r\n                  border: '1px solid #d1d5db',\r\n                  borderRadius: '6px',\r\n                  backgroundColor: 'white',\r\n                  cursor: 'pointer',\r\n                  fontSize: '16px',\r\n                  '&:hover': {\r\n                    backgroundColor: '#f9fafb'\r\n                  }\r\n                }}>\r\n                  🔄\r\n                </Box>\r\n\r\n                {/* Export Button */}\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: 1,\r\n                  px: 3,\r\n                  py: 1,\r\n                  backgroundColor: '#3b82f6',\r\n                  color: 'white',\r\n                  borderRadius: '6px',\r\n                  cursor: 'pointer',\r\n                  fontSize: '14px',\r\n                  fontWeight: 'medium',\r\n                  '&:hover': {\r\n                    backgroundColor: '#2563eb'\r\n                  }\r\n                }}>\r\n                  📤 Export\r\n                </Box>\r\n              </Box>\r\n            </Box>\r\n\r\n            {/* Navigation Tabs */}\r\n            <StyledTabs value={selectedTab} onChange={handleTabChange}>\r\n              <Tab label=\"Overview\" />\r\n              <Tab label=\"Analytics\" />\r\n              <Tab label=\"AI Performance\" />\r\n            </StyledTabs>\r\n\r\n            {/* Render Tab Content */}\r\n            {renderTabContent()}\r\n          </Container>\r\n        </DashboardWrapper>\r\n\r\n        {/* Interactive Tooltip */}\r\n        {tooltip.visible && (\r\n          <Box\r\n            sx={{\r\n              position: 'fixed',\r\n              left: tooltip.x,\r\n              top: tooltip.y,\r\n              transform: 'translate(-50%, -100%)',\r\n              backgroundColor: 'white',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '6px',\r\n              padding: '6px 10px',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n              zIndex: 10000,\r\n              pointerEvents: 'none',\r\n              fontSize: '11px',\r\n              minWidth: '70px',\r\n              textAlign: 'center',\r\n              fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\r\n            }}\r\n          >\r\n            <Typography variant=\"body2\" sx={{ fontSize: '10px', color: '#6b7280', mb: 0.2, lineHeight: 1.2 }}>\r\n              {tooltip.title}\r\n            </Typography>\r\n            <Typography variant=\"body2\" sx={{ fontSize: '11px', color: '#111827', fontWeight: '600', lineHeight: 1.2 }}>\r\n              {tooltip.content}\r\n            </Typography>\r\n          </Box>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ModernDashboard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,UAAU,EAAcC,SAAS,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AACjF,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SACEC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,WAAW,EACXC,IAAI,EACJC,QAAQ,QAGH,qBAAqB;AAC5B,OAAOC,IAAI,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAclC,MAAMC,gBAAgB,GAAGZ,MAAM,CAAC,KAAK,CAAC,CAAC;EACrCa,eAAe,EAAE,SAAS;EAC1BC,SAAS,EAAE;AACb,CAAC,CAAC;AAACC,EAAA,GAHGH,gBAAgB;AAKtB,MAAMI,aAAa,GAAGhB,MAAM,CAACL,GAAG,CAAC,CAAC;EAChCsB,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE,eAAe;EAC/BC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAE;AAChB,CAAC,CAAC;AAEF,MAAMC,UAAU,GAAGrB,MAAM,CAACF,IAAI,CAAC,CAAC;EAC9BsB,YAAY,EAAE,kBAAkB;EAChC,sBAAsB,EAAE;IACtBP,eAAe,EAAE;EACnB,CAAC;EACD,gBAAgB,EAAE;IAChBS,QAAQ,EAAE,qBAAqB;IAC/BC,UAAU,EAAE,2BAA2B;IACvCC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE,uBAAuB;IAC9B,gBAAgB,EAAE;MAChBA,KAAK,EAAE,0BAA0B;MACjCF,UAAU,EAAE;IACd;EACF;AACF,CAAC,CAAC;AAACG,GAAA,GAfGL,UAAU;AAiBhB,MAAMM,aAAa,GAAG3B,MAAM,CAACL,GAAG,CAAC,CAAC;EAChCsB,OAAO,EAAE,MAAM;EACfW,GAAG,EAAE,kBAAkB;EACvBT,UAAU,EAAE;AACd,CAAC,CAAC;AAEF,MAAMU,WAAW,GAAG7B,MAAM,CAACL,GAAG,CAAC,CAAC;EAC9BsB,OAAO,EAAE,MAAM;EACfa,mBAAmB,EAAE,sCAAsC;EAC3DF,GAAG,EAAE,kBAAkB;EACvBR,YAAY,EAAE;AAChB,CAAC,CAAC;AAACW,GAAA,GALGF,WAAW;AAOjB,MAAMG,mBAAmB,GAAGhC,MAAM,CAACO,IAAI,CAAC,CAAC;EACvC0B,OAAO,EAAE,kBAAkB;EAC3BhB,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBS,GAAG,EAAE;AACP,CAAC,CAAC;AAACM,GAAA,GALGF,mBAAmB;AAOzB,MAAMG,UAAU,GAAGnC,MAAM,CAACL,GAAG,CAAC,CAAoB,CAAC;EAAE8B;AAAM,CAAC,MAAM;EAChEW,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,YAAY,EAAE,kBAAkB;EAChCzB,eAAe,EAAE,GAAGY,KAAK,IAAI;EAC7BR,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBD,cAAc,EAAE,QAAQ;EAExB,OAAO,EAAE;IACPO,KAAK,EAAEA,KAAK;IACZH,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC,CAAC;AAACiB,GAAA,GAbEJ,UAAU;AAehB,MAAMK,aAAa,GAAGxC,MAAM,CAACL,GAAG,CAAC,CAAC;EAChC8C,IAAI,EAAE;AACR,CAAC,CAAC;AAACC,GAAA,GAFGF,aAAa;AAInB,MAAMG,WAAW,GAAG3C,MAAM,CAACJ,UAAU,CAAC,CAAC;EACrC0B,QAAQ,EAAE,qBAAqB;EAC/BG,KAAK,EAAE,uBAAuB;EAC9BL,YAAY,EAAE;AAChB,CAAC,CAAC;AAACwB,GAAA,GAJGD,WAAW;AAMjB,MAAME,WAAW,GAAG7C,MAAM,CAACJ,UAAU,CAAC,CAAC;EACrC0B,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,yBAAyB;EACrCE,KAAK,EAAE,uBAAuB;EAC9BL,YAAY,EAAE;AAChB,CAAC,CAAC;AAAC0B,GAAA,GALGD,WAAW;AAOjB,MAAME,YAAY,GAAG/C,MAAM,CAACL,GAAG,CAAC,CAAC;EAC/BsB,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBS,GAAG,EAAE;AACP,CAAC,CAAC;AAACoB,GAAA,GAJGD,YAAY;AAMlB,MAAME,eAAe,GAAGjD,MAAM,CAACL,GAAG,CAAC,CAA2B,CAAC;EAAEuD;AAAM,CAAC,MAAM;EAC5EjC,OAAO,EAAE,MAAM;EACfE,UAAU,EAAE,QAAQ;EACpBS,GAAG,EAAE,kBAAkB;EACvBN,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,2BAA2B;EACvCE,KAAK,EAAEyB,KAAK,KAAK,IAAI,GAAG,0BAA0B,GAAG,wBAAwB;EAE7E,OAAO,EAAE;IACP5B,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC,CAAC;AAAC6B,GAAA,GAXEF,eAAe;AAarB,MAAMG,UAAqC,GAAGA,CAAC;EAC7CC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,WAAW;EACXN,KAAK;EACLO,IAAI;EACJhC;AACF,CAAC,KAAK;EACJ,oBACEhB,OAAA,CAACuB,mBAAmB;IAAC0B,MAAM,EAAC,IAAI;IAACC,KAAK;IAAAC,QAAA,gBACpCnD,OAAA,CAAC0B,UAAU;MAACV,KAAK,EAAEA,KAAM;MAAAmC,QAAA,EACtBH;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACbvD,OAAA,CAAC+B,aAAa;MAAAoB,QAAA,gBACZnD,OAAA,CAACkC,WAAW;QAAAiB,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAClCvD,OAAA,CAACoC,WAAW;QAAAe,QAAA,EAAEN;MAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAClCvD,OAAA,CAACsC,YAAY;QAAAa,QAAA,gBACXnD,OAAA,CAACwC,eAAe;UAACC,KAAK,EAAEA,KAAM;UAAAU,QAAA,GAC3BV,KAAK,KAAK,IAAI,gBAAGzC,OAAA,CAACR,UAAU;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACP,YAAY;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAClDT,MAAM;QAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAClBvD,OAAA,CAACb,UAAU;UAACqE,OAAO,EAAC,SAAS;UAACxC,KAAK,EAAC,gBAAgB;UAAAmC,QAAA,EACjDJ;QAAW;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAE1B,CAAC;AAACE,GAAA,GA7BId,UAAqC;AA+B3C,MAAMe,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9E,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAAC+E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGhF,QAAQ,CAAgB,IAAI,CAAC;EACvF,MAAM,CAACiF,OAAO,EAAEC,UAAU,CAAC,GAAGlF,QAAQ,CAMnC;IACDmF,OAAO,EAAE,KAAK;IACdC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,OAAO,EAAE,EAAE;IACX3B,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAM4B,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,CAAC;IAAEC,cAAc,EAAE,EAAE;IAAE5D,KAAK,EAAE;EAAU,CAAC,EAC9E;IAAEyD,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,EAAE;IAAEC,cAAc,EAAE,EAAE;IAAE5D,KAAK,EAAE;EAAU,CAAC,EACnF;IAAEyD,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,CAAC;IAAEC,cAAc,EAAE,EAAE;IAAE5D,KAAK,EAAE;EAAU,CAAC,EAC9F;IAAEyD,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,EAAE;IAAEC,cAAc,EAAE,EAAE;IAAE5D,KAAK,EAAE;EAAU,CAAC,EACrF;IAAEyD,EAAE,EAAE,cAAc;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,CAAC;IAAEC,cAAc,EAAE,EAAE;IAAE5D,KAAK,EAAE;EAAU,CAAC,EAC5F;IAAEyD,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC;IAAEC,cAAc,EAAE,EAAE;IAAE5D,KAAK,EAAE;EAAU,CAAC,CACrF;EAED,MAAM6D,YAAmC,GAAG;IAC1CC,KAAK,EAAE,CACL;MAAEL,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,oBAAoB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAa,CAAC,EACvI;MAAEV,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,mBAAmB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAY,CAAC,EAC/H;MAAEV,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,mBAAmB;MAAEK,KAAK,EAAE,GAAG;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,iBAAiB;MAAEC,WAAW,EAAE;IAAa,CAAC,CAC3I;IACDC,OAAO,EAAE,CACP;MAAEX,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,gBAAgB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,CAAC;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAY,CAAC,EACnI;MAAEV,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,sBAAsB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAa,CAAC,CAC5I;IACDE,aAAa,EAAE,CACb;MAAEZ,EAAE,EAAE,gBAAgB;MAAEC,IAAI,EAAE,qBAAqB;MAAEK,KAAK,EAAE,GAAG;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAa,CAAC,EACzI;MAAEV,EAAE,EAAE,gBAAgB;MAAEC,IAAI,EAAE,oBAAoB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAa,CAAC,CAC1I;IACDG,QAAQ,EAAE,CACR;MAAEb,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAE,eAAe;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAY,CAAC,EACpI;MAAEV,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAE,eAAe;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAa,CAAC,CACtI;IACDI,YAAY,EAAE,CACZ;MAAEd,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,qBAAqB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,iBAAiB;MAAEC,WAAW,EAAE;IAAa,CAAC,CAChJ;IACDK,QAAQ,EAAE,CACR;MAAEf,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAE,mBAAmB;MAAEK,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAa,CAAC;EAEvI,CAAC;EAED,MAAMM,WAAW,GAAGA,CAACC,KAAuB,EAAE9C,KAAa,EAAE2B,OAAe,KAAK;IAC/E,MAAMoB,IAAI,GAAGD,KAAK,CAACE,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,OAAO,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACC,UAAU;IACzE,MAAMC,OAAO,GAAGL,MAAM,CAACM,WAAW,IAAIJ,QAAQ,CAACC,eAAe,CAACI,SAAS;IAExEnC,UAAU,CAAC;MACTC,OAAO,EAAE,IAAI;MACbC,CAAC,EAAEsB,IAAI,CAACY,IAAI,GAAGT,OAAO,GAAGH,IAAI,CAAChE,KAAK,GAAG,CAAC;MACvC2C,CAAC,EAAEqB,IAAI,CAACa,GAAG,GAAGJ,OAAO,GAAG,EAAE;MAC1B7B,OAAO;MACP3B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6D,WAAW,GAAGA,CAAA,KAAM;IACxBtC,UAAU,CAACuC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtC,OAAO,EAAE;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMuC,eAAe,GAAG,CACtB;IACE/D,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACL,WAAW;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBvC,KAAK,EAAE;EACT,CAAC,EACD;IACE4B,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,YAAY;IACzBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACJ,IAAI;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdvC,KAAK,EAAE;EACT,CAAC,EACD;IACE4B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,OAAO;IACpBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACH,QAAQ;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBvC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM4F,gBAAgB,GAAG,CACvB;IACEhE,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACN,MAAM;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBvC,KAAK,EAAE;EACT,CAAC,EACD;IACE4B,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACL,WAAW;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBvC,KAAK,EAAE;EACT,CAAC,EACD;IACE4B,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,YAAY;IACzBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACJ,IAAI;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdvC,KAAK,EAAE;EACT,CAAC,EACD;IACE4B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE,OAAO;IACpBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACH,QAAQ;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBvC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM6F,oBAAoB,GAAG,CAC3B;IACEjE,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACH,QAAQ;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBvC,KAAK,EAAE;EACT,CAAC,EACD;IACE4B,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,UAAU;IACvBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACL,WAAW;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBvC,KAAK,EAAE;EACT,CAAC,EACD;IACE4B,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE,QAAQ;IACrBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACJ,IAAI;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACdvC,KAAK,EAAE;EACT,CAAC,EACD;IACE4B,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE,UAAU;IACvBN,KAAK,EAAE,IAAa;IACpBO,IAAI,eAAEhD,OAAA,CAACN,MAAM;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBvC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAM8F,eAAe,GAAGA,CAACpB,KAA2B,EAAEqB,QAAgB,KAAK;IACzElD,cAAc,CAACkD,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC7B,QAAQrD,WAAW;MACjB,KAAK,CAAC;QAAE;QACN,oBACE5D,OAAA,CAAAE,SAAA;UAAAiD,QAAA,gBAIEnD,OAAA,CAACoB,WAAW;YAAA+B,QAAA,EACTyD,gBAAgB,CAACM,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAClCpH,OAAA,CAAC2C,UAAU;cAAA,GAAiBwE;YAAM,GAAjBC,KAAK;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAGdvD,OAAA,CAACd,GAAG;YAACmI,EAAE,EAAE;cAAE7G,OAAO,EAAE,MAAM;cAAEa,mBAAmB,EAAE,SAAS;cAAEF,GAAG,EAAE,kBAAkB;cAAEmG,EAAE,EAAE;YAAE,CAAE;YAAAnE,QAAA,gBAE3FnD,OAAA,CAACF,IAAI;cAAC8C,KAAK,EAAC,mCAAyB;cAAC2E,QAAQ,EAAC,6CAA6C;cAAC/F,OAAO,EAAC,IAAI;cAAA2B,QAAA,eACvGnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAEzF,MAAM,EAAE,OAAO;kBAAE4F,QAAQ,EAAE,UAAU;kBAAEpH,eAAe,EAAE,OAAO;kBAAEyB,YAAY,EAAE;gBAAmB,CAAE;gBAAAsB,QAAA,gBAK7GnD,OAAA;kBAAK2B,KAAK,EAAC,MAAM;kBAACC,MAAM,EAAC,KAAK;kBAAC6F,OAAO,EAAC,aAAa;kBAACC,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAU,CAAE;kBAAAxE,QAAA,gBAElFnD,OAAA;oBAAAmD,QAAA,eACEnD,OAAA;sBAASyE,EAAE,EAAC,cAAc;sBAAC9C,KAAK,EAAC,KAAK;sBAACC,MAAM,EAAC,IAAI;sBAACgG,YAAY,EAAC,gBAAgB;sBAAAzE,QAAA,eAC9EnD,OAAA;wBAAM6H,CAAC,EAAC,oBAAoB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAG;wBAAA5E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACPvD,OAAA;oBAAM2B,KAAK,EAAC,MAAM;oBAACC,MAAM,EAAC,KAAK;oBAACkG,IAAI,EAAC;kBAAoB;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG5DvD,OAAA;oBAAMiI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACL,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAA5E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAEzEvD,OAAA;oBAAMiI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACL,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAA5E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG3EvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAlF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAlF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAlF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAlF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,KAAK;oBAAAlF,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG3EvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpFvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGpFvD,OAAA;oBAAM6H,CAAC,EAAC,mBAAmB;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACM,aAAa,EAAC;kBAAO;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChGvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/CvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG/CvD,OAAA;oBAAM6H,CAAC,EAAC,mBAAmB;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACM,aAAa,EAAC;kBAAO;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChGvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/CvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG/CvD,OAAA;oBAAM6H,CAAC,EAAC,qBAAqB;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACM,aAAa,EAAC;kBAAO;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAClGvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAGhDvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACvH,UAAU,EAAC,KAAK;oBAAAqC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnGvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACvH,UAAU,EAAC,KAAK;oBAAAqC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAEnGvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACvH,UAAU,EAAC,KAAK;oBAAAqC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnGvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACvH,UAAU,EAAC,KAAK;oBAAAqC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAEnGvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACvH,UAAU,EAAC,KAAK;oBAAAqC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpGvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACvH,UAAU,EAAC,KAAK;oBAAAqC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACPG,QAAQ,EAAE,UAAU;oBACpBkB,MAAM,EAAE,EAAE;oBACVnC,IAAI,EAAE,KAAK;oBACXoC,SAAS,EAAE,kBAAkB;oBAC7BnI,OAAO,EAAE,MAAM;oBACfW,GAAG,EAAE,CAAC;oBACNf,eAAe,EAAE,uBAAuB;oBACxCoB,OAAO,EAAE,UAAU;oBACnBK,YAAY,EAAE,KAAK;oBACnB+G,MAAM,EAAE;kBACV,CAAE;kBAAAzF,QAAA,gBACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE;oBAAE,CAAE;oBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE1F,KAAK,EAAE,EAAE;wBAAEC,MAAM,EAAE,EAAE;wBAAExB,eAAe,EAAE,SAAS;wBAAEyB,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE;sBAAU,CAAE;sBAAAmC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE;oBAAE,CAAE;oBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE1F,KAAK,EAAE,EAAE;wBAAEC,MAAM,EAAE,EAAE;wBAAExB,eAAe,EAAE,SAAS;wBAAEyB,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE;sBAAU,CAAE;sBAAAmC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE;oBAAE,CAAE;oBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE1F,KAAK,EAAE,EAAE;wBAAEC,MAAM,EAAE,EAAE;wBAAExB,eAAe,EAAE,SAAS;wBAAEyB,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE;sBAAU,CAAE;sBAAAmC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGPvD,OAAA,CAACF,IAAI;cAAC8C,KAAK,EAAC,sCAAiC;cAAC2E,QAAQ,EAAC,mCAAmC;cAAC/F,OAAO,EAAC,IAAI;cAAA2B,QAAA,eACrGnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAEzF,MAAM,EAAE,OAAO;kBAAEpB,OAAO,EAAE,MAAM;kBAAEqI,aAAa,EAAE,QAAQ;kBAAEpI,cAAc,EAAE,QAAQ;kBAAEC,UAAU,EAAE,QAAQ;kBAAEN,eAAe,EAAE,OAAO;kBAAEyB,YAAY,EAAE,kBAAkB;kBAAE2F,QAAQ,EAAE;gBAAW,CAAE;gBAAArE,QAAA,gBAEvMnD,OAAA;kBAAK2B,KAAK,EAAC,KAAK;kBAACC,MAAM,EAAC,KAAK;kBAAC6F,OAAO,EAAC,aAAa;kBAACC,KAAK,EAAE;oBAAE/G,YAAY,EAAE;kBAAO,CAAE;kBAAAwC,QAAA,gBAElFnD,OAAA;oBACEuI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,GAAG;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC,eAGFvD,OAAA;oBACEuI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,KAAK;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eAGFvD,OAAA;oBACEuI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,MAAM;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eAGFvD,OAAA;oBACEuI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,MAAM;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eAGFvD,OAAA;oBACEuI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,MAAM;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eAGFvD,OAAA;oBACEuI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,IAAI;oBACpEc,eAAe,EAAC,QAAQ;oBAACC,gBAAgB,EAAC,MAAM;oBAACJ,SAAS,EAAC;kBAAmB;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eAGFvD,OAAA;oBAAQuI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACX,IAAI,EAAC;kBAAO;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC7CvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAACvH,UAAU,EAAC,MAAM;oBAAAqC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClGvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP7G,OAAO,EAAE,MAAM;oBACfa,mBAAmB,EAAE,gBAAgB;oBACrCF,GAAG,EAAE,CAAC;oBACNQ,KAAK,EAAE,MAAM;oBACbqH,QAAQ,EAAE;kBACZ,CAAE;kBAAA7F,QAAA,gBACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA0C,QAAA,gBAClFnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE;sBAAE,CAAE;sBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE1F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAExB,eAAe,EAAE,SAAS;0BAAEyB,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAAC6D,EAAE,EAAE;0BAAExG,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAmC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC,eACNvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAqC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA0C,QAAA,gBAClFnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE;sBAAE,CAAE;sBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE1F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAExB,eAAe,EAAE,SAAS;0BAAEyB,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAAC6D,EAAE,EAAE;0BAAExG,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAmC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC,eACNvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAqC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA0C,QAAA,gBAClFnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE;sBAAE,CAAE;sBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE1F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAExB,eAAe,EAAE,SAAS;0BAAEyB,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAAC6D,EAAE,EAAE;0BAAExG,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAmC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACNvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAqC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA0C,QAAA,gBAClFnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE;sBAAE,CAAE;sBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE1F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAExB,eAAe,EAAE,SAAS;0BAAEyB,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAAC6D,EAAE,EAAE;0BAAExG,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAmC,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACNvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAqC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA0C,QAAA,gBAClFnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE;sBAAE,CAAE;sBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE1F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAExB,eAAe,EAAE,SAAS;0BAAEyB,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAAC6D,EAAE,EAAE;0BAAExG,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAmC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5F,CAAC,eACNvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAqC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAENvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE;oBAAgB,CAAE;oBAAA0C,QAAA,gBAClFnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE;sBAAE,CAAE;sBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE1F,KAAK,EAAE,EAAE;0BAAEC,MAAM,EAAE,EAAE;0BAAExB,eAAe,EAAE,SAAS;0BAAEyB,YAAY,EAAE;wBAAM;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvFvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAAC6D,EAAE,EAAE;0BAAExG,QAAQ,EAAE,MAAM;0BAAEG,KAAK,EAAE;wBAAU,CAAE;wBAAAmC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,eACNvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAAC6D,EAAE,EAAE;wBAAExG,QAAQ,EAAE,MAAM;wBAAEG,KAAK,EAAE,SAAS;wBAAEF,UAAU,EAAE;sBAAI,CAAE;sBAAAqC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAMNvD,OAAA,CAACd,GAAG;YAACmI,EAAE,EAAE;cAAE7G,OAAO,EAAE,MAAM;cAAEa,mBAAmB,EAAE,SAAS;cAAEF,GAAG,EAAE,kBAAkB;cAAEmG,EAAE,EAAE;YAAE,CAAE;YAAAnE,QAAA,gBAE3FnD,OAAA,CAACF,IAAI;cAAC8C,KAAK,EAAC,kCAA6B;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eACpDnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAE7G,OAAO,EAAE,MAAM;kBAAEqI,aAAa,EAAE,QAAQ;kBAAE1H,GAAG,EAAE;gBAAmB,CAAE;gBAAAgC,QAAA,gBAE7EnD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAES,GAAG,EAAE;kBAAE,CAAE;kBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE,CAAC;sBAAE8H,QAAQ,EAAE;oBAAI,CAAE;oBAAA9F,QAAA,gBACxEnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE1F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAExB,eAAe,EAAE,SAAS;wBAAEyB,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,OAAO;sBAAC1C,UAAU,EAAC,QAAQ;sBAAAqC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACPrF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACTxB,eAAe,EAAE,uBAAuB;sBACxCyB,YAAY,EAAE,oBAAoB;sBAClC8F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAA/F,QAAA,eACAnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACdxB,eAAe,EAAE,SAAS;wBAC1ByB,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAAC1C,UAAU,EAAC,MAAM;oBAACuG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAhG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAES,GAAG,EAAE;kBAAE,CAAE;kBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE,CAAC;sBAAE8H,QAAQ,EAAE;oBAAI,CAAE;oBAAA9F,QAAA,gBACxEnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE1F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAExB,eAAe,EAAE,SAAS;wBAAEyB,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,OAAO;sBAAC1C,UAAU,EAAC,QAAQ;sBAAAqC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACPrF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACTxB,eAAe,EAAE,uBAAuB;sBACxCyB,YAAY,EAAE,oBAAoB;sBAClC8F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAA/F,QAAA,eACAnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACdxB,eAAe,EAAE,SAAS;wBAC1ByB,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAAC1C,UAAU,EAAC,MAAM;oBAACuG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAhG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAES,GAAG,EAAE;kBAAE,CAAE;kBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE,CAAC;sBAAE8H,QAAQ,EAAE;oBAAI,CAAE;oBAAA9F,QAAA,gBACxEnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE1F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAExB,eAAe,EAAE,SAAS;wBAAEyB,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,OAAO;sBAAC1C,UAAU,EAAC,QAAQ;sBAAAqC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACPrF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACTxB,eAAe,EAAE,uBAAuB;sBACxCyB,YAAY,EAAE,oBAAoB;sBAClC8F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAA/F,QAAA,eACAnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,KAAK;wBACZC,MAAM,EAAE,MAAM;wBACdxB,eAAe,EAAE,SAAS;wBAC1ByB,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAAC1C,UAAU,EAAC,MAAM;oBAACuG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAhG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAES,GAAG,EAAE;kBAAE,CAAE;kBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE,CAAC;sBAAE8H,QAAQ,EAAE;oBAAI,CAAE;oBAAA9F,QAAA,gBACxEnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE1F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAExB,eAAe,EAAE,SAAS;wBAAEyB,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,OAAO;sBAAC1C,UAAU,EAAC,QAAQ;sBAAAqC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACPrF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACTxB,eAAe,EAAE,uBAAuB;sBACxCyB,YAAY,EAAE,oBAAoB;sBAClC8F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAA/F,QAAA,eACAnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,IAAI;wBACXC,MAAM,EAAE,MAAM;wBACdxB,eAAe,EAAE,SAAS;wBAC1ByB,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAAC1C,UAAU,EAAC,MAAM;oBAACuG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAhG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAES,GAAG,EAAE;kBAAE,CAAE;kBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE,CAAC;sBAAE8H,QAAQ,EAAE;oBAAI,CAAE;oBAAA9F,QAAA,gBACxEnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE1F,KAAK,EAAE,CAAC;wBAAEC,MAAM,EAAE,CAAC;wBAAExB,eAAe,EAAE,SAAS;wBAAEyB,YAAY,EAAE;sBAAM;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrFvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,OAAO;sBAAC1C,UAAU,EAAC,QAAQ;sBAAAqC,QAAA,EAAC;oBAEhD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACPrF,IAAI,EAAE,CAAC;sBACPJ,MAAM,EAAE,CAAC;sBACTxB,eAAe,EAAE,uBAAuB;sBACxCyB,YAAY,EAAE,oBAAoB;sBAClC8F,QAAQ,EAAE,QAAQ;sBAClBuB,EAAE,EAAE;oBACN,CAAE;oBAAA/F,QAAA,eACAnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,IAAI;wBACXC,MAAM,EAAE,MAAM;wBACdxB,eAAe,EAAE,SAAS;wBAC1ByB,YAAY,EAAE;sBAChB;oBAAE;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAAC1C,UAAU,EAAC,MAAM;oBAACuG,EAAE,EAAE;sBAAE4B,QAAQ,EAAE,EAAE;sBAAEE,SAAS,EAAE;oBAAQ,CAAE;oBAAAhG,QAAA,EAAC;kBAExF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEa,mBAAmB,EAAE,gBAAgB;oBAAEF,GAAG,EAAE,CAAC;oBAAEiI,EAAE,EAAE;kBAAE,CAAE;kBAAAjG,QAAA,gBACjFnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACPgC,CAAC,EAAE,CAAC;sBACJjJ,eAAe,EAAE,SAAS;sBAC1ByB,YAAY,EAAE,kBAAkB;sBAChCsH,SAAS,EAAE;oBACb,CAAE;oBAAAhG,QAAA,gBACAnD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,IAAI;sBAAC1C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAACxC,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACPgC,CAAC,EAAE,CAAC;sBACJjJ,eAAe,EAAE,SAAS;sBAC1ByB,YAAY,EAAE,kBAAkB;sBAChCsH,SAAS,EAAE;oBACb,CAAE;oBAAAhG,QAAA,gBACAnD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,IAAI;sBAAC1C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAACxC,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACPgC,CAAC,EAAE,CAAC;sBACJjJ,eAAe,EAAE,SAAS;sBAC1ByB,YAAY,EAAE,kBAAkB;sBAChCsH,SAAS,EAAE;oBACb,CAAE;oBAAAhG,QAAA,gBACAnD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,IAAI;sBAAC1C,UAAU,EAAC,MAAM;sBAACE,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,SAAS;sBAACxC,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAE9C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGPvD,OAAA,CAACF,IAAI;cAAC8C,KAAK,EAAC,iCAAuB;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eAC9CnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAEzF,MAAM,EAAE,OAAO;kBAAEpB,OAAO,EAAE,MAAM;kBAAEqI,aAAa,EAAE,QAAQ;kBAAEpI,cAAc,EAAE,QAAQ;kBAAEC,UAAU,EAAE,QAAQ;kBAAEN,eAAe,EAAE,SAAS;kBAAEyB,YAAY,EAAE,kBAAkB;kBAAE2F,QAAQ,EAAE;gBAAW,CAAE;gBAAArE,QAAA,eAEzMnD,OAAA;kBAAK2B,KAAK,EAAC,MAAM;kBAACC,MAAM,EAAC,KAAK;kBAAC6F,OAAO,EAAC,aAAa;kBAACC,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAU,CAAE;kBAAAxE,QAAA,gBAElFnD,OAAA;oBAAAmD,QAAA,eACEnD,OAAA;sBAASyE,EAAE,EAAC,cAAc;sBAAC9C,KAAK,EAAC,OAAO;sBAACC,MAAM,EAAC,IAAI;sBAACgG,YAAY,EAAC,gBAAgB;sBAAAzE,QAAA,eAChFnD,OAAA;wBAAM6H,CAAC,EAAC,sBAAsB;wBAACC,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAA5E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACPvD,OAAA;oBAAM2B,KAAK,EAAC,MAAM;oBAACC,MAAM,EAAC,MAAM;oBAACkG,IAAI,EAAC;kBAAoB;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG7DvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzDvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG1DvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,IAAI;oBAACiH,IAAI,EAAC,SAAS;oBAAA3E,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG7DvD,OAAA;oBAAM6H,CAAC,EAAC,2DAA2D;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAA5E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAGlHvD,OAAA;oBAAQuI,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/CvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChDvD,OAAA;oBAAQuI,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACX,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAG/CvD,OAAA;oBAAMqE,CAAC,EAAC,IAAI;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,GAAG;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,GAAG;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,GAAG;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,KAAK;oBAACzD,QAAQ,EAAC,GAAG;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,GAAG;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/EvD,OAAA;oBAAMqE,CAAC,EAAC,KAAK;oBAACC,CAAC,EAAC,IAAI;oBAACzD,QAAQ,EAAC,GAAG;oBAACiH,IAAI,EAAC,SAAS;oBAACO,UAAU,EAAC,QAAQ;oBAAAlF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNvD,OAAA,CAACF,IAAI;YAAC8C,KAAK,EAAC,+BAAqB;YAACpB,OAAO,EAAC,IAAI;YAAA2B,QAAA,eAC5CnD,OAAA,CAACd,GAAG;cAACmI,EAAE,EAAE;gBAAE7G,OAAO,EAAE,MAAM;gBAAEa,mBAAmB,EAAE,gBAAgB;gBAAEF,GAAG,EAAE;cAAmB,CAAE;cAAAgC,QAAA,gBAE3FnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJjJ,eAAe,EAAE,SAAS;kBAC1ByB,YAAY,EAAE,kBAAkB;kBAChCsH,SAAS,EAAE;gBACb,CAAE;gBAAAhG,QAAA,gBACAnD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,IAAI;kBAAC1C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,cAAc;kBAACqG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAAnE,QAAA,EAAC;gBAE/E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACxC,KAAK,EAAC,gBAAgB;kBAAAmC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJjJ,eAAe,EAAE,SAAS;kBAC1ByB,YAAY,EAAE,kBAAkB;kBAChCsH,SAAS,EAAE;gBACb,CAAE;gBAAAhG,QAAA,gBACAnD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,IAAI;kBAAC1C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACqG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAAnE,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACxC,KAAK,EAAC,gBAAgB;kBAAAmC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJjJ,eAAe,EAAE,SAAS;kBAC1ByB,YAAY,EAAE,kBAAkB;kBAChCsH,SAAS,EAAE;gBACb,CAAE;gBAAAhG,QAAA,gBACAnD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,IAAI;kBAAC1C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACqG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAAnE,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACxC,KAAK,EAAC,gBAAgB;kBAAAmC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJjJ,eAAe,EAAE,SAAS;kBAC1ByB,YAAY,EAAE,kBAAkB;kBAChCsH,SAAS,EAAE;gBACb,CAAE;gBAAAhG,QAAA,gBACAnD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,IAAI;kBAAC1C,UAAU,EAAC,MAAM;kBAACE,KAAK,EAAC,SAAS;kBAACqG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAAnE,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACxC,KAAK,EAAC,gBAAgB;kBAAAmC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,eAGP,CAAC;MAGP,KAAK,CAAC;QAAE;QACN,oBACEvD,OAAA,CAAAE,SAAA;UAAAiD,QAAA,gBAEEnD,OAAA,CAACF,IAAI;YAAC8C,KAAK,EAAC,oBAAoB;YAAC2E,QAAQ,EAAC,gDAAgD;YAAC/F,OAAO,EAAC,IAAI;YAAA2B,QAAA,eACrGnD,OAAA,CAACd,GAAG;cAACmI,EAAE,EAAE;gBAAE7G,OAAO,EAAE,MAAM;gBAAEa,mBAAmB,EAAE,sCAAsC;gBAAEF,GAAG,EAAE,kBAAkB;gBAAEmG,EAAE,EAAE;cAAE,CAAE;cAAAnE,QAAA,EACvHqB,UAAU,CAAC0C,GAAG,CAAEoC,SAAS,iBACxBtJ,OAAA,CAACd,GAAG;gBAEFqK,OAAO,EAAEA,CAAA,KAAM;kBACbxF,oBAAoB,CAACuF,SAAS,CAAC7E,EAAE,CAAC;kBAClCR,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClC,CAAE;gBACFoD,EAAE,EAAE;kBACFgC,CAAC,EAAE,CAAC;kBACJT,MAAM,EAAE9E,iBAAiB,KAAKwF,SAAS,CAAC7E,EAAE,GAAG,aAAa6E,SAAS,CAACtI,KAAK,EAAE,GAAG,iCAAiC;kBAC/Ga,YAAY,EAAE,kBAAkB;kBAChCzB,eAAe,EAAE0D,iBAAiB,KAAKwF,SAAS,CAAC7E,EAAE,GAAG,GAAG6E,SAAS,CAACtI,KAAK,IAAI,GAAG,OAAO;kBACtFwI,MAAM,EAAE,SAAS;kBACjBC,UAAU,EAAE,eAAe;kBAC3B,SAAS,EAAE;oBACTrJ,eAAe,EAAE0D,iBAAiB,KAAKwF,SAAS,CAAC7E,EAAE,GAAG,GAAG6E,SAAS,CAACtI,KAAK,IAAI,GAAG,sBAAsB;oBACrG2H,SAAS,EAAE,kBAAkB;oBAC7Be,SAAS,EAAE;kBACb;gBACF,CAAE;gBAAAvG,QAAA,gBAEFnD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAED,cAAc,EAAE,eAAe;oBAAE6G,EAAE,EAAE;kBAAE,CAAE;kBAAAnE,QAAA,gBACzFnD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,IAAI;oBAAC1C,UAAU,EAAC,UAAU;oBAACuG,EAAE,EAAE;sBAAErG,KAAK,EAAE8C,iBAAiB,KAAKwF,SAAS,CAAC7E,EAAE,GAAG6E,SAAS,CAACtI,KAAK,GAAG;oBAAwB,CAAE;oBAAAmC,QAAA,EAC1ImG,SAAS,CAAC5E;kBAAI;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACbvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACP1F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACVC,YAAY,EAAE,KAAK;sBACnBzB,eAAe,EAAEkJ,SAAS,CAACtI;oBAC7B;kBAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAED,cAAc,EAAE,eAAe;oBAAE6G,EAAE,EAAE;kBAAE,CAAE;kBAAAnE,QAAA,gBACzFnD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAACxC,KAAK,EAAC,gBAAgB;oBAAAmC,QAAA,GAC/CmG,SAAS,CAAC3E,KAAK,EAAC,SACnB;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,IAAI;oBAAC1C,UAAU,EAAC,MAAM;oBAACuG,EAAE,EAAE;sBAAErG,KAAK,EAAEsI,SAAS,CAACtI;oBAAM,CAAE;oBAAAmC,QAAA,GACvEmG,SAAS,CAAC1E,cAAc,EAAC,GAC5B;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP1F,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,CAAC;oBACTxB,eAAe,EAAE,uBAAuB;oBACxCyB,YAAY,EAAE,oBAAoB;oBAClC8F,QAAQ,EAAE;kBACZ,CAAE;kBAAAxE,QAAA,eACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACP1F,KAAK,EAAE,GAAG2H,SAAS,CAAC1E,cAAc,GAAG;sBACrChD,MAAM,EAAE,MAAM;sBACdxB,eAAe,EAAEkJ,SAAS,CAACtI,KAAK;sBAChCa,YAAY,EAAE;oBAChB;kBAAE;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,GAnDD+F,SAAS,CAAC7E,EAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoDd,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAGNO,iBAAiB,iBAChB9D,OAAA,CAACd,GAAG;YAACmI,EAAE,EAAE;cAAE7G,OAAO,EAAE,MAAM;cAAEa,mBAAmB,EAAE,SAAS;cAAEF,GAAG,EAAE;YAAmB,CAAE;YAAAgC,QAAA,gBAEpFnD,OAAA,CAACF,IAAI;cAAC8C,KAAK,EAAC,qCAA2B;cAAC2E,QAAQ,EAAC,sEAAsE;cAAC/F,OAAO,EAAC,IAAI;cAAA2B,QAAA,eAClInD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAE7G,OAAO,EAAE,MAAM;kBAAEqI,aAAa,EAAE,QAAQ;kBAAE1H,GAAG,EAAE;gBAAmB,CAAE;gBAAAgC,QAAA,GAAA8D,qBAAA,GAC5EpC,YAAY,CAACf,iBAAiB,CAAC,cAAAmD,qBAAA,uBAA/BA,qBAAA,CAAiCC,GAAG,CAAC,CAACyC,KAAK,EAAEvC,KAAK,KAAK;kBAAA,IAAAwC,gBAAA;kBACtD,MAAMhF,cAAc,GAAGiF,IAAI,CAACC,KAAK,CAAEH,KAAK,CAAC3E,SAAS,GAAG2E,KAAK,CAAC5E,KAAK,GAAI,GAAG,CAAC;kBACxE,MAAMgF,UAAU,GAAG,EAAAH,gBAAA,GAAApF,UAAU,CAACwF,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACxF,EAAE,KAAKX,iBAAiB,CAAC,cAAA8F,gBAAA,uBAAlDA,gBAAA,CAAoD5I,KAAK,KAAI,SAAS;kBAEzF,oBACEhB,OAAA,CAACd,GAAG;oBAEFqK,OAAO,EAAEA,CAAA,KAAMtF,wBAAwB,CAAC0F,KAAK,CAAClF,EAAE,CAAE;oBAClD4C,EAAE,EAAE;sBACFgC,CAAC,EAAE,CAAC;sBACJT,MAAM,EAAE5E,qBAAqB,KAAK2F,KAAK,CAAClF,EAAE,GAAG,aAAasF,UAAU,EAAE,GAAG,iCAAiC;sBAC1GlI,YAAY,EAAE,kBAAkB;sBAChCzB,eAAe,EAAE4D,qBAAqB,KAAK2F,KAAK,CAAClF,EAAE,GAAG,GAAGsF,UAAU,IAAI,GAAG,OAAO;sBACjFP,MAAM,EAAE,SAAS;sBACjBC,UAAU,EAAE,eAAe;sBAC3B,SAAS,EAAE;wBACTrJ,eAAe,EAAE4D,qBAAqB,KAAK2F,KAAK,CAAClF,EAAE,GAAG,GAAGsF,UAAU,IAAI,GAAG,sBAAsB;wBAChGpB,SAAS,EAAE,kBAAkB;wBAC7Be,SAAS,EAAE;sBACb;oBACF,CAAE;oBAAAvG,QAAA,gBAGFnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAED,cAAc,EAAE,eAAe;wBAAE6G,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,gBACzFnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE7G,OAAO,EAAE,MAAM;0BAAEE,UAAU,EAAE,QAAQ;0BAAES,GAAG,EAAE;wBAAE,CAAE;wBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;0BAACmI,EAAE,EAAE;4BACP1F,KAAK,EAAE,CAAC;4BACRC,MAAM,EAAE,CAAC;4BACTC,YAAY,EAAE,KAAK;4BACnBzB,eAAe,EAAE2J;0BACnB;wBAAE;0BAAA3G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACLvD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,IAAI;0BAAC1C,UAAU,EAAC,UAAU;0BAAAqC,QAAA,EAC3CwG,KAAK,CAACjF;wBAAI;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE7G,OAAO,EAAE,MAAM;0BAAEE,UAAU,EAAE,QAAQ;0BAAES,GAAG,EAAE;wBAAE,CAAE;wBAAAgC,QAAA,gBACzDnD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,IAAI;0BAAC1C,UAAU,EAAC,MAAM;0BAACuG,EAAE,EAAE;4BAAErG,KAAK,EAAE+I;0BAAW,CAAE;0BAAA5G,QAAA,GAClEyB,cAAc,EAAC,GAClB;wBAAA;0BAAAxB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;0BAACmI,EAAE,EAAE;4BACP6C,EAAE,EAAE,GAAG;4BACPC,EAAE,EAAE,GAAG;4BACP/J,eAAe,EAAEuJ,KAAK,CAACzE,MAAM,KAAK,WAAW,GAAG,SAAS,GAAGyE,KAAK,CAACzE,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;4BAC3GlE,KAAK,EAAE2I,KAAK,CAACzE,MAAM,KAAK,WAAW,GAAG,SAAS,GAAGyE,KAAK,CAACzE,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;4BACjGrD,YAAY,EAAE,kBAAkB;4BAChChB,QAAQ,EAAE,MAAM;4BAChBC,UAAU,EAAE;0BACd,CAAE;0BAAAqC,QAAA,EACCwG,KAAK,CAACzE;wBAAM;0BAAA9B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAEC,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,eACjBnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP1F,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,CAAC;0BACTxB,eAAe,EAAE,uBAAuB;0BACxCyB,YAAY,EAAE,oBAAoB;0BAClC8F,QAAQ,EAAE;wBACZ,CAAE;wBAAAxE,QAAA,eACAnD,OAAA,CAACd,GAAG;0BAACmI,EAAE,EAAE;4BACP1F,KAAK,EAAE,GAAGiD,cAAc,GAAG;4BAC3BhD,MAAM,EAAE,MAAM;4BACdxB,eAAe,EAAE2J,UAAU;4BAC3BlI,YAAY,EAAE;0BAChB;wBAAE;0BAAAuB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAED,cAAc,EAAE;sBAAgB,CAAE;sBAAA0C,QAAA,gBAClFnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE8B,SAAS,EAAE;wBAAS,CAAE;wBAAAhG,QAAA,gBAC/BnD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,IAAI;0BAAC1C,UAAU,EAAC,MAAM;0BAACuG,EAAE,EAAE;4BAAErG,KAAK,EAAE;0BAAU,CAAE;0BAAAmC,QAAA,EACjEwG,KAAK,CAAC5E,KAAK,CAACqF,cAAc,CAAC;wBAAC;0BAAAhH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB,CAAC,eACbvD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,SAAS;0BAACxC,KAAK,EAAC,gBAAgB;0BAAAmC,QAAA,EAAC;wBAErD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE8B,SAAS,EAAE;wBAAS,CAAE;wBAAAhG,QAAA,gBAC/BnD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,IAAI;0BAAC1C,UAAU,EAAC,MAAM;0BAACuG,EAAE,EAAE;4BAAErG,KAAK,EAAE;0BAAU,CAAE;0BAAAmC,QAAA,EACjEwG,KAAK,CAAC3E,SAAS,CAACoF,cAAc,CAAC;wBAAC;0BAAAhH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACbvD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,SAAS;0BAACxC,KAAK,EAAC,gBAAgB;0BAAAmC,QAAA,EAAC;wBAErD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE8B,SAAS,EAAE;wBAAS,CAAE;wBAAAhG,QAAA,gBAC/BnD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,IAAI;0BAAC1C,UAAU,EAAC,MAAM;0BAACuG,EAAE,EAAE;4BAC7CrG,KAAK,EAAE2I,KAAK,CAAC1E,OAAO,GAAG,EAAE,GAAG,SAAS,GAAG0E,KAAK,CAAC1E,OAAO,GAAG,EAAE,GAAG,SAAS,GAAG;0BAC3E,CAAE;0BAAA9B,QAAA,GACCwG,KAAK,CAAC1E,OAAO,EAAC,GACjB;wBAAA;0BAAA7B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,SAAS;0BAACxC,KAAK,EAAC,gBAAgB;0BAAAmC,QAAA,EAAC;wBAErD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GA7FDoG,KAAK,CAAClF,EAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA8FV,CAAC;gBAEV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGPvD,OAAA,CAACF,IAAI;cAAC8C,KAAK,EAAC,uCAA6B;cAAC2E,QAAQ,EAAC,iEAAiE;cAAC/F,OAAO,EAAC,IAAI;cAAA2B,QAAA,EAC9Ha,qBAAqB,GACpB,CAACqG,sBAAA,IAAM;gBACL,MAAMC,aAAa,IAAAD,sBAAA,GAAGxF,YAAY,CAACf,iBAAiB,CAAC,cAAAuG,sBAAA,uBAA/BA,sBAAA,CAAiCL,IAAI,CAACO,CAAC,IAAIA,CAAC,CAAC9F,EAAE,KAAKT,qBAAqB,CAAC;gBAChG,IAAI,CAACsG,aAAa,EAAE,OAAO,IAAI;;gBAE/B;gBACA,MAAME,WAAW,GAAG,CAClB;kBACE9F,IAAI,EAAE,eAAe;kBACrB+F,KAAK,EAAEH,aAAa,CAACvF,KAAK;kBAC1B2F,IAAI,EAAE,GAAG;kBACT1J,KAAK,EAAE,SAAS;kBAChBiE,OAAO,EAAE;gBACX,CAAC,EACD;kBACEP,IAAI,EAAE,iBAAiB;kBACvB+F,KAAK,EAAEZ,IAAI,CAACC,KAAK,CAACQ,aAAa,CAACvF,KAAK,GAAG,IAAI,CAAC;kBAC7C2F,IAAI,EAAE,EAAE;kBACR1J,KAAK,EAAE,SAAS;kBAChBiE,OAAO,EAAE;gBACX,CAAC,EACD;kBACEP,IAAI,EAAE,eAAe;kBACrB+F,KAAK,EAAEZ,IAAI,CAACC,KAAK,CAACQ,aAAa,CAACvF,KAAK,GAAG,IAAI,CAAC;kBAC7C2F,IAAI,EAAE,EAAE;kBACR1J,KAAK,EAAE,SAAS;kBAChBiE,OAAO,EAAE;gBACX,CAAC,EACD;kBACEP,IAAI,EAAE,uBAAuB;kBAC7B+F,KAAK,EAAEZ,IAAI,CAACC,KAAK,CAACQ,aAAa,CAACvF,KAAK,GAAG,IAAI,CAAC;kBAC7C2F,IAAI,EAAE,EAAE;kBACR1J,KAAK,EAAE,SAAS;kBAChBiE,OAAO,EAAE;gBACX,CAAC,EACD;kBACEP,IAAI,EAAE,iBAAiB;kBACvB+F,KAAK,EAAEH,aAAa,CAACtF,SAAS;kBAC9B0F,IAAI,EAAEb,IAAI,CAACC,KAAK,CAAEQ,aAAa,CAACtF,SAAS,GAAGsF,aAAa,CAACvF,KAAK,GAAI,GAAG,CAAC;kBACvE/D,KAAK,EAAE,SAAS;kBAChBiE,OAAO,EAAE4E,IAAI,CAACC,KAAK,CAAC,CAAEQ,aAAa,CAACvF,KAAK,GAAG,IAAI,GAAIuF,aAAa,CAACtF,SAAS,KAAKsF,aAAa,CAACvF,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG;gBACnH,CAAC,CACF;gBAED,oBACE/E,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAE7G,OAAO,EAAE,MAAM;oBAAEqI,aAAa,EAAE,QAAQ;oBAAE1H,GAAG,EAAE;kBAAmB,CAAE;kBAAAgC,QAAA,GAC5EqH,WAAW,CAACtD,GAAG,CAAC,CAACyD,IAAI,EAAEvD,KAAK,kBAC3BpH,OAAA,CAACd,GAAG;oBAEFmI,EAAE,EAAE;sBACFgC,CAAC,EAAE,CAAC;sBACJjJ,eAAe,EAAE,GAAGuK,IAAI,CAAC3J,KAAK,IAAI;sBAClC4H,MAAM,EAAE,aAAa+B,IAAI,CAAC3J,KAAK,IAAI;sBACnCa,YAAY,EAAE,kBAAkB;sBAChC2F,QAAQ,EAAE;oBACZ,CAAE;oBAAArE,QAAA,gBAGFnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAED,cAAc,EAAE,eAAe;wBAAE6G,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,gBACzFnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAE7G,OAAO,EAAE,MAAM;0BAAEE,UAAU,EAAE,QAAQ;0BAAES,GAAG,EAAE;wBAAE,CAAE;wBAAAgC,QAAA,gBACzDnD,OAAA,CAACd,GAAG;0BAACmI,EAAE,EAAE;4BACP1F,KAAK,EAAE,CAAC;4BACRC,MAAM,EAAE,CAAC;4BACTC,YAAY,EAAE,KAAK;4BACnBzB,eAAe,EAAEuK,IAAI,CAAC3J;0BACxB;wBAAE;0BAAAoC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACLvD,OAAA,CAACb,UAAU;0BAACqE,OAAO,EAAC,OAAO;0BAAC1C,UAAU,EAAC,UAAU;0BAAAqC,QAAA,EAC9CwH,IAAI,CAACjG;wBAAI;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,MAAM;wBAACuG,EAAE,EAAE;0BAAErG,KAAK,EAAE2J,IAAI,CAAC3J;wBAAM,CAAE;wBAAAmC,QAAA,GAClEwH,IAAI,CAACD,IAAI,EAAC,GACb;sBAAA;wBAAAtH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAED,cAAc,EAAE,eAAe;wBAAE6G,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,gBACzFnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,MAAM;wBAACuG,EAAE,EAAE;0BAAErG,KAAK,EAAE2J,IAAI,CAAC3J;wBAAM,CAAE;wBAAAmC,QAAA,EAClEwH,IAAI,CAACF,KAAK,CAACL,cAAc,CAAC;sBAAC;wBAAAhH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACbvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,OAAO;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAEnD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eAENvD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,OAAO;sBAACxC,KAAK,EAAC,gBAAgB;sBAACqG,EAAE,EAAE;wBAAEC,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,EAAC;oBAElE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,EAGZoH,IAAI,CAAC1F,OAAO,GAAG,CAAC,iBACfjF,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACPG,QAAQ,EAAE,UAAU;wBACpBhB,GAAG,EAAE,CAAC,CAAC;wBACPoE,KAAK,EAAE,EAAE;wBACTV,EAAE,EAAE,CAAC;wBACLC,EAAE,EAAE,GAAG;wBACP/J,eAAe,EAAE,SAAS;wBAC1BY,KAAK,EAAE,SAAS;wBAChBa,YAAY,EAAE,oBAAoB;wBAClChB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,QAAQ;wBACpB8H,MAAM,EAAE;sBACV,CAAE;sBAAAzF,QAAA,GAAC,GACA,EAACwH,IAAI,CAAC1F,OAAO,EAAC,WAAS,EAAC4E,IAAI,CAACC,KAAK,CAACQ,aAAa,CAACvF,KAAK,IAAI4F,IAAI,CAAC1F,OAAO,GAAG,GAAG,CAAC,CAAC,CAACmF,cAAc,CAAC,CAAC,EAAC,GACnG;oBAAA;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CACN;kBAAA,GA1DI6D,KAAK;oBAAAhE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA2DP,CACN,CAAC,eAGFvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACP7G,OAAO,EAAE,MAAM;sBACfC,cAAc,EAAE,eAAe;sBAC/BC,UAAU,EAAE,QAAQ;sBACpB0I,EAAE,EAAE,CAAC;sBACLyB,EAAE,EAAE,CAAC;sBACLC,SAAS,EAAE;oBACb,CAAE;oBAAA3H,QAAA,gBACAnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE8B,SAAS,EAAE;sBAAS,CAAE;sBAAAhG,QAAA,gBAC/BnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,MAAM;wBAACuG,EAAE,EAAE;0BAAErG,KAAK,EAAE;wBAAU,CAAE;wBAAAmC,QAAA,GACjE0G,IAAI,CAACC,KAAK,CAAEQ,aAAa,CAACtF,SAAS,GAAGsF,aAAa,CAACvF,KAAK,GAAI,GAAG,CAAC,EAAC,GACrE;sBAAA;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,OAAO;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAEnD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE8B,SAAS,EAAE;sBAAS,CAAE;sBAAAhG,QAAA,gBAC/BnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,MAAM;wBAACuG,EAAE,EAAE;0BAAErG,KAAK,EAAE;wBAAU,CAAE;wBAAAmC,QAAA,GACjEmH,aAAa,CAACrF,OAAO,EAAC,GACzB;sBAAA;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,OAAO;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAEnD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV,CAAC,EAAE,CAAC,gBAEJvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACP7G,OAAO,EAAE,MAAM;kBACfqI,aAAa,EAAE,QAAQ;kBACvBnI,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxB0J,EAAE,EAAE,CAAC;kBACLhB,SAAS,EAAE;gBACb,CAAE;gBAAAhG,QAAA,gBACAnD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,IAAI;kBAACxC,KAAK,EAAC,gBAAgB;kBAACqG,EAAE,EAAE;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAAnE,QAAA,EAAC;gBAE/D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,OAAO;kBAACxC,KAAK,EAAC,gBAAgB;kBAAAmC,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA,eACD,CAAC;MAGP,KAAK,CAAC;QAAE;QACN,oBACEvD,OAAA,CAAAE,SAAA;UAAAiD,QAAA,gBAGEnD,OAAA,CAACd,GAAG;YAACmI,EAAE,EAAE;cAAE7G,OAAO,EAAE,MAAM;cAAEa,mBAAmB,EAAE,gBAAgB;cAAEF,GAAG,EAAE,kBAAkB;cAAEmG,EAAE,EAAE;YAAE,CAAE;YAAAnE,QAAA,gBAElGnD,OAAA,CAACd,GAAG;cAACmI,EAAE,EAAE;gBACPgC,CAAC,EAAE,CAAC;gBACJjJ,eAAe,EAAE,OAAO;gBACxByB,YAAY,EAAE,kBAAkB;gBAChC+G,MAAM,EAAE,iCAAiC;gBACzCc,SAAS,EAAE,8BAA8B;gBACzClC,QAAQ,EAAE;cACZ,CAAE;cAAArE,QAAA,gBACAnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAE7G,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE4G,EAAE,EAAE;gBAAE,CAAE;gBAAAnE,QAAA,gBAC7FnD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,SAAS;kBAACxC,KAAK,EAAC,SAAS;kBAACqG,EAAE,EAAE;oBAAEvG,UAAU,EAAE;kBAAS,CAAE;kBAAAqC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP1F,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVxB,eAAe,EAAE,SAAS;oBAC1ByB,YAAY,EAAE,kBAAkB;oBAChCrB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA0C,QAAA,eACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACP1F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACVxB,eAAe,EAAE,SAAS;sBAC1ByB,YAAY,EAAE,kBAAkB;sBAChCrB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBO,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAqC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA,CAACb,UAAU;gBAACqE,OAAO,EAAC,IAAI;gBAAC1C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACqG,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEzG,QAAQ,EAAE;gBAAO,CAAE;gBAAAsC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;gBAACqE,OAAO,EAAC,SAAS;gBAACxC,KAAK,EAAC,SAAS;gBAACqG,EAAE,EAAE;kBAAEvG,UAAU,EAAE;gBAAS,CAAE;gBAAAqC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;cAACmI,EAAE,EAAE;gBACPgC,CAAC,EAAE,CAAC;gBACJjJ,eAAe,EAAE,OAAO;gBACxByB,YAAY,EAAE,kBAAkB;gBAChC+G,MAAM,EAAE,iCAAiC;gBACzCc,SAAS,EAAE,8BAA8B;gBACzClC,QAAQ,EAAE;cACZ,CAAE;cAAArE,QAAA,gBACAnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAE7G,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE4G,EAAE,EAAE;gBAAE,CAAE;gBAAAnE,QAAA,gBAC7FnD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,SAAS;kBAACxC,KAAK,EAAC,SAAS;kBAACqG,EAAE,EAAE;oBAAEvG,UAAU,EAAE;kBAAS,CAAE;kBAAAqC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP1F,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVxB,eAAe,EAAE,SAAS;oBAC1ByB,YAAY,EAAE,kBAAkB;oBAChCrB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA0C,QAAA,eACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACP1F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACVxB,eAAe,EAAE,SAAS;sBAC1ByB,YAAY,EAAE,KAAK;sBACnBrB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBO,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAqC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA,CAACb,UAAU;gBAACqE,OAAO,EAAC,IAAI;gBAAC1C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACqG,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEzG,QAAQ,EAAE;gBAAO,CAAE;gBAAAsC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;gBAACqE,OAAO,EAAC,SAAS;gBAACxC,KAAK,EAAC,SAAS;gBAACqG,EAAE,EAAE;kBAAEvG,UAAU,EAAE;gBAAS,CAAE;gBAAAqC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;cAACmI,EAAE,EAAE;gBACPgC,CAAC,EAAE,CAAC;gBACJjJ,eAAe,EAAE,OAAO;gBACxByB,YAAY,EAAE,kBAAkB;gBAChC+G,MAAM,EAAE,iCAAiC;gBACzCc,SAAS,EAAE,8BAA8B;gBACzClC,QAAQ,EAAE;cACZ,CAAE;cAAArE,QAAA,gBACAnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAE7G,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE4G,EAAE,EAAE;gBAAE,CAAE;gBAAAnE,QAAA,gBAC7FnD,OAAA,CAACb,UAAU;kBAACqE,OAAO,EAAC,SAAS;kBAACxC,KAAK,EAAC,SAAS;kBAACqG,EAAE,EAAE;oBAAEvG,UAAU,EAAE;kBAAS,CAAE;kBAAAqC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP1F,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVxB,eAAe,EAAE,SAAS;oBAC1ByB,YAAY,EAAE,kBAAkB;oBAChCrB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAA0C,QAAA,eACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBACP1F,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACVxB,eAAe,EAAE,SAAS;sBAC1ByB,YAAY,EAAE,kBAAkB;sBAChCrB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBO,KAAK,EAAE,OAAO;sBACdH,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE;oBACd,CAAE;oBAAAqC,QAAA,EAAC;kBAEH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA,CAACb,UAAU;gBAACqE,OAAO,EAAC,IAAI;gBAAC1C,UAAU,EAAC,MAAM;gBAACE,KAAK,EAAC,cAAc;gBAACqG,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEzG,QAAQ,EAAE;gBAAO,CAAE;gBAAAsC,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;gBAACqE,OAAO,EAAC,SAAS;gBAACxC,KAAK,EAAC,SAAS;gBAACqG,EAAE,EAAE;kBAAEvG,UAAU,EAAE;gBAAS,CAAE;gBAAAqC,QAAA,EAAC;cAE5E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;YAACmI,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAnE,QAAA,eACjBnD,OAAA,CAACF,IAAI;cAAC8C,KAAK,EAAC,qBAAqB;cAACpB,OAAO,EAAC,IAAI;cAAA2B,QAAA,eAC9CnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAE7G,OAAO,EAAE,MAAM;kBAAEqI,aAAa,EAAE,QAAQ;kBAAE1H,GAAG,EAAE;gBAAmB,CAAE;gBAAAgC,QAAA,gBAE7EnD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP7G,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B4I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzC/G,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACTzB,eAAe,EAAE,sBAAsB;sBACvCoJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAArG,QAAA,gBACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAErF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEmG,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,gBAChEnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,UAAU;wBAAAqC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP6C,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACP/J,eAAe,EAAE,SAAS;0BAC1BY,KAAK,EAAE,SAAS;0BAChBa,YAAY,EAAE,kBAAkB;0BAChChB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAqC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAsC,QAAA,gBAC3GnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAErG,KAAK,EAAE,0BAA0B;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAsC,QAAA,EAAC;sBAExF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE;oBAAE,CAAE;oBAAAgC,QAAA,gBACzDnD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,IAAI;sBAAC1C,UAAU,EAAC,MAAM;sBAAAqC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACTxB,eAAe,EAAE,uBAAuB;wBACxCyB,YAAY,EAAE,oBAAoB;wBAClC8F,QAAQ,EAAE;sBACZ,CAAE;sBAAAxE,QAAA,eACAnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP1F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACdxB,eAAe,EAAE,uBAAuB;0BACxCyB,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP7G,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B4I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzC/G,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACTzB,eAAe,EAAE,sBAAsB;sBACvCoJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAArG,QAAA,gBACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAErF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEmG,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,gBAChEnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,UAAU;wBAAAqC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP6C,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACP/J,eAAe,EAAE,SAAS;0BAC1BY,KAAK,EAAE,SAAS;0BAChBa,YAAY,EAAE,kBAAkB;0BAChChB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAqC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAsC,QAAA,gBAC3GnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAErG,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAsC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE;oBAAE,CAAE;oBAAAgC,QAAA,gBACzDnD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,IAAI;sBAAC1C,UAAU,EAAC,MAAM;sBAAAqC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACTxB,eAAe,EAAE,uBAAuB;wBACxCyB,YAAY,EAAE,oBAAoB;wBAClC8F,QAAQ,EAAE;sBACZ,CAAE;sBAAAxE,QAAA,eACAnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP1F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACdxB,eAAe,EAAE,uBAAuB;0BACxCyB,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP7G,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B4I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzC/G,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACTzB,eAAe,EAAE,sBAAsB;sBACvCoJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAArG,QAAA,gBACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAErF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEmG,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,gBAChEnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,UAAU;wBAAAqC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP6C,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACP/J,eAAe,EAAE,SAAS;0BAC1BY,KAAK,EAAE,SAAS;0BAChBa,YAAY,EAAE,kBAAkB;0BAChChB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAqC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAsC,QAAA,gBAC3GnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAErG,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAsC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE;oBAAE,CAAE;oBAAAgC,QAAA,gBACzDnD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,IAAI;sBAAC1C,UAAU,EAAC,MAAM;sBAAAqC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACTxB,eAAe,EAAE,uBAAuB;wBACxCyB,YAAY,EAAE,oBAAoB;wBAClC8F,QAAQ,EAAE;sBACZ,CAAE;sBAAAxE,QAAA,eACAnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP1F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACdxB,eAAe,EAAE,uBAAuB;0BACxCyB,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP7G,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B4I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzC/G,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACTzB,eAAe,EAAE,sBAAsB;sBACvCoJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAArG,QAAA,gBACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAErF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEmG,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,gBAChEnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,UAAU;wBAAAqC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP6C,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACP/J,eAAe,EAAE,SAAS;0BAC1BY,KAAK,EAAE,SAAS;0BAChBa,YAAY,EAAE,kBAAkB;0BAChChB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAqC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAsC,QAAA,gBAC3GnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAErG,KAAK,EAAE,wBAAwB;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAsC,QAAA,EAAC;sBAEtF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE;oBAAE,CAAE;oBAAAgC,QAAA,gBACzDnD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,IAAI;sBAAC1C,UAAU,EAAC,MAAM;sBAAAqC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACTxB,eAAe,EAAE,uBAAuB;wBACxCyB,YAAY,EAAE,oBAAoB;wBAClC8F,QAAQ,EAAE;sBACZ,CAAE;sBAAAxE,QAAA,eACAnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP1F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACdxB,eAAe,EAAE,uBAAuB;0BACxCyB,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP7G,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,eAAe;oBAC/B4I,CAAC,EAAE,CAAC;oBACJT,MAAM,EAAE,iCAAiC;oBACzC/G,YAAY,EAAE,kBAAkB;oBAChC,SAAS,EAAE;sBACTzB,eAAe,EAAE,sBAAsB;sBACvCoJ,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAArG,QAAA,gBACAnD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAErF,IAAI,EAAE;oBAAE,CAAE;oBAAAmB,QAAA,gBACnBnD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEmG,EAAE,EAAE;sBAAE,CAAE;sBAAAnE,QAAA,gBAChEnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,IAAI;wBAAC1C,UAAU,EAAC,UAAU;wBAAAqC,QAAA,EAAC;sBAE/C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP6C,EAAE,EAAE,GAAG;0BACPC,EAAE,EAAE,GAAG;0BACP/J,eAAe,EAAE,SAAS;0BAC1BY,KAAK,EAAE,SAAS;0BAChBa,YAAY,EAAE,kBAAkB;0BAChChB,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE;wBACd,CAAE;wBAAAqC,QAAA,EAAC;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNvD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBAAE7G,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAES,GAAG,EAAE,CAAC;wBAAEH,KAAK,EAAE,uBAAuB;wBAAEH,QAAQ,EAAE;sBAAO,CAAE;sBAAAsC,QAAA,gBAC3GnD,OAAA,CAACb,UAAU;wBAACqE,OAAO,EAAC,SAAS;wBAACxC,KAAK,EAAC,gBAAgB;wBAAAmC,QAAA,EAAC;sBAErD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BAAErG,KAAK,EAAE,0BAA0B;0BAAEF,UAAU,EAAE,QAAQ;0BAAED,QAAQ,EAAE;wBAAO,CAAE;wBAAAsC,QAAA,EAAC;sBAExF;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvD,OAAA,CAACd,GAAG;oBAACmI,EAAE,EAAE;sBAAE7G,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAES,GAAG,EAAE;oBAAE,CAAE;oBAAAgC,QAAA,gBACzDnD,OAAA,CAACb,UAAU;sBAACqE,OAAO,EAAC,IAAI;sBAAC1C,UAAU,EAAC,MAAM;sBAAAqC,QAAA,EAAC;oBAE3C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvD,OAAA,CAACd,GAAG;sBAACmI,EAAE,EAAE;wBACP1F,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,CAAC;wBACTxB,eAAe,EAAE,uBAAuB;wBACxCyB,YAAY,EAAE,oBAAoB;wBAClC8F,QAAQ,EAAE;sBACZ,CAAE;sBAAAxE,QAAA,eACAnD,OAAA,CAACd,GAAG;wBAACmI,EAAE,EAAE;0BACP1F,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,MAAM;0BACdxB,eAAe,EAAE,uBAAuB;0BACxCyB,YAAY,EAAE;wBAChB;sBAAE;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGNvD,OAAA,CAACF,IAAI;YAAC8C,KAAK,EAAC,+BAA+B;YAACpB,OAAO,EAAC,IAAI;YAAA2B,QAAA,eACtDnD,OAAA,CAACd,GAAG;cAACmI,EAAE,EAAE;gBAAE7G,OAAO,EAAE,MAAM;gBAAEqI,aAAa,EAAE,QAAQ;gBAAE1H,GAAG,EAAE;cAAmB,CAAE;cAAAgC,QAAA,gBAE7EnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJjJ,eAAe,EAAE,SAAS;kBAC1BwI,MAAM,EAAE,mBAAmB;kBAC3B/G,YAAY,EAAE,kBAAkB;kBAChCrB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBS,GAAG,EAAE;gBACP,CAAE;gBAAAgC,QAAA,gBACAnD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP1F,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACTxB,eAAe,EAAE,SAAS;oBAC1ByB,YAAY,EAAE,KAAK;oBACnBuH,EAAE,EAAE;kBACN;gBAAE;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAErF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBnD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAAC1C,UAAU,EAAC,QAAQ;oBAACuG,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAAnE,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,SAAS;oBAACxC,KAAK,EAAC,gBAAgB;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJjJ,eAAe,EAAE,SAAS;kBAC1BwI,MAAM,EAAE,mBAAmB;kBAC3B/G,YAAY,EAAE,kBAAkB;kBAChCrB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBS,GAAG,EAAE;gBACP,CAAE;gBAAAgC,QAAA,gBACAnD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP1F,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACTxB,eAAe,EAAE,SAAS;oBAC1ByB,YAAY,EAAE,KAAK;oBACnBuH,EAAE,EAAE;kBACN;gBAAE;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAErF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBnD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAAC1C,UAAU,EAAC,QAAQ;oBAACuG,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAAnE,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,SAAS;oBAACxC,KAAK,EAAC,gBAAgB;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACPgC,CAAC,EAAE,CAAC;kBACJjJ,eAAe,EAAE,SAAS;kBAC1BwI,MAAM,EAAE,mBAAmB;kBAC3B/G,YAAY,EAAE,kBAAkB;kBAChCrB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,YAAY;kBACxBS,GAAG,EAAE;gBACP,CAAE;gBAAAgC,QAAA,gBACAnD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACP1F,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACTxB,eAAe,EAAE,SAAS;oBAC1ByB,YAAY,EAAE,KAAK;oBACnBuH,EAAE,EAAE;kBACN;gBAAE;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBAAErF,IAAI,EAAE;kBAAE,CAAE;kBAAAmB,QAAA,gBACnBnD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,OAAO;oBAAC1C,UAAU,EAAC,QAAQ;oBAACuG,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAI,CAAE;oBAAAnE,QAAA,EAAC;kBAEjE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;oBAACqE,OAAO,EAAC,SAAS;oBAACxC,KAAK,EAAC,gBAAgB;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,eACP,CAAC;MAKP;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEvD,OAAA;IAAK+K,SAAS,EAAC,WAAW;IAAA5H,QAAA,eACxBnD,OAAA;MAAK+K,SAAS,EAAC,kBAAkB;MAAA5H,QAAA,gBAC/BnD,OAAA,CAACG,gBAAgB;QAAAgD,QAAA,eACfnD,OAAA,CAACZ,SAAS;UAAC4J,QAAQ,EAAC,IAAI;UAAC3B,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAAhH,QAAA,gBAErCnD,OAAA,CAACd,GAAG;YAACmI,EAAE,EAAE;cACP7G,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,eAAe;cAC/B6G,EAAE,EAAE,CAAC;cACL0D,EAAE,EAAE,CAAC;cACLC,YAAY,EAAE;YAChB,CAAE;YAAA9H,QAAA,gBAEAnD,OAAA,CAACd,GAAG;cAAAiE,QAAA,gBACFnD,OAAA,CAACb,UAAU;gBAACqE,OAAO,EAAC,IAAI;gBAAC1C,UAAU,EAAC,MAAM;gBAACuG,EAAE,EAAE;kBAAErG,KAAK,EAAE,SAAS;kBAAEsG,EAAE,EAAE;gBAAI,CAAE;gBAAAnE,QAAA,EAAC;cAE9E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvD,OAAA,CAACb,UAAU;gBAACqE,OAAO,EAAC,OAAO;gBAAC6D,EAAE,EAAE;kBAAErG,KAAK,EAAE;gBAAU,CAAE;gBAAAmC,QAAA,EAAC;cAEtD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNvD,OAAA,CAACd,GAAG;cAACmI,EAAE,EAAE;gBAAE7G,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAES,GAAG,EAAE;cAAE,CAAE;cAAAgC,QAAA,gBAEzDnD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBAAEG,QAAQ,EAAE;gBAAW,CAAE;gBAAArE,QAAA,gBAChCnD,OAAA;kBACEkL,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,yBAAyB;kBACrCzD,KAAK,EAAE;oBACL/F,KAAK,EAAE,OAAO;oBACdC,MAAM,EAAE,MAAM;oBACdJ,OAAO,EAAE,eAAe;oBACxBoH,MAAM,EAAE,mBAAmB;oBAC3B/G,YAAY,EAAE,KAAK;oBACnBhB,QAAQ,EAAE,MAAM;oBAChBT,eAAe,EAAE,OAAO;oBACxBgL,OAAO,EAAE;kBACX;gBAAE;kBAAAhI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFvD,OAAA,CAACd,GAAG;kBAACmI,EAAE,EAAE;oBACPG,QAAQ,EAAE,UAAU;oBACpBjB,IAAI,EAAE,MAAM;oBACZC,GAAG,EAAE,KAAK;oBACVmC,SAAS,EAAE,kBAAkB;oBAC7B3H,KAAK,EAAE,SAAS;oBAChBH,QAAQ,EAAE;kBACZ,CAAE;kBAAAsC,QAAA,EAAC;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACP7G,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBS,GAAG,EAAE,CAAC;kBACN+I,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,CAAC;kBACLvB,MAAM,EAAE,mBAAmB;kBAC3B/G,YAAY,EAAE,KAAK;kBACnBzB,eAAe,EAAE,OAAO;kBACxBoJ,MAAM,EAAE,SAAS;kBACjB3I,QAAQ,EAAE,MAAM;kBAChBG,KAAK,EAAE,SAAS;kBAChB,SAAS,EAAE;oBACTZ,eAAe,EAAE;kBACnB;gBACF,CAAE;gBAAA+C,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAGNvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACP7G,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBkB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdgH,MAAM,EAAE,mBAAmB;kBAC3B/G,YAAY,EAAE,KAAK;kBACnBzB,eAAe,EAAE,OAAO;kBACxBoJ,MAAM,EAAE,SAAS;kBACjB3I,QAAQ,EAAE,MAAM;kBAChB,SAAS,EAAE;oBACTT,eAAe,EAAE;kBACnB;gBACF,CAAE;gBAAA+C,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAGNvD,OAAA,CAACd,GAAG;gBAACmI,EAAE,EAAE;kBACP7G,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBS,GAAG,EAAE,CAAC;kBACN+I,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,CAAC;kBACL/J,eAAe,EAAE,SAAS;kBAC1BY,KAAK,EAAE,OAAO;kBACda,YAAY,EAAE,KAAK;kBACnB2H,MAAM,EAAE,SAAS;kBACjB3I,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,QAAQ;kBACpB,SAAS,EAAE;oBACTV,eAAe,EAAE;kBACnB;gBACF,CAAE;gBAAA+C,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvD,OAAA,CAACY,UAAU;YAACiC,KAAK,EAAEe,WAAY;YAACyH,QAAQ,EAAEvE,eAAgB;YAAA3D,QAAA,gBACxDnD,OAAA,CAACV,GAAG;cAACgM,KAAK,EAAC;YAAU;cAAAlI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxBvD,OAAA,CAACV,GAAG;cAACgM,KAAK,EAAC;YAAW;cAAAlI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBvD,OAAA,CAACV,GAAG;cAACgM,KAAK,EAAC;YAAgB;cAAAlI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,EAGZyD,gBAAgB,CAAC,CAAC;QAAA;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGlBW,OAAO,CAACE,OAAO,iBACdpE,OAAA,CAACd,GAAG;QACFmI,EAAE,EAAE;UACFG,QAAQ,EAAE,OAAO;UACjBjB,IAAI,EAAErC,OAAO,CAACG,CAAC;UACfmC,GAAG,EAAEtC,OAAO,CAACI,CAAC;UACdqE,SAAS,EAAE,wBAAwB;UACnCvI,eAAe,EAAE,OAAO;UACxBwI,MAAM,EAAE,mBAAmB;UAC3B/G,YAAY,EAAE,KAAK;UACnBL,OAAO,EAAE,UAAU;UACnBkI,SAAS,EAAE,uEAAuE;UAClF6B,MAAM,EAAE,KAAK;UACbC,aAAa,EAAE,MAAM;UACrB3K,QAAQ,EAAE,MAAM;UAChBoI,QAAQ,EAAE,MAAM;UAChBE,SAAS,EAAE,QAAQ;UACnBsC,UAAU,EAAE;QACd,CAAE;QAAAtI,QAAA,gBAEFnD,OAAA,CAACb,UAAU;UAACqE,OAAO,EAAC,OAAO;UAAC6D,EAAE,EAAE;YAAExG,QAAQ,EAAE,MAAM;YAAEG,KAAK,EAAE,SAAS;YAAEsG,EAAE,EAAE,GAAG;YAAEoE,UAAU,EAAE;UAAI,CAAE;UAAAvI,QAAA,EAC9Fe,OAAO,CAACtB;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbvD,OAAA,CAACb,UAAU;UAACqE,OAAO,EAAC,OAAO;UAAC6D,EAAE,EAAE;YAAExG,QAAQ,EAAE,MAAM;YAAEG,KAAK,EAAE,SAAS;YAAEF,UAAU,EAAE,KAAK;YAAE4K,UAAU,EAAE;UAAI,CAAE;UAAAvI,QAAA,EACxGe,OAAO,CAACK;QAAO;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,CAzuDID,eAAyB;AAAAiI,IAAA,GAAzBjI,eAAyB;AA2uD/B,eAAeA,eAAe;AAAC,IAAApD,EAAA,EAAAW,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAe,GAAA,EAAAkI,IAAA;AAAAC,YAAA,CAAAtL,EAAA;AAAAsL,YAAA,CAAA3K,GAAA;AAAA2K,YAAA,CAAAtK,GAAA;AAAAsK,YAAA,CAAAnK,GAAA;AAAAmK,YAAA,CAAA9J,GAAA;AAAA8J,YAAA,CAAA3J,GAAA;AAAA2J,YAAA,CAAAzJ,GAAA;AAAAyJ,YAAA,CAAAvJ,GAAA;AAAAuJ,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}