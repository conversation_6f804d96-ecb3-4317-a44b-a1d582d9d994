"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[656],{1119:(a,e,t)=>{var i=t(4994);e.A=void 0;var D=i(t(39)),n=t(579);e.A=(0,D.default)((0,n.jsx)("path",{d:"M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h5v-2H4v-6h18V6c0-1.11-.89-2-2-2m0 4H4V6h16zm-5.07 11.17-2.83-2.83-1.41 1.41L14.93 22 22 14.93l-1.41-1.41z"}),"CreditScore")},3635:(a,e,t)=>{var i=t(4994);e.A=void 0;var D=i(t(39)),n=t(579);e.A=(0,D.default)((0,n.jsx)("path",{d:"M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.72-4.8 5.74-7.39c.51-.66.04-1.61-.79-1.61H5.04c-.83 0-1.3.95-.79 1.61"}),"FilterAlt")},4598:(a,e,t)=>{t.d(e,{A:()=>x});var i=t(8587),D=t(8168),n=t(5043),s=t(9292),l=t(8610),y=t(7266),T=t(6803),o=t(3064),r=t(4535),d=t(8206),h=t(2532),f=t(2372);function g(a){return(0,f.Ay)("MuiSwitch",a)}const O=(0,h.A)("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]);var m=t(579);const k=["className","color","edge","size","sx"],u=(0,r.Ay)("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(a,e)=>{const{ownerState:t}=a;return[e.root,t.edge&&e["edge".concat((0,T.A)(t.edge))],e["size".concat((0,T.A)(t.size))]]}})({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,["& .".concat(O.thumb)]:{width:16,height:16},["& .".concat(O.switchBase)]:{padding:4,["&.".concat(O.checked)]:{transform:"translateX(16px)"}}}}]}),W=(0,r.Ay)(o.A,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(a,e)=>{const{ownerState:t}=a;return[e.switchBase,{["& .".concat(O.input)]:e.input},"default"!==t.color&&e["color".concat((0,T.A)(t.color))]]}})((a=>{let{theme:e}=a;return{position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:"".concat("light"===e.palette.mode?e.palette.common.white:e.palette.grey[300]),transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),["&.".concat(O.checked)]:{transform:"translateX(20px)"},["&.".concat(O.disabled)]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:"".concat("light"===e.palette.mode?e.palette.grey[100]:e.palette.grey[600])},["&.".concat(O.checked," + .").concat(O.track)]:{opacity:.5},["&.".concat(O.disabled," + .").concat(O.track)]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:"".concat("light"===e.palette.mode?.12:.2)},["& .".concat(O.input)]:{left:"-100%",width:"300%"}}}),(a=>{let{theme:e}=a;return{"&:hover":{backgroundColor:e.vars?"rgba(".concat(e.vars.palette.action.activeChannel," / ").concat(e.vars.palette.action.hoverOpacity,")"):(0,y.X4)(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter((a=>{let[,e]=a;return e.main&&e.light})).map((a=>{let[t]=a;return{props:{color:t},style:{["&.".concat(O.checked)]:{color:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:e.vars?"rgba(".concat(e.vars.palette[t].mainChannel," / ").concat(e.vars.palette.action.hoverOpacity,")"):(0,y.X4)(e.palette[t].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(O.disabled)]:{color:e.vars?e.vars.palette.Switch["".concat(t,"DisabledColor")]:"".concat("light"===e.palette.mode?(0,y.a)(e.palette[t].main,.62):(0,y.e$)(e.palette[t].main,.55))}},["&.".concat(O.checked," + .").concat(O.track)]:{backgroundColor:(e.vars||e).palette[t].main}}}}))]}})),S=(0,r.Ay)("span",{name:"MuiSwitch",slot:"Track",overridesResolver:(a,e)=>e.track})((a=>{let{theme:e}=a;return{height:"100%",width:"100%",borderRadius:7,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:"".concat("light"===e.palette.mode?e.palette.common.black:e.palette.common.white),opacity:e.vars?e.vars.opacity.switchTrack:"".concat("light"===e.palette.mode?.38:.3)}})),c=(0,r.Ay)("span",{name:"MuiSwitch",slot:"Thumb",overridesResolver:(a,e)=>e.thumb})((a=>{let{theme:e}=a;return{boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}})),x=n.forwardRef((function(a,e){const t=(0,d.b)({props:a,name:"MuiSwitch"}),{className:n,color:y="primary",edge:o=!1,size:r="medium",sx:h}=t,f=(0,i.A)(t,k),O=(0,D.A)({},t,{color:y,edge:o,size:r}),x=(a=>{const{classes:e,edge:t,size:i,color:n,checked:s,disabled:y}=a,o={root:["root",t&&"edge".concat((0,T.A)(t)),"size".concat((0,T.A)(i))],switchBase:["switchBase","color".concat((0,T.A)(n)),s&&"checked",y&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},r=(0,l.A)(o,g,e);return(0,D.A)({},e,r)})(O),I=(0,m.jsx)(c,{className:x.thumb,ownerState:O});return(0,m.jsxs)(u,{className:(0,s.A)(x.root,n),sx:h,ownerState:O,children:[(0,m.jsx)(W,(0,D.A)({type:"checkbox",icon:I,checkedIcon:I,ref:e,ownerState:O},f,{classes:(0,D.A)({},x,{root:x.switchBase})})),(0,m.jsx)(S,{className:x.track,ownerState:O})]})}))},6559:(a,e,t)=>{var i=t(4994);e.A=void 0;var D=i(t(39)),n=t(579);e.A=(0,D.default)([(0,n.jsx)("path",{d:"M12 5.99 19.53 19H4.47zM12 2 1 21h22z"},"0"),(0,n.jsx)("path",{d:"M13 16h-2v2h2zm0-6h-2v5h2z"},"1")],"WarningAmber")},6656:(a,e,t)=>{t.r(e),t.d(e,{default:()=>$});var i=t(9379),D=t(5043),n=t(4598),s=(t(4348),t(3216)),l=t(3158),y=t(694),T=t(4605),o=t(7739),r=t(7392),d=t(7254),h=t(5540),f=t(2102),g=t(5335);const O=[{Id:"India Standard Time",DisplayName:"(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi",StandardName:"India Standard Time",DaylightName:"India Daylight Time",BaseUtcOffset:"05:30:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Dateline Standard Time",DisplayName:"(UTC-12:00) International Date Line West",StandardName:"Dateline Standard Time",DaylightName:"Dateline Daylight Time",BaseUtcOffset:"-12:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"UTC-11",DisplayName:"(UTC-11:00) Coordinated Universal Time-11",StandardName:"UTC-11",DaylightName:"UTC-11",BaseUtcOffset:"-11:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Aleutian Standard Time",DisplayName:"(UTC-10:00) Aleutian Islands",StandardName:"Aleutian Standard Time",DaylightName:"Aleutian Daylight Time",BaseUtcOffset:"-10:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Hawaiian Standard Time",DisplayName:"(UTC-10:00) Hawaii",StandardName:"Hawaiian Standard Time",DaylightName:"Hawaiian Daylight Time",BaseUtcOffset:"-10:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Marquesas Standard Time",DisplayName:"(UTC-09:30) Marquesas Islands",StandardName:"Marquesas Standard Time",DaylightName:"Marquesas Daylight Time",BaseUtcOffset:"-09:30:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Alaskan Standard Time",DisplayName:"(UTC-09:00) Alaska",StandardName:"Alaskan Standard Time",DaylightName:"Alaskan Daylight Time",BaseUtcOffset:"-09:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"UTC-09",DisplayName:"(UTC-09:00) Coordinated Universal Time-09",StandardName:"UTC-09",DaylightName:"UTC-09",BaseUtcOffset:"-09:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Pacific Standard Time (Mexico)",DisplayName:"(UTC-08:00) Baja California",StandardName:"Pacific Standard Time (Mexico)",DaylightName:"Pacific Daylight Time (Mexico)",BaseUtcOffset:"-08:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"UTC-08",DisplayName:"(UTC-08:00) Coordinated Universal Time-08",StandardName:"UTC-08",DaylightName:"UTC-08",BaseUtcOffset:"-08:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Pacific Standard Time",DisplayName:"(UTC-08:00) Pacific Time (US & Canada)",StandardName:"Pacific Standard Time",DaylightName:"Pacific Daylight Time",BaseUtcOffset:"-08:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"US Mountain Standard Time",DisplayName:"(UTC-07:00) Arizona",StandardName:"US Mountain Standard Time",DaylightName:"US Mountain Daylight Time",BaseUtcOffset:"-07:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Mountain Standard Time (Mexico)",DisplayName:"(UTC-07:00) Chihuahua, La Paz, Mazatlan",StandardName:"Mountain Standard Time (Mexico)",DaylightName:"Mountain Daylight Time (Mexico)",BaseUtcOffset:"-07:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Mountain Standard Time",DisplayName:"(UTC-07:00) Mountain Time (US & Canada)",StandardName:"Mountain Standard Time",DaylightName:"Mountain Daylight Time",BaseUtcOffset:"-07:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Central America Standard Time",DisplayName:"(UTC-06:00) Central America",StandardName:"Central America Standard Time",DaylightName:"Central America Daylight Time",BaseUtcOffset:"-06:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Central Standard Time",DisplayName:"(UTC-06:00) Central Time (US & Canada)",StandardName:"Central Standard Time",DaylightName:"Central Daylight Time",BaseUtcOffset:"-06:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Easter Island Standard Time",DisplayName:"(UTC-06:00) Easter Island",StandardName:"Easter Island Standard Time",DaylightName:"Easter Island Daylight Time",BaseUtcOffset:"-06:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:8,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:5,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:8,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T22:00:00",Month:5,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Central Standard Time (Mexico)",DisplayName:"(UTC-06:00) Guadalajara, Mexico City, Monterrey",StandardName:"Central Standard Time (Mexico)",DaylightName:"Central Daylight Time (Mexico)",BaseUtcOffset:"-06:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Canada Central Standard Time",DisplayName:"(UTC-06:00) Saskatchewan",StandardName:"Canada Central Standard Time",DaylightName:"Canada Central Daylight Time",BaseUtcOffset:"-06:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"SA Pacific Standard Time",DisplayName:"(UTC-05:00) Bogota, Lima, Quito, Rio Branco",StandardName:"SA Pacific Standard Time",DaylightName:"SA Pacific Daylight Time",BaseUtcOffset:"-05:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Eastern Standard Time (Mexico)",DisplayName:"(UTC-05:00) Chetumal",StandardName:"Eastern Standard Time (Mexico)",DaylightName:"Eastern Daylight Time (Mexico)",BaseUtcOffset:"-05:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:2,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Eastern Standard Time",DisplayName:"(UTC-05:00) Eastern Time (US & Canada)",StandardName:"Eastern Standard Time",DaylightName:"Eastern Daylight Time",BaseUtcOffset:"-05:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Haiti Standard Time",DisplayName:"(UTC-05:00) Haiti",StandardName:"Haiti Standard Time",DaylightName:"Haiti Daylight Time",BaseUtcOffset:"-05:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Cuba Standard Time",DisplayName:"(UTC-05:00) Havana",StandardName:"Cuba Standard Time",DaylightName:"Cuba Daylight Time",BaseUtcOffset:"-05:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2003-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2004-01-01T00:00:00",DateEnd:"2004-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2005-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:11,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"US Eastern Standard Time",DisplayName:"(UTC-05:00) Indiana (East)",StandardName:"US Eastern Standard Time",DaylightName:"US Eastern Daylight Time",BaseUtcOffset:"-05:00:00",AdjustmentRules:[{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Turks And Caicos Standard Time",DisplayName:"(UTC-05:00) Turks and Caicos",StandardName:"Turks and Caicos Standard Time",DaylightName:"Turks and Caicos Daylight Time",BaseUtcOffset:"-05:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Paraguay Standard Time",DisplayName:"(UTC-04:00) Asuncion",StandardName:"Paraguay Standard Time",DaylightName:"Paraguay Daylight Time",BaseUtcOffset:"-04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"2019-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2020-01-01T00:00:00",DateEnd:"2020-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2021-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Atlantic Standard Time",DisplayName:"(UTC-04:00) Atlantic Time (Canada)",StandardName:"Atlantic Standard Time",DaylightName:"Atlantic Daylight Time",BaseUtcOffset:"-04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Venezuela Standard Time",DisplayName:"(UTC-04:00) Caracas",StandardName:"Venezuela Standard Time",DaylightName:"Venezuela Daylight Time",BaseUtcOffset:"-04:00:00",AdjustmentRules:[{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:12,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:30:00",Month:5,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Central Brazilian Standard Time",DisplayName:"(UTC-04:00) Cuiaba",StandardName:"Central Brazilian Standard Time",DaylightName:"Central Brazilian Daylight Time",BaseUtcOffset:"-04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2004-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:11,Week:1,Day:1,DayOfWeek:2,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2005-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:10,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:10,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:10,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"2019-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2020-01-01T00:00:00",DateEnd:"2020-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2021-01-01T00:00:00",DateEnd:"2021-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2022-01-01T00:00:00",DateEnd:"2022-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2023-01-01T00:00:00",DateEnd:"2023-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2024-01-01T00:00:00",DateEnd:"2024-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2025-01-01T00:00:00",DateEnd:"2025-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2026-01-01T00:00:00",DateEnd:"2026-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2027-01-01T00:00:00",DateEnd:"2027-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2028-01-01T00:00:00",DateEnd:"2028-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2029-01-01T00:00:00",DateEnd:"2029-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2030-01-01T00:00:00",DateEnd:"2030-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2031-01-01T00:00:00",DateEnd:"2031-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2032-01-01T00:00:00",DateEnd:"2032-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2033-01-01T00:00:00",DateEnd:"2033-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2034-01-01T00:00:00",DateEnd:"2034-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2035-01-01T00:00:00",DateEnd:"2035-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2036-01-01T00:00:00",DateEnd:"2036-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2037-01-01T00:00:00",DateEnd:"2037-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2038-01-01T00:00:00",DateEnd:"2038-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2039-01-01T00:00:00",DateEnd:"2039-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2040-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"SA Western Standard Time",DisplayName:"(UTC-04:00) Georgetown, La Paz, Manaus, San Juan",StandardName:"SA Western Standard Time",DaylightName:"SA Western Daylight Time",BaseUtcOffset:"-04:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Pacific SA Standard Time",DisplayName:"(UTC-04:00) Santiago",StandardName:"Pacific SA Standard Time",DaylightName:"Pacific SA Daylight Time",BaseUtcOffset:"-04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Newfoundland Standard Time",DisplayName:"(UTC-03:30) Newfoundland",StandardName:"Newfoundland Standard Time",DaylightName:"Newfoundland Daylight Time",BaseUtcOffset:"-03:30:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:01:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:01:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:01:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:01:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:01:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:01:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:01:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:01:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:01:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:01:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:01:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Tocantins Standard Time",DisplayName:"(UTC-03:00) Araguaina",StandardName:"Tocantins Standard Time",DaylightName:"Tocantins Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:[{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:10,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:2,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"E. South America Standard Time",DisplayName:"(UTC-03:00) Brasilia",StandardName:"E. South America Standard Time",DaylightName:"E. South America Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2004-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:11,Week:1,Day:1,DayOfWeek:2,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2005-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:10,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:10,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:10,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"2019-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2020-01-01T00:00:00",DateEnd:"2020-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2021-01-01T00:00:00",DateEnd:"2021-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2022-01-01T00:00:00",DateEnd:"2022-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2023-01-01T00:00:00",DateEnd:"2023-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2024-01-01T00:00:00",DateEnd:"2024-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2025-01-01T00:00:00",DateEnd:"2025-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2026-01-01T00:00:00",DateEnd:"2026-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2027-01-01T00:00:00",DateEnd:"2027-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2028-01-01T00:00:00",DateEnd:"2028-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2029-01-01T00:00:00",DateEnd:"2029-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2030-01-01T00:00:00",DateEnd:"2030-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2031-01-01T00:00:00",DateEnd:"2031-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2032-01-01T00:00:00",DateEnd:"2032-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2033-01-01T00:00:00",DateEnd:"2033-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2034-01-01T00:00:00",DateEnd:"2034-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2035-01-01T00:00:00",DateEnd:"2035-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2036-01-01T00:00:00",DateEnd:"2036-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2037-01-01T00:00:00",DateEnd:"2037-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2038-01-01T00:00:00",DateEnd:"2038-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2039-01-01T00:00:00",DateEnd:"2039-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2040-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"SA Eastern Standard Time",DisplayName:"(UTC-03:00) Cayenne, Fortaleza",StandardName:"SA Eastern Standard Time",DaylightName:"SA Eastern Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Argentina Standard Time",DisplayName:"(UTC-03:00) City of Buenos Aires",StandardName:"Argentina Standard Time",DaylightName:"Argentina Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:[{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:12,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Greenland Standard Time",DisplayName:"(UTC-03:00) Greenland",StandardName:"Greenland Standard Time",DaylightName:"Greenland Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2004-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2005-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"2019-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2020-01-01T00:00:00",DateEnd:"2020-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2021-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T22:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Montevideo Standard Time",DisplayName:"(UTC-03:00) Montevideo",StandardName:"Montevideo Standard Time",DaylightName:"Montevideo Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Magallanes Standard Time",DisplayName:"(UTC-03:00) Punta Arenas",StandardName:"Magallanes Standard Time",DaylightName:"Magallanes Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Saint Pierre Standard Time",DisplayName:"(UTC-03:00) Saint Pierre and Miquelon",StandardName:"Saint Pierre Standard Time",DaylightName:"Saint Pierre Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Bahia Standard Time",DisplayName:"(UTC-03:00) Salvador",StandardName:"Bahia Standard Time",DaylightName:"Bahia Daylight Time",BaseUtcOffset:"-03:00:00",AdjustmentRules:[{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:2,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"UTC-02",DisplayName:"(UTC-02:00) Coordinated Universal Time-02",StandardName:"UTC-02",DaylightName:"UTC-02",BaseUtcOffset:"-02:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Mid-Atlantic Standard Time",DisplayName:"(UTC-02:00) Mid-Atlantic - Old",StandardName:"Mid-Atlantic Standard Time",DaylightName:"Mid-Atlantic Daylight Time",BaseUtcOffset:"-02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Azores Standard Time",DisplayName:"(UTC-01:00) Azores",StandardName:"Azores Standard Time",DaylightName:"Azores Daylight Time",BaseUtcOffset:"-01:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Cape Verde Standard Time",DisplayName:"(UTC-01:00) Cabo Verde Is.",StandardName:"Cabo Verde Standard Time",DaylightName:"Cabo Verde Daylight Time",BaseUtcOffset:"-01:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"UTC",DisplayName:"(UTC) Coordinated Universal Time",StandardName:"Coordinated Universal Time",DaylightName:"Coordinated Universal Time",BaseUtcOffset:"00:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"GMT Standard Time",DisplayName:"(UTC+00:00) Dublin, Edinburgh, Lisbon, London",StandardName:"GMT Standard Time",DaylightName:"GMT Daylight Time",BaseUtcOffset:"00:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T01:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Greenwich Standard Time",DisplayName:"(UTC+00:00) Monrovia, Reykjavik",StandardName:"Greenwich Standard Time",DaylightName:"Greenwich Daylight Time",BaseUtcOffset:"00:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"W. Europe Standard Time",DisplayName:"(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna",StandardName:"W. Europe Standard Time",DaylightName:"W. Europe Daylight Time",BaseUtcOffset:"01:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Central Europe Standard Time",DisplayName:"(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague",StandardName:"Central Europe Standard Time",DaylightName:"Central Europe Daylight Time",BaseUtcOffset:"01:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Romance Standard Time",DisplayName:"(UTC+01:00) Brussels, Copenhagen, Madrid, Paris",StandardName:"Romance Standard Time",DaylightName:"Romance Daylight Time",BaseUtcOffset:"01:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Morocco Standard Time",DisplayName:"(UTC+01:00) Casablanca",StandardName:"Morocco Standard Time",DaylightName:"Morocco Daylight Time",BaseUtcOffset:"01:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:7,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:6,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Sao Tome Standard Time",DisplayName:"(UTC+01:00) Sao Tome",StandardName:"Sao Tome Standard Time",DaylightName:"Sao Tome Daylight Time",BaseUtcOffset:"01:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:1,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Central European Standard Time",DisplayName:"(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb",StandardName:"Central European Standard Time",DaylightName:"Central European Daylight Time",BaseUtcOffset:"01:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"W. Central Africa Standard Time",DisplayName:"(UTC+01:00) West Central Africa",StandardName:"W. Central Africa Standard Time",DaylightName:"W. Central Africa Daylight Time",BaseUtcOffset:"01:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Jordan Standard Time",DisplayName:"(UTC+02:00) Amman",StandardName:"Jordan Standard Time",DaylightName:"Jordan Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:2,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:12,Week:3,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"GTB Standard Time",DisplayName:"(UTC+02:00) Athens, Bucharest",StandardName:"GTB Standard Time",DaylightName:"GTB Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Middle East Standard Time",DisplayName:"(UTC+02:00) Beirut",StandardName:"Middle East Standard Time",DaylightName:"Middle East Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"2019-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2020-01-01T00:00:00",DateEnd:"2020-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2021-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Egypt Standard Time",DisplayName:"(UTC+02:00) Cairo",StandardName:"Egypt Standard Time",DaylightName:"Egypt Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:4,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"E. Europe Standard Time",DisplayName:"(UTC+02:00) Chisinau",StandardName:"E. Europe Standard Time",DaylightName:"E. Europe Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Syria Standard Time",DisplayName:"(UTC+02:00) Damascus",StandardName:"Syria Standard Time",DaylightName:"Syria Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2004-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2005-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:11,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:4,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:4,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"2019-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:4,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2020-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"West Bank Standard Time",DisplayName:"(UTC+02:00) Gaza, Hebron",StandardName:"West Bank Gaza Standard Time",DaylightName:"West Bank Gaza Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:9,Week:3,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:4,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:4,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T01:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T01:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T01:00:00",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"South Africa Standard Time",DisplayName:"(UTC+02:00) Harare, Pretoria",StandardName:"South Africa Standard Time",DaylightName:"South Africa Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"FLE Standard Time",DisplayName:"(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius",StandardName:"FLE Standard Time",DaylightName:"FLE Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Israel Standard Time",DisplayName:"(UTC+02:00) Jerusalem",StandardName:"Jerusalem Standard Time",DaylightName:"Jerusalem Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2004-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T01:00:00",Month:4,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T01:00:00",Month:9,Week:4,Day:1,DayOfWeek:3,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2005-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:4,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:4,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:4,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"2019-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2020-01-01T00:00:00",DateEnd:"2020-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2021-01-01T00:00:00",DateEnd:"2021-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2022-01-01T00:00:00",DateEnd:"2022-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2023-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:4,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Kaliningrad Standard Time",DisplayName:"(UTC+02:00) Kaliningrad",StandardName:"Russia TZ 1 Standard Time",DaylightName:"Russia TZ 1 Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Sudan Standard Time",DisplayName:"(UTC+02:00) Khartoum",StandardName:"Sudan Standard Time",DaylightName:"Sudan Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:2,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Libya Standard Time",DisplayName:"(UTC+02:00) Tripoli",StandardName:"Libya Standard Time",DaylightName:"Libya Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:2,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T01:00:00",Month:3,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:2,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Namibia Standard Time",DisplayName:"(UTC+02:00) Windhoek",StandardName:"Namibia Standard Time",DaylightName:"Namibia Daylight Time",BaseUtcOffset:"02:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Arabic Standard Time",DisplayName:"(UTC+03:00) Baghdad",StandardName:"Arabic Standard Time",DaylightName:"Arabic Daylight Time",BaseUtcOffset:"03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2004-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:4,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2005-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:4,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Turkey Standard Time",DisplayName:"(UTC+03:00) Istanbul",StandardName:"Turkey Standard Time",DaylightName:"Turkey Daylight Time",BaseUtcOffset:"03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:11,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Arab Standard Time",DisplayName:"(UTC+03:00) Kuwait, Riyadh",StandardName:"Arab Standard Time",DaylightName:"Arab Daylight Time",BaseUtcOffset:"03:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Belarus Standard Time",DisplayName:"(UTC+03:00) Minsk",StandardName:"Belarus Standard Time",DaylightName:"Belarus Daylight Time",BaseUtcOffset:"03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Russian Standard Time",DisplayName:"(UTC+03:00) Moscow, St. Petersburg",StandardName:"Russia TZ 2 Standard Time",DaylightName:"Russia TZ 2 Daylight Time",BaseUtcOffset:"03:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"E. Africa Standard Time",DisplayName:"(UTC+03:00) Nairobi",StandardName:"E. Africa Standard Time",DaylightName:"E. Africa Daylight Time",BaseUtcOffset:"03:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Iran Standard Time",DisplayName:"(UTC+03:30) Tehran",StandardName:"Iran Standard Time",DaylightName:"Iran Daylight Time",BaseUtcOffset:"03:30:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2004-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:1,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2005-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:2,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:3,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:1,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:2,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:2,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:3,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:1,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:2,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2019-01-01T00:00:00",DateEnd:"2019-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2020-01-01T00:00:00",DateEnd:"2020-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2021-01-01T00:00:00",DateEnd:"2021-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:2,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2022-01-01T00:00:00",DateEnd:"2022-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:2,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:3,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2023-01-01T00:00:00",DateEnd:"2023-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:4,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2024-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:3,Week:3,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:3,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Arabian Standard Time",DisplayName:"(UTC+04:00) Abu Dhabi, Muscat",StandardName:"Arabian Standard Time",DaylightName:"Arabian Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Astrakhan Standard Time",DisplayName:"(UTC+04:00) Astrakhan, Ulyanovsk",StandardName:"Astrakhan Standard Time",DaylightName:"Astrakhan Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Azerbaijan Standard Time",DisplayName:"(UTC+04:00) Baku",StandardName:"Azerbaijan Standard Time",DaylightName:"Azerbaijan Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T04:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T05:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Russia Time Zone 3",DisplayName:"(UTC+04:00) Izhevsk, Samara",StandardName:"Russia TZ 3 Standard Time",DaylightName:"Russia TZ 3 Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Mauritius Standard Time",DisplayName:"(UTC+04:00) Port Louis",StandardName:"Mauritius Standard Time",DaylightName:"Mauritius Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:[{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:2,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Saratov Standard Time",DisplayName:"(UTC+04:00) Saratov",StandardName:"Saratov Standard Time",DaylightName:"Saratov Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:12,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Georgian Standard Time",DisplayName:"(UTC+04:00) Tbilisi",StandardName:"Georgian Standard Time",DaylightName:"Georgian Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Volgograd Standard Time",DisplayName:"(UTC+04:00) Volgograd",StandardName:"Volgograd Standard Time",DaylightName:"Volgograd Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Caucasus Standard Time",DisplayName:"(UTC+04:00) Yerevan",StandardName:"Caucasus Standard Time",DaylightName:"Caucasus Daylight Time",BaseUtcOffset:"04:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Afghanistan Standard Time",DisplayName:"(UTC+04:30) Kabul",StandardName:"Afghanistan Standard Time",DaylightName:"Afghanistan Daylight Time",BaseUtcOffset:"04:30:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"West Asia Standard Time",DisplayName:"(UTC+05:00) Ashgabat, Tashkent",StandardName:"West Asia Standard Time",DaylightName:"West Asia Daylight Time",BaseUtcOffset:"05:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Ekaterinburg Standard Time",DisplayName:"(UTC+05:00) Ekaterinburg",StandardName:"Russia TZ 4 Standard Time",DaylightName:"Russia TZ 4 Daylight Time",BaseUtcOffset:"05:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Pakistan Standard Time",DisplayName:"(UTC+05:00) Islamabad, Karachi",StandardName:"Pakistan Standard Time",DaylightName:"Pakistan Daylight Time",BaseUtcOffset:"05:00:00",AdjustmentRules:[{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:5,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:59:59.999",Month:4,Week:2,Day:1,DayOfWeek:2,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:10,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Sri Lanka Standard Time",DisplayName:"(UTC+05:30) Sri Jayawardenepura",StandardName:"Sri Lanka Standard Time",DaylightName:"Sri Lanka Daylight Time",BaseUtcOffset:"05:30:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Nepal Standard Time",DisplayName:"(UTC+05:45) Kathmandu",StandardName:"Nepal Standard Time",DaylightName:"Nepal Daylight Time",BaseUtcOffset:"05:45:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Central Asia Standard Time",DisplayName:"(UTC+06:00) Astana",StandardName:"Central Asia Standard Time",DaylightName:"Central Asia Daylight Time",BaseUtcOffset:"06:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Bangladesh Standard Time",DisplayName:"(UTC+06:00) Dhaka",StandardName:"Bangladesh Standard Time",DaylightName:"Bangladesh Daylight Time",BaseUtcOffset:"06:00:00",AdjustmentRules:[{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T23:00:00",Month:6,Week:3,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:12,Week:5,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Omsk Standard Time",DisplayName:"(UTC+06:00) Omsk",StandardName:"Omsk Standard Time",DaylightName:"Omsk Daylight Time",BaseUtcOffset:"06:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Myanmar Standard Time",DisplayName:"(UTC+06:30) Yangon (Rangoon)",StandardName:"Myanmar Standard Time",DaylightName:"Myanmar Daylight Time",BaseUtcOffset:"06:30:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"SE Asia Standard Time",DisplayName:"(UTC+07:00) Bangkok, Hanoi, Jakarta",StandardName:"SE Asia Standard Time",DaylightName:"SE Asia Daylight Time",BaseUtcOffset:"07:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Altai Standard Time",DisplayName:"(UTC+07:00) Barnaul, Gorno-Altaysk",StandardName:"Altai Standard Time",DaylightName:"Altai Daylight Time",BaseUtcOffset:"07:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"W. Mongolia Standard Time",DisplayName:"(UTC+07:00) Hovd",StandardName:"W. Mongolia Standard Time",DaylightName:"W. Mongolia Daylight Time",BaseUtcOffset:"07:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:4,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"North Asia Standard Time",DisplayName:"(UTC+07:00) Krasnoyarsk",StandardName:"Russia TZ 6 Standard Time",DaylightName:"Russia TZ 6 Daylight Time",BaseUtcOffset:"07:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"N. Central Asia Standard Time",DisplayName:"(UTC+07:00) Novosibirsk",StandardName:"Novosibirsk Standard Time",DaylightName:"Novosibirsk Daylight Time",BaseUtcOffset:"07:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:7,Week:4,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Tomsk Standard Time",DisplayName:"(UTC+07:00) Tomsk",StandardName:"Tomsk Standard Time",DaylightName:"Tomsk Daylight Time",BaseUtcOffset:"07:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:5,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"China Standard Time",DisplayName:"(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi",StandardName:"China Standard Time",DaylightName:"China Daylight Time",BaseUtcOffset:"08:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"North Asia East Standard Time",DisplayName:"(UTC+08:00) Irkutsk",StandardName:"Russia TZ 7 Standard Time",DaylightName:"Russia TZ 7 Daylight Time",BaseUtcOffset:"08:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Singapore Standard Time",DisplayName:"(UTC+08:00) Kuala Lumpur, Singapore",StandardName:"Malay Peninsula Standard Time",DaylightName:"Malay Peninsula Daylight Time",BaseUtcOffset:"08:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"W. Australia Standard Time",DisplayName:"(UTC+08:00) Perth",StandardName:"W. Australia Standard Time",DaylightName:"W. Australia Daylight Time",BaseUtcOffset:"08:00:00",AdjustmentRules:[{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:12,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"2008-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Taipei Standard Time",DisplayName:"(UTC+08:00) Taipei",StandardName:"Taipei Standard Time",DaylightName:"Taipei Daylight Time",BaseUtcOffset:"08:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Ulaanbaatar Standard Time",DisplayName:"(UTC+08:00) Ulaanbaatar",StandardName:"Ulaanbaatar Standard Time",DaylightName:"Ulaanbaatar Daylight Time",BaseUtcOffset:"08:00:00",AdjustmentRules:[{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:5,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:9,Week:4,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Aus Central W. Standard Time",DisplayName:"(UTC+08:45) Eucla",StandardName:"Aus Central W. Standard Time",DaylightName:"Aus Central W. Daylight Time",BaseUtcOffset:"08:45:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Transbaikal Standard Time",DisplayName:"(UTC+09:00) Chita",StandardName:"Transbaikal Standard Time",DaylightName:"Transbaikal Daylight Time",BaseUtcOffset:"09:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"02:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Tokyo Standard Time",DisplayName:"(UTC+09:00) Osaka, Sapporo, Tokyo",StandardName:"Tokyo Standard Time",DaylightName:"Tokyo Daylight Time",BaseUtcOffset:"09:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"North Korea Standard Time",DisplayName:"(UTC+09:00) Pyongyang",StandardName:"North Korea Standard Time",DaylightName:"North Korea Daylight Time",BaseUtcOffset:"09:00:00",AdjustmentRules:[{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:59:59.999",Month:8,Week:2,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-00:30:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"2018-12-31T00:00:00",DaylightDelta:"-00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:1,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T23:30:00",Month:5,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Korea Standard Time",DisplayName:"(UTC+09:00) Seoul",StandardName:"Korea Standard Time",DaylightName:"Korea Daylight Time",BaseUtcOffset:"09:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Yakutsk Standard Time",DisplayName:"(UTC+09:00) Yakutsk",StandardName:"Russia TZ 8 Standard Time",DaylightName:"Russia TZ 8 Daylight Time",BaseUtcOffset:"09:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Cen. Australia Standard Time",DisplayName:"(UTC+09:30) Adelaide",StandardName:"Cen. Australia Standard Time",DaylightName:"Cen. Australia Daylight Time",BaseUtcOffset:"09:30:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"AUS Central Standard Time",DisplayName:"(UTC+09:30) Darwin",StandardName:"AUS Central Standard Time",DaylightName:"AUS Central Daylight Time",BaseUtcOffset:"09:30:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"E. Australia Standard Time",DisplayName:"(UTC+10:00) Brisbane",StandardName:"E. Australia Standard Time",DaylightName:"E. Australia Daylight Time",BaseUtcOffset:"10:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"AUS Eastern Standard Time",DisplayName:"(UTC+10:00) Canberra, Melbourne, Sydney",StandardName:"AUS Eastern Standard Time",DaylightName:"AUS Eastern Daylight Time",BaseUtcOffset:"10:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"West Pacific Standard Time",DisplayName:"(UTC+10:00) Guam, Port Moresby",StandardName:"West Pacific Standard Time",DaylightName:"West Pacific Daylight Time",BaseUtcOffset:"10:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Tasmania Standard Time",DisplayName:"(UTC+10:00) Hobart",StandardName:"Tasmania Standard Time",DaylightName:"Tasmania Daylight Time",BaseUtcOffset:"10:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Vladivostok Standard Time",DisplayName:"(UTC+10:00) Vladivostok",StandardName:"Russia TZ 9 Standard Time",DaylightName:"Russia TZ 9 Daylight Time",BaseUtcOffset:"10:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Lord Howe Standard Time",DisplayName:"(UTC+10:30) Lord Howe Island",StandardName:"Lord Howe Standard Time",DaylightName:"Lord Howe Daylight Time",BaseUtcOffset:"10:30:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2005-12-31T00:00:00",DaylightDelta:"00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2006-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Bougainville Standard Time",DisplayName:"(UTC+11:00) Bougainville Island",StandardName:"Bougainville Standard Time",DaylightName:"Bougainville Daylight Time",BaseUtcOffset:"11:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:12,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Russia Time Zone 10",DisplayName:"(UTC+11:00) Chokurdakh",StandardName:"Russia TZ 10 Standard Time",DaylightName:"Russia TZ 10 Daylight Time",BaseUtcOffset:"11:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Magadan Standard Time",DisplayName:"(UTC+11:00) Magadan",StandardName:"Magadan Standard Time",DaylightName:"Magadan Daylight Time",BaseUtcOffset:"11:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"02:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:4,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Norfolk Standard Time",DisplayName:"(UTC+11:00) Norfolk Island",StandardName:"Norfolk Standard Time",DaylightName:"Norfolk Daylight Time",BaseUtcOffset:"11:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"00:30:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:30:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Sakhalin Standard Time",DisplayName:"(UTC+11:00) Sakhalin",StandardName:"Sakhalin Standard Time",DaylightName:"Sakhalin Daylight Time",BaseUtcOffset:"11:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:3,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"-01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Central Pacific Standard Time",DisplayName:"(UTC+11:00) Solomon Is., New Caledonia",StandardName:"Central Pacific Standard Time",DaylightName:"Central Pacific Daylight Time",BaseUtcOffset:"11:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Russia Time Zone 11",DisplayName:"(UTC+12:00) Anadyr, Petropavlovsk-Kamchatsky",StandardName:"Russia TZ 11 Standard Time",DaylightName:"Russia TZ 11 Daylight Time",BaseUtcOffset:"12:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-01:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"New Zealand Standard Time",DisplayName:"(UTC+12:00) Auckland, Wellington",StandardName:"New Zealand Standard Time",DaylightName:"New Zealand Daylight Time",BaseUtcOffset:"12:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"UTC+12",DisplayName:"(UTC+12:00) Coordinated Universal Time+12",StandardName:"UTC+12",DaylightName:"UTC+12",BaseUtcOffset:"12:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Fiji Standard Time",DisplayName:"(UTC+12:00) Fiji",StandardName:"Fiji Standard Time",DaylightName:"Fiji Daylight Time",BaseUtcOffset:"12:00:00",AdjustmentRules:[{DateStart:"2009-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:4,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:4,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:4,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:3,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"2012-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:1,Week:4,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2013-01-01T00:00:00",DateEnd:"2013-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:10,Week:4,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:1,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2014-01-01T00:00:00",DateEnd:"2014-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T02:00:00",Month:1,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2015-01-01T00:00:00",DateEnd:"2015-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:1,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:1,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:1,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2018-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:1,Week:2,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Kamchatka Standard Time",DisplayName:"(UTC+12:00) Petropavlovsk-Kamchatsky - Old",StandardName:"Kamchatka Standard Time",DaylightName:"Kamchatka Daylight Time",BaseUtcOffset:"12:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:3,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:10,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Chatham Islands Standard Time",DisplayName:"(UTC+12:45) Chatham Islands",StandardName:"Chatham Islands Standard Time",DaylightName:"Chatham Islands Daylight Time",BaseUtcOffset:"12:45:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2006-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:45:00",Month:10,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:45:00",Month:3,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2007-01-01T00:00:00",DateEnd:"2007-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:45:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:45:00",Month:3,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2008-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:45:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:45:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"UTC+13",DisplayName:"(UTC+13:00) Coordinated Universal Time+13",StandardName:"UTC+13",DaylightName:"UTC+13",BaseUtcOffset:"13:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1},{Id:"Tonga Standard Time",DisplayName:"(UTC+13:00) Nuku'alofa",StandardName:"Tonga Standard Time",DaylightName:"Tonga Daylight Time",BaseUtcOffset:"13:00:00",AdjustmentRules:[{DateStart:"2016-01-01T00:00:00",DateEnd:"2016-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T02:00:00",Month:11,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1},{DateStart:"2017-01-01T00:00:00",DateEnd:"2017-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T03:00:00",Month:1,Week:3,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Samoa Standard Time",DisplayName:"(UTC+13:00) Samoa",StandardName:"Samoa Standard Time",DaylightName:"Samoa Daylight Time",BaseUtcOffset:"13:00:00",AdjustmentRules:[{DateStart:"0001-01-01T00:00:00",DateEnd:"2009-12-31T00:00:00",DaylightDelta:"00:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00.001",Month:1,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!0},BaseUtcOffsetDelta:"-1.00:00:00",NoDaylightTransitions:!1},{DateStart:"2010-01-01T00:00:00",DateEnd:"2010-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T00:00:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T00:00:00",Month:1,Week:1,Day:1,DayOfWeek:5,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-1.00:00:00",NoDaylightTransitions:!1},{DateStart:"2011-01-01T00:00:00",DateEnd:"2011-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:9,Week:4,Day:1,DayOfWeek:6,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:4,Week:1,Day:1,DayOfWeek:6,IsFixedDateRule:!1},BaseUtcOffsetDelta:"-1.00:00:00",NoDaylightTransitions:!1},{DateStart:"2012-01-01T00:00:00",DateEnd:"9999-12-31T00:00:00",DaylightDelta:"01:00:00",DaylightTransitionStart:{TimeOfDay:"0001-01-01T03:00:00",Month:9,Week:5,Day:1,DayOfWeek:0,IsFixedDateRule:!1},DaylightTransitionEnd:{TimeOfDay:"0001-01-01T04:00:00",Month:4,Week:1,Day:1,DayOfWeek:0,IsFixedDateRule:!1},BaseUtcOffsetDelta:"00:00:00",NoDaylightTransitions:!1}],SupportsDaylightSavingTime:!0},{Id:"Line Islands Standard Time",DisplayName:"(UTC+14:00) Kiritimati Island",StandardName:"Line Islands Standard Time",DaylightName:"Line Islands Daylight Time",BaseUtcOffset:"14:00:00",AdjustmentRules:null,SupportsDaylightSavingTime:!1}];var m=t(579);const k=a=>{const{modelsData:e,setModelsData:t,showEditPopup:s,setShowEditPopup:y,OrganizationId:o,sortModel:r,filters:h,setLoading:f,setModels:k,models:u,handleClose:W,skip:S,top:c,setTotalcount:x,updateOrganizationDetails:I}=a,[R,M]=(0,D.useState)({OrganizationId:"",Name:"",TimeZone:"Asia/Kolkata",DateFormat:"dd-MM-yyyy",Logo:"",Status:"",CreatedDate:"",UpdatedDate:"",AuthorizationType:"",IsActive:"",Rtl:!1,TimeFormat:"",ThemeId:"",Type:"",OrganizationPlanId:"",OrganizationPlan:null,Plan:""}),[N,F]=(0,D.useState)({Name:"",Logo:"",TimeZone:"",DateFormat:"",Type:"",Rtl:""}),[E,p]=(0,D.useState)((0,i.A)({},R)),[U,B]=(0,D.useState)(!1),[A,v]=(0,D.useState)(""),[C,j]=(0,D.useState)("success"),b=()=>{B(!1)};(0,D.useEffect)((()=>{""!==R.Name&&(async()=>{if(""===R.Name.trim())return void F((a=>(0,i.A)((0,i.A)({},a),{},{Name:""})));const a=e.every((a=>a.Name!==R.Name||a.OrganizationId===R.OrganizationId));F(a?a=>(0,i.A)((0,i.A)({},a),{},{Name:""}):a=>(0,i.A)((0,i.A)({},a),{},{Name:"Organization Name already exists"}))})()}),[R.Name,e,u,R.OrganizationId]);const z=a=>{const{name:e,value:t}=a.target;M((a=>(0,i.A)((0,i.A)({},a),{},{[e]:t}))),w(e,t)},w=(a,e)=>{let t=!0,D=(0,i.A)({},N);switch(a){case"Name":e?e.length<5?(D.Name="Organization Name must be at least 5 characters",t=!1):e.length>50?(D.Name="Organization Name must be at most 50 characters",t=!1):/^[a-zA-Z0-9 ]+$/.test(e)?P(e,R.OrganizationId)?D.Name="":(D.Name="Organization Name already exists",t=!1):(D.Name="Organization Name can only contain letters, numbers, and spaces",t=!1):(D.Name="Organization Name is required",t=!1);break;case"Logo":e?D.Logo="":(D.Logo="Logo is required",t=!1);break;case"TimeZone":e?D.TimeZone="":(D.TimeZone="Timezone is required",t=!1);break;case"DateFormat":e?D.DateFormat="":(D.DateFormat="Date Format is required",t=!1);break;case"Type":e?D.Type="":(D.Type="Type is required",t=!1)}return F(D),t},P=(a,t)=>e.every((e=>e.Name!==a||e.OrganizationId===t)),L=()=>JSON.stringify(R)!==JSON.stringify(E),q=async a=>{if(a.preventDefault(),(()=>{let a=!0;for(const[e,t]of Object.entries(R))w(e,t)||(a=!1);return a})())try{I(R)}catch(e){console.error("Failed to update organization:",e),v("Failed to update organization"),j("error"),B(!0)}else v("Please correct the errors in the form"),j("error"),B(!0)};(0,D.useEffect)((()=>{s&&Z(o)}),[s,o]);const Z=async a=>{try{const e=localStorage.getItem("access_token"),t=await fetch("".concat(g.nN,"/Organization/GetOrganizationById?organizationId=").concat(a),{method:"GET",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!t.ok)throw new Error("Network response was not ok");const i=await t.json();M({OrganizationId:i.OrganizationId,Name:i.Name,TimeZone:i.TimeZone,DateFormat:i.DateFormat,Logo:i.Logo,Status:i.Status,CreatedDate:i.CreatedDate,UpdatedDate:i.UpdatedDate,AuthorizationType:i.AuthorizationType,IsActive:i.IsActive,Rtl:i.RTL,TimeFormat:i.TimeFormat,ThemeId:i.ThemeId,Type:i.Type,OrganizationPlanId:i.OrganizationPlanId,OrganizationPlan:i.OrganizationPlan,Plan:i.Plan}),p({OrganizationId:i.OrganizationId,Name:i.Name,TimeZone:i.TimeZone,DateFormat:i.DateFormat,Logo:i.Logo,Status:i.Status,CreatedDate:i.CreatedDate,UpdatedDate:i.UpdatedDate,AuthorizationType:i.AuthorizationType,IsActive:i.IsActive,Rtl:i.RTL,TimeFormat:i.TimeFormat,ThemeId:i.ThemeId,Type:i.Type,OrganizationPlanId:i.OrganizationPlanId,OrganizationPlan:i.OrganizationPlan,Plan:i.Plan})}catch(e){console.error("Failed to fetch organization details:",e)}};return(0,m.jsxs)("div",{children:[s&&(0,m.jsxs)("div",{className:"user-popup",children:[(0,m.jsxs)("div",{className:"qadpt-header",children:[(0,m.jsx)("span",{children:"Edit Organization"}),(0,m.jsx)("svg",{onClick:()=>W(),className:"close-icon",xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",width:"24",height:"24",viewBox:"0 0 50 50",children:(0,m.jsx)("path",{d:"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"})})]}),(0,m.jsx)("div",{className:"qadpt-usrform",children:(0,m.jsxs)("form",{onSubmit:q,children:[(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"organizationname",className:"qadpt-txtlabel",children:"Organization Name*"}),(0,m.jsx)("input",{className:"qadpt-txtinp",type:"text",name:"Name",value:R.Name,onChange:z}),N.Name&&(0,m.jsx)("div",{className:"error",children:N.Name})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"logo",className:"qadpt-txtlabel",children:"Logo*"}),(0,m.jsx)("input",{className:"qadpt-txtinp",type:"file",name:"logo",accept:"image/*",onChange:a=>{if(a.target.files&&a.target.files[0]){const e=a.target.files[0];M((a=>(0,i.A)((0,i.A)({},a),{},{Logo:e.name})))}}}),N.Logo&&(0,m.jsx)("div",{className:"error",children:N.Logo})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"timezone",className:"qadpt-txtlabel",children:"Timezone*"}),(0,m.jsx)("select",{value:R.TimeZone,onChange:a=>{M((e=>(0,i.A)((0,i.A)({},e),{},{TimeZone:a.target.value})))},className:"qadpt-txtinp",children:O.map((a=>(0,m.jsx)("option",{value:a.Id,children:a.DisplayName},a.Id)))}),N.TimeZone&&(0,m.jsx)("span",{className:"error",children:N.TimeZone})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"dateFormat",className:"qadpt-txtlabel",children:"Date Format*"}),(0,m.jsxs)("select",{id:"dateFormat",name:"DateFormat",value:R.DateFormat,onChange:z,className:N.DateFormat?"error-input":"qadpt-txtinp",children:[(0,m.jsx)("option",{value:"dd-MM-yyyy",children:"dd-MM-yyyy"}),(0,m.jsx)("option",{value:"MM-dd-yyyy",children:"MM-dd-yyyy"}),(0,m.jsx)("option",{value:"yyyy-MM-dd",children:"yyyy-MM-dd"})]}),N.DateFormat&&(0,m.jsx)("span",{className:"error",children:N.DateFormat}),"            "]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"type",className:"qadpt-txtlabel",children:"Type*"}),(0,m.jsxs)("select",{id:"type",name:"Type",value:R.Type,onChange:z,className:N.Type?"error-input":"qadpt-txtinp",children:[(0,m.jsx)("option",{value:"Client",children:"Client"}),(0,m.jsx)("option",{value:"Testing",children:"Testing"}),(0,m.jsx)("option",{value:"POC",children:"POC"}),(0,m.jsx)("option",{value:"Prospects",children:"Prospects"})]}),N.Type&&(0,m.jsx)("span",{className:"error",children:N.Type})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld qadpt-switch",children:[(0,m.jsx)("span",{className:"qadpt-txtlabel",children:"RTL"}),(0,m.jsx)(T.A,{control:(0,m.jsx)(n.A,{checked:R.Rtl,onChange:a=>M((e=>(0,i.A)((0,i.A)({},e),{},{Rtl:a.target.checked})))}),label:""})]})]})}),(0,m.jsx)("div",{className:"qadpt-button",children:(0,m.jsx)("button",{type:"submit",className:L()?"qadpt-enab":"qadpt-disab",disabled:!L(),onClick:q,children:"Update"})})]}),(0,m.jsx)(l.A,{open:U,autoHideDuration:6e3,onClose:b,anchorOrigin:{vertical:"top",horizontal:"center"},sx:{zIndex:1e4,marginTop:4},children:(0,m.jsx)(d.A,{onClose:b,severity:C,sx:{width:"100%"},children:A})}),"    "]})};var u=t(4353),W=t(2058),S=t(45),c=t(2381),x=t(7784),I=t(1962),R=t(2143),M=t(8734),N=t(3336),F=t(6446),E=t(2518),p=t(3635),U=t(8624);const B=["column","skip","top","OrganizationId","setTotalcount","orderByFields","setModels","sortModel","setLoading","filters","setFilters","options","onSearch","open","hideMenu","models","colDef","modelsData","optionsModel","paginationModel"],A=a=>{let{column:e,skip:t,top:n,OrganizationId:s,setTotalcount:l,orderByFields:y,setModels:T,sortModel:o,setLoading:r,filters:d,setFilters:h,options:f,onSearch:g,open:O,hideMenu:k,models:W,colDef:c,modelsData:A,optionsModel:v,paginationModel:C}=a;(0,S.A)(a,B);const[j,b]=(0,D.useState)(""),[z,w]=(0,D.useState)([]),[P,L]=(0,D.useState)(f),[q,Z]=(0,D.useState)([]);(0,D.useEffect)((()=>{const a=Array.from(new Set(f));if(L(a),"Name"===e)if(d.length)if(d.length&&d.find((a=>"Type"==a.FieldName))){const a=Array.from(new Set(W.map((a=>a.Name))));Array.from(new Set(W.map((a=>a.Name))));Z(a),w(a),L(a)}else{const a=Array.from(new Set(W.map((a=>a.Name)))),e=Array.from(new Set(v.map((a=>a.Name))));Z(a),w(a),L(e)}else{const a=Array.from(new Set(W.map((a=>a.Name)))),e=Array.from(new Set(v.map((a=>a.Name))));Z(a),w(a),L(e)}}),[f,W,e]),(0,D.useEffect)((()=>{if("Type"===e)if(d.length)if(Array.isArray(W)&&d.find((a=>"Name"==a.FieldName))){const a=Array.from(new Set(W.map((a=>a.Type))));w(a),L(a)}else{const a=Array.from(new Set(W.map((a=>a.Type)))),e=Array.from(new Set(v.map((a=>a.Type))));w(a),L(e)}else if(Array.isArray(v)){const a=Array.from(new Set(v.map((a=>a.Type))));w(a),L(a)}}),[e,W,q]);const H=()=>{const a=z.length===P.length?[]:P;w(a)},K=!j&&z.length===f.length,G=0===z.length&&!j,[Y,V]=(0,D.useState)(!1);return(0,m.jsxs)("div",{className:"qadpt-org-filter",style:{position:"relative"},children:[(0,m.jsx)(U.A,{multiple:!0,disableCloseOnSelect:!0,options:P,value:z,onChange:(a,e)=>{w(e)},onInputChange:(a,e)=>b(e),onOpen:()=>V(!0),renderOption:(a,e,t)=>{let{selected:n}=t;return(0,m.jsxs)("div",{children:[e===P[0]&&(0,m.jsxs)(R.A,{onClick:H,children:[(0,m.jsx)(I.A,{checked:z.length===P.length,indeterminate:z.length>0&&z.length<P.length}),(0,m.jsx)(M.A,{primary:"Select All"})]},"select-all"),(0,D.createElement)(R.A,(0,i.A)((0,i.A)({},a),{},{key:e}),(0,m.jsx)(I.A,{checked:z.includes(e)}),(0,m.jsx)(M.A,{primary:e}))]})},renderInput:a=>(0,m.jsx)(x.A,(0,i.A)((0,i.A)({},a),{},{variant:"outlined",label:"Search",placeholder:"Select Options",onKeyDown:a=>{["ArrowUp","ArrowDown","Enter"].includes(a.key),a.stopPropagation()},InputProps:(0,i.A)((0,i.A)({},a.InputProps),{},{endAdornment:(0,m.jsx)(D.Fragment,{children:a.InputProps.endAdornment})})})),PaperComponent:a=>(0,m.jsx)(N.A,(0,i.A)((0,i.A)({},a),{},{style:{position:"absolute",top:"100%",left:0,width:"100%",zIndex:10,marginTop:"4px"},onWheel:a=>a.stopPropagation()})),ListboxProps:{style:{maxHeight:"220px",overflowY:"auto"}},renderTags:()=>null}),(0,m.jsxs)(F.A,{display:"flex",justifyContent:"flex-end",mt:2,className:"qadpt-btn",sx:{display:"flex",justifyContent:"flex-end",padding:"0 10px",marginTop:Y?"250px":"10px",transition:"margin-top 0.3s ease-in-out"},children:[(0,m.jsx)(E.A,{variant:"outlined",color:"primary",onClick:async a=>{const t=z.length?z:[j],i=t.join(","),D={FieldName:e,ElementType:"string",Condition:"in",Value:i,IsCustomField:!1},n=[...d],s=n.findIndex((a=>a.FieldName===e));-1!==s?n[s]=D:n.push(D),h(n),await(0,u.h6)(T,r,0,C.pageSize,l,o,n),w(t),g(z),k(a)},disabled:G,children:"OK"}),(0,m.jsx)(E.A,{variant:"outlined",color:"secondary",onClick:a=>{k(a)},style:{marginLeft:"8px"},children:"Cancel"}),(0,m.jsx)(E.A,{onClick:a=>{b(""),h([]),g([]),k(a)},startIcon:(0,m.jsx)(p.A,{}),disabled:K,style:{marginLeft:"8px"},children:"Clear Filters"})]})]})},v=["column","skip","top","OrganizationId","setTotalcount","orderByFields","setModels","sortModel","setLoading","filters","setFilters","options","onSearch","hideMenu","models","modelsData","optionsModel","paginationModel"],C=a=>{const{column:e,skip:t,top:D,OrganizationId:n,setTotalcount:s,orderByFields:l,setModels:y,sortModel:T,setLoading:o,filters:r,setFilters:d,options:h,onSearch:f,hideMenu:g,models:O,modelsData:k,optionsModel:u,paginationModel:W}=a,x=(0,S.A)(a,v);return(0,m.jsx)("div",{children:(0,m.jsx)(c.aU,(0,i.A)((0,i.A)({},x),{},{slots:{columnMenuUserItem:a=>(0,m.jsx)(A,{column:e,setModels:y,setLoading:o,skip:t,top:D,OrganizationId:n,sortModel:T,setTotalcount:s,orderByFields:l,filters:r,setFilters:d,options:h,onSearch:f,models:O,modelsData:k,hideMenu:a=>g(a),open:a.open,colDef:a.colDef,optionsModel:u,paginationModel:W}),GridColumnMenuHideItem:null},slotProps:{columnMenuUserItem:{displayOrder:15}},hideMenu:g}))})};var j=t(6559),b=t(7225),z=t(1119),w=t(1081),P=t(6600),L=t(5316),q=t(8911),Z=t(9347),H=t(4145),K=t(8390),G=t(9302),Y=t(446),V=t.n(Y);const J=a=>{let{showSubscribe:e,closeSubscribe:t,organizationId:n,setSnackbarOpen:s,setSnackbarSeverity:l,setSnackbarMessage:y,updateOrganizationDetails:T}=a;const[o,r]=(0,D.useState)(),[d,h]=(0,D.useState)(Date.now()),[f,O]=(0,D.useState)({OrganizationId:"",Name:"",TimeZone:"Asia/Kolkata",DateFormat:"dd-MM-yyyy",Logo:"",Status:"",CreatedDate:"",UpdatedDate:"",AuthorizationType:"",IsActive:"",Rtl:!1,TimeFormat:"",ThemeId:"",Type:"",OrganizationPlanId:"",OrganizationPlan:{EndDate:"",StartDate:""},Plan:""}),[k,u]=(0,D.useState)((0,i.A)({},f));(0,D.useEffect)((()=>{e&&W(n)}),[e,n]);const W=async a=>{try{var e,t,i,D;const n=localStorage.getItem("access_token"),s=await fetch("".concat(g.nN,"/Organization/GetOrganizationById?organizationId=").concat(a),{method:"GET",headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}});if(!s.ok)throw new Error("Network response was not ok");const l=await s.json();O({OrganizationId:l.OrganizationId,Name:l.Name,TimeZone:l.TimeZone,DateFormat:l.DateFormat,Logo:l.Logo,Status:l.Status,CreatedDate:l.CreatedDate,UpdatedDate:l.UpdatedDate,AuthorizationType:l.AuthorizationType,IsActive:l.IsActive,Rtl:l.RTL,TimeFormat:l.TimeFormat,ThemeId:l.ThemeId,Type:l.Type,OrganizationPlanId:l.OrganizationPlanId,OrganizationPlan:l.OrganizationPlan,Plan:l.Plan}),u({OrganizationId:l.OrganizationId,Name:l.Name,TimeZone:l.TimeZone,DateFormat:l.DateFormat,Logo:l.Logo,Status:l.Status,CreatedDate:l.CreatedDate,UpdatedDate:l.UpdatedDate,AuthorizationType:l.AuthorizationType,IsActive:l.IsActive,Rtl:l.RTL,TimeFormat:l.TimeFormat,ThemeId:l.ThemeId,Type:l.Type,OrganizationPlanId:l.OrganizationPlanId,OrganizationPlan:l.OrganizationPlan,Plan:l.Plan}),r(null!==(e=null===(t=l.OrganizationPlan)||void 0===t?void 0:t.StartDate)&&void 0!==e?e:Date.now()),h(null!==(i=null===(D=l.OrganizationPlan)||void 0===D?void 0:D.EndDate)&&void 0!==i?i:Date.now())}catch(n){console.error("Failed to fetch organization details:",n)}};return(0,m.jsx)(K.$,{dateAdapter:G.R,children:(0,m.jsx)(F.A,{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",children:(0,m.jsxs)(w.A,{open:e,onClose:t,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{p:2,borderRadius:2}},children:[(0,m.jsx)(P.A,{children:"Update Subscription"}),(0,m.jsx)(L.A,{dividers:!0,children:(0,m.jsxs)(q.A,{spacing:2,children:[(0,m.jsx)(x.A,{select:!0,label:"Select Plan",fullWidth:!0,value:k.Plan,onChange:a=>{return e=a.target.value,void u((a=>(0,i.A)((0,i.A)({},a),{},{Plan:e})));var e},children:["Free Trail","Basic","Advanced"].map((a=>(0,m.jsx)(R.A,{value:a,children:a},a)))}),(0,m.jsx)(H.l,{label:"Start Date",value:V()(o),onChange:a=>r(a),readOnly:!0,slotProps:{textField:{fullWidth:!0,disabled:!0}}}),(0,m.jsx)(H.l,{label:"End Date",value:V()(d),onChange:a=>h(a),slotProps:{textField:{fullWidth:!0}}})]})}),(0,m.jsxs)(Z.A,{children:[(0,m.jsx)(E.A,{onClick:t,children:"Cancel"}),(0,m.jsx)(E.A,{onClick:()=>{if("Free Trail"==k.Plan)y("You can't Select/Upgrade Free Trail Again"),l("error"),s(!0);else if(V()(d).diff(V()(o),"day")<15)y("The Subscription Update Should be above 15 Days"),l("error"),s(!0);else{O((a=>(0,i.A)((0,i.A)({},a),{},{Plan:k.Plan,OrganizationPlan:{StartDate:V()(o).format("YYYY-MM-DDTHH:mm:ss.SSSZ"),EndDate:V()(d).format("YYYY-MM-DDTHH:mm:ss.SSSZ")}})));try{T((0,i.A)((0,i.A)({},f),{},{Plan:k.Plan,OrganizationPlan:{StartDate:V()(o).format("YYYY-MM-DDTHH:mm:ss.SSSZ"),EndDate:V()(d).format("YYYY-MM-DDTHH:mm:ss.SSSZ")}}))}catch(a){console.error("Failed to update organization:",a),y("Failed to update organization"),l("error"),s(!0)}}},variant:"contained",children:"Save"})]})]})})})},$=()=>{const[a,e]=(0,D.useState)(!1),[t,g]=(0,D.useState)(""),[S,c]=(0,D.useState)(500),[x,I]=(0,D.useState)([]),[R,M]=(0,D.useState)(!0),[N,F]=(0,D.useState)(""),[E,p]=(0,D.useState)(""),[U,B]=(0,D.useState)(""),[A,v]=(0,D.useState)(null),[w,P]=(0,D.useState)(""),[L,q]=(0,D.useState)(""),[Z,H]=(0,D.useState)(null),[K,G]=(0,D.useState)(""),[Y,V]=(0,D.useState)({}),[$,X]=(0,D.useState)(null),[_,Q]=(0,D.useState)(null),[aa,ea]=(0,D.useState)([]),[ta,ia]=(0,D.useState)([]),[Da,na]=(0,D.useState)([]),[sa,la]=(0,D.useState)(""),[ya,Ta]=(0,D.useState)(!1),[oa,ra]=(0,D.useState)("dd-MM-yyyy"),[da,ha]=(0,D.useState)("Client"),[fa,ga]=(0,D.useState)(null),[Oa,ma]=(0,D.useState)(null),[ka,ua]=(0,D.useState)(""),[Wa,Sa]=(0,D.useState)(""),[ca,xa]=(0,D.useState)(""),[Ia,Ra]=(0,D.useState)(""),[Ma,Na]=(0,D.useState)("Asia/Kolkata"),[Fa,Ea]=(0,D.useState)({}),[pa,Ua]=(0,D.useState)(!1),[Ba,Aa]=(0,D.useState)(""),[va,Ca]=(0,D.useState)("success"),[ja,ba]=(0,D.useState)(0),[za,wa]=(0,D.useState)(15),[Pa,La]=(0,D.useState)(0),[qa,Za]=(0,D.useState)({page:0,pageSize:15}),[Ha,Ka]=(0,D.useState)(!1),[Ga,Ya]=(0,D.useState)("Free Trail"),[Va,Ja]=(0,D.useState)(""),[$a,Xa]=(0,D.useState)(""),_a=()=>{Ua(!1)},[Qa,ae]=(0,D.useState)(null),[ee,te]=(0,D.useState)(!0),[ie,De]=(0,D.useState)(!0),ne=a=>(0,m.jsxs)("div",{children:[(0,m.jsx)(T.A,{control:(0,m.jsx)(o.A,{title:"Mail",children:(0,m.jsx)(r.A,{className:"qadpt-grdicon","aria-label":"mail",onClick:()=>{}})}),label:""}),(0,m.jsx)(T.A,{control:(0,m.jsx)(o.A,{title:"Edit Organization",children:(0,m.jsx)(r.A,{className:"qadpt-grdicon","aria-label":"edit",onClick:()=>{return e=a.organizationid,ce(!0),void B(e);var e},children:(0,m.jsx)(h.A,{})})}),label:""}),(0,m.jsx)(T.A,{control:(0,m.jsx)(o.A,{title:"Edit Subscirption",children:(0,m.jsx)(r.A,{className:"qadpt-grdicon","aria-label":"edit",onClick:()=>ve(a),children:(0,m.jsx)(z.A,{})})}),label:""}),(0,m.jsx)(T.A,{control:(0,m.jsx)(n.A,{checked:Fa[a.organizationid]||!1,onChange:()=>{return e=a.organizationid,t=Fa[a.organizationid],pe(!1),Fe(!1),p(e),ae(!t),void(t?Fe(!0):pe(!0));var e,t},color:"primary"}),label:""})]}),se=a=>{g(a.join(" "))},le=[{field:"Name",headerName:"Organization Name",width:200,flex:1,filterable:!1},{field:"Plan",headerName:"Organization Plan",width:200,flex:1,filterable:!1},{field:"CreatedDate",headerName:"Created Date",width:200,flex:1,filterable:!1,sortable:!0,disableColumnMenu:!0,renderCell:a=>{const e=a.row.CreatedDate,t=a.row.DateFormat;return(0,W.r)(e,t)}},{field:"Type",headerName:"Type",width:200,flex:1,filterable:!1},{field:"actions",headerName:"Actions",sortable:!1,disableColumnMenu:!0,width:300,flex:1,renderCell:a=>{const e=a.row.OrganizationId||!1;X(e);const t=a.row.CreatedDate||!1;return Xa(t),(0,m.jsx)("div",{children:(0,m.jsx)(ne,{organizationid:e,index:a.row.id})})}}],[ye,Te]=(0,D.useState)("option1"),[oe,re]=(0,D.useState)([]),[de,he]=(0,D.useState)([{field:"Name",sort:"asc"}]),[fe,ge]=(0,D.useState)({items:[]}),Oe=a=>{I(a),Be(a)};(0,D.useEffect)((()=>{Ha?Ka(!1):(async()=>{const a=qa.pageSize||10,e=qa.page*a,t=qa.pageSize;ba(e),wa(t),Da.length>0?await(0,u.h6)(I,M,e,t,La,de,Da):await(0,u.h6)(Oe,M,e,t,La,de,Da)})()}),[qa]),(0,D.useEffect)((()=>{(async()=>{const a=qa.pageSize||10,e=qa.page*a,t=qa.pageSize;ba(e),wa(t),Da.length>0?await(0,u.h6)(I,M,e,t,La,de,Da):await(0,u.h6)(Oe,M,e,t,La,de,Da)})()}),[de]),(0,D.useEffect)((()=>{var a;a=Da.length>0,Za((a=>(0,i.A)((0,i.A)({},a),{},{page:0}))),Ka(a)}),[Da]);const[me,ke]=(0,D.useState)([]);(0,D.useEffect)((()=>{(async()=>{try{await(0,u.fV)(ke,M)}catch(a){}finally{M(!1)}})()}),[]);x.filter((a=>{const e=a.EmailId||"",i=a.ContactNumber||"",D=a.UserName||"";return e.toLowerCase().includes(t.toLowerCase())||i.toLowerCase().includes(t.toLowerCase())||D.toLowerCase().includes(t.toLowerCase())}));(0,D.useEffect)((()=>{Ea(x.reduce(((a,e)=>(a[e.OrganizationId]=e.IsActive,a)),{}))}),[x]);const[ue,We]=(0,D.useState)(!1),[Se,ce]=(0,D.useState)(!1),[xe,Ie]=(0,D.useState)(!1),[Re,Me]=(0,D.useState)(!1),[Ne,Fe]=(0,D.useState)(!1),[Ee,pe]=(0,D.useState)(!1),[Ue,Be]=(0,D.useState)([]),Ae=(0,s.Zp)(),ve=a=>{B(a.organizationid),Ie(!0)},Ce=(a,e)=>{Ea((t=>(0,i.A)((0,i.A)({},t),{},{[a]:e})))};let je="";const be=a=>{const{name:e,value:t,checked:i}=a.target;switch(e){case"Name":P(t),we(t);break;case"isRTL":Ta(i);break;case"Type":ha(t);break;case"Plan Type":Ya(t)}},ze=a=>{const e=new Date;let t="";switch(a){case"yyyy-MM-dd":default:t=e.toISOString().split("T")[0];break;case"dd-MM-yyyy":t="".concat(String(e.getDate()).padStart(2,"0"),"-").concat(String(e.getMonth()+1).padStart(2,"0"),"-").concat(e.getFullYear());break;case"MM-dd-yyyy":t="".concat(String(e.getMonth()+1).padStart(2,"0"),"-").concat(String(e.getDate()).padStart(2,"0"),"-").concat(e.getFullYear());break;case"dd/mm/yyyy":t="".concat(String(e.getDate()).padStart(2,"0"),"/").concat(String(e.getMonth()+1).padStart(2,"0"),"/").concat(e.getFullYear());break;case"mm/dd/yyyy":t="".concat(String(e.getMonth()+1).padStart(2,"0"),"/").concat(String(e.getDate()).padStart(2,"0"),"/").concat(e.getFullYear());break;case"yyyy/mm/dd":t="".concat(e.getFullYear(),"/").concat(String(e.getMonth()+1).padStart(2,"0"),"/").concat(String(e.getDate()).padStart(2,"0"))}xa(t)};(0,D.useEffect)((()=>{ze(oa)}),[]);const we=a=>{q("");const e=a.trim();if(""===e)return void q("Organization Name is required");if((e.length<5||e.length>50)&&!/^[a-zA-Z0-9 ]+$/.test(e))return void q("Organization Name must be between 5 and 50 characters.Special characters are not allowed");if(e.length<5||e.length>50)return void q("Organization Name must be between 5 and 50 characters");if(!/^[a-zA-Z0-9 ]+$/.test(e))return void q("Special characters are not allowed");me.some((a=>a.Name===e))?q("Organization Name already exists"):q("")},Pe=()=>{H(null),Me(!1),P(""),v(null),We(!1),ce(!1),Ie(!1),q(""),G("")},Le=async a=>{a.preventDefault(),q(""),Sa(""),Ra(""),G("");let e=!1;const t=w.trim();if(we(t),L&&(e=!0),oa||(Sa("Date format is required."),e=!0),da||(Ra("Type is required."),e=!0),Ga||(Ya("Plan is Requried"),e=!0),A?(G(""),je=A.name):(G("Logo is required"),e=!0),e)return;const i={Name:w,Logo:je,TimeZone:Ma,DateFormat:oa,Type:da,RTL:ya,Plan:Ga};(0,u.EC)(M,We,I,ce,i,Aa,Ca,Ua,(a=>{q(""),P(""),v(null),G("");const e="/superadmin/".concat(a,"/createadmin");setTimeout((()=>{Ae(e)}),3e3)}))},qe=async a=>{await(0,u.L_)(M,I,ce,a,Aa,Ca,Ua,(()=>{ce(!1),(0,u.h6)(I,M,ja,za,La,de,[]),(0,u.fV)(ke,M)})),Ie(!1),ce(!1)},[Ze,He]=(0,D.useState)(null),Ke=!L&&!Wa&&!Ia&&A;return(0,m.jsxs)("div",{children:[(0,m.jsxs)("div",{className:"qadpt-head",children:[(0,m.jsx)("div",{className:"qadpt-title-sec",children:(0,m.jsx)("div",{className:"qadpt-title",children:"Organization List"})}),(0,m.jsx)("div",{className:"qadpt-right-part",children:(0,m.jsxs)("button",{onClick:()=>{We(!0)},className:"qadpt-memberButton",children:[(0,m.jsx)("i",{className:"fal fa-add-plus"}),(0,m.jsx)("span",{children:"Create Organization"})]})}),(0,m.jsx)("div",{})]}),R?(0,m.jsx)("div",{className:"Loaderstyles",children:(0,m.jsx)("img",{src:f,alt:"Spinner",className:"LoaderSpinnerStyles"})}):(0,m.jsx)("div",{children:(0,m.jsx)(y.z,{className:"qadpt-org-grd",rows:x,columns:le,getRowId:a=>a.OrganizationId,paginationModel:qa,onPaginationModelChange:a=>{Za(a),M(!1)},pagination:!0,paginationMode:"server",rowCount:Pa,pageSizeOptions:[15,25,50,100],localeText:{MuiTablePagination:{labelRowsPerPage:"Records Per Page"}},disableColumnSelector:!0,loading:R,disableRowSelectionOnClick:!1,sortModel:de,onSortModelChange:a=>{he(a),M(!1)},slots:{columnMenu:a=>"Name"===a.colDef.field||"Type"===a.colDef.field?(0,m.jsx)(C,(0,i.A)((0,i.A)({column:a.colDef.field,setModels:I,setLoading:M,skip:ja,top:za,OrganizationId:$,sortModel:de,setTotalcount:La,orderByFields:sa,filters:Da,models:x,modelsData:me,setFilters:na},a),{},{optionsModel:Ue,options:me.map((e=>e[a.colDef.field]||"")),onSearch:se,hideMenu:a.hideMenu,paginationModel:qa})):null}})}),ue?(0,m.jsxs)("div",{className:"user-popup",children:[(0,m.jsxs)("div",{className:"qadpt-header",children:[(0,m.jsx)("span",{children:"Create Organization"}),(0,m.jsx)("svg",{onClick:()=>Pe(),className:"close-icon",xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",width:"24",height:"24",viewBox:"0 0 50 50",children:(0,m.jsx)("path",{d:"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"})})]}),(0,m.jsx)("div",{className:"qadpt-usrform",children:(0,m.jsxs)("form",{onSubmit:Le,children:[(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"organizationname",className:"qadpt-txtlabel",children:"Organization Name*"}),(0,m.jsx)("input",{className:"qadpt-txtinp ".concat(L?"error-input":""),type:"text",name:"Name",value:w,onChange:be}),L&&(0,m.jsx)("span",{className:"error",children:L})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"logo",className:"qadpt-txtlabel",children:" Logo*"}),(0,m.jsx)("input",{className:K?"error-input":"qadpt-txtinp",type:"file",name:"logo",accept:"image/*",onChange:a=>{if(a.target.files&&a.target.files[0]){const e=a.target.files[0];v(e),ma(URL.createObjectURL(e)),je=e.name,G("")}}}),K&&(0,m.jsx)("span",{className:"error",children:K})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"timezone",className:"qadpt-txtlabel",children:"Timezone*"}),(0,m.jsx)("select",{value:Ma,onChange:a=>{Na(a.target.value)},className:"qadpt-txtinp",children:O.map((a=>(0,m.jsx)("option",{value:a.Id,children:a.DisplayName},a.Id)))}),ka&&(0,m.jsx)("span",{className:"error",children:ka})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"dateFormat",className:"qadpt-txtlabel",children:"Date Format*"}),(0,m.jsxs)("select",{id:"dateFormat",name:"DateFormat",value:oa,onChange:a=>{const e=a.target.value;ra(e),ze(e)},className:Wa?"error-input":"qadpt-txtinp",children:[(0,m.jsx)("option",{value:"dd-MM-yyyy",children:"dd-MM-yyyy"}),(0,m.jsx)("option",{value:"MM-dd-yyyy",children:"MM-dd-yyyy"}),(0,m.jsx)("option",{value:"yyyy-MM-dd",children:"yyyy-MM-dd"}),(0,m.jsx)("option",{value:"dd/mm/yyyy",children:"dd/mm/yyyy"}),(0,m.jsx)("option",{value:"mm/dd/yyyy",children:"mm/dd/yyyy"}),(0,m.jsx)("option",{value:"yyyy/mm/dd",children:"yyyy/mm/dd"})]}),Wa&&(0,m.jsx)("span",{className:"error",children:Wa})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"type",className:"qadpt-txtlabel",children:"Type* "}),(0,m.jsxs)("select",{id:"type",name:"Type",value:da,onChange:be,className:Ia?"error-input":"qadpt-txtinp",children:[(0,m.jsx)("option",{value:"Client",children:"Client"}),(0,m.jsx)("option",{value:"Testing",children:"Testing"}),(0,m.jsx)("option",{value:"POC",children:"POC"}),(0,m.jsx)("option",{value:"Prospects",children:"Prospects"})]}),Ia&&(0,m.jsx)("span",{className:"error",children:Ia})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld",children:[(0,m.jsx)("label",{htmlFor:"type",className:"qadpt-txtlabel",children:"Plan "}),(0,m.jsxs)("select",{id:"plan",name:"Plan Type",value:Ga,onChange:be,className:Va?"error-input":"qadpt-txtinp",children:[(0,m.jsx)("option",{value:"Free Trail",children:"Free Trail"}),(0,m.jsx)("option",{value:"Basic",children:"Basic"}),(0,m.jsx)("option",{value:"Advanced",children:"Advanced"})]}),Va&&(0,m.jsx)("span",{className:"error",children:Va})]}),(0,m.jsxs)("div",{className:"qadpt-txtfld qadpt-switch",children:[(0,m.jsx)("span",{className:"qadpt-txtlabel",children:"RTL "}),(0,m.jsx)(T.A,{control:(0,m.jsx)(n.A,{id:"isRTL",name:"isRTL",checked:ya,onChange:be}),label:""})]}),(0,m.jsx)("div",{})]})}),(0,m.jsx)("div",{className:"qadpt-button",children:(0,m.jsx)("button",{onClick:Le,className:Ke?"qadpt-enab":"qadpt-disab",disabled:!Ke,children:"Save"})})]}):"",Se?(0,m.jsx)(k,{modelsData:me,setModelsData:ke,showEditPopup:Se,setShowEditPopup:ce,OrganizationId:U,sortModel:de,filters:Da,getOrganizations:u.h6,setModels:I,models:x,setLoading:M,handleClose:Pe,skip:ja,top:za,setTotalcount:La,updateOrganizationDetails:qe}):"",Ee?(0,m.jsx)("div",{className:"qadpt-modal-overlay",children:(0,m.jsxs)("div",{className:"qadpt-usrconfirm-popup qadpt-success",children:[(0,m.jsx)("div",{children:(0,m.jsx)("div",{className:"qadpt-icon",children:(0,m.jsx)(b.A,{})})}),(0,m.jsx)("div",{className:"qadpt-popup-title",children:"Activate Account"}),(0,m.jsx)("div",{className:"qadpt-warning",children:"Are you sure you want to Activate Organization"}),(0,m.jsxs)("div",{className:"qadpt-buttons",children:[(0,m.jsx)("button",{onClick:()=>Fe(!1),className:"qadpt-cancel-button",children:"Cancel"}),(0,m.jsx)("button",{onClick:async()=>{null!==E&&(await(0,u.xr)(E,ee,pe,I,M),Ce(E,!0),await(0,u.h6)(I,M,ja,za,La,de,Da),pe(!1),Aa("Organization Activated successfully"),Ca("success"),Ua(!0),setTimeout((()=>{Ua(!1)}),2e3))},className:"qadpt-conform-button",type:"submit",children:"Activate"})]})]})}):"",Ne?(0,m.jsx)("div",{className:"qadpt-modal-overlay",children:(0,m.jsxs)("div",{className:"qadpt-usrconfirm-popup qadpt-danger",children:[(0,m.jsx)("div",{children:(0,m.jsx)("div",{className:"qadpt-icon",children:(0,m.jsx)(j.A,{})})}),(0,m.jsx)("div",{className:"qadpt-popup-title",children:"Deactivate Account"}),(0,m.jsx)("div",{className:"qadpt-warning",children:"Are you sure you want to deactivate this organization? This action cannot be undone."}),(0,m.jsxs)("div",{className:"qadpt-buttons",children:[(0,m.jsx)("button",{onClick:()=>Fe(!1),className:"qadpt-cancel-button",children:"Cancel"}),(0,m.jsx)("button",{onClick:async()=>{null!==E&&(await(0,u._C)(E,ee,Fe,I,M),Ce(E,!1),await(0,u.h6)(I,M,ja,za,La,de,Da),Fe(!1),Aa("Organization Deactivated successfully"),Ca("success"),Ua(!0),setTimeout((()=>{Ua(!1)}),2e3))},className:"qadpt-conform-button",type:"submit",children:"Deactivate"})]})]})}):"",(0,m.jsx)(l.A,{open:pa,autoHideDuration:6e3,onClose:_a,anchorOrigin:{vertical:"top",horizontal:"center"},sx:{zIndex:1e4,marginTop:4},children:(0,m.jsx)(d.A,{onClose:_a,severity:va,sx:{width:"100%"},children:Ba})}),xe?(0,m.jsx)(J,{closeSubscribe:()=>{Ie(!1)},showSubscribe:xe,organizationId:U,setSnackbarOpen:Ua,setSnackbarSeverity:Ca,setSnackbarMessage:Aa,updateOrganizationDetails:qe}):""]})}},7225:(a,e,t)=>{var i=t(4994);e.A=void 0;var D=i(t(39)),n=t(579);e.A=(0,D.default)((0,n.jsx)("path",{d:"M12 7V3H2v18h20V7zm-2 12H4v-2h6zm0-4H4v-2h6zm0-4H4V9h6zm0-4H4V5h6zm10 12h-8V9h8zm-2-8h-4v2h4zm0 4h-4v2h4z"}),"CorporateFare")}}]);
//# sourceMappingURL=656.7f5f5ab2.chunk.js.map