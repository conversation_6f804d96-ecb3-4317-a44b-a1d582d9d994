"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[130],{130:(e,a,t)=>{t.r(a),t.d(a,{default:()=>I});var s=t(9379),d=t(5043),n=(t(6094),t(9155),t(6064),t(585),t(694)),i=t(2518),l=(t(5424),t(2143),t(8624)),r=t(7784),o=(t(4190),t(8390)),c=t(2102),u=t(7587),p=t(4076),m=t(5917),f=t(4709),g=t(9393),h=t(3230),y=t(446),S=t.n(y),v=t(9302),A=t(579);const C={UserSession:["Login","Logout"],Experiences:["TourCreated","TourUpdated","TourDeleted","TourPublished","AnnouncementCreated","AnnouncementUpdated","AnnouncementDeleted","TooltipCreated","TooltipUpdated","TooltipDeleted","BannerCreated","BannerUpdated","BannerDeleted","ChecklistCreated","ChecklistUpdated","ChecklistDeleted","SurveyCreated","SurveyUpdated","SurveyDeleted","HotspotCreated","HotspotUpdated","HotspotDeleted","HotspotPublished"],User:["NewUserCreated","UserUpdated","UserDeactivated","UserReactivated"],UserRole:["UserRoleAssigned","UserRoleUpdated","UserRoleDeleted"],Account:["AccountCreated","AccountUpdated","AccountDeleted"]},I=()=>{const[e,a]=(0,d.useState)([]),[t,y]=(0,d.useState)(),[I,x]=(0,d.useState)(!1),[N,b]=(0,d.useState)([]),[w,j]=(0,d.useState)(),[T,D]=(0,d.useState)(h.vW),[O,P]=(0,d.useState)([]),[U,z]=(0,d.useState)(""),[R,q]=(0,d.useState)(null),[B,M]=(0,d.useState)(null),[k,E]=(0,d.useState)([]),[L,H]=(0,d.useState)(""),[F,K]=(0,d.useState)(null),[V,Q]=(0,d.useState)([]),[W,$]=(0,d.useState)(""),[G,J]=(0,d.useState)(""),[X,Y]=(0,d.useState)(""),[Z,_]=(0,d.useState)(""),[ee,ae]=(0,d.useState)(""),[te,se]=(0,d.useState)(""),[de,ne]=(0,d.useState)(""),[ie,le]=(0,d.useState)([]),[re,oe]=(0,d.useState)(null),[ce,ue]=(0,d.useState)([]),[pe,me]=(0,d.useState)(null),[fe,ge]=(0,d.useState)(!1),[he,ye]=(0,d.useState)(""),[Se,ve]=(0,d.useState)([]),[Ae,Ce]=(0,d.useState)((0,u.Fl)()),[Ie,xe]=(0,d.useState)({page:0,pageSize:15}),[Ne,be]=(0,d.useState)(0),[we,je]=(0,d.useState)(10),[Te,De]=(0,d.useState)(0),Oe=e=>(0,m.A)(new Date(e),"dd-MM-yyyy HH:mm:ss");(0,d.useEffect)((()=>{E(B&&C[B]?C[B]:[]),q("")}),[B]),(0,d.useEffect)((()=>{(async()=>{const e=await(0,p.fV)(Q,x);if(e&&e.length>0){Q(e);const a=e[0];a&&(J(a),j(a.OrganizationId),Y(a.name))}})()}),[]),(0,d.useEffect)((()=>{(async()=>{const e=await(0,p.fV)(Q,x);if(e){Q(e);const a=e.find((e=>"Quixy"===e.Name));a&&$(a.OrganizationId)}})()}),[]),(0,d.useEffect)((()=>{if(w){(async()=>{await(0,g.PH)(_,x,w,0,15,De,"",[])})(),Me(pe,w),j(w)}}),[w]),(0,d.useEffect)((()=>{if(Array.isArray(Z)){const e=Z.map((e=>e.EmailId));ue(e)}else console.error("Models is not an array:",Z)}),[Z]);const Pe=S()(),Ue=Pe.subtract(2,"day"),[ze,Re]=(0,d.useState)(S()(Ue)),[qe,Be]=(0,d.useState)(S()(Pe).endOf("day"));(0,d.useEffect)((()=>{Me(pe,w)}),[Ie]);const Me=async(e,t)=>{if(t)try{x(!0);const s=Ie.pageSize||15,d=Ie.page*s,n=Ie.pageSize;be(d),je(n);const i=await(0,p.Oq)(d,n,t,he,Se,null===ze||void 0===ze?void 0:ze.toISOString(),null===qe||void 0===qe?void 0:qe.toISOString(),De,a,R,B,te,e);if(i&&Array.isArray(i)){const e=i.map((e=>({AuditLogId:e.AuditLogId,ReferenceId:e.ReferenceId,Name:e.Name,OrganizationId:e.OrganizationId,ReferenceType:e.ReferenceType,Type:e.Type,CreatedBy:e.CreatedBy,Browser:e.Browser,IPAddress:e.IPAddress,CreatedDate:Oe(e.CreatedDate)})));a(e)}else console.error("Invalid response format:",i)}catch(s){console.error("Error fetching audit logs:",s)}finally{x(!1)}else console.error("Organization ID is required to fetch data")};return(0,d.useEffect)((()=>{(0,u.B1)((()=>{Ce((0,u.Fl)())}))}),[]),(0,A.jsxs)("div",{children:[(0,A.jsxs)("div",{className:"qadpt-head",children:[(0,A.jsx)("div",{className:"qadpt-title-sec",children:(0,A.jsx)("div",{className:"qadpt-title",children:"Audit Logs"})}),(0,A.jsx)("div",{className:"qadpt-right-part",children:(0,A.jsx)(l.A,{options:V,getOptionLabel:e=>e.Name,value:w?V.find((e=>e.OrganizationId===w)):null,onChange:(e,a)=>{j((null===a||void 0===a?void 0:a.OrganizationId)||null)},renderInput:e=>(0,A.jsx)(r.A,(0,s.A)((0,s.A)({},e),{},{label:"Select Organization",InputProps:(0,s.A)((0,s.A)({},e.InputProps),{},{sx:{"& .MuiAutocomplete-popupIndicator":{top:"-1px"},"& .MuiAutocomplete-clearIndicator":{visibility:B?"visible":"hidden",top:"-1px"}}})}))})})]}),(0,A.jsx)("div",{className:"grid-toolbar-options",children:(0,A.jsxs)("div",{className:"left-options",children:[(0,A.jsxs)("div",{className:"drp-fields",children:[(0,A.jsx)(l.A,{className:"auto-filed",options:Object.keys(C),value:B,onChange:(e,a)=>M(a),renderInput:e=>(0,A.jsx)(r.A,(0,s.A)((0,s.A)({className:"qadpt-activty-category ".concat(B?"visible":"hidden")},e),{},{label:"Category",InputProps:(0,s.A)({},e.InputProps)}))}),(0,A.jsx)(l.A,{className:"auto-filed",options:k,value:R,onChange:(e,a)=>q(a),renderInput:e=>(0,A.jsx)(r.A,(0,s.A)((0,s.A)({className:"qadpt-activty-category ".concat(B?"visible":"hidden")},e),{},{label:"Event Type",InputProps:(0,s.A)({},e.InputProps)}))})]}),(0,A.jsx)("div",{className:"dt-fields",children:(0,A.jsxs)(o.$,{dateAdapter:v.R,children:[(0,A.jsx)(f.K,{className:"qadpt-DateTime ".concat(ze?"":"hide-close"),label:"From Date",views:["year","month","day"],value:ze,onChange:e=>Re(e?S()(e):null),maxDateTime:null!==qe&&void 0!==qe?qe:void 0,slotProps:{textField:{className:"qadpt-datepicker"},field:{clearable:!0}}}),(0,A.jsx)(f.K,{className:"qadpt-DateTime dt-fld2 ".concat(ze?"":"hide-close"),label:"To Date",views:["year","month","day"],value:qe,onChange:e=>Be(e?S()(e).endOf("day"):null),minDateTime:null!==ze&&void 0!==ze?ze:void 0,slotProps:{textField:{className:"qadpt-datepicker"},field:{clearable:!0}}})]})}),(0,A.jsx)(r.A,{className:"name-fld",variant:"outlined",label:"Name",value:L,onChange:e=>{H(e.target.value),se(e.target.value)}}),(0,A.jsxs)("div",{className:"right-options",children:[(0,A.jsx)(i.A,{variant:"outlined",color:"primary",onClick:()=>{ge(!0),z(""),q(""),M(""),H(""),se(""),Re(null),Be(null),K(""),xe({page:0,pageSize:15})},children:"Clear"}),(0,A.jsx)(i.A,{variant:"contained",color:"primary",onClick:()=>(async e=>{if(e)try{x(!0);const t=Ie.pageSize||15,s=Ie.page*t,d=Ie.pageSize;be(s),je(d);const n=await(0,p.Oq)(s,d,e,he,Se,null===ze||void 0===ze?void 0:ze.toISOString(),null===qe||void 0===qe?void 0:qe.toISOString(),De,a,R,B,L,ee);if(n&&Array.isArray(n)){const e=n.map((e=>({AuditLogId:e.AuditLogId,ReferenceId:e.ReferenceId,Name:e.Name,OrganizationId:e.OrganizationId,ReferenceType:e.ReferenceType,Type:e.Type,CreatedBy:e.CreatedBy,Browser:e.Browser,IPAddress:e.IPAddress,CreatedDate:Oe(e.CreatedDate)})));a(e)}else console.error("Invalid response format:",n)}catch(t){console.error("Error fetching audit logs:",t)}finally{x(!1)}else console.error("Organization ID is required to perform search")})(w),disabled:""===U&&(""===R||null===R)&&(""===B||null===B)&&null===ze&&null===qe&&""===F&&""===L,children:"Search"})]})]})}),I?(0,A.jsx)("div",{className:"Loaderstyles",children:(0,A.jsx)("img",{src:c,alt:"Spinner",className:"LoaderSpinnerStyles"})}):(0,A.jsx)(n.z,{className:"qadpt-setting-grd",rows:e,columns:[{field:"ReferenceType",headerName:"Category",width:350,flex:1,disableColumnMenu:!0},{field:"Type",headerName:"Event Type",width:350,flex:1,disableColumnMenu:!0},{field:"Name",headerName:"Name",width:350,flex:1,disableColumnMenu:!0},{field:"IPAddress",headerName:"IP Address",width:350,flex:1,disableColumnMenu:!0},{field:"Browser",headerName:"Browser",width:300,flex:1,disableColumnMenu:!0},{field:"CreatedBy",headerName:"Created By",width:300,flex:1,disableColumnMenu:!0},{field:"CreatedDate",headerName:"Created Date",width:400,flex:1,disableColumnMenu:!0}],getRowId:e=>e.AuditLogId,paginationModel:Ie,onPaginationModelChange:xe,pagination:!0,paginationMode:"server",rowCount:Te,pageSizeOptions:[15,25,50,100],localeText:{MuiTablePagination:{labelRowsPerPage:"Records Per Page"}},disableRowSelectionOnClick:!0,loading:I})]})}}}]);
//# sourceMappingURL=130.df5d0f15.chunk.js.map