import React, { useState } from 'react';
import { Box, Typography, IconButton, Container, Tabs, Tab } from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  TrendingUp,
  TrendingDown,
  People,
  CheckCircle,
  Star,
  Schedule,
  FilterList,
  CalendarToday
} from '@mui/icons-material';
import Card from '../common/Card';
import ModernButton from '../common/ModernButton';
import ChartPlaceholder from './ChartPlaceholder';

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeValue: string;
  trend: 'up' | 'down';
  icon: React.ReactNode;
  color: string;
}

const DashboardWrapper = styled('div')({
  backgroundColor: '#f6f9ff',
  minHeight: '100vh',
});

const HeaderSection = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: 'var(--spacing-4)',
});

const StyledTabs = styled(Tabs)({
  marginBottom: 'var(--spacing-4)',
  '& .MuiTabs-indicator': {
    backgroundColor: 'var(--color-primary-600)',
  },
  '& .MuiTab-root': {
    fontSize: 'var(--font-size-sm)',
    fontWeight: 'var(--font-weight-medium)',
    textTransform: 'none',
    color: 'var(--color-gray-600)',
    '&.Mui-selected': {
      color: 'var(--color-primary-600)',
      fontWeight: 'var(--font-weight-semibold)',
    },
  },
});

const FilterSection = styled(Box)({
  display: 'flex',
  gap: 'var(--spacing-3)',
  alignItems: 'center',
});

const MetricsGrid = styled(Box)({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
  gap: 'var(--spacing-4)',
  marginBottom: 'var(--spacing-6)',
});

const MetricCardContainer = styled(Card)({
  padding: 'var(--spacing-5)',
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-4)',
});

const MetricIcon = styled(Box)<{ color: string }>(({ color }) => ({
  width: '48px',
  height: '48px',
  borderRadius: 'var(--radius-lg)',
  backgroundColor: `${color}15`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  
  '& svg': {
    color: color,
    fontSize: '24px',
  },
}));

const MetricContent = styled(Box)({
  flex: 1,
});

const MetricTitle = styled(Typography)({
  fontSize: 'var(--font-size-sm)',
  color: 'var(--color-gray-600)',
  marginBottom: 'var(--spacing-1)',
});

const MetricValue = styled(Typography)({
  fontSize: 'var(--font-size-2xl)',
  fontWeight: 'var(--font-weight-bold)',
  color: 'var(--color-gray-900)',
  marginBottom: 'var(--spacing-1)',
});

const MetricChange = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
});

const ChangeIndicator = styled(Box)<{ trend: 'up' | 'down' }>(({ trend }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
  fontSize: 'var(--font-size-xs)',
  fontWeight: 'var(--font-weight-medium)',
  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',
  
  '& svg': {
    fontSize: '16px',
  },
}));

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeValue,
  trend,
  icon,
  color,
}) => {
  return (
    <MetricCardContainer shadow="sm" hover>
      <MetricIcon color={color}>
        {icon}
      </MetricIcon>
      <MetricContent>
        <MetricTitle>{title}</MetricTitle>
        <MetricValue>{value}</MetricValue>
        <MetricChange>
          <ChangeIndicator trend={trend}>
            {trend === 'up' ? <TrendingUp /> : <TrendingDown />}
            {change}
          </ChangeIndicator>
          <Typography variant="caption" color="text.secondary">
            {changeValue}
          </Typography>
        </MetricChange>
      </MetricContent>
    </MetricCardContainer>
  );
};

const ModernDashboard: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedGuideType, setSelectedGuideType] = useState<string | null>(null);
  const [selectedGuideFromList, setSelectedGuideFromList] = useState<string | null>(null);
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    x: number;
    y: number;
    content: string;
    title: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    content: '',
    title: ''
  });

  // Mock data for guide types and guides
  const guideTypes = [
    { id: 'tours', name: 'Tours', count: 8, completionRate: 85, color: '#8b5cf6' },
    { id: 'banners', name: 'Banners', count: 12, completionRate: 92, color: '#06b6d4' },
    { id: 'announcements', name: 'Announcements', count: 6, completionRate: 78, color: '#f59e0b' },
    { id: 'tooltips', name: 'Tooltips', count: 15, completionRate: 88, color: '#10b981' },
    { id: 'announcement', name: 'Announcement', count: 4, completionRate: 73, color: '#ef4444' },
    { id: 'hotspots', name: 'Hotspots', count: 9, completionRate: 81, color: '#f97316' }
  ];

  const guidesByType: Record<string, any[]> = {
    tours: [
      { id: 'tour-1', name: 'Product Onboarding', views: 1400, completed: 1247, dropOff: 11, status: 'excellent', lastUpdated: '2 days ago' },
      { id: 'tour-2', name: 'Feature Discovery', views: 1174, completed: 892, dropOff: 24, status: 'good', lastUpdated: '1 day ago' },
      { id: 'tour-3', name: 'Advanced Settings', views: 962, completed: 634, dropOff: 32, status: 'needs attention', lastUpdated: '5 days ago' }
    ],
    banners: [
      { id: 'banner-1', name: 'Welcome Banner', views: 2100, completed: 1932, dropOff: 8, status: 'excellent', lastUpdated: '1 day ago' },
      { id: 'banner-2', name: 'Feature Announcement', views: 1850, completed: 1665, dropOff: 10, status: 'excellent', lastUpdated: '3 days ago' }
    ],
    announcements: [
      { id: 'announcement-1', name: 'New Feature Release', views: 980, completed: 764, dropOff: 22, status: 'good', lastUpdated: '1 week ago' },
      { id: 'announcement-2', name: 'Maintenance Notice', views: 1200, completed: 936, dropOff: 22, status: 'good', lastUpdated: '2 days ago' }
    ],
    tooltips: [
      { id: 'tooltip-1', name: 'Button Helper', views: 3200, completed: 2816, dropOff: 12, status: 'excellent', lastUpdated: '1 day ago' },
      { id: 'tooltip-2', name: 'Form Guidance', views: 2800, completed: 2464, dropOff: 12, status: 'excellent', lastUpdated: '2 days ago' }
    ],
    announcement: [
      { id: 'modal-1', name: 'Confirmation Dialog', views: 1500, completed: 1095, dropOff: 27, status: 'needs attention', lastUpdated: '4 days ago' }
    ],
    hotspots: [
      { id: 'hotspot-1', name: 'Navigation Helper', views: 1100, completed: 891, dropOff: 19, status: 'good', lastUpdated: '3 days ago' }
    ]
  };

  const showTooltip = (event: React.MouseEvent, title: string, content: string) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;

    setTooltip({
      visible: true,
      x: rect.left + scrollX + rect.width / 2,
      y: rect.top + scrollY - 10,
      content,
      title
    });
  };

  const hideTooltip = () => {
    setTooltip(prev => ({ ...prev, visible: false }));
  };

  const overviewMetrics = [
    {
      title: 'Completion Rate',
      value: '87.3%',
      change: '****%',
      changeValue: '+2.8pp',
      trend: 'up' as const,
      icon: <CheckCircle />,
      color: 'var(--color-success-600)',
    },
    {
      title: 'User Satisfaction',
      value: '4.6',
      change: '+0.2',
      changeValue: 'out of 5.0',
      trend: 'up' as const,
      icon: <Star />,
      color: 'var(--color-warning-600)',
    },
    {
      title: 'Hours Saved',
      value: '2,847',
      change: '+18.7%',
      changeValue: '+447h',
      trend: 'up' as const,
      icon: <Schedule />,
      color: 'var(--color-primary-600)',
    },
  ];

  const analyticsMetrics = [
    {
      title: 'Active Users',
      value: '12,847',
      change: '+12.5%',
      changeValue: '+1,432',
      trend: 'up' as const,
      icon: <People />,
      color: 'var(--color-primary-600)',
    },
    {
      title: 'Completion Rate',
      value: '87.3%',
      change: '****%',
      changeValue: '+2.8pp',
      trend: 'up' as const,
      icon: <CheckCircle />,
      color: 'var(--color-success-600)',
    },
    {
      title: 'User Satisfaction',
      value: '4.6',
      change: '+0.2',
      changeValue: 'out of 5.0',
      trend: 'up' as const,
      icon: <Star />,
      color: 'var(--color-warning-600)',
    },
    {
      title: 'Hours Saved',
      value: '2,847',
      change: '+18.7%',
      changeValue: '+447h',
      trend: 'up' as const,
      icon: <Schedule />,
      color: 'var(--color-primary-600)',
    },
  ];

  const aiPerformanceMetrics = [
    {
      title: 'AI Response Time',
      value: '1.2s',
      change: '-15%',
      changeValue: 'faster',
      trend: 'up' as const,
      icon: <Schedule />,
      color: 'var(--color-success-600)',
    },
    {
      title: 'AI Accuracy',
      value: '94.8%',
      change: '+2.1%',
      changeValue: 'improved',
      trend: 'up' as const,
      icon: <CheckCircle />,
      color: 'var(--color-primary-600)',
    },
    {
      title: 'Model Confidence',
      value: '89.3%',
      change: '+1.8%',
      changeValue: 'higher',
      trend: 'up' as const,
      icon: <Star />,
      color: 'var(--color-warning-600)',
    },
    {
      title: 'Processing Load',
      value: '67%',
      change: '+5%',
      changeValue: 'capacity',
      trend: 'up' as const,
      icon: <People />,
      color: 'var(--color-error-500)',
    },
  ];

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0: // Overview
        return (
          <>
            {/* Header with Filters */}
            <HeaderSection>
              <Box>
                <Typography variant="h4" fontWeight="bold" color="text.primary">
                  Dashboard Overview
                </Typography>
              </Box>
              <FilterSection>
                <ModernButton
                  variant="outline"
                  startIcon={<FilterList />}
                  size="sm"
                >
                  Filter
                </ModernButton>
                <ModernButton
                  variant="outline"
                  startIcon={<CalendarToday />}
                  size="sm"
                >
                  Last 30 days
                </ModernButton>
              </FilterSection>
            </HeaderSection>

            {/* Overview Metrics Grid (4 cards like Analytics) */}
            <MetricsGrid>
              {analyticsMetrics.map((metric, index) => (
                <MetricCard key={index} {...metric} />
              ))}
            </MetricsGrid>

            {/* Overview Charts - Growth Trends and User Satisfaction */}
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
              {/* User Activity Trends Chart */}
              <Card title="📈 User Activity Trends" subtitle="Active, retained, and total users over time" padding="lg">
                <Box sx={{ height: '300px', position: 'relative', backgroundColor: 'white', borderRadius: 'var(--radius-md)' }}>

                  

                  {/* Multi-line Chart */}
                  <svg width="100%" height="280" viewBox="0 0 450 250" style={{ overflow: 'visible' }}>
                    {/* Grid lines */}
                    <defs>
                      <pattern id="activityGrid" width="200" height="50" patternUnits="userSpaceOnUse">
                        <path d="M 200 0 L 0 0 0 50" fill="none" stroke="#f1f5f9" strokeWidth="1"/>
                      </pattern>
                    </defs>
                    <rect width="100%" height="200" fill="url(#activityGrid)" />

                    {/* Y-axis */}
                    <line x1="50" y1="20" x2="50" y2="200" stroke="#e2e8f0" strokeWidth="1"/>
                    {/* X-axis */}
                    <line x1="50" y1="200" x2="400" y2="200" stroke="#e2e8f0" strokeWidth="1"/>

                    {/* Y-axis labels */}
                    <text x="40" y="25" fontSize="11" fill="#64748b" textAnchor="end">60000</text>
                    <text x="40" y="65" fontSize="11" fill="#64748b" textAnchor="end">45000</text>
                    <text x="40" y="105" fontSize="11" fill="#64748b" textAnchor="end">30000</text>
                    <text x="40" y="145" fontSize="11" fill="#64748b" textAnchor="end">15000</text>
                    <text x="40" y="205" fontSize="11" fill="#64748b" textAnchor="end">0</text>

                    {/* X-axis labels */}
                    <text x="125" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Week 1</text>
                    <text x="325" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Week 2</text>

                    {/* Total Users Line (Purple) */}
                    <path d="M 125 50 L 325 45" fill="none" stroke="#8b5cf6" strokeWidth="3" strokeLinecap="round"/>
                    <circle cx="125" cy="50" r="5" fill="#8b5cf6"/>
                    <circle cx="325" cy="45" r="5" fill="#8b5cf6"/>

                    {/* Active Users Line (Blue) */}
                    <path d="M 125 80 L 325 75" fill="none" stroke="#3b82f6" strokeWidth="3" strokeLinecap="round"/>
                    <circle cx="125" cy="80" r="5" fill="#3b82f6"/>
                    <circle cx="325" cy="75" r="5" fill="#3b82f6"/>

                    {/* Retained Users Line (Green) */}
                    <path d="M 125 110 L 325 105" fill="none" stroke="#10b981" strokeWidth="3" strokeLinecap="round"/>
                    <circle cx="125" cy="110" r="5" fill="#10b981"/>
                    <circle cx="325" cy="105" r="5" fill="#10b981"/>

                    {/* Data point values */}
                    <text x="125" y="40" fontSize="10" fill="#8b5cf6" textAnchor="middle" fontWeight="600">36000</text>
                    <text x="325" y="35" fontSize="10" fill="#8b5cf6" textAnchor="middle" fontWeight="600">37000</text>

                    <text x="125" y="70" fontSize="10" fill="#3b82f6" textAnchor="middle" fontWeight="600">30000</text>
                    <text x="325" y="65" fontSize="10" fill="#3b82f6" textAnchor="middle" fontWeight="600">31000</text>

                    <text x="125" y="100" fontSize="10" fill="#10b981" textAnchor="middle" fontWeight="600">24000</text>
                    <text x="325" y="95" fontSize="10" fill="#10b981" textAnchor="middle" fontWeight="600">25000</text>
                  </svg>

                  {/* Legend */}
                  <Box sx={{
                    position: 'absolute',
                    bottom: 10,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    display: 'flex',
                    gap: 3,
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Active</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Retained</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Total</Typography>
                    </Box>
                  </Box>
                </Box>
              </Card>

              {/* Feature Adoption Distribution Chart */}
              <Card title="� Feature Adoption Distribution" subtitle="Interactive feature usage metrics" padding="lg">
                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', borderRadius: 'var(--radius-md)', position: 'relative' }}>
                  {/* Donut Chart */}
                  <svg width="180" height="180" viewBox="0 0 180 180" style={{ marginBottom: '20px' }}>
                    {/* Tours - 25% - Blue */}
                    <circle
                      cx="90" cy="90" r="60" fill="none" stroke="#3b82f6" strokeWidth="25"
                      strokeDasharray="94 377" strokeDashoffset="0" transform="rotate(-90 90 90)"
                    />

                    {/* Banners - 18% - Orange */}
                    <circle
                      cx="90" cy="90" r="60" fill="none" stroke="#f97316" strokeWidth="25"
                      strokeDasharray="68 377" strokeDashoffset="-94" transform="rotate(-90 90 90)"
                    />

                    {/* Hotspots - 12% - Teal */}
                    <circle
                      cx="90" cy="90" r="60" fill="none" stroke="#14b8a6" strokeWidth="25"
                      strokeDasharray="45 377" strokeDashoffset="-162" transform="rotate(-90 90 90)"
                    />

                    {/* Tooltips - 20% - Green */}
                    <circle
                      cx="90" cy="90" r="60" fill="none" stroke="#10b981" strokeWidth="25"
                      strokeDasharray="75 377" strokeDashoffset="-207" transform="rotate(-90 90 90)"
                    />

                    {/* AI Assistant - 19% - Purple */}
                    <circle
                      cx="90" cy="90" r="60" fill="none" stroke="#8b5cf6" strokeWidth="25"
                      strokeDasharray="71 377" strokeDashoffset="-282" transform="rotate(-90 90 90)"
                    />

                    {/* Checklists - 6% - Red */}
                    <circle
                      cx="90" cy="90" r="60" fill="none" stroke="#ef4444" strokeWidth="25"
                      strokeDasharray="23 377" strokeDashoffset="-353" transform="rotate(-90 90 90)"
                    />

                    {/* Center circle for donut effect with 100% Total */}
                    <circle cx="90" cy="90" r="35" fill="white"/>
                    <text x="90" y="85" fontSize="16" fill="#1f2937" textAnchor="middle" fontWeight="bold">100%</text>
                    <text x="90" y="100" fontSize="11" fill="#6b7280" textAnchor="middle">Total</text>
                  </svg>

                  {/* Legend */}
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(3, 1fr)',
                    gap: 2,
                    width: '100%',
                    maxWidth: '400px'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />
                        <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Tours</Typography>
                      </Box>
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>25%</Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />
                        <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Tooltips</Typography>
                      </Box>
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>20%</Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />
                        <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Announcement</Typography>
                      </Box>
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>19%</Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ width: 12, height: 12, backgroundColor: '#f97316', borderRadius: '50%' }} />
                        <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Banners</Typography>
                      </Box>
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>18%</Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ width: 12, height: 12, backgroundColor: '#14b8a6', borderRadius: '50%' }} />
                        <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Hotspots</Typography>
                      </Box>
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>12%</Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ width: 12, height: 12, backgroundColor: '#ef4444', borderRadius: '50%' }} />
                        <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Checklists</Typography>
                      </Box>
                      <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>6%</Typography>
                    </Box>
                  </Box>
                </Box>
              </Card>
            </Box>

            {/* Quick Actions and Recent Activity - Commented out for now */}

            {/* User Feedback & Satisfaction Section */}
            {/* User Satisfaction Ratings and Satisfaction Trend */}
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
              {/* User Satisfaction Ratings */}
              <Card title="⭐ User Satisfaction Ratings" padding="lg">
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                  {/* Excellent (5★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Excellent (5★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '65%',
                        height: '100%',
                        backgroundColor: '#10b981',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      1,247
                    </Typography>
                  </Box>

                  {/* Good (4★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Good (4★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '45%',
                        height: '100%',
                        backgroundColor: '#84cc16',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      892
                    </Typography>
                  </Box>

                  {/* Average (3★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Average (3★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '22%',
                        height: '100%',
                        backgroundColor: '#f59e0b',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      434
                    </Typography>
                  </Box>

                  {/* Poor (2★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Poor (2★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '8%',
                        height: '100%',
                        backgroundColor: '#f97316',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      123
                    </Typography>
                  </Box>

                  {/* Very Poor (1★) */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                      <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />
                      <Typography variant="body2" fontWeight="medium">
                        Very Poor (1★)
                      </Typography>
                    </Box>
                    <Box sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden',
                      mr: 2
                    }}>
                      <Box sx={{
                        width: '4%',
                        height: '100%',
                        backgroundColor: '#ef4444',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                    <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                      57
                    </Typography>
                  </Box>

                  {/* Summary Cards */}
                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>
                    <Box sx={{
                      p: 2,
                      backgroundColor: '#f0fdf4',
                      borderRadius: 'var(--radius-md)',
                      textAlign: 'center'
                    }}>
                      <Typography variant="h5" fontWeight="bold" color="#16a34a">
                        77%
                      </Typography>
                      <Typography variant="caption" color="#16a34a">
                        Positive
                      </Typography>
                    </Box>
                    <Box sx={{
                      p: 2,
                      backgroundColor: '#fffbeb',
                      borderRadius: 'var(--radius-md)',
                      textAlign: 'center'
                    }}>
                      <Typography variant="h5" fontWeight="bold" color="#d97706">
                        16%
                      </Typography>
                      <Typography variant="caption" color="#d97706">
                        Neutral
                      </Typography>
                    </Box>
                    <Box sx={{
                      p: 2,
                      backgroundColor: '#fef2f2',
                      borderRadius: 'var(--radius-md)',
                      textAlign: 'center'
                    }}>
                      <Typography variant="h5" fontWeight="bold" color="#dc2626">
                        7%
                      </Typography>
                      <Typography variant="caption" color="#dc2626">
                        Negative
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Card>

              {/* Satisfaction Trend */}
              <Card title="📈 Satisfaction Trend" padding="lg">
                <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8fafc', borderRadius: 'var(--radius-md)', position: 'relative' }}>
                  {/* Satisfaction Trend Line Chart */}
                  <svg width="100%" height="250" viewBox="0 0 400 200" style={{ overflow: 'visible' }}>
                    {/* Grid lines */}
                    <defs>
                      <pattern id="feedbackGrid" width="66.67" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 66.67 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" strokeWidth="0.5"/>
                      </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#feedbackGrid)" />

                    {/* Y-axis labels */}
                    <text x="10" y="20" fontSize="10" fill="#64748b">5</text>
                    <text x="10" y="60" fontSize="10" fill="#64748b">4.75</text>
                    <text x="10" y="100" fontSize="10" fill="#64748b">4.5</text>
                    <text x="10" y="140" fontSize="10" fill="#64748b">4.25</text>
                    <text x="10" y="180" fontSize="10" fill="#64748b">4</text>

                    {/* X-axis labels */}
                    <text x="50" y="215" fontSize="10" fill="#64748b">Jan</text>
                    <text x="110" y="215" fontSize="10" fill="#64748b">Feb</text>
                    <text x="170" y="215" fontSize="10" fill="#64748b">Mar</text>
                    <text x="230" y="215" fontSize="10" fill="#64748b">Apr</text>
                    <text x="290" y="215" fontSize="10" fill="#64748b">May</text>
                    <text x="350" y="215" fontSize="10" fill="#64748b">Jun</text>

                    {/* Line showing satisfaction trend from 4.2 to 4.7 */}
                    <path d="M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88" fill="none" stroke="#3b82f6" strokeWidth="3"/>

                    {/* Data points */}
                    <circle cx="50" cy="168" r="4" fill="#3b82f6"/>
                    <circle cx="110" cy="152" r="4" fill="#3b82f6"/>
                    <circle cx="170" cy="136" r="4" fill="#3b82f6"/>
                    <circle cx="230" cy="120" r="4" fill="#3b82f6"/>
                    <circle cx="290" cy="104" r="4" fill="#3b82f6"/>
                    <circle cx="350" cy="88" r="4" fill="#3b82f6"/>

                    {/* Value labels on data points */}
                    <text x="50" y="160" fontSize="9" fill="#3b82f6" textAnchor="middle">4.2</text>
                    <text x="110" y="144" fontSize="9" fill="#3b82f6" textAnchor="middle">4.3</text>
                    <text x="170" y="128" fontSize="9" fill="#3b82f6" textAnchor="middle">4.4</text>
                    <text x="230" y="112" fontSize="9" fill="#3b82f6" textAnchor="middle">4.5</text>
                    <text x="290" y="96" fontSize="9" fill="#3b82f6" textAnchor="middle">4.6</text>
                    <text x="350" y="80" fontSize="9" fill="#3b82f6" textAnchor="middle">4.7</text>
                  </svg>
                </Box>
              </Card>
            </Box>

            {/* Feedback Summary */}
            <Card title="📊 Feedback Summary" padding="lg">
              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 'var(--spacing-6)' }}>
                {/* Total Feedback */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f8fafc',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1 }}>
                    2,238
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Feedback
                  </Typography>
                </Box>

                {/* Positive Sentiment */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f0fdf4',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h3" fontWeight="bold" color="#16a34a" sx={{ mb: 1 }}>
                    85.8%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Positive Sentiment
                  </Typography>
                </Box>

                {/* Average Rating */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#eff6ff',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h3" fontWeight="bold" color="#2563eb" sx={{ mb: 1 }}>
                    4.6/5
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average Rating
                  </Typography>
                </Box>

                {/* Growth vs Last Month */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#fdf4ff',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h3" fontWeight="bold" color="#9333ea" sx={{ mb: 1 }}>
                    +12%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    vs Last Month
                  </Typography>
                </Box>
              </Box>
            </Card>

            {/* Recent Feedback - Commented out for now */}
          </>
        );

      case 1: // Analytics
        return (
          <>
            {/* Level 1 - Guide Type Cards */}
            <Card title="Analytics Overview" subtitle="Select a guide type to view detailed analytics" padding="lg">
              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: 'var(--spacing-4)', mb: 4 }}>
                {guideTypes.map((guideType) => (
                  <Box
                    key={guideType.id}
                    onClick={() => {
                      setSelectedGuideType(guideType.id);
                      setSelectedGuideFromList(null); // Clear guide selection when changing type
                    }}
                    sx={{
                      p: 3,
                      border: selectedGuideType === guideType.id ? `2px solid ${guideType.color}` : '1px solid var(--color-gray-200)',
                      borderRadius: 'var(--radius-md)',
                      backgroundColor: selectedGuideType === guideType.id ? `${guideType.color}08` : 'white',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        backgroundColor: selectedGuideType === guideType.id ? `${guideType.color}12` : 'var(--color-gray-50)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="h6" fontWeight="semibold" sx={{ color: selectedGuideType === guideType.id ? guideType.color : 'var(--color-gray-900)' }}>
                        {guideType.name}
                      </Typography>
                      <Box sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: guideType.color
                      }} />
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        {guideType.count} guides
                      </Typography>
                      <Typography variant="h5" fontWeight="bold" sx={{ color: guideType.color }}>
                        {guideType.completionRate}%
                      </Typography>
                    </Box>
                    <Box sx={{
                      width: '100%',
                      height: 6,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: `${guideType.completionRate}%`,
                        height: '100%',
                        backgroundColor: guideType.color,
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                ))}
              </Box>
            </Card>

            {/* Level 2 & 3 - Top Guides Performance and Interactive Guide Funnel */}
            {selectedGuideType && (
              <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)' }}>
                {/* Left Column - Top Guides Performance */}
                <Card title="📊 Top Guides Performance" subtitle="Comprehensive guide analytics with completion and engagement metrics" padding="lg">
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                    {guidesByType[selectedGuideType]?.map((guide, index) => {
                      const completionRate = Math.round((guide.completed / guide.views) * 100);
                      const guideColor = guideTypes.find(gt => gt.id === selectedGuideType)?.color || '#8b5cf6';

                      return (
                        <Box
                          key={guide.id}
                          onClick={() => setSelectedGuideFromList(guide.id)}
                          sx={{
                            p: 3,
                            border: selectedGuideFromList === guide.id ? `2px solid ${guideColor}` : '1px solid var(--color-gray-200)',
                            borderRadius: 'var(--radius-lg)',
                            backgroundColor: selectedGuideFromList === guide.id ? `${guideColor}08` : 'white',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              backgroundColor: selectedGuideFromList === guide.id ? `${guideColor}12` : 'var(--color-gray-50)',
                              transform: 'translateY(-2px)',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                            }
                          }}
                        >
                          {/* Guide Header */}
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                backgroundColor: guideColor
                              }} />
                              <Typography variant="h6" fontWeight="semibold">
                                {guide.name}
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="h5" fontWeight="bold" sx={{ color: guideColor }}>
                                {completionRate}%
                              </Typography>
                              <Box sx={{
                                px: 1.5,
                                py: 0.5,
                                backgroundColor: guide.status === 'excellent' ? '#e8f5e9' : guide.status === 'good' ? '#e3f2fd' : '#fff3e0',
                                color: guide.status === 'excellent' ? '#2e7d32' : guide.status === 'good' ? '#1976d2' : '#f57c00',
                                borderRadius: 'var(--radius-sm)',
                                fontSize: '11px',
                                fontWeight: 'medium'
                              }}>
                                {guide.status}
                              </Box>
                            </Box>
                          </Box>

                          {/* Progress Bar */}
                          <Box sx={{ mb: 2 }}>
                            <Box sx={{
                              width: '100%',
                              height: 8,
                              backgroundColor: 'var(--color-gray-200)',
                              borderRadius: 'var(--radius-full)',
                              overflow: 'hidden'
                            }}>
                              <Box sx={{
                                width: `${completionRate}%`,
                                height: '100%',
                                backgroundColor: guideColor,
                                borderRadius: 'var(--radius-full)'
                              }} />
                            </Box>
                          </Box>

                          {/* Metrics Row */}
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h6" fontWeight="bold" sx={{ color: '#3b82f6' }}>
                                {guide.views.toLocaleString()}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                👁 Views
                              </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h6" fontWeight="bold" sx={{ color: '#10b981' }}>
                                {guide.completed.toLocaleString()}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                ✅ Completed
                              </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h6" fontWeight="bold" sx={{
                                color: guide.dropOff < 15 ? '#10b981' : guide.dropOff < 25 ? '#f59e0b' : '#ef4444'
                              }}>
                                {guide.dropOff}%
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                📉 Drop-off
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      );
                    })}
                  </Box>
                </Card>

                {/* Right Column - Interactive Guide Funnel */}
                <Card title="🔄 Interactive Guide Funnel" subtitle="Step-by-step user journey with conversion and drop-off analysis" padding="lg">
                  {selectedGuideFromList ? (
                    (() => {
                      const selectedGuide = guidesByType[selectedGuideType]?.find(g => g.id === selectedGuideFromList);
                      if (!selectedGuide) return null;

                      // Mock funnel data for the selected guide
                      const funnelSteps = [
                        {
                          name: 'Guide Started',
                          users: selectedGuide.views,
                          rate: 100,
                          color: '#10b981',
                          dropOff: 0
                        },
                        {
                          name: 'Step 1: Welcome',
                          users: Math.round(selectedGuide.views * 0.85),
                          rate: 85,
                          color: '#3b82f6',
                          dropOff: 15
                        },
                        {
                          name: 'Step 2: Setup',
                          users: Math.round(selectedGuide.views * 0.72),
                          rate: 72,
                          color: '#f59e0b',
                          dropOff: 13
                        },
                        {
                          name: 'Step 3: Configuration',
                          users: Math.round(selectedGuide.views * 0.65),
                          rate: 65,
                          color: '#8b5cf6',
                          dropOff: 7
                        },
                        {
                          name: 'Guide Completed',
                          users: selectedGuide.completed,
                          rate: Math.round((selectedGuide.completed / selectedGuide.views) * 100),
                          color: '#ef4444',
                          dropOff: Math.round(((selectedGuide.views * 0.65) - selectedGuide.completed) / (selectedGuide.views * 0.65) * 100)
                        }
                      ];

                      return (
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>
                          {funnelSteps.map((step, index) => (
                            <Box
                              key={index}
                              sx={{
                                p: 3,
                                backgroundColor: `${step.color}08`,
                                border: `1px solid ${step.color}20`,
                                borderRadius: 'var(--radius-lg)',
                                position: 'relative'
                              }}
                            >
                              {/* Step Header */}
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Box sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: '50%',
                                    backgroundColor: step.color
                                  }} />
                                  <Typography variant="body1" fontWeight="semibold">
                                    {step.name}
                                  </Typography>
                                </Box>
                                <Typography variant="h5" fontWeight="bold" sx={{ color: step.color }}>
                                  {step.rate}%
                                </Typography>
                              </Box>

                              {/* Metrics */}
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="h4" fontWeight="bold" sx={{ color: step.color }}>
                                  {step.users.toLocaleString()}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  Users
                                </Typography>
                              </Box>

                              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                Avg Step: 2m 15s
                              </Typography>

                              {/* Drop-off indicator */}
                              {step.dropOff > 0 && (
                                <Box sx={{
                                  position: 'absolute',
                                  top: -8,
                                  right: 16,
                                  px: 2,
                                  py: 0.5,
                                  backgroundColor: '#fef2f2',
                                  color: '#dc2626',
                                  borderRadius: 'var(--radius-full)',
                                  fontSize: '12px',
                                  fontWeight: 'medium',
                                  border: '1px solid #fecaca'
                                }}>
                                  -{step.dropOff}% users ({Math.round(selectedGuide.views * (step.dropOff / 100)).toLocaleString()})
                                </Box>
                              )}
                            </Box>
                          ))}

                          {/* Overall Conversion Summary */}
                          <Box sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            mt: 3,
                            pt: 3,
                            borderTop: '1px solid var(--color-gray-200)'
                          }}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h3" fontWeight="bold" sx={{ color: '#10b981' }}>
                                {Math.round((selectedGuide.completed / selectedGuide.views) * 100)}%
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Overall Conversion
                              </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h3" fontWeight="bold" sx={{ color: '#ef4444' }}>
                                {selectedGuide.dropOff}%
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Total Drop-off
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      );
                    })()
                  ) : (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      py: 8,
                      textAlign: 'center'
                    }}>
                      <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                        Select a guide from the left
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Choose a guide to view its step-by-step funnel analysis
                      </Typography>
                    </Box>
                  )}
                </Card>
              </Box>
            )}
          </>
        );

      case 2: // AI Performance
        return (
          <>
            
             {/* Bottom Metrics Cards */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 'var(--spacing-6)', mb: 4 }}>
              {/* Total Interactions Card */}
              <Box sx={{
                p: 4,
                backgroundColor: 'white',
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--color-gray-200)',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                position: 'relative'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="caption" color="#3b82f6" sx={{ fontWeight: 'medium' }}>
                    Total Interactions
                  </Typography>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: '#e3f2fd',
                    borderRadius: 'var(--radius-md)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Box sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: '#3b82f6',
                      borderRadius: 'var(--radius-sm)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '14px',
                      fontWeight: 'bold'
                    }}>
                      💬
                    </Box>
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1, fontSize: '2rem' }}>
                  2,847
                </Typography>
                <Typography variant="caption" color="#10b981" sx={{ fontWeight: 'medium' }}>
                  +12% from last month
                </Typography>
              </Box>

              {/* Success Rate Card */}
              <Box sx={{
                p: 4,
                backgroundColor: 'white',
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--color-gray-200)',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                position: 'relative'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="caption" color="#10b981" sx={{ fontWeight: 'medium' }}>
                    Success Rate
                  </Typography>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: '#e8f5e9',
                    borderRadius: 'var(--radius-md)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Box sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: '#10b981',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '14px',
                      fontWeight: 'bold'
                    }}>
                      ✓
                    </Box>
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1, fontSize: '2rem' }}>
                  91%
                </Typography>
                <Typography variant="caption" color="#10b981" sx={{ fontWeight: 'medium' }}>
                  +3% improvement
                </Typography>
              </Box>

              {/* Avg Response Time Card */}
              <Box sx={{
                p: 4,
                backgroundColor: 'white',
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--color-gray-200)',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                position: 'relative'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="caption" color="#8b5cf6" sx={{ fontWeight: 'medium' }}>
                    Avg Response Time
                  </Typography>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: '#f3e8ff',
                    borderRadius: 'var(--radius-md)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Box sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: '#8b5cf6',
                      borderRadius: 'var(--radius-sm)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '14px',
                      fontWeight: 'bold'
                    }}>
                      ⚡
                    </Box>
                  </Box>
                </Box>
                <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1, fontSize: '2rem' }}>
                  1.9s
                </Typography>
                <Typography variant="caption" color="#10b981" sx={{ fontWeight: 'medium' }}>
                  -0.3s faster
                </Typography>
              </Box>
            </Box>

            {/* AI Task Performance Section */}
            <Box sx={{ mb: 4 }}>
              <Card title="AI Task Performance" padding="lg">
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
                {/* Password Reset */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Password Reset
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#e8f5e9',
                        color: '#2e7d32',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        96%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        342 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 1.2s
                      </Typography>
                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        +2% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      96%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '96%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Account Setup */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Account Setup
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#e3f2fd',
                        color: '#1976d2',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        89%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        198 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 1.4s
                      </Typography>
                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        -5% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      89%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '89%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Feature Explanation */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Feature Explanation
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#e3f2fd',
                        color: '#1976d2',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        90%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        267 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 2.1s
                      </Typography>
                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        -1% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      90%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '90%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Troubleshooting */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Troubleshooting
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#fff3e0',
                        color: '#f57c00',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        88%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        156 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 3.1s
                      </Typography>
                      <Box sx={{ color: 'var(--color-error-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        -3% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      88%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '88%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>

                {/* Integration Help */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 3,
                  border: '1px solid var(--color-gray-200)',
                  borderRadius: 'var(--radius-md)',
                  '&:hover': {
                    backgroundColor: 'var(--color-gray-50)',
                    cursor: 'pointer'
                  }
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h6" fontWeight="semibold">
                        Integration Help
                      </Typography>
                      <Box sx={{
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: '#fff3e0',
                        color: '#f57c00',
                        borderRadius: 'var(--radius-sm)',
                        fontSize: '12px',
                        fontWeight: 'medium'
                      }}>
                        87%
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        123 interactions
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'var(--color-gray-600)', fontSize: '14px' }}>
                      <Typography variant="caption" color="text.secondary">
                        Avg time: 2.5s
                      </Typography>
                      <Box sx={{ color: 'var(--color-success-600)', fontWeight: 'medium', fontSize: '12px' }}>
                        +1% trend
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="h4" fontWeight="bold">
                      87%
                    </Typography>
                    <Box sx={{
                      width: 80,
                      height: 8,
                      backgroundColor: 'var(--color-gray-200)',
                      borderRadius: 'var(--radius-full)',
                      overflow: 'hidden'
                    }}>
                      <Box sx={{
                        width: '87%',
                        height: '100%',
                        backgroundColor: 'var(--color-gray-800)',
                        borderRadius: 'var(--radius-full)'
                      }} />
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Card>
            </Box>

            {/* AI Insights & Recommendations */}
            <Card title="AI Insights & Recommendations" padding="lg">
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-3)' }}>
                {/* Optimize Workflow */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#fffbeb',
                  border: '1px solid #fbbf24',
                  borderRadius: 'var(--radius-md)',
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 2
                }}>
                  <Box sx={{
                    width: 6,
                    height: 6,
                    backgroundColor: '#f59e0b',
                    borderRadius: '50%',
                    mt: 1
                  }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="medium" sx={{ mb: 0.5 }}>
                      Optimize Workflow
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Consider optimizing your workflow to reduce response time by 15%
                    </Typography>
                  </Box>
                </Box>

                {/* Excluded Performance */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f0f9ff',
                  border: '1px solid #3b82f6',
                  borderRadius: 'var(--radius-md)',
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 2
                }}>
                  <Box sx={{
                    width: 6,
                    height: 6,
                    backgroundColor: '#3b82f6',
                    borderRadius: '50%',
                    mt: 1
                  }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="medium" sx={{ mb: 0.5 }}>
                      Excluded Performance
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Excluded tasks are performing well with 94% accuracy rate
                    </Typography>
                  </Box>
                </Box>

                {/* Personalized Suggestions */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f0f9ff',
                  border: '1px solid #3b82f6',
                  borderRadius: 'var(--radius-md)',
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 2
                }}>
                  <Box sx={{
                    width: 6,
                    height: 6,
                    backgroundColor: '#3b82f6',
                    borderRadius: '50%',
                    mt: 1
                  }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="medium" sx={{ mb: 0.5 }}>
                      Personalized Suggestions
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      AI suggests implementing advanced filtering for better user experience
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Card>
          </>
        );



      default:
        return null;
    }
  };

  return (
    <div className='qadpt-web'>
      <div className='qadpt-webcontent'>
        <DashboardWrapper>
          <Container maxWidth="xl" sx={{ py: 3 }}>
            {/* Navigation Tabs */}
            <StyledTabs value={selectedTab} onChange={handleTabChange}>
              <Tab label="Overview" />
              <Tab label="Analytics" />
              <Tab label="AI Performance" />
            </StyledTabs>

            {/* Render Tab Content */}
            {renderTabContent()}
          </Container>
        </DashboardWrapper>

        {/* Interactive Tooltip */}
        {tooltip.visible && (
          <Box
            sx={{
              position: 'fixed',
              left: tooltip.x,
              top: tooltip.y,
              transform: 'translate(-50%, -100%)',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              padding: '6px 10px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              zIndex: 10000,
              pointerEvents: 'none',
              fontSize: '11px',
              minWidth: '70px',
              textAlign: 'center',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
            }}
          >
            <Typography variant="body2" sx={{ fontSize: '10px', color: '#6b7280', mb: 0.2, lineHeight: 1.2 }}>
              {tooltip.title}
            </Typography>
            <Typography variant="body2" sx={{ fontSize: '11px', color: '#111827', fontWeight: '600', lineHeight: 1.2 }}>
              {tooltip.content}
            </Typography>
          </Box>
        )}
      </div>
    </div>
  );
};

export default ModernDashboard;
