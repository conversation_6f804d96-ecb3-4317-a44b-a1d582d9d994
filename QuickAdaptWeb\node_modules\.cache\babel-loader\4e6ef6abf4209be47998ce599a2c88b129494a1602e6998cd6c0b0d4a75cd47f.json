{"ast": null, "code": "import React from\"react\";import{lazy}from\"react\";import{Navigate,useRoutes}from\"react-router-dom\";import NotificationSettings from\"../components/settings/NotificationSettings\";import Builder from\"../components/guide/Builder\";import{Dashboard}from\"@mui/icons-material\";import AdminList from\"../components/organization/AdminList\";import AdminPage from\"../components/organization/AdminPage\";import DomainSettings from\"../components/settings/DomainSettings\";import{useAuth}from\"../components/auth/AuthProvider\";import Audience from\"../components/audience/Audience\";import Tours from\"../components/tours/Tours\";import Announcements from\"../components/announcements/Announcements\";import Banners from\"../components/banners/Banners\";import Tooltip from\"../components/tooltips/Tooltips\";import Hotspots from\"../components/hotspots/Hotspots\";import Checklists from\"../components/checklists/Checklists\";import Surveys from\"../components/surveys/Survey\";import ProtectedRoute from\"./ProtectedRoute\";import{useParams}from\"react-router\";//import { toLanguage } from \"../components/adminMenu/AdminMenu\"\nimport Scripts from\"../components/agents/Scripts\";import ScriptHistory from\"../components/agents/ScriptHistory\";import ScriptHistoryViewer from\"../components/agents/ScriptHistoryViewer\";import AgentsList from\"../components/agents/Agentslist\";import ModernDashboard from\"../components/dashboard/ModernDashboard\";import{checkSessionExpired}from\"../services/APIService\";import{jsx as _jsx}from\"react/jsx-runtime\";const AccountCreate=/*#__PURE__*/lazy(()=>import(\"../components/account/AccountCreate\"));const AuditLogList=/*#__PURE__*/lazy(()=>import(\"../components/auditLog/AuditLogList\"));const Login=/*#__PURE__*/lazy(()=>import(\"../components/login/login\"));const OrganizationList=/*#__PURE__*/lazy(()=>import(\"../components/organization/OrganizationList\"));const AccountsList=/*#__PURE__*/lazy(()=>import(\"../components/account/AccountList\"));const GuidesList=/*#__PURE__*/lazy(()=>import(\"../components/guide/GuideList\"));const UserList=/*#__PURE__*/lazy(()=>import(\"../components/user/UserList\"));const Teamsetting=/*#__PURE__*/lazy(()=>import(\"../components/settings/TeamSettings\"));const Settings=/*#__PURE__*/lazy(()=>import(\"../components/settings/Settings\"));const Training=/*#__PURE__*/lazy(()=>import(\"../components/training/Training\"));const InstallSetting=/*#__PURE__*/lazy(()=>import(\"../components/settings/InstallSettings\"));const UnInstall=/*#__PURE__*/lazy(()=>import(\"../components/settings/UnInstall\"));const BillingSetting=/*#__PURE__*/lazy(()=>import(\"../components/settings/BillingSettings\"));const RightSettings=/*#__PURE__*/lazy(()=>import(\"../components/settings/RightSettings\"));const SuperAdminAuditLogList=/*#__PURE__*/lazy(()=>import(\"../components/auditLog/SuperAdminAuditLogList\"));const Callback=/*#__PURE__*/lazy(()=>import(\"../services/Callback\"));const DashBoard=/*#__PURE__*/lazy(()=>import(\"../components/dashboard/Dashboard\"));const FileList=/*#__PURE__*/lazy(()=>import(\"../components/fileManagement/FileList\"));const ThemeSettings=/*#__PURE__*/lazy(()=>import(\"../components/settings/ThemeSettings\"));const Forgotpassword=/*#__PURE__*/lazy(()=>import(\"../components/login/Forgotpassword\"));const ResetPassword=/*#__PURE__*/lazy(()=>import(\"../components/login/ResetPassword\"));const AdminLoginPage=/*#__PURE__*/lazy(()=>import(\"../components/login/Superadminloginpage\"));const ExpiredLink=/*#__PURE__*/lazy(()=>import(\"../components/login/Expiredlink\"));// const AnnouncementSettings = lazy(() => import(\"../components/announcements/AnnouncementSettings\"));\nconst WebAppSettingspage=/*#__PURE__*/lazy(()=>import(\"../components/webappsettingspage/WebAppSettings\"));// const TooltipSettings = lazy(() => import(\"../components/tooltips/TooltipSettings\"));\n// const ToursSettings = lazy(() => import(\"../components/tours/ToursSettings\"));\nconst Routing=()=>{const{user,userDetails}=useAuth();const{passwordLogId}=useParams();// const initialBanner: Banner = {\nconst routes=useRoutes([// Public routes\n{path:\"/login\",element:localStorage.getItem(\"access_token\")&&!checkSessionExpired()?/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true}):/*#__PURE__*/_jsx(Login,{})},{path:\"/callback\",element:/*#__PURE__*/_jsx(Callback,{})},{path:\"/forgotpassword\",element:/*#__PURE__*/_jsx(Forgotpassword,{})},{path:\"/resetpassword/:passwordLogId\",element:/*#__PURE__*/_jsx(ResetPassword,{})},{path:\"/linkexpired\",element:/*#__PURE__*/_jsx(ExpiredLink,{})},{path:\"/uninstall\",element:/*#__PURE__*/_jsx(UnInstall,{})},{path:\"/admin/adminlogin\",element:/*#__PURE__*/_jsx(AdminLoginPage,{})},{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(ModernDashboard,{})}]},{path:\"*\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(ModernDashboard,{})}]},// Protected routes for Admin and User\n{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Dashboard,{})}]},{path:\"/modern-dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(ModernDashboard,{})}]},{path:\"/settings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Settings,{})}]},{path:\"/auditlog\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(AuditLogList,{})}]},{path:\"superadmin\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"superadmin\"]}),children:[{path:\"organizations\",element:/*#__PURE__*/_jsx(OrganizationList,{})},{path:\"*\",element:/*#__PURE__*/_jsx(OrganizationList,{})},{path:\":organizationId/createadmin\",element:/*#__PURE__*/_jsx(AdminPage,{})},{path:\"adminList\",element:/*#__PURE__*/_jsx(AdminList,{})},{path:\"auditlogs\",element:/*#__PURE__*/_jsx(SuperAdminAuditLogList,{})}]},{path:\"/createaccount\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(AccountCreate,{})}]},{path:\"/guides\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(GuidesList,{})}]},{path:\"/:organizationId\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"accounts\",element:/*#__PURE__*/_jsx(AccountsList,{})},{path:\"team\",element:/*#__PURE__*/_jsx(UserList,{})}]},{path:\"/user\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(UserList,{})}]},{path:\"/filelist\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(FileList,{})}]},{path:\"/guide\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(GuidesList,{})}]},{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(DashBoard,{})}]},{path:\"/ThemeSettings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(ThemeSettings,{})}]},{path:\"/activitylog\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(AuditLogList,{})}]},{path:\"/settings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"rights\",element:/*#__PURE__*/_jsx(RightSettings,{})},{path:\"domain\",element:/*#__PURE__*/_jsx(DomainSettings,{})},{path:\"install\",element:/*#__PURE__*/_jsx(InstallSetting,{})},{path:\"domain\",element:/*#__PURE__*/_jsx(DomainSettings,{})},{path:\"notifications\",element:/*#__PURE__*/_jsx(NotificationSettings,{})},{path:\"billing\",element:/*#__PURE__*/_jsx(BillingSetting,{})},{path:\"scripts\",element:/*#__PURE__*/_jsx(Scripts,{})},{path:\"scripthistory\",element:/*#__PURE__*/_jsx(ScriptHistory,{})},{path:\"scripthistoryviewer\",element:/*#__PURE__*/_jsx(ScriptHistoryViewer,{})},{path:\"agents\",element:/*#__PURE__*/_jsx(AgentsList,{})},{path:\"training\",element:/*#__PURE__*/_jsx(Training,{})}]},{path:\"/Builder\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Builder,{})}]},{path:\"/audience\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Audience,{})}]},{path:\"/tours\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Tours,{})}]},{path:\"/announcements\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Announcements,{})}]},{path:\"/:guideName/:guideId/settings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(WebAppSettingspage,{})}]},{path:\"/tooltips\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Tooltip,{})}]},{path:\"/hotspots\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Hotspots,{})}]},{path:\"/banners\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Banners,{})}]},{path:\"/checklists\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Checklists,{})}]},{path:\"/surveys\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(Surveys,{})}]},{path:\"/analytics-dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedUserTypes:[\"admin\",\"user\"]}),children:[{path:\"\",element:/*#__PURE__*/_jsx(ModernDashboard,{})}]}// {\n// \tpath: \"/agents\",\n// \telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\n// \tchildren: [{ path: \"\", element: <AgentsList /> }],\n// },\n]);return routes;};export default Routing;", "map": {"version": 3, "names": ["React", "lazy", "Navigate", "useRoutes", "NotificationSettings", "Builder", "Dashboard", "AdminList", "AdminPage", "DomainSettings", "useAuth", "Audience", "Tours", "Announcements", "Banners", "<PERSON><PERSON><PERSON>", "Hotspots", "Checklists", "Surveys", "ProtectedRoute", "useParams", "<PERSON><PERSON><PERSON>", "ScriptHistory", "ScriptHistoryViewer", "AgentsList", "ModernDashboard", "checkSessionExpired", "jsx", "_jsx", "AccountCreate", "AuditLogList", "<PERSON><PERSON>", "OrganizationList", "AccountsList", "GuidesList", "UserList", "Teamsetting", "Settings", "Training", "InstallSetting", "UnInstall", "BillingSetting", "RightSettings", "SuperAdminAuditLogList", "Callback", "DashBoard", "FileList", "ThemeSettings", "Forgotpassword", "ResetPassword", "AdminLoginPage", "ExpiredLink", "WebAppSettingspage", "Routing", "user", "userDetails", "passwordLogId", "routes", "path", "element", "localStorage", "getItem", "to", "replace", "allowedUserTypes", "children"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/routing/Routings.tsx"], "sourcesContent": ["import React from \"react\"\r\nimport { lazy } from \"react\"\r\nimport { Navigate, Outlet, useRoutes } from \"react-router-dom\"\r\nimport { useLocation, useNavigate } from \"react-router\"\r\nimport Home from \"../components/common/Home\"\r\nimport NotificationSettings from \"../components/settings/NotificationSettings\"\r\nimport ProfileSettings from \"../components/settings/ProfileSettings\"\r\nimport Builder from \"../components/guide/Builder\"\r\nimport { Dashboard, Domain } from \"@mui/icons-material\"\r\nimport AdminList from \"../components/organization/AdminList\"\r\nimport AdminPage from \"../components/organization/AdminPage\"\r\nimport Translater from \"../components/multilingual/Multilingual\"\r\nimport DomainSettings from \"../components/settings/DomainSettings\"\r\nimport { useAuth } from \"../components/auth/AuthProvider\"\r\nimport CodeInstall from \"../components/settings/InstallSettings\"\r\nimport Audience from \"../components/audience/Audience\"\r\nimport Tours from \"../components/tours/Tours\"\r\nimport Announcements from \"../components/announcements/Announcements\"\r\nimport Banners from \"../components/banners/Banners\"\r\nimport Tooltip from \"../components/tooltips/Tooltips\"\r\nimport Hotspots from \"../components/hotspots/Hotspots\"\r\nimport Checklists from \"../components/checklists/Checklists\"\r\nimport Surveys from \"../components/surveys/Survey\";\r\nimport ProtectedRoute from \"./ProtectedRoute\";\r\nimport { useParams } from \"react-router\";\r\n\r\n//import { toLanguage } from \"../components/adminMenu/AdminMenu\"\r\nimport Scripts from \"../components/agents/Scripts\"\r\nimport ScriptHistory from \"../components/agents/ScriptHistory\"\r\nimport ScriptHistoryViewer from \"../components/agents/ScriptHistoryViewer\"\r\nimport AgentsList from \"../components/agents/Agentslist\"\r\nimport ModernDashboard from \"../components/dashboard/ModernDashboard\"\r\nimport { checkSessionExpired } from \"../services/APIService\"\r\nconst AccountCreate = lazy(() => import(\"../components/account/AccountCreate\"));\r\nconst AuditLogList = lazy(() => import(\"../components/auditLog/AuditLogList\"));\r\nconst Login = lazy(() => import(\"../components/login/login\"));\r\nconst OrganizationList = lazy(() => import(\"../components/organization/OrganizationList\"));\r\nconst AccountsList = lazy(() => import(\"../components/account/AccountList\"));\r\nconst GuidesList = lazy(() => import(\"../components/guide/GuideList\"));\r\nconst UserList = lazy(() => import(\"../components/user/UserList\"));\r\nconst Teamsetting = lazy(() => import(\"../components/settings/TeamSettings\"));\r\nconst Settings = lazy(() => import(\"../components/settings/Settings\"));\r\nconst Training = lazy(() => import(\"../components/training/Training\"));\r\nconst InstallSetting = lazy(() => import(\"../components/settings/InstallSettings\"));\r\nconst UnInstall = lazy(() => import(\"../components/settings/UnInstall\"));\r\nconst BillingSetting = lazy(() => import(\"../components/settings/BillingSettings\"));\r\nconst RightSettings = lazy(() => import(\"../components/settings/RightSettings\"));\r\nconst SuperAdminAuditLogList = lazy(() => import(\"../components/auditLog/SuperAdminAuditLogList\"));\r\nconst Callback = lazy(() => import(\"../services/Callback\"));\r\nconst DashBoard = lazy(() => import(\"../components/dashboard/Dashboard\"));\r\nconst FileList = lazy(() => import(\"../components/fileManagement/FileList\"));\r\nconst ThemeSettings = lazy(() => import(\"../components/settings/ThemeSettings\"));\r\nconst Forgotpassword = lazy(() => import(\"../components/login/Forgotpassword\"));\r\nconst ResetPassword = lazy(() => import(\"../components/login/ResetPassword\"));\r\nconst AdminLoginPage = lazy(() => import(\"../components/login/Superadminloginpage\"));\r\nconst ExpiredLink = lazy(() => import(\"../components/login/Expiredlink\"));\r\n// const AnnouncementSettings = lazy(() => import(\"../components/announcements/AnnouncementSettings\"));\r\nconst WebAppSettingspage = lazy(() => import(\"../components/webappsettingspage/WebAppSettings\"));\r\n// const TooltipSettings = lazy(() => import(\"../components/tooltips/TooltipSettings\"));\r\n// const ToursSettings = lazy(() => import(\"../components/tours/ToursSettings\"));\r\nconst Routing = () => {\r\n\tconst { user, userDetails } = useAuth();\r\n\tconst { passwordLogId } = useParams();\r\n\t// const initialBanner: Banner = {\r\n  const routes = useRoutes([\r\n\t\t// Public routes\r\n{\r\n  path: \"/login\",\r\n  element: localStorage.getItem(\"access_token\") && !checkSessionExpired() ? (\r\n    <Navigate to=\"/\" replace />\r\n  ) : (\r\n    <Login />\r\n  ),\r\n},\r\n\r\n\r\n\r\n\t\t{\r\n\t\t\tpath: \"/callback\",\r\n\t\t\telement: <Callback />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/forgotpassword\",\r\n\t\t\telement: <Forgotpassword />,\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/resetpassword/:passwordLogId\",\r\n\t\t\telement: <ResetPassword />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/linkexpired\",\r\n\t\t\telement: <ExpiredLink />,\r\n\t \t },\r\n\t  \t{\r\n\t\t\tpath: \"/uninstall\",\r\n\t\t\telement: <UnInstall />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/admin/adminlogin\",\r\n\t\t\telement: <AdminLoginPage />,\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ModernDashboard /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"*\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ModernDashboard /> }],\r\n\t\t},\r\n\r\n\t\t// Protected routes for Admin and User\r\n\t\t{\r\n\t\t\tpath: \"/dashboard\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Dashboard /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/modern-dashboard\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ModernDashboard /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/settings\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Settings /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/auditlog\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <AuditLogList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"superadmin\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"superadmin\"]} />,\r\n\t\t\tchildren: [\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"organizations\",\r\n\t\t\t\t\telement: <OrganizationList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"*\",\r\n\t\t\t\t\telement: <OrganizationList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \":organizationId/createadmin\",\r\n\t\t\t\t\telement: <AdminPage />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"adminList\",\r\n\t\t\t\t\telement: <AdminList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"auditlogs\",\r\n\t\t\t\t\telement: <SuperAdminAuditLogList />,\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/createaccount\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <AccountCreate /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/guides\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <GuidesList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/:organizationId\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"accounts\",\r\n\t\t\t\t\telement: <AccountsList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"team\",\r\n\t\t\t\t\telement: <UserList />,\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/user\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <UserList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/filelist\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <FileList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/guide\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <GuidesList /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/dashboard\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <DashBoard /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/ThemeSettings\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ThemeSettings /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/activitylog\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <AuditLogList /> }],\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/settings\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"rights\",\r\n\t\t\t\t\telement: <RightSettings />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"domain\",\r\n\t\t\t\t\telement: <DomainSettings />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"install\",\r\n\t\t\t\t\telement: <InstallSetting />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"domain\",\r\n\t\t\t\t\telement: <DomainSettings />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"notifications\",\r\n\t\t\t\t\telement: <NotificationSettings />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"billing\",\r\n\t\t\t\t\telement: <BillingSetting />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"scripts\",\r\n\t\t\t\t\telement: <Scripts />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"scripthistory\",\r\n\t\t\t\t\telement: <ScriptHistory />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"scripthistoryviewer\",\r\n\t\t\t\t\telement: <ScriptHistoryViewer />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"agents\",\r\n\t\t\t\t\telement: <AgentsList />,\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tpath: \"training\",\r\n\t\t\t\t\telement: <Training />,\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/Builder\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Builder /> }],\r\n\t\t},\r\n\r\n\t\t{\r\n\t\t\tpath: \"/audience\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Audience /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/tours\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Tours /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/announcements\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Announcements /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/:guideName/:guideId/settings\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <WebAppSettingspage /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/tooltips\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Tooltip /> }],\r\n\t  \t},\r\n\t  \t{\r\n\t\t\tpath: \"/hotspots\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Hotspots/> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/banners\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Banners /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/checklists\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Checklists /> }],\r\n\t\t},\r\n\t\t{\r\n\t\t\tpath: \"/surveys\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <Surveys /> }],\r\n\t  },\r\n\t\t{\r\n\t\t\tpath: \"/analytics-dashboard\",\r\n\t\t\telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t\tchildren: [{ path: \"\", element: <ModernDashboard /> }],\r\n\t\t},\r\n\t\t// {\r\n\t\t// \tpath: \"/agents\",\r\n\t\t// \telement: <ProtectedRoute allowedUserTypes={[\"admin\", \"user\"]} />,\r\n\t\t// \tchildren: [{ path: \"\", element: <AgentsList /> }],\r\n\t\t// },\r\n\t]);\r\n\r\n  return routes;\r\n};\r\n\r\nexport default Routing;\r\n\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,OAAO,CAC5B,OAASC,QAAQ,CAAUC,SAAS,KAAQ,kBAAkB,CAG9D,MAAO,CAAAC,oBAAoB,KAAM,6CAA6C,CAE9E,MAAO,CAAAC,OAAO,KAAM,6BAA6B,CACjD,OAASC,SAAS,KAAgB,qBAAqB,CACvD,MAAO,CAAAC,SAAS,KAAM,sCAAsC,CAC5D,MAAO,CAAAC,SAAS,KAAM,sCAAsC,CAE5D,MAAO,CAAAC,cAAc,KAAM,uCAAuC,CAClE,OAASC,OAAO,KAAQ,iCAAiC,CAEzD,MAAO,CAAAC,QAAQ,KAAM,iCAAiC,CACtD,MAAO,CAAAC,KAAK,KAAM,2BAA2B,CAC7C,MAAO,CAAAC,aAAa,KAAM,2CAA2C,CACrE,MAAO,CAAAC,OAAO,KAAM,+BAA+B,CACnD,MAAO,CAAAC,OAAO,KAAM,iCAAiC,CACrD,MAAO,CAAAC,QAAQ,KAAM,iCAAiC,CACtD,MAAO,CAAAC,UAAU,KAAM,qCAAqC,CAC5D,MAAO,CAAAC,OAAO,KAAM,8BAA8B,CAClD,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,OAASC,SAAS,KAAQ,cAAc,CAExC;AACA,MAAO,CAAAC,OAAO,KAAM,8BAA8B,CAClD,MAAO,CAAAC,aAAa,KAAM,oCAAoC,CAC9D,MAAO,CAAAC,mBAAmB,KAAM,0CAA0C,CAC1E,MAAO,CAAAC,UAAU,KAAM,iCAAiC,CACxD,MAAO,CAAAC,eAAe,KAAM,yCAAyC,CACrE,OAASC,mBAAmB,KAAQ,wBAAwB,QAAAC,GAAA,IAAAC,IAAA,yBAC5D,KAAM,CAAAC,aAAa,cAAG5B,IAAI,CAAC,IAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC,CAC/E,KAAM,CAAA6B,YAAY,cAAG7B,IAAI,CAAC,IAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC,CAC9E,KAAM,CAAA8B,KAAK,cAAG9B,IAAI,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAC7D,KAAM,CAAA+B,gBAAgB,cAAG/B,IAAI,CAAC,IAAM,MAAM,CAAC,6CAA6C,CAAC,CAAC,CAC1F,KAAM,CAAAgC,YAAY,cAAGhC,IAAI,CAAC,IAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC,CAC5E,KAAM,CAAAiC,UAAU,cAAGjC,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACtE,KAAM,CAAAkC,QAAQ,cAAGlC,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAClE,KAAM,CAAAmC,WAAW,cAAGnC,IAAI,CAAC,IAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC,CAC7E,KAAM,CAAAoC,QAAQ,cAAGpC,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CACtE,KAAM,CAAAqC,QAAQ,cAAGrC,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CACtE,KAAM,CAAAsC,cAAc,cAAGtC,IAAI,CAAC,IAAM,MAAM,CAAC,wCAAwC,CAAC,CAAC,CACnF,KAAM,CAAAuC,SAAS,cAAGvC,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACxE,KAAM,CAAAwC,cAAc,cAAGxC,IAAI,CAAC,IAAM,MAAM,CAAC,wCAAwC,CAAC,CAAC,CACnF,KAAM,CAAAyC,aAAa,cAAGzC,IAAI,CAAC,IAAM,MAAM,CAAC,sCAAsC,CAAC,CAAC,CAChF,KAAM,CAAA0C,sBAAsB,cAAG1C,IAAI,CAAC,IAAM,MAAM,CAAC,+CAA+C,CAAC,CAAC,CAClG,KAAM,CAAA2C,QAAQ,cAAG3C,IAAI,CAAC,IAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAC3D,KAAM,CAAA4C,SAAS,cAAG5C,IAAI,CAAC,IAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC,CACzE,KAAM,CAAA6C,QAAQ,cAAG7C,IAAI,CAAC,IAAM,MAAM,CAAC,uCAAuC,CAAC,CAAC,CAC5E,KAAM,CAAA8C,aAAa,cAAG9C,IAAI,CAAC,IAAM,MAAM,CAAC,sCAAsC,CAAC,CAAC,CAChF,KAAM,CAAA+C,cAAc,cAAG/C,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAC/E,KAAM,CAAAgD,aAAa,cAAGhD,IAAI,CAAC,IAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC,CAC7E,KAAM,CAAAiD,cAAc,cAAGjD,IAAI,CAAC,IAAM,MAAM,CAAC,yCAAyC,CAAC,CAAC,CACpF,KAAM,CAAAkD,WAAW,cAAGlD,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CACzE;AACA,KAAM,CAAAmD,kBAAkB,cAAGnD,IAAI,CAAC,IAAM,MAAM,CAAC,iDAAiD,CAAC,CAAC,CAChG;AACA;AACA,KAAM,CAAAoD,OAAO,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,IAAI,CAAEC,WAAY,CAAC,CAAG7C,OAAO,CAAC,CAAC,CACvC,KAAM,CAAE8C,aAAc,CAAC,CAAGpC,SAAS,CAAC,CAAC,CACrC;AACC,KAAM,CAAAqC,MAAM,CAAGtD,SAAS,CAAC,CACzB;AACF,CACEuD,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAEC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAI,CAACnC,mBAAmB,CAAC,CAAC,cACrEE,IAAA,CAAC1B,QAAQ,EAAC4D,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,cAE3BnC,IAAA,CAACG,KAAK,GAAE,CAEZ,CAAC,CAIC,CACC2B,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACgB,QAAQ,GAAE,CACrB,CAAC,CACD,CACCc,IAAI,CAAE,iBAAiB,CACvBC,OAAO,cAAE/B,IAAA,CAACoB,cAAc,GAAE,CAC3B,CAAC,CAED,CACCU,IAAI,CAAE,+BAA+B,CACrCC,OAAO,cAAE/B,IAAA,CAACqB,aAAa,GAAE,CAC1B,CAAC,CACD,CACCS,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAE/B,IAAA,CAACuB,WAAW,GAAE,CACtB,CAAC,CACD,CACDO,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAE/B,IAAA,CAACY,SAAS,GAAE,CACtB,CAAC,CACD,CACCkB,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAE/B,IAAA,CAACsB,cAAc,GAAE,CAC3B,CAAC,CACD,CACCQ,IAAI,CAAE,GAAG,CACTC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACH,eAAe,GAAE,CAAE,CAAC,CACtD,CAAC,CACD,CACCiC,IAAI,CAAE,GAAG,CACTC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACH,eAAe,GAAE,CAAE,CAAC,CACtD,CAAC,CAED;AACA,CACCiC,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACtB,SAAS,GAAE,CAAE,CAAC,CAChD,CAAC,CACD,CACCoD,IAAI,CAAE,mBAAmB,CACzBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACH,eAAe,GAAE,CAAE,CAAC,CACtD,CAAC,CACD,CACCiC,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,CAAE,CAAC,CACxDC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACS,QAAQ,GAAE,CAAE,CAAC,CAC/C,CAAC,CACD,CACCqB,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACE,YAAY,GAAE,CAAE,CAAC,CACnD,CAAC,CACD,CACC4B,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,YAAY,CAAE,CAAE,CAAC,CAC7DC,QAAQ,CAAE,CACT,CACCP,IAAI,CAAE,eAAe,CACrBC,OAAO,cAAE/B,IAAA,CAACI,gBAAgB,GAAE,CAC7B,CAAC,CACD,CACC0B,IAAI,CAAE,GAAG,CACTC,OAAO,cAAE/B,IAAA,CAACI,gBAAgB,GAAE,CAC7B,CAAC,CACD,CACC0B,IAAI,CAAE,6BAA6B,CACnCC,OAAO,cAAE/B,IAAA,CAACpB,SAAS,GAAE,CACtB,CAAC,CACD,CACCkD,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACrB,SAAS,GAAE,CACtB,CAAC,CACD,CACCmD,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACe,sBAAsB,GAAE,CACnC,CAAC,CAEH,CAAC,CACD,CACCe,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACC,aAAa,GAAE,CAAE,CAAC,CACpD,CAAC,CACD,CACC6B,IAAI,CAAE,SAAS,CACfC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACM,UAAU,GAAE,CAAE,CAAC,CACjD,CAAC,CACD,CACCwB,IAAI,CAAE,kBAAkB,CACxBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CACT,CACCP,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAE/B,IAAA,CAACK,YAAY,GAAE,CACzB,CAAC,CACD,CACCyB,IAAI,CAAE,MAAM,CACZC,OAAO,cAAE/B,IAAA,CAACO,QAAQ,GAAE,CACrB,CAAC,CAEH,CAAC,CAED,CACCuB,IAAI,CAAE,OAAO,CACbC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACO,QAAQ,GAAE,CAAE,CAAC,CAC/C,CAAC,CACD,CACCuB,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACkB,QAAQ,GAAE,CAAE,CAAC,CAC/C,CAAC,CACD,CACCY,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACM,UAAU,GAAE,CAAE,CAAC,CACjD,CAAC,CACD,CACCwB,IAAI,CAAE,YAAY,CAClBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACiB,SAAS,GAAE,CAAE,CAAC,CAChD,CAAC,CACD,CACCa,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACmB,aAAa,GAAE,CAAE,CAAC,CACpD,CAAC,CACD,CACCW,IAAI,CAAE,cAAc,CACpBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACE,YAAY,GAAE,CAAE,CAAC,CACnD,CAAC,CAED,CACC4B,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CACT,CACCP,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAE/B,IAAA,CAACc,aAAa,GAAE,CAC1B,CAAC,CACD,CACCgB,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAE/B,IAAA,CAACnB,cAAc,GAAE,CAC3B,CAAC,CACD,CACCiD,IAAI,CAAE,SAAS,CACfC,OAAO,cAAE/B,IAAA,CAACW,cAAc,GAAE,CAC3B,CAAC,CACD,CACCmB,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAE/B,IAAA,CAACnB,cAAc,GAAE,CAC3B,CAAC,CACD,CACCiD,IAAI,CAAE,eAAe,CACrBC,OAAO,cAAE/B,IAAA,CAACxB,oBAAoB,GAAE,CACjC,CAAC,CACD,CACCsD,IAAI,CAAE,SAAS,CACfC,OAAO,cAAE/B,IAAA,CAACa,cAAc,GAAE,CAC3B,CAAC,CACD,CACCiB,IAAI,CAAE,SAAS,CACfC,OAAO,cAAE/B,IAAA,CAACP,OAAO,GAAE,CACpB,CAAC,CACD,CACCqC,IAAI,CAAE,eAAe,CACrBC,OAAO,cAAE/B,IAAA,CAACN,aAAa,GAAE,CAC1B,CAAC,CACD,CACCoC,IAAI,CAAE,qBAAqB,CAC3BC,OAAO,cAAE/B,IAAA,CAACL,mBAAmB,GAAE,CAChC,CAAC,CACD,CACCmC,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAE/B,IAAA,CAACJ,UAAU,GAAE,CACvB,CAAC,CACD,CACCkC,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAE/B,IAAA,CAACU,QAAQ,GAAE,CACrB,CAAC,CAEH,CAAC,CAED,CACCoB,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACvB,OAAO,GAAE,CAAE,CAAC,CAC9C,CAAC,CAED,CACCqD,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACjB,QAAQ,GAAE,CAAE,CAAC,CAC/C,CAAC,CACD,CACC+C,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAAChB,KAAK,GAAE,CAAE,CAAC,CAC5C,CAAC,CACD,CACC8C,IAAI,CAAE,gBAAgB,CACtBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACf,aAAa,GAAE,CAAE,CAAC,CACpD,CAAC,CACD,CACC6C,IAAI,CAAE,+BAA+B,CACrCC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACwB,kBAAkB,GAAE,CAAE,CAAC,CACzD,CAAC,CACD,CACCM,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACb,OAAO,GAAE,CAAE,CAAC,CAC5C,CAAC,CACD,CACD2C,IAAI,CAAE,WAAW,CACjBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACZ,QAAQ,GAAC,CAAE,CAAC,CAC9C,CAAC,CACD,CACC0C,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACd,OAAO,GAAE,CAAE,CAAC,CAC9C,CAAC,CACD,CACC4C,IAAI,CAAE,aAAa,CACnBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACX,UAAU,GAAE,CAAE,CAAC,CACjD,CAAC,CACD,CACCyC,IAAI,CAAE,UAAU,CAChBC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACV,OAAO,GAAE,CAAE,CAAC,CAC7C,CAAC,CACF,CACCwC,IAAI,CAAE,sBAAsB,CAC5BC,OAAO,cAAE/B,IAAA,CAACT,cAAc,EAAC6C,gBAAgB,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,CAAE,CAAC,CAChEC,QAAQ,CAAE,CAAC,CAAEP,IAAI,CAAE,EAAE,CAAEC,OAAO,cAAE/B,IAAA,CAACH,eAAe,GAAE,CAAE,CAAC,CACtD,CACA;AACA;AACA;AACA;AACA;AAAA,CACA,CAAC,CAED,MAAO,CAAAgC,MAAM,CACf,CAAC,CAED,cAAe,CAAAJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}