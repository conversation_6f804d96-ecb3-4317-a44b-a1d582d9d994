[{"E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\index.tsx": "1", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\reportWebVitals.ts": "2", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\App.tsx": "3", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\AuthProvider.tsx": "4", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\SnackbarContext.tsx": "5", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\ExtensionContext.tsx": "6", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\RtlContext.tsx": "7", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\layout\\Layout.tsx": "8", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ExpirelinkService.tsx": "9", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\Routings.tsx": "10", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\APIService.tsx": "11", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountContext.tsx": "12", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\UseAuth.tsx": "13", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountList.tsx": "14", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\Callback.tsx": "15", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\ProtectedRoute.tsx": "16", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountCreate.tsx": "17", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\RegistrationPage\\RegistrationPage.tsx": "18", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\AdminMenu.tsx": "19", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\DomainSettings.tsx": "20", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\NotificationSettings.tsx": "21", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\ThemeSettings.tsx": "22", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\RightSettings.tsx": "23", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\BillingSettings.tsx": "24", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\UnInstall.tsx": "25", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\InstallSettings.tsx": "26", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\Settings.tsx": "27", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\TeamSettings.tsx": "28", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\Builder.tsx": "29", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideList.tsx": "30", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AdminList.tsx": "31", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AdminPage.tsx": "32", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationList.tsx": "33", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\audience\\Audience.tsx": "34", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\tours\\Tours.tsx": "35", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\banners\\Banners.tsx": "36", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\announcements\\Announcements.tsx": "37", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\tooltips\\Tooltips.tsx": "38", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\hotspots\\Hotspots.tsx": "39", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\checklists\\Checklists.tsx": "40", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\surveys\\Survey.tsx": "41", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\Dashboard.tsx": "42", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\ScriptHistory.tsx": "43", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\Scripts.tsx": "44", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\ScriptHistoryViewer.tsx": "45", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\Agentslist.tsx": "46", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\webappsettingspage\\WebAppSettings.tsx": "47", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Expiredlink.tsx": "48", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Superadminloginpage.tsx": "49", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\ResetPassword.tsx": "50", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Forgotpassword.tsx": "51", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\login.tsx": "52", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\OidcConfig.ts": "53", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\OrganizationService.ts": "54", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\AccountService.tsx": "55", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\sidemenustate.tsx": "56", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\MultilingualService.ts": "57", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountsColumnMenu.tsx": "58", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountEdit.tsx": "59", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\Signup.tsx": "60", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\pagewrapper.tsx": "61", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\ProfileSettings.tsx": "62", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\sideMenu.tsx": "63", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\AlertSettings.tsx": "64", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\fileManagement\\FileList.tsx": "65", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auditLog\\SuperAdminAuditLogList.tsx": "66", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auditLog\\AuditLogList.tsx": "67", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\training\\Training.tsx": "68", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserList.tsx": "69", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Home.tsx": "70", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\GuideService.tsx": "71", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\UserRoleService.ts": "72", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\Filterpopup.tsx": "73", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideEdit.tsx": "74", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideCreate.tsx": "75", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideTable.tsx": "76", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\UserService.ts": "77", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Grid.tsx": "78", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AddAdmin.tsx": "79", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\timezones.tsx": "80", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\LanguageContext.tsx": "81", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\Multilingual.tsx": "82", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\feedback\\ShareFeedbackPopup.tsx": "83", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\roles\\RolePopup.tsx": "84", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationEdit.tsx": "85", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\TimeZoneConversion.tsx": "86", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationCustomColumnMenu.tsx": "87", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\i18n.tsx": "88", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\roles\\RoleDeletePopup.tsx": "89", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CreateNewGuidePopup.tsx": "90", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ExtensionRequiredPopup.tsx": "91", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\assets\\icons\\icons.tsx": "92", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CloneGuidePopup.tsx": "93", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\utils\\openGuideInBuilder.ts": "94", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ScriptService.ts": "95", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\SystemPromtServices.tsx": "96", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ForgotPasswordService.tsx": "97", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\LoginService.tsx": "98", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ResetpasswordService.tsx": "99", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\changepassword.tsx": "100", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountCustomColumnMenuUserItem.tsx": "101", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ProfileSettingPageService.tsx": "102", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Popup.tsx": "103", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\FileManagementService.tsx": "104", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\AuditLogServices.tsx": "105", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\TrainingService.tsx": "106", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCreate.tsx": "107", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserEdit.tsx": "108", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserPasswordReset.tsx": "109", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCustomColumnMenu.tsx": "110", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\orgData.js": "111", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\models\\Training.ts": "112", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserUnblock.tsx": "113", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserEnable.tsx": "114", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\Userdisable.tsx": "115", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserBlock.tsx": "116", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\FeedbackService.tsx": "117", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\feedback\\FeedbackConfirmPopup.tsx": "118", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationcustomcolumnMenuItem.tsx": "119", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCustomColumnMenuUserItem.tsx": "120", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\logoutpopup.tsx": "121", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\AnalyticsDashboard.tsx": "122", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\EditSubscription.tsx": "123", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Card.tsx": "124", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ModernButton.tsx": "125", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ModernDataGrid.tsx": "126", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\ModernDashboard.tsx": "127", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\ChartPlaceholder.tsx": "128"}, {"size": 789, "mtime": 1742548499275, "results": "129", "hashOfConfig": "130"}, {"size": 440, "mtime": 1742548446309, "results": "131", "hashOfConfig": "130"}, {"size": 4168, "mtime": 1752131906020, "results": "132", "hashOfConfig": "130"}, {"size": 5071, "mtime": 1752839122565, "results": "133", "hashOfConfig": "130"}, {"size": 2384, "mtime": 1752839122518, "results": "134", "hashOfConfig": "130"}, {"size": 3194, "mtime": 1752131906020, "results": "135", "hashOfConfig": "130"}, {"size": 730, "mtime": 1752831946495, "results": "136", "hashOfConfig": "130"}, {"size": 526, "mtime": 1742548499197, "results": "137", "hashOfConfig": "130"}, {"size": 872, "mtime": 1742548499291, "results": "138", "hashOfConfig": "130"}, {"size": 10886, "mtime": 1753077436325, "results": "139", "hashOfConfig": "130"}, {"size": 3219, "mtime": 1744630761185, "results": "140", "hashOfConfig": "130"}, {"size": 636, "mtime": 1752839122518, "results": "141", "hashOfConfig": "130"}, {"size": 193, "mtime": 1742548446242, "results": "142", "hashOfConfig": "130"}, {"size": 21079, "mtime": 1752839122534, "results": "143", "hashOfConfig": "130"}, {"size": 925, "mtime": 1742548499291, "results": "144", "hashOfConfig": "130"}, {"size": 2679, "mtime": 1749624151084, "results": "145", "hashOfConfig": "130"}, {"size": 11574, "mtime": 1752839122518, "results": "146", "hashOfConfig": "130"}, {"size": 22209, "mtime": 1742548446227, "results": "147", "hashOfConfig": "130"}, {"size": 34543, "mtime": 1752839122534, "results": "148", "hashOfConfig": "130"}, {"size": 999, "mtime": 1742548499244, "results": "149", "hashOfConfig": "130"}, {"size": 191, "mtime": 1742548446273, "results": "150", "hashOfConfig": "130"}, {"size": 153, "mtime": 1742548446273, "results": "151", "hashOfConfig": "130"}, {"size": 5719, "mtime": 1752831950345, "results": "152", "hashOfConfig": "130"}, {"size": 1269, "mtime": 1742548499244, "results": "153", "hashOfConfig": "130"}, {"size": 3373, "mtime": 1752131906083, "results": "154", "hashOfConfig": "130"}, {"size": 6117, "mtime": 1752839123278, "results": "155", "hashOfConfig": "130"}, {"size": 5568, "mtime": 1753077436323, "results": "156", "hashOfConfig": "130"}, {"size": 41902, "mtime": 1752839123868, "results": "157", "hashOfConfig": "130"}, {"size": 3285, "mtime": 1752839122596, "results": "158", "hashOfConfig": "130"}, {"size": 6466, "mtime": 1742548446258, "results": "159", "hashOfConfig": "130"}, {"size": 11554, "mtime": 1742548499213, "results": "160", "hashOfConfig": "130"}, {"size": 21131, "mtime": 1742548499213, "results": "161", "hashOfConfig": "130"}, {"size": 33676, "mtime": 1752839122627, "results": "162", "hashOfConfig": "130"}, {"size": 643, "mtime": 1742548499166, "results": "163", "hashOfConfig": "130"}, {"size": 16637, "mtime": 1752839124483, "results": "164", "hashOfConfig": "130"}, {"size": 18108, "mtime": 1752839122565, "results": "165", "hashOfConfig": "130"}, {"size": 16924, "mtime": 1752839122549, "results": "166", "hashOfConfig": "130"}, {"size": 15689, "mtime": 1752839124246, "results": "167", "hashOfConfig": "130"}, {"size": 15650, "mtime": 1752839122627, "results": "168", "hashOfConfig": "130"}, {"size": 16881, "mtime": 1752839122565, "results": "169", "hashOfConfig": "130"}, {"size": 623, "mtime": 1742548499260, "results": "170", "hashOfConfig": "130"}, {"size": 520, "mtime": 1752148863346, "results": "171", "hashOfConfig": "130"}, {"size": 12988, "mtime": 1752839122549, "results": "172", "hashOfConfig": "130"}, {"size": 22785, "mtime": 1753077436309, "results": "173", "hashOfConfig": "130"}, {"size": 9054, "mtime": 1752839122549, "results": "174", "hashOfConfig": "130"}, {"size": 19654, "mtime": 1753077436307, "results": "175", "hashOfConfig": "130"}, {"size": 66659, "mtime": 1752839127221, "results": "176", "hashOfConfig": "130"}, {"size": 1063, "mtime": 1742548499197, "results": "177", "hashOfConfig": "130"}, {"size": 10319, "mtime": 1750748792820, "results": "178", "hashOfConfig": "130"}, {"size": 15795, "mtime": 1752839122627, "results": "179", "hashOfConfig": "130"}, {"size": 12243, "mtime": 1742548499197, "results": "180", "hashOfConfig": "130"}, {"size": 13559, "mtime": 1750757275857, "results": "181", "hashOfConfig": "130"}, {"size": 746, "mtime": 1742548499166, "results": "182", "hashOfConfig": "130"}, {"size": 7880, "mtime": 1742548499307, "results": "183", "hashOfConfig": "130"}, {"size": 10385, "mtime": 1752839127253, "results": "184", "hashOfConfig": "130"}, {"size": 435, "mtime": 1742548446242, "results": "185", "hashOfConfig": "130"}, {"size": 3974, "mtime": 1752839127268, "results": "186", "hashOfConfig": "130"}, {"size": 1501, "mtime": 1742548499150, "results": "187", "hashOfConfig": "130"}, {"size": 13687, "mtime": 1752839122534, "results": "188", "hashOfConfig": "130"}, {"size": 1027, "mtime": 1742548446317, "results": "189", "hashOfConfig": "130"}, {"size": 559, "mtime": 1742548499213, "results": "190", "hashOfConfig": "130"}, {"size": 15685, "mtime": 1752839123496, "results": "191", "hashOfConfig": "130"}, {"size": 14035, "mtime": 1753077436303, "results": "192", "hashOfConfig": "130"}, {"size": 1260, "mtime": 1742548499244, "results": "193", "hashOfConfig": "130"}, {"size": 25572, "mtime": 1752839122596, "results": "194", "hashOfConfig": "130"}, {"size": 24298, "mtime": 1752839122565, "results": "195", "hashOfConfig": "130"}, {"size": 18513, "mtime": 1752839122565, "results": "196", "hashOfConfig": "130"}, {"size": 23014, "mtime": 1752839124780, "results": "197", "hashOfConfig": "130"}, {"size": 49294, "mtime": 1752839126833, "results": "198", "hashOfConfig": "130"}, {"size": 3023, "mtime": 1752839122581, "results": "199", "hashOfConfig": "130"}, {"size": 9230, "mtime": 1753340131768, "results": "200", "hashOfConfig": "130"}, {"size": 3461, "mtime": 1752839127299, "results": "201", "hashOfConfig": "130"}, {"size": 8059, "mtime": 1752839122690, "results": "202", "hashOfConfig": "130"}, {"size": 7028, "mtime": 1742548446258, "results": "203", "hashOfConfig": "130"}, {"size": 18058, "mtime": 1752839122596, "results": "204", "hashOfConfig": "130"}, {"size": 9869, "mtime": 1742548446258, "results": "205", "hashOfConfig": "130"}, {"size": 12153, "mtime": 1752839127299, "results": "206", "hashOfConfig": "130"}, {"size": 4881, "mtime": 1742548446242, "results": "207", "hashOfConfig": "130"}, {"size": 6872, "mtime": 1742548499213, "results": "208", "hashOfConfig": "130"}, {"size": 405227, "mtime": 1742548446323, "results": "209", "hashOfConfig": "130"}, {"size": 1020, "mtime": 1742548499197, "results": "210", "hashOfConfig": "130"}, {"size": 37775, "mtime": 1752839122627, "results": "211", "hashOfConfig": "130"}, {"size": 13156, "mtime": 1752839122581, "results": "212", "hashOfConfig": "130"}, {"size": 10816, "mtime": 1752839122643, "results": "213", "hashOfConfig": "130"}, {"size": 13628, "mtime": 1752831950134, "results": "214", "hashOfConfig": "130"}, {"size": 883, "mtime": 1742548499182, "results": "215", "hashOfConfig": "130"}, {"size": 2421, "mtime": 1742548499213, "results": "216", "hashOfConfig": "130"}, {"size": 336, "mtime": 1742548499197, "results": "217", "hashOfConfig": "130"}, {"size": 1916, "mtime": 1752839122643, "results": "218", "hashOfConfig": "130"}, {"size": 9815, "mtime": 1752839122565, "results": "219", "hashOfConfig": "130"}, {"size": 2620, "mtime": 1752839122581, "results": "220", "hashOfConfig": "130"}, {"size": 2765, "mtime": 1752831946502, "results": "221", "hashOfConfig": "130"}, {"size": 4518, "mtime": 1752839122565, "results": "222", "hashOfConfig": "130"}, {"size": 339, "mtime": 1752839127346, "results": "223", "hashOfConfig": "130"}, {"size": 2499, "mtime": 1747915360725, "results": "224", "hashOfConfig": "130"}, {"size": 5667, "mtime": 1748948053337, "results": "225", "hashOfConfig": "130"}, {"size": 1266, "mtime": 1742548499291, "results": "226", "hashOfConfig": "130"}, {"size": 1248, "mtime": 1742548499291, "results": "227", "hashOfConfig": "130"}, {"size": 1566, "mtime": 1742548499307, "results": "228", "hashOfConfig": "130"}, {"size": 12472, "mtime": 1752839122534, "results": "229", "hashOfConfig": "130"}, {"size": 5304, "mtime": 1752839122534, "results": "230", "hashOfConfig": "130"}, {"size": 1971, "mtime": 1742548499307, "results": "231", "hashOfConfig": "130"}, {"size": 2561, "mtime": 1742548446242, "results": "232", "hashOfConfig": "130"}, {"size": 3220, "mtime": 1752131906104, "results": "233", "hashOfConfig": "130"}, {"size": 2343, "mtime": 1742548499291, "results": "234", "hashOfConfig": "130"}, {"size": 6070, "mtime": 1748943304967, "results": "235", "hashOfConfig": "130"}, {"size": 23162, "mtime": 1752839126047, "results": "236", "hashOfConfig": "130"}, {"size": 19057, "mtime": 1752839126362, "results": "237", "hashOfConfig": "130"}, {"size": 6298, "mtime": 1742548499275, "results": "238", "hashOfConfig": "130"}, {"size": 1743, "mtime": 1742548499260, "results": "239", "hashOfConfig": "130"}, {"size": 103561, "mtime": 1742548446273, "results": "240", "hashOfConfig": "130"}, {"size": 1275, "mtime": 1748436448770, "results": "241", "hashOfConfig": "130"}, {"size": 3171, "mtime": 1752839127028, "results": "242", "hashOfConfig": "130"}, {"size": 3228, "mtime": 1752839126518, "results": "243", "hashOfConfig": "130"}, {"size": 3328, "mtime": 1752839127112, "results": "244", "hashOfConfig": "130"}, {"size": 2568, "mtime": 1752839125229, "results": "245", "hashOfConfig": "130"}, {"size": 767, "mtime": 1742548499291, "results": "246", "hashOfConfig": "130"}, {"size": 4371, "mtime": 1752831948941, "results": "247", "hashOfConfig": "130"}, {"size": 9584, "mtime": 1742548499213, "results": "248", "hashOfConfig": "130"}, {"size": 6105, "mtime": 1742548499260, "results": "249", "hashOfConfig": "130"}, {"size": 3528, "mtime": 1742548499166, "results": "250", "hashOfConfig": "130"}, {"size": 27045, "mtime": 1753077436320, "results": "251", "hashOfConfig": "130"}, {"size": 7038, "mtime": 1752831950128, "results": "252", "hashOfConfig": "130"}, {"size": 2502, "mtime": 1752844714979, "results": "253", "hashOfConfig": "130"}, {"size": 4346, "mtime": 1752844736208, "results": "254", "hashOfConfig": "130"}, {"size": 4594, "mtime": 1752845769016, "results": "255", "hashOfConfig": "130"}, {"size": 75186, "mtime": 1753350133544, "results": "256", "hashOfConfig": "130"}, {"size": 2910, "mtime": 1753072658211, "results": "257", "hashOfConfig": "130"}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vegnqt", {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 72, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 34, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 67, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 37, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 88, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 71, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\index.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\reportWebVitals.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\App.tsx", ["642", "643", "644", "645", "646", "647", "648", "649", "650", "651"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\AuthProvider.tsx", ["652", "653", "654", "655", "656"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\SnackbarContext.tsx", ["657", "658", "659", "660", "661", "662", "663", "664", "665"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\ExtensionContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\RtlContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\layout\\Layout.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ExpirelinkService.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\Routings.tsx", ["666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\APIService.tsx", ["678"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountList.tsx", ["679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\Callback.tsx", ["711", "712", "713", "714", "715", "716"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\routing\\ProtectedRoute.tsx", ["717", "718", "719", "720", "721", "722", "723", "724"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountCreate.tsx", ["725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\RegistrationPage\\RegistrationPage.tsx", ["746", "747"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\AdminMenu.tsx", ["748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\DomainSettings.tsx", ["802", "803"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\NotificationSettings.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\ThemeSettings.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\RightSettings.tsx", ["804", "805"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\BillingSettings.tsx", ["806", "807"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\UnInstall.tsx", ["808", "809", "810", "811", "812", "813"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\InstallSettings.tsx", ["814", "815", "816", "817", "818", "819", "820", "821"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\Settings.tsx", ["822", "823", "824", "825", "826", "827", "828", "829", "830", "831"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\TeamSettings.tsx", ["832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\Builder.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideList.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AdminList.tsx", ["872", "873", "874", "875"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AdminPage.tsx", ["876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationList.tsx", ["889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\audience\\Audience.tsx", ["961", "962"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\tours\\Tours.tsx", ["963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\banners\\Banners.tsx", ["997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\announcements\\Announcements.tsx", ["1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\tooltips\\Tooltips.tsx", ["1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\hotspots\\Hotspots.tsx", ["1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\checklists\\Checklists.tsx", ["1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\surveys\\Survey.tsx", ["1126"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\Dashboard.tsx", ["1127"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\ScriptHistory.tsx", ["1128", "1129", "1130", "1131", "1132"], ["1133"], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\Scripts.tsx", [], ["1134"], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\ScriptHistoryViewer.tsx", ["1135", "1136"], ["1137"], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\agents\\Agentslist.tsx", ["1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\webappsettingspage\\WebAppSettings.tsx", ["1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Expiredlink.tsx", ["1233", "1234"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Superadminloginpage.tsx", ["1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\ResetPassword.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\Forgotpassword.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\login\\login.tsx", ["1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\OrganizationService.ts", ["1285", "1286", "1287", "1288", "1289"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\AccountService.tsx", ["1290", "1291"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\sidemenustate.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\MultilingualService.ts", ["1292", "1293"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountsColumnMenu.tsx", ["1294"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountEdit.tsx", ["1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\Signup.tsx", ["1319", "1320"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\pagewrapper.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\ProfileSettings.tsx", ["1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\sideMenu.tsx", ["1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\AlertSettings.tsx", ["1365", "1366"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\fileManagement\\FileList.tsx", ["1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auditLog\\SuperAdminAuditLogList.tsx", ["1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\auditLog\\AuditLogList.tsx", ["1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\training\\Training.tsx", ["1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserList.tsx", ["1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Home.tsx", ["1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\GuideService.tsx", ["1581", "1582"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\UserRoleService.ts", ["1583"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\settings\\Filterpopup.tsx", ["1584"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideEdit.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideCreate.tsx", ["1585", "1586"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\guide\\GuideTable.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\UserService.ts", ["1587", "1588", "1589", "1590"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Grid.tsx", ["1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\AddAdmin.tsx", ["1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\timezones.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\LanguageContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\Multilingual.tsx", ["1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\feedback\\ShareFeedbackPopup.tsx", ["1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\roles\\RolePopup.tsx", ["1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationEdit.tsx", ["1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\TimeZoneConversion.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationCustomColumnMenu.tsx", ["1745", "1746", "1747", "1748", "1749", "1750"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\multilingual\\i18n.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\roles\\RoleDeletePopup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CreateNewGuidePopup.tsx", ["1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ExtensionRequiredPopup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\assets\\icons\\icons.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\CloneGuidePopup.tsx", ["1761", "1762"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\utils\\openGuideInBuilder.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ScriptService.ts", ["1763"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\SystemPromtServices.tsx", ["1764"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ForgotPasswordService.tsx", ["1765", "1766"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\LoginService.tsx", ["1767", "1768", "1769"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ResetpasswordService.tsx", ["1770", "1771"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\changepassword.tsx", ["1772", "1773"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\account\\AccountCustomColumnMenuUserItem.tsx", ["1774"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\ProfileSettingPageService.tsx", ["1775", "1776", "1777"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Popup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\FileManagementService.tsx", ["1778", "1779"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\AuditLogServices.tsx", ["1780", "1781"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\TrainingService.tsx", ["1782", "1783", "1784", "1785"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCreate.tsx", ["1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserEdit.tsx", ["1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserPasswordReset.tsx", ["1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCustomColumnMenu.tsx", ["1862"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\orgData.js", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\models\\Training.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserUnblock.tsx", ["1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserEnable.tsx", ["1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\Userdisable.tsx", ["1915", "1916", "1917", "1918", "1919", "1920", "1921", "1922", "1923", "1924", "1925", "1926", "1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939", "1940"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserBlock.tsx", ["1941", "1942", "1943", "1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956", "1957", "1958", "1959", "1960", "1961", "1962", "1963", "1964", "1965", "1966"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\services\\FeedbackService.tsx", ["1967"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\feedback\\FeedbackConfirmPopup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\OrganizationcustomcolumnMenuItem.tsx", ["1968", "1969", "1970", "1971", "1972", "1973"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\user\\UserCustomColumnMenuUserItem.tsx", ["1974"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\adminMenu\\logoutpopup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\AnalyticsDashboard.tsx", ["1975"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\organization\\EditSubscription.tsx", ["1976"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\Card.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ModernButton.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\common\\ModernDataGrid.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\ModernDashboard.tsx", ["1977", "1978", "1979", "1980"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptWeb\\src\\components\\dashboard\\ChartPlaceholder.tsx", [], [], {"ruleId": "1981", "severity": 1, "message": "1982", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "1985", "line": 2, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "1986", "line": 10, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "1987", "line": 15, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 26, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1989", "line": 26, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "1990", "line": 28, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "1991", "line": 31, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "1992", "line": 31, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 46}, {"ruleId": "1993", "severity": 1, "message": "1994", "line": 85, "column": 6, "nodeType": "1995", "endLine": 85, "endColumn": 21, "suggestions": "1996"}, {"ruleId": "1981", "severity": 1, "message": "1997", "line": 2, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "1998", "line": 8, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 10, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2000", "line": 54, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 54, "endColumn": 31}, {"ruleId": "1993", "severity": 1, "message": "2001", "line": 87, "column": 5, "nodeType": "1995", "endLine": 87, "endColumn": 16, "suggestions": "2002"}, {"ruleId": "1981", "severity": 1, "message": "2003", "line": 3, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2004", "line": 4, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2005", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2006", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2007", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2008", "line": 8, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2009", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2010", "line": 10, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2011", "line": 29, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 29, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2012", "line": 3, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2013", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2014", "line": 4, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2015", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2016", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2017", "line": 9, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2018", "line": 12, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2019", "line": 15, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2020", "line": 41, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2021", "line": 62, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2022", "line": 62, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2023", "line": 63, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 5, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2025", "line": 5, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2026", "line": 14, "column": 34, "nodeType": "1983", "messageId": "1984", "endLine": 14, "endColumn": 50}, {"ruleId": "1981", "severity": 1, "message": "2027", "line": 16, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2028", "line": 17, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2029", "line": 20, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2030", "line": 21, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2031", "line": 26, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2032", "line": 27, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2033", "line": 33, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2034", "line": 38, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 38, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2035", "line": 39, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 39, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2036", "line": 46, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 46, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2037", "line": 68, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 68, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2038", "line": 74, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 74, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2039", "line": 74, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 74, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 75, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 75, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2041", "line": 78, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 78, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 80, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 80, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2043", "line": 85, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 85, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2044", "line": 90, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 90, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2045", "line": 112, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 112, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2046", "line": 115, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 115, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2047", "line": 115, "column": 31, "nodeType": "1983", "messageId": "1984", "endLine": 115, "endColumn": 54}, {"ruleId": "1993", "severity": 1, "message": "2048", "line": 142, "column": 5, "nodeType": "1995", "endLine": 142, "endColumn": 22, "suggestions": "2049"}, {"ruleId": "1981", "severity": 1, "message": "2050", "line": 167, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 167, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2051", "line": 168, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 168, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2052", "line": 232, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 232, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2053", "line": 234, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 234, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2054", "line": 285, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 285, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2055", "line": 296, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 296, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2056", "line": 373, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 373, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2057", "line": 376, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 376, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2058", "line": 2, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2059", "line": 4, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2060", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2061", "line": 6, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2021", "line": 12, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 15}, {"ruleId": "1993", "severity": 1, "message": "2062", "line": 22, "column": 6, "nodeType": "1995", "endLine": 22, "endColumn": 8, "suggestions": "2063"}, {"ruleId": "1981", "severity": 1, "message": "2064", "line": 6, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 8, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2066", "line": 17, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 37}, {"ruleId": "1993", "severity": 1, "message": "2067", "line": 52, "column": 5, "nodeType": "1995", "endLine": 52, "endColumn": 7, "suggestions": "2068"}, {"ruleId": "2069", "severity": 1, "message": "2070", "line": 55, "column": 50, "nodeType": "2071", "messageId": "2072", "endLine": 55, "endColumn": 52}, {"ruleId": "2069", "severity": 1, "message": "2070", "line": 55, "column": 88, "nodeType": "2071", "messageId": "2072", "endLine": 55, "endColumn": 90}, {"ruleId": "2073", "severity": 1, "message": "2074", "line": 67, "column": 79, "nodeType": "2075", "messageId": "2076", "endLine": 67, "endColumn": 81}, {"ruleId": "2073", "severity": 1, "message": "2074", "line": 69, "column": 81, "nodeType": "2075", "messageId": "2076", "endLine": 69, "endColumn": 83}, {"ruleId": "1981", "severity": 1, "message": "2077", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 5, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 5, "column": 29, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 5, "column": 47, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 57}, {"ruleId": "1981", "severity": 1, "message": "2081", "line": 5, "column": 95, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 101}, {"ruleId": "1981", "severity": 1, "message": "2026", "line": 5, "column": 103, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 119}, {"ruleId": "1981", "severity": 1, "message": "2082", "line": 5, "column": 121, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 126}, {"ruleId": "1981", "severity": 1, "message": "2083", "line": 5, "column": 128, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 138}, {"ruleId": "1981", "severity": 1, "message": "2084", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 31, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 32, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2086", "line": 33, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2087", "line": 34, "column": 14, "nodeType": "1983", "messageId": "1984", "endLine": 34, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2088", "line": 35, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 35, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2089", "line": 42, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 42, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2090", "line": 67, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 67, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2091", "line": 71, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2092", "line": 75, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 75, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2093", "line": 78, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 78, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2094", "line": 230, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 230, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2095", "line": 238, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 238, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2096", "line": 34, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 34, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2097", "line": 35, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 35, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2098", "line": 5, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2099", "line": 6, "column": 39, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 45}, {"ruleId": "1981", "severity": 1, "message": "2100", "line": 10, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2101", "line": 11, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2102", "line": 12, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2103", "line": 13, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2104", "line": 14, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 14, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 15, "column": 51, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2105", "line": 15, "column": 73, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 77}, {"ruleId": "1981", "severity": 1, "message": "2106", "line": 17, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2107", "line": 18, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2108", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2109", "line": 25, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2110", "line": 25, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 80}, {"ruleId": "1981", "severity": 1, "message": "2111", "line": 26, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2112", "line": 27, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2113", "line": 28, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2114", "line": 29, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 29, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2115", "line": 31, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2116", "line": 36, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2117", "line": 43, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 43, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 52, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 52, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2118", "line": 56, "column": 43, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 50}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 103, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 103, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2021", "line": 108, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 108, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2119", "line": 114, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 114, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2120", "line": 114, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 114, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2121", "line": 116, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 116, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 117, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 117, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2122", "line": 118, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 118, "endColumn": 26}, {"ruleId": "1993", "severity": 1, "message": "2067", "line": 167, "column": 5, "nodeType": "1995", "endLine": 167, "endColumn": 22, "suggestions": "2123"}, {"ruleId": "1993", "severity": 1, "message": "2124", "line": 195, "column": 7, "nodeType": "1995", "endLine": 195, "endColumn": 9, "suggestions": "2125"}, {"ruleId": "2073", "severity": 1, "message": "2074", "line": 199, "column": 41, "nodeType": "2075", "messageId": "2076", "endLine": 199, "endColumn": 43}, {"ruleId": "1993", "severity": 1, "message": "2067", "line": 223, "column": 5, "nodeType": "1995", "endLine": 223, "endColumn": 7, "suggestions": "2126"}, {"ruleId": "1981", "severity": 1, "message": "2127", "line": 255, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 255, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2128", "line": 261, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 261, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2129", "line": 313, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 313, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2130", "line": 329, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 329, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2131", "line": 335, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 335, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2132", "line": 341, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 341, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2133", "line": 347, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 347, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2134", "line": 353, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 353, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2135", "line": 359, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 359, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2136", "line": 365, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 365, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2137", "line": 371, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 371, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2138", "line": 377, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 377, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2139", "line": 411, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 411, "endColumn": 29}, {"ruleId": "1993", "severity": 1, "message": "2140", "line": 427, "column": 7, "nodeType": "1995", "endLine": 427, "endColumn": 17, "suggestions": "2141"}, {"ruleId": "1981", "severity": 1, "message": "2142", "line": 432, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 432, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2143", "line": 442, "column": 13, "nodeType": "1983", "messageId": "1984", "endLine": 442, "endColumn": 19}, {"ruleId": "2144", "severity": 1, "message": "2145", "line": 490, "column": 8, "nodeType": "2146", "endLine": 493, "endColumn": 9}, {"ruleId": "2144", "severity": 1, "message": "2145", "line": 496, "column": 8, "nodeType": "2146", "endLine": 499, "endColumn": 9}, {"ruleId": "2144", "severity": 1, "message": "2145", "line": 503, "column": 8, "nodeType": "2146", "endLine": 506, "endColumn": 9}, {"ruleId": "2073", "severity": 1, "message": "2074", "line": 802, "column": 34, "nodeType": "2075", "messageId": "2076", "endLine": 802, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 11, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 11, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 9, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 21}, {"ruleId": "2148", "severity": 1, "message": "2149", "line": 51, "column": 9, "nodeType": "2146", "endLine": 57, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 11, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 11, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2150", "line": 2, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2151", "line": 2, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 47}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 2, "column": 49, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 55}, {"ruleId": "1981", "severity": 1, "message": "2153", "line": 2, "column": 57, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 2, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 73}, {"ruleId": "1981", "severity": 1, "message": "2154", "line": 2, "column": 75, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 89}, {"ruleId": "1981", "severity": 1, "message": "2155", "line": 3, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2156", "line": 3, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2157", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2058", "line": 9, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 12, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 12, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2022", "line": 16, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2158", "line": 25, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2159", "line": 3, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2160", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2161", "line": 4, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 35}, {"ruleId": "1981", "severity": 1, "message": "2162", "line": 4, "column": 37, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2163", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2164", "line": 72, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 73, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 73, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2165", "line": 75, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 75, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2021", "line": 77, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 77, "endColumn": 14}, {"ruleId": "1993", "severity": 1, "message": "2067", "line": 108, "column": 5, "nodeType": "1995", "endLine": 108, "endColumn": 7, "suggestions": "2166"}, {"ruleId": "1981", "severity": 1, "message": "2151", "line": 4, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 4, "column": 72, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 82}, {"ruleId": "1981", "severity": 1, "message": "2167", "line": 4, "column": 142, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 154}, {"ruleId": "1981", "severity": 1, "message": "2168", "line": 4, "column": 173, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 179}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 4, "column": 189, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 194}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 5, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2170", "line": 6, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2171", "line": 7, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2172", "line": 8, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2173", "line": 10, "column": 53, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 72}, {"ruleId": "1981", "severity": 1, "message": "2032", "line": 11, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2174", "line": 12, "column": 122, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 136}, {"ruleId": "1981", "severity": 1, "message": "2175", "line": 13, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2150", "line": 19, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2176", "line": 24, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2177", "line": 27, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2178", "line": 32, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2179", "line": 41, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2180", "line": 41, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 43}, {"ruleId": "1981", "severity": 1, "message": "2181", "line": 51, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2182", "line": 62, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2183", "line": 63, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2184", "line": 64, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 64, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2185", "line": 103, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 103, "endColumn": 27}, {"ruleId": "1993", "severity": 1, "message": "2186", "line": 122, "column": 8, "nodeType": "1995", "endLine": 122, "endColumn": 32, "suggestions": "2187"}, {"ruleId": "1993", "severity": 1, "message": "2186", "line": 148, "column": 8, "nodeType": "1995", "endLine": 148, "endColumn": 24, "suggestions": "2188"}, {"ruleId": "1993", "severity": 1, "message": "2189", "line": 173, "column": 8, "nodeType": "1995", "endLine": 173, "endColumn": 10, "suggestions": "2190"}, {"ruleId": "1981", "severity": 1, "message": "2191", "line": 195, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 195, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2192", "line": 229, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 229, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2191", "line": 239, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 239, "endColumn": 38}, {"ruleId": "1993", "severity": 1, "message": "2193", "line": 274, "column": 8, "nodeType": "1995", "endLine": 274, "endColumn": 42, "suggestions": "2194"}, {"ruleId": "1981", "severity": 1, "message": "2195", "line": 354, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 354, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2196", "line": 367, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 367, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2197", "line": 368, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 368, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2198", "line": 369, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 369, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2191", "line": 489, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 489, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2191", "line": 544, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 544, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2199", "line": 576, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 576, "endColumn": 23}, {"ruleId": "1993", "severity": 1, "message": "2200", "line": 592, "column": 12, "nodeType": "1995", "endLine": 592, "endColumn": 44, "suggestions": "2201"}, {"ruleId": "1981", "severity": 1, "message": "2202", "line": 618, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 618, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2203", "line": 18, "column": 29, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 48}, {"ruleId": "1981", "severity": 1, "message": "2204", "line": 32, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2205", "line": 171, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 171, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2206", "line": 241, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 241, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2013", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2207", "line": 6, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2155", "line": 9, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2208", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 23, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2209", "line": 35, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 35, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2210", "line": 36, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2211", "line": 37, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 37, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2212", "line": 37, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 37, "endColumn": 45}, {"ruleId": "1981", "severity": 1, "message": "2213", "line": 41, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2214", "line": 42, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 42, "endColumn": 25}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 131, "column": 26, "nodeType": "2075", "messageId": "2076", "endLine": 131, "endColumn": 28}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 208, "column": 24, "nodeType": "2075", "messageId": "2076", "endLine": 208, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2216", "line": 2, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2217", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2218", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2150", "line": 9, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2025", "line": 15, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 20, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2220", "line": 20, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 20, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2221", "line": 20, "column": 73, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 82}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 20, "column": 84, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 90}, {"ruleId": "1981", "severity": 1, "message": "2222", "line": 20, "column": 92, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 95}, {"ruleId": "1981", "severity": 1, "message": "2027", "line": 22, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2028", "line": 23, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2223", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2031", "line": 26, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2029", "line": 27, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 28, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2224", "line": 34, "column": 114, "nodeType": "1983", "messageId": "1984", "endLine": 34, "endColumn": 134}, {"ruleId": "1981", "severity": 1, "message": "2225", "line": 36, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2226", "line": 38, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 38, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2036", "line": 49, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2227", "line": 55, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 55, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2228", "line": 55, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 55, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2229", "line": 57, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 57, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2230", "line": 57, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 57, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2231", "line": 60, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 60, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2038", "line": 66, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 66, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2204", "line": 68, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 68, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2232", "line": 68, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 68, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2233", "line": 70, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2234", "line": 70, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2235", "line": 71, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2236", "line": 71, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2237", "line": 72, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2238", "line": 72, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2239", "line": 74, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 74, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2240", "line": 78, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 78, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2241", "line": 78, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 78, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2242", "line": 79, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 79, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2243", "line": 81, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 81, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2244", "line": 83, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 83, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2245", "line": 99, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 99, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2246", "line": 101, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 101, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2247", "line": 114, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 114, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2248", "line": 115, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 115, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2249", "line": 116, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 116, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2250", "line": 116, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 116, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2251", "line": 122, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 122, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2252", "line": 144, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 144, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2253", "line": 275, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 275, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2254", "line": 277, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 277, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2052", "line": 280, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 280, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2053", "line": 282, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 282, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2255", "line": 284, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 284, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2256", "line": 288, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 288, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2257", "line": 288, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 288, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2258", "line": 290, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 290, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2259", "line": 290, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 290, "endColumn": 36}, {"ruleId": "1993", "severity": 1, "message": "2260", "line": 315, "column": 5, "nodeType": "1995", "endLine": 315, "endColumn": 22, "suggestions": "2261"}, {"ruleId": "1993", "severity": 1, "message": "2262", "line": 334, "column": 4, "nodeType": "1995", "endLine": 334, "endColumn": 15, "suggestions": "2263"}, {"ruleId": "1981", "severity": 1, "message": "2264", "line": 361, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 361, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2211", "line": 381, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 381, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2054", "line": 396, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 396, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2038", "line": 397, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 397, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2265", "line": 399, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 399, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2055", "line": 407, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 407, "endColumn": 33}, {"ruleId": "1993", "severity": 1, "message": "2266", "line": 540, "column": 5, "nodeType": "1995", "endLine": 540, "endColumn": 7, "suggestions": "2267"}, {"ruleId": "1981", "severity": 1, "message": "2268", "line": 582, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 582, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2269", "line": 690, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 690, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2270", "line": 697, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 697, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2271", "line": 699, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 699, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2272", "line": 3, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 11, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 2, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2168", "line": 2, "column": 40, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2172", "line": 2, "column": 48, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 2, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 76}, {"ruleId": "1981", "severity": 1, "message": "2170", "line": 2, "column": 78, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 89}, {"ruleId": "1981", "severity": 1, "message": "2171", "line": 2, "column": 114, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 131}, {"ruleId": "1981", "severity": 1, "message": "2273", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2009", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2274", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2275", "line": 10, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2276", "line": 17, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2277", "line": 23, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 56, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 56, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2278", "line": 57, "column": 14, "nodeType": "1983", "messageId": "1984", "endLine": 57, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 58, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 58, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2279", "line": 59, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 59, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 59, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 59, "endColumn": 43}, {"ruleId": "1981", "severity": 1, "message": "2088", "line": 60, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 60, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2280", "line": 60, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 60, "endColumn": 35}, {"ruleId": "1981", "severity": 1, "message": "2281", "line": 61, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 61, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2239", "line": 61, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 61, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2282", "line": 62, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 62, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2284", "line": 63, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2086", "line": 63, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2285", "line": 64, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 64, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2087", "line": 64, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 64, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2286", "line": 72, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2287", "line": 72, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2288", "line": 93, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 93, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 94, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 94, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2289", "line": 94, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 94, "endColumn": 29}, {"ruleId": "1993", "severity": 1, "message": "2290", "line": 164, "column": 6, "nodeType": "1995", "endLine": 164, "endColumn": 44, "suggestions": "2291"}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 2, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2168", "line": 2, "column": 40, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2172", "line": 2, "column": 48, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 2, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 76}, {"ruleId": "1981", "severity": 1, "message": "2170", "line": 2, "column": 78, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 89}, {"ruleId": "1981", "severity": 1, "message": "2171", "line": 2, "column": 114, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 131}, {"ruleId": "1981", "severity": 1, "message": "2273", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2009", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2274", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2275", "line": 11, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2276", "line": 18, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2277", "line": 25, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 57, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 57, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 57, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 57, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2292", "line": 59, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 59, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2293", "line": 59, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 59, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2278", "line": 60, "column": 14, "nodeType": "1983", "messageId": "1984", "endLine": 60, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 61, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 61, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2279", "line": 62, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 62, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 43}, {"ruleId": "1981", "severity": 1, "message": "2088", "line": 63, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2280", "line": 63, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 35}, {"ruleId": "1981", "severity": 1, "message": "2281", "line": 64, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 64, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2239", "line": 64, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 64, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2282", "line": 65, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 65, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 65, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 65, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2284", "line": 66, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 66, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2086", "line": 66, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 66, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2285", "line": 67, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 67, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2087", "line": 67, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 67, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2294", "line": 75, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 75, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2295", "line": 75, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 75, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2288", "line": 96, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 96, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 97, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 97, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2289", "line": 97, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 97, "endColumn": 29}, {"ruleId": "1993", "severity": 1, "message": "2296", "line": 102, "column": 6, "nodeType": "1995", "endLine": 102, "endColumn": 19, "suggestions": "2297"}, {"ruleId": "1981", "severity": 1, "message": "2298", "line": 158, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 158, "endColumn": 22}, {"ruleId": "1993", "severity": 1, "message": "2296", "line": 170, "column": 6, "nodeType": "1995", "endLine": 170, "endColumn": 44, "suggestions": "2299"}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 2, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2168", "line": 2, "column": 40, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2172", "line": 2, "column": 48, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 2, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 76}, {"ruleId": "1981", "severity": 1, "message": "2170", "line": 2, "column": 78, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 89}, {"ruleId": "1981", "severity": 1, "message": "2171", "line": 2, "column": 114, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 131}, {"ruleId": "1981", "severity": 1, "message": "2273", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2009", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2274", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2275", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2277", "line": 23, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2278", "line": 65, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 65, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2300", "line": 66, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 66, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 69, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 69, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2288", "line": 70, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 23}, {"ruleId": "1993", "severity": 1, "message": "2301", "line": 81, "column": 4, "nodeType": "1995", "endLine": 81, "endColumn": 17, "suggestions": "2302"}, {"ruleId": "1993", "severity": 1, "message": "2301", "line": 141, "column": 6, "nodeType": "1995", "endLine": 141, "endColumn": 44, "suggestions": "2303"}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 2, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2168", "line": 2, "column": 40, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2172", "line": 2, "column": 48, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 2, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 76}, {"ruleId": "1981", "severity": 1, "message": "2170", "line": 2, "column": 78, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 89}, {"ruleId": "1981", "severity": 1, "message": "2171", "line": 2, "column": 114, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 131}, {"ruleId": "1981", "severity": 1, "message": "2273", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2009", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2274", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2275", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 33}, {"ruleId": "2304", "severity": 1, "message": "2305", "line": 27, "column": 11, "nodeType": "1983", "messageId": "2306", "endLine": 27, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2307", "line": 45, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 45, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2278", "line": 49, "column": 14, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2308", "line": 51, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2309", "line": 51, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2281", "line": 52, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 52, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2239", "line": 52, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 52, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2282", "line": 53, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 53, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 53, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 53, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2310", "line": 54, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 54, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2311", "line": 54, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 54, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 67, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 67, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2289", "line": 67, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 67, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2288", "line": 71, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2284", "line": 72, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2086", "line": 72, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2285", "line": 73, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 73, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2087", "line": 73, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 73, "endColumn": 21}, {"ruleId": "1993", "severity": 1, "message": "2312", "line": 156, "column": 6, "nodeType": "1995", "endLine": 156, "endColumn": 45, "suggestions": "2313"}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 2, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2168", "line": 2, "column": 40, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2172", "line": 2, "column": 48, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 2, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 76}, {"ruleId": "1981", "severity": 1, "message": "2170", "line": 2, "column": 78, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 89}, {"ruleId": "1981", "severity": 1, "message": "2171", "line": 2, "column": 114, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 131}, {"ruleId": "1981", "severity": 1, "message": "2273", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2009", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2274", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2275", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2307", "line": 43, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 43, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2278", "line": 47, "column": 14, "nodeType": "1983", "messageId": "1984", "endLine": 47, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2314", "line": 49, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2315", "line": 49, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2281", "line": 50, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 50, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2239", "line": 50, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 50, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2282", "line": 51, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 51, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2310", "line": 52, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 52, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2311", "line": 52, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 52, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 65, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 65, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2289", "line": 65, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 65, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2288", "line": 69, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 69, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2284", "line": 70, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2086", "line": 70, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2285", "line": 71, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2087", "line": 71, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 21}, {"ruleId": "1993", "severity": 1, "message": "2316", "line": 152, "column": 6, "nodeType": "1995", "endLine": 152, "endColumn": 45, "suggestions": "2317"}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 2, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2168", "line": 2, "column": 40, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2172", "line": 2, "column": 48, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 2, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 76}, {"ruleId": "1981", "severity": 1, "message": "2170", "line": 2, "column": 78, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 89}, {"ruleId": "1981", "severity": 1, "message": "2171", "line": 2, "column": 114, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 131}, {"ruleId": "1981", "severity": 1, "message": "2273", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2009", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2274", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2275", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2277", "line": 23, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2278", "line": 65, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 65, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2300", "line": 66, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 66, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 69, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 69, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2288", "line": 70, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 23}, {"ruleId": "1993", "severity": 1, "message": "2318", "line": 81, "column": 4, "nodeType": "1995", "endLine": 81, "endColumn": 17, "suggestions": "2319"}, {"ruleId": "1993", "severity": 1, "message": "2318", "line": 141, "column": 6, "nodeType": "1995", "endLine": 141, "endColumn": 44, "suggestions": "2320"}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 10, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 9, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2321", "line": 9, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2322", "line": 29, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 29, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2323", "line": 30, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 30, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2324", "line": 151, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 151, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2325", "line": 174, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 174, "endColumn": 28}, {"ruleId": "1993", "severity": 1, "message": "2326", "line": 71, "column": 6, "nodeType": "1995", "endLine": 71, "endColumn": 34, "suggestions": "2327", "suppressions": "2328"}, {"ruleId": "1993", "severity": 1, "message": "2329", "line": 204, "column": 6, "nodeType": "1995", "endLine": 204, "endColumn": 17, "suggestions": "2330", "suppressions": "2331"}, {"ruleId": "1981", "severity": 1, "message": "2332", "line": 14, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 14, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2333", "line": 19, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 17}, {"ruleId": "1993", "severity": 1, "message": "2334", "line": 54, "column": 6, "nodeType": "1995", "endLine": 54, "endColumn": 19, "suggestions": "2335", "suppressions": "2336"}, {"ruleId": "1981", "severity": 1, "message": "2337", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2025", "line": 5, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2173", "line": 12, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2026", "line": 15, "column": 34, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 50}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 15, "column": 52, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 62}, {"ruleId": "1981", "severity": 1, "message": "2151", "line": 15, "column": 86, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 95}, {"ruleId": "1981", "severity": 1, "message": "2338", "line": 15, "column": 117, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 128}, {"ruleId": "1981", "severity": 1, "message": "2339", "line": 18, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2027", "line": 19, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2028", "line": 20, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2029", "line": 23, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2030", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2340", "line": 25, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2177", "line": 27, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2341", "line": 28, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2342", "line": 28, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2343", "line": 28, "column": 53, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 68}, {"ruleId": "1981", "severity": 1, "message": "2031", "line": 31, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2032", "line": 32, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2344", "line": 33, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2033", "line": 38, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 38, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2154", "line": 39, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 39, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2345", "line": 40, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 40, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2346", "line": 41, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2034", "line": 43, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 43, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2035", "line": 44, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 44, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 47, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 47, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2348", "line": 48, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 48, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2349", "line": 49, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 80, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 80, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2038", "line": 86, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 86, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2039", "line": 86, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 86, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 87, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 87, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2350", "line": 88, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 88, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2351", "line": 88, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 88, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2352", "line": 89, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 89, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2353", "line": 89, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 89, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2041", "line": 90, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 90, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2354", "line": 90, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 90, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2211", "line": 91, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 91, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2212", "line": 91, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 91, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 92, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 92, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2355", "line": 94, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 94, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2356", "line": 95, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 95, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2043", "line": 97, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 97, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2357", "line": 98, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 98, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2088", "line": 101, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 101, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2044", "line": 102, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 102, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2358", "line": 106, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 106, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 107, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 107, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2359", "line": 108, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 108, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2360", "line": 117, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 117, "endColumn": 44}, {"ruleId": "1993", "severity": 1, "message": "2361", "line": 188, "column": 5, "nodeType": "1995", "endLine": 188, "endColumn": 32, "suggestions": "2362"}, {"ruleId": "1981", "severity": 1, "message": "2325", "line": 223, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 223, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2363", "line": 296, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 296, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2364", "line": 297, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 297, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2052", "line": 318, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 318, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2053", "line": 320, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 320, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2054", "line": 323, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 323, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2055", "line": 335, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 335, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2091", "line": 376, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 376, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2195", "line": 379, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 379, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2365", "line": 383, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 383, "endColumn": 26}, {"ruleId": "2073", "severity": 1, "message": "2074", "line": 447, "column": 21, "nodeType": "2075", "messageId": "2076", "endLine": 447, "endColumn": 23}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 495, "column": 31, "nodeType": "2075", "messageId": "2076", "endLine": 495, "endColumn": 33}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 495, "column": 57, "nodeType": "2075", "messageId": "2076", "endLine": 495, "endColumn": 59}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 497, "column": 34, "nodeType": "2075", "messageId": "2076", "endLine": 497, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2366", "line": 3, "column": 92, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 102}, {"ruleId": "1981", "severity": 1, "message": "2367", "line": 3, "column": 123, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 130}, {"ruleId": "1981", "severity": 1, "message": "2368", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2369", "line": 17, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2370", "line": 19, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2027", "line": 22, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2371", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2035", "line": 25, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2372", "line": 26, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2373", "line": 135, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 135, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2374", "line": 159, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 159, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2375", "line": 159, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 159, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2376", "line": 160, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 160, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2377", "line": 160, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 160, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2378", "line": 177, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 177, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2379", "line": 177, "column": 31, "nodeType": "1983", "messageId": "1984", "endLine": 177, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2380", "line": 178, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 178, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2381", "line": 178, "column": 31, "nodeType": "1983", "messageId": "1984", "endLine": 178, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2382", "line": 179, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 179, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2383", "line": 179, "column": 33, "nodeType": "1983", "messageId": "1984", "endLine": 179, "endColumn": 55}, {"ruleId": "1981", "severity": 1, "message": "2384", "line": 196, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 196, "endColumn": 24}, {"ruleId": "1993", "severity": 1, "message": "2385", "line": 240, "column": 8, "nodeType": "1995", "endLine": 240, "endColumn": 37, "suggestions": "2386"}, {"ruleId": "1981", "severity": 1, "message": "2387", "line": 314, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 314, "endColumn": 26}, {"ruleId": "1993", "severity": 1, "message": "2388", "line": 336, "column": 8, "nodeType": "1995", "endLine": 336, "endColumn": 18, "suggestions": "2389"}, {"ruleId": "1993", "severity": 1, "message": "2390", "line": 382, "column": 8, "nodeType": "1995", "endLine": 382, "endColumn": 24, "suggestions": "2391"}, {"ruleId": "2069", "severity": 1, "message": "2392", "line": 591, "column": 27, "nodeType": "2071", "messageId": "2072", "endLine": 591, "endColumn": 29}, {"ruleId": "2069", "severity": 1, "message": "2392", "line": 591, "column": 42, "nodeType": "2071", "messageId": "2072", "endLine": 591, "endColumn": 44}, {"ruleId": "1993", "severity": 1, "message": "2393", "line": 686, "column": 4, "nodeType": "1995", "endLine": 686, "endColumn": 11, "suggestions": "2394"}, {"ruleId": "1981", "severity": 1, "message": "2395", "line": 2, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2396", "line": 8, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2077", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2397", "line": 3, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2398", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2399", "line": 6, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2400", "line": 7, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2401", "line": 10, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2117", "line": 11, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2060", "line": 13, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2402", "line": 14, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 14, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2176", "line": 15, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "1997", "line": 18, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2403", "line": 23, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2021", "line": 25, "column": 13, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 25, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "1989", "line": 25, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2404", "line": 26, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2405", "line": 27, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2237", "line": 31, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2406", "line": 31, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2407", "line": 33, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2408", "line": 33, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 43}, {"ruleId": "1981", "severity": 1, "message": "2094", "line": 34, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 34, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2409", "line": 34, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 34, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2410", "line": 35, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 35, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2411", "line": 35, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 35, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2412", "line": 36, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2413", "line": 36, "column": 33, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 55}, {"ruleId": "1981", "severity": 1, "message": "1991", "line": 37, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 37, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2397", "line": 3, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2398", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2400", "line": 7, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2401", "line": 10, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2402", "line": 13, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "1997", "line": 16, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2414", "line": 24, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2415", "line": 29, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 29, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2405", "line": 30, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 30, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2237", "line": 51, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2406", "line": 51, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2407", "line": 53, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 53, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2094", "line": 54, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 54, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2410", "line": 55, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 55, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2411", "line": 55, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 55, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2412", "line": 56, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2413", "line": 56, "column": 33, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 55}, {"ruleId": "1981", "severity": 1, "message": "1991", "line": 57, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 57, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "1989", "line": 61, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 61, "endColumn": 31}, {"ruleId": "1993", "severity": 1, "message": "2062", "line": 79, "column": 4, "nodeType": "1995", "endLine": 79, "endColumn": 10, "suggestions": "2416"}, {"ruleId": "1993", "severity": 1, "message": "2417", "line": 138, "column": 8, "nodeType": "1995", "endLine": 138, "endColumn": 21, "suggestions": "2418"}, {"ruleId": "1993", "severity": 1, "message": "2419", "line": 163, "column": 26, "nodeType": "2420", "endLine": 163, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2421", "line": 2, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 39}, {"ruleId": "1981", "severity": 1, "message": "2422", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2014", "line": 5, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2423", "line": 10, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2424", "line": 11, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2421", "line": 4, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2423", "line": 19, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 1, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2423", "line": 3, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2425", "line": 2, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 48}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 4, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 35}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 7, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 9}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 8, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 10, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 11, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 12, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2084", "line": 17, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 19, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2428", "line": 36, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 42, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 42, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2429", "line": 45, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 45, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2430", "line": 46, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 46, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2431", "line": 47, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 47, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2086", "line": 48, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 48, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2087", "line": 49, "column": 14, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 51, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2021", "line": 53, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 53, "endColumn": 13}, {"ruleId": "1993", "severity": 1, "message": "2432", "line": 98, "column": 5, "nodeType": "1995", "endLine": 98, "endColumn": 20, "suggestions": "2433"}, {"ruleId": "1993", "severity": 1, "message": "2067", "line": 151, "column": 5, "nodeType": "1995", "endLine": 151, "endColumn": 7, "suggestions": "2434"}, {"ruleId": "1981", "severity": 1, "message": "2435", "line": 192, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 192, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2094", "line": 194, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 194, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2093", "line": 199, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 199, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2436", "line": 336, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 336, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 4, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 4, "column": 40, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 48}, {"ruleId": "1981", "severity": 1, "message": "2160", "line": 8, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2154", "line": 8, "column": 57, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 71}, {"ruleId": "1981", "severity": 1, "message": "2083", "line": 9, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2026", "line": 9, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2082", "line": 9, "column": 33, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2338", "line": 9, "column": 40, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2437", "line": 9, "column": 53, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 62}, {"ruleId": "1981", "severity": 1, "message": "2099", "line": 9, "column": 64, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 70}, {"ruleId": "1981", "severity": 1, "message": "2221", "line": 10, "column": 14, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2081", "line": 10, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 10, "column": 43, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2438", "line": 13, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2439", "line": 14, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 14, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2013", "line": 15, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2401", "line": 20, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2402", "line": 20, "column": 46, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 57}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2440", "line": 26, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2441", "line": 31, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2442", "line": 32, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2443", "line": 32, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 39}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 33, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 18}, {"ruleId": "1993", "severity": 1, "message": "2444", "line": 94, "column": 3, "nodeType": "1995", "endLine": 94, "endColumn": 24, "suggestions": "2445"}, {"ruleId": "1993", "severity": 1, "message": "2067", "line": 104, "column": 5, "nodeType": "1995", "endLine": 104, "endColumn": 34, "suggestions": "2446"}, {"ruleId": "1981", "severity": 1, "message": "2447", "line": 105, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 105, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2412", "line": 117, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 117, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2448", "line": 258, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 258, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2272", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2449", "line": 8, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2450", "line": 14, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 14, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2451", "line": 16, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 9}, {"ruleId": "1981", "severity": 1, "message": "2452", "line": 19, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2081", "line": 25, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2113", "line": 27, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2367", "line": 33, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2453", "line": 33, "column": 30, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2454", "line": 109, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 109, "endColumn": 47}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 110, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 110, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2021", "line": 112, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 112, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2165", "line": 115, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 115, "endColumn": 31}, {"ruleId": "1993", "severity": 1, "message": "2067", "line": 147, "column": 6, "nodeType": "1995", "endLine": 147, "endColumn": 8, "suggestions": "2455"}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 196, "column": 41, "nodeType": "2075", "messageId": "2076", "endLine": 196, "endColumn": 43}, {"ruleId": "1981", "severity": 1, "message": "2142", "line": 207, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 207, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 10, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 10, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2456", "line": 26, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2457", "line": 36, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2029", "line": 41, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2458", "line": 46, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 46, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2459", "line": 47, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 47, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2460", "line": 48, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 48, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2461", "line": 49, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 19}, {"ruleId": "2304", "severity": 1, "message": "2462", "line": 53, "column": 11, "nodeType": "1983", "messageId": "2306", "endLine": 53, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2041", "line": 62, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2463", "line": 63, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2210", "line": 69, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 69, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2211", "line": 70, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2464", "line": 71, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2465", "line": 71, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 39}, {"ruleId": "1981", "severity": 1, "message": "2091", "line": 126, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 126, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2466", "line": 183, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 183, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2467", "line": 228, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 228, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2054", "line": 239, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 239, "endColumn": 34}, {"ruleId": "2073", "severity": 1, "message": "2074", "line": 335, "column": 31, "nodeType": "2075", "messageId": "2076", "endLine": 335, "endColumn": 33}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 503, "column": 65, "nodeType": "2075", "messageId": "2076", "endLine": 503, "endColumn": 67}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 512, "column": 51, "nodeType": "2075", "messageId": "2076", "endLine": 512, "endColumn": 53}, {"ruleId": "2468", "severity": 1, "message": "2469", "line": 523, "column": 49, "nodeType": "2146", "endLine": 523, "endColumn": 95}, {"ruleId": "2468", "severity": 1, "message": "2469", "line": 596, "column": 45, "nodeType": "2146", "endLine": 596, "endColumn": 142}, {"ruleId": "1981", "severity": 1, "message": "2470", "line": 9, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 17, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2471", "line": 20, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2472", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2473", "line": 24, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2474", "line": 26, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 60}, {"ruleId": "1981", "severity": 1, "message": "2475", "line": 27, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2346", "line": 29, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 29, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2222", "line": 33, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2476", "line": 100, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 100, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2477", "line": 100, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 100, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2478", "line": 102, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 102, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2479", "line": 102, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 102, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2480", "line": 104, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 104, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2481", "line": 104, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 104, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2482", "line": 105, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 105, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2483", "line": 105, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 105, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2484", "line": 113, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 113, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2485", "line": 114, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 114, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2486", "line": 115, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 115, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2487", "line": 117, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 117, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2488", "line": 119, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 119, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2489", "line": 119, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 119, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2490", "line": 120, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 120, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2491", "line": 120, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 120, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2090", "line": 121, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 121, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2492", "line": 121, "column": 33, "nodeType": "1983", "messageId": "1984", "endLine": 121, "endColumn": 58}, {"ruleId": "1981", "severity": 1, "message": "2237", "line": 122, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 122, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2493", "line": 123, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 123, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2494", "line": 124, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 124, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2239", "line": 125, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 125, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 126, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 126, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 130, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 130, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2284", "line": 136, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 136, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2285", "line": 137, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 137, "endColumn": 15}, {"ruleId": "1993", "severity": 1, "message": "2495", "line": 226, "column": 7, "nodeType": "1995", "endLine": 226, "endColumn": 23, "suggestions": "2496"}, {"ruleId": "1993", "severity": 1, "message": "2497", "line": 253, "column": 6, "nodeType": "1995", "endLine": 253, "endColumn": 23, "suggestions": "2498"}, {"ruleId": "1981", "severity": 1, "message": "2054", "line": 435, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 435, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2471", "line": 19, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2472", "line": 21, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2474", "line": 24, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 60}, {"ruleId": "1981", "severity": 1, "message": "2475", "line": 25, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2222", "line": 27, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 27, "column": 14, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2346", "line": 28, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2405", "line": 32, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2478", "line": 120, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 120, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2479", "line": 120, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 120, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2490", "line": 121, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 121, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2491", "line": 121, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 121, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2256", "line": 123, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 123, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2208", "line": 124, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 124, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2494", "line": 125, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 125, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2284", "line": 126, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 126, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2285", "line": 127, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 127, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2239", "line": 128, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 128, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 129, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 129, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2499", "line": 130, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 130, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2500", "line": 130, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 130, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2044", "line": 131, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 131, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2501", "line": 131, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 131, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2482", "line": 132, "column": 13, "nodeType": "1983", "messageId": "1984", "endLine": 132, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2483", "line": 132, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 132, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2487", "line": 139, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 139, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2488", "line": 141, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 141, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2489", "line": 141, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 141, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 142, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 142, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2502", "line": 143, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 143, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2165", "line": 143, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 143, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 145, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 145, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2503", "line": 165, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 165, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2504", "line": 165, "column": 23, "nodeType": "1983", "messageId": "1984", "endLine": 165, "endColumn": 38}, {"ruleId": "1993", "severity": 1, "message": "2505", "line": 187, "column": 7, "nodeType": "1995", "endLine": 187, "endColumn": 23, "suggestions": "2506"}, {"ruleId": "1993", "severity": 1, "message": "2505", "line": 250, "column": 4, "nodeType": "1995", "endLine": 250, "endColumn": 37, "suggestions": "2507"}, {"ruleId": "1981", "severity": 1, "message": "2054", "line": 388, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 388, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 45, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 45, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 63, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2239", "line": 64, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 64, "endColumn": 43}, {"ruleId": "1993", "severity": 1, "message": "2508", "line": 75, "column": 8, "nodeType": "1995", "endLine": 75, "endColumn": 65, "suggestions": "2509"}, {"ruleId": "1981", "severity": 1, "message": "2467", "line": 143, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 143, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2510", "line": 223, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 223, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2511", "line": 246, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 246, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2512", "line": 255, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 255, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2513", "line": 260, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 260, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2272", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2025", "line": 4, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2514", "line": 12, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2515", "line": 19, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 9}, {"ruleId": "1981", "severity": 1, "message": "2150", "line": 20, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2081", "line": 22, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 25, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 26, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2367", "line": 27, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 9}, {"ruleId": "1981", "severity": 1, "message": "2516", "line": 31, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2517", "line": 32, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2027", "line": 34, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 34, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2030", "line": 36, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2518", "line": 39, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 39, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2029", "line": 40, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 40, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2519", "line": 41, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 42, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 42, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2520", "line": 43, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 43, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2521", "line": 49, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2522", "line": 55, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 55, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2523", "line": 56, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2524", "line": 56, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2525", "line": 56, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 43}, {"ruleId": "1981", "severity": 1, "message": "2526", "line": 56, "column": 45, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 53}, {"ruleId": "1981", "severity": 1, "message": "2207", "line": 59, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 59, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2527", "line": 60, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 60, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2528", "line": 64, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 64, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2529", "line": 65, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 65, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2530", "line": 66, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 66, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2531", "line": 67, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 67, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2532", "line": 69, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 69, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2101", "line": 70, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2533", "line": 73, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 73, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2534", "line": 80, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 80, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2036", "line": 104, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 104, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2535", "line": 118, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 118, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2536", "line": 119, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 119, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 124, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 124, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2537", "line": 127, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 127, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2538", "line": 127, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 127, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2237", "line": 128, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 128, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2238", "line": 128, "column": 16, "nodeType": "1983", "messageId": "1984", "endLine": 128, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 129, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 129, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2539", "line": 131, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 131, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2540", "line": 132, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 132, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2541", "line": 133, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 133, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2429", "line": 134, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 134, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2430", "line": 135, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 135, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2355", "line": 135, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 135, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2431", "line": 136, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 136, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2356", "line": 136, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 136, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 137, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 137, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2542", "line": 141, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 141, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2543", "line": 165, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 165, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2544", "line": 165, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 165, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2545", "line": 169, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 169, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2546", "line": 169, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 169, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2045", "line": 179, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 179, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2547", "line": 233, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 233, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2548", "line": 234, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 234, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2549", "line": 243, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 243, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2550", "line": 250, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 250, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2551", "line": 257, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 257, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2436", "line": 264, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 264, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2552", "line": 284, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 284, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2553", "line": 286, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 286, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2554", "line": 286, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 286, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2555", "line": 287, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 287, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2556", "line": 287, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 287, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2557", "line": 288, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 288, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2250", "line": 288, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 288, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2252", "line": 389, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 389, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2249", "line": 455, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 455, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2052", "line": 1120, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1120, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2053", "line": 1121, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1121, "endColumn": 24}, {"ruleId": "1993", "severity": 1, "message": "2558", "line": 1148, "column": 5, "nodeType": "1995", "endLine": 1148, "endColumn": 52, "suggestions": "2559"}, {"ruleId": "1981", "severity": 1, "message": "2560", "line": 1156, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1156, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2561", "line": 1164, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1164, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2205", "line": 1183, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1183, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2054", "line": 1192, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1192, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2055", "line": 1203, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 1203, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2562", "line": 1252, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1252, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2563", "line": 1259, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1259, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2092", "line": 1263, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1263, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2564", "line": 1272, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1272, "endColumn": 45}, {"ruleId": "1981", "severity": 1, "message": "2565", "line": 1273, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1273, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2566", "line": 1275, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1275, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2264", "line": 1277, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1277, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2276", "line": 3, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2117", "line": 4, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2176", "line": 5, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2058", "line": 10, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "1991", "line": 23, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2567", "line": 23, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2412", "line": 24, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2413", "line": 24, "column": 30, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 52}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 25, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2147", "line": 25, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2568", "line": 26, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2569", "line": 26, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 26, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2288", "line": 27, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 27, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2407", "line": 28, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2408", "line": 28, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 28, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2570", "line": 29, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 29, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2571", "line": 47, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 47, "endColumn": 27}, {"ruleId": "2073", "severity": 1, "message": "2074", "line": 49, "column": 30, "nodeType": "2075", "messageId": "2076", "endLine": 49, "endColumn": 32}, {"ruleId": "1993", "severity": 1, "message": "2572", "line": 62, "column": 5, "nodeType": "1995", "endLine": 62, "endColumn": 18, "suggestions": "2573"}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 1, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2574", "line": 3, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2421", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 41}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 35, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 35, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2575", "line": 74, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 74, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2576", "line": 76, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 76, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 3, "column": 43, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2118", "line": 3, "column": 53, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 60}, {"ruleId": "1981", "severity": 1, "message": "2577", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2578", "line": 99, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 99, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2579", "line": 2, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 3, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 4, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 5, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2580", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2581", "line": 7, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2582", "line": 11, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2583", "line": 52, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 52, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2584", "line": 54, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 54, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2565", "line": 80, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 80, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2566", "line": 88, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 88, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 3, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2585", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2586", "line": 10, "column": 52, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 72}, {"ruleId": "1981", "severity": 1, "message": "2204", "line": 11, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2232", "line": 11, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2237", "line": 12, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2238", "line": 12, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 25}, {"ruleId": "1993", "severity": 1, "message": "2587", "line": 44, "column": 5, "nodeType": "1995", "endLine": 44, "endColumn": 20, "suggestions": "2588"}, {"ruleId": "1981", "severity": 1, "message": "2589", "line": 77, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 77, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 3, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2590", "line": 4, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2107", "line": 7, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2591", "line": 10, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2592", "line": 12, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2332", "line": 19, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2593", "line": 19, "column": 127, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 139}, {"ruleId": "1981", "severity": 1, "message": "2594", "line": 19, "column": 167, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 172}, {"ruleId": "1981", "severity": 1, "message": "2452", "line": 19, "column": 186, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 194}, {"ruleId": "1981", "severity": 1, "message": "2595", "line": 21, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 22, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2596", "line": 23, "column": 57, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 66}, {"ruleId": "1981", "severity": 1, "message": "2110", "line": 23, "column": 110, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 127}, {"ruleId": "1981", "severity": 1, "message": "2597", "line": 56, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 56, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2598", "line": 63, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2599", "line": 63, "column": 31, "nodeType": "1983", "messageId": "1984", "endLine": 63, "endColumn": 53}, {"ruleId": "1981", "severity": 1, "message": "2429", "line": 70, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 70, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2430", "line": 71, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2355", "line": 71, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2431", "line": 72, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2356", "line": 72, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2600", "line": 73, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 73, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2601", "line": 73, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 73, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2042", "line": 74, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 74, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2602", "line": 75, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 75, "endColumn": 47}, {"ruleId": "1981", "severity": 1, "message": "2603", "line": 77, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 77, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2604", "line": 77, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 77, "endColumn": 47}, {"ruleId": "1981", "severity": 1, "message": "2605", "line": 78, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 78, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2606", "line": 78, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 78, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2607", "line": 81, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 81, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2608", "line": 81, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 81, "endColumn": 45}, {"ruleId": "1981", "severity": 1, "message": "2040", "line": 82, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 82, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2609", "line": 91, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 91, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2436", "line": 100, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 100, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2610", "line": 118, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 118, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2576", "line": 119, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 119, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2611", "line": 119, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 119, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2612", "line": 121, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 121, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2128", "line": 140, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 140, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2613", "line": 141, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 141, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2614", "line": 162, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 162, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2615", "line": 163, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 163, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2616", "line": 177, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 177, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2617", "line": 177, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 177, "endColumn": 55}, {"ruleId": "1981", "severity": 1, "message": "2618", "line": 196, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 196, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2619", "line": 196, "column": 24, "nodeType": "1983", "messageId": "1984", "endLine": 196, "endColumn": 39}, {"ruleId": "1981", "severity": 1, "message": "2620", "line": 311, "column": 15, "nodeType": "1983", "messageId": "1984", "endLine": 311, "endColumn": 32}, {"ruleId": "1993", "severity": 1, "message": "2621", "line": 332, "column": 6, "nodeType": "1995", "endLine": 332, "endColumn": 37, "suggestions": "2622"}, {"ruleId": "1981", "severity": 1, "message": "2623", "line": 335, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 335, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2624", "line": 354, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 354, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2625", "line": 556, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 556, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2626", "line": 556, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 556, "endColumn": 43}, {"ruleId": "1981", "severity": 1, "message": "2627", "line": 558, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 558, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2628", "line": 558, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 558, "endColumn": 47}, {"ruleId": "1993", "severity": 1, "message": "2629", "line": 604, "column": 4, "nodeType": "1995", "endLine": 604, "endColumn": 6, "suggestions": "2630"}, {"ruleId": "1981", "severity": 1, "message": "2631", "line": 649, "column": 13, "nodeType": "1983", "messageId": "1984", "endLine": 649, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2632", "line": 685, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 685, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2633", "line": 685, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 685, "endColumn": 43}, {"ruleId": "1981", "severity": 1, "message": "2634", "line": 686, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 686, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2635", "line": 686, "column": 21, "nodeType": "1983", "messageId": "1984", "endLine": 686, "endColumn": 33}, {"ruleId": "1981", "severity": 1, "message": "2636", "line": 687, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 687, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2637", "line": 688, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 688, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2638", "line": 688, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 688, "endColumn": 48}, {"ruleId": "1981", "severity": 1, "message": "2639", "line": 689, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 689, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2640", "line": 689, "column": 29, "nodeType": "1983", "messageId": "1984", "endLine": 689, "endColumn": 50}, {"ruleId": "1981", "severity": 1, "message": "2641", "line": 692, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 692, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2642", "line": 692, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 692, "endColumn": 45}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 693, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 693, "endColumn": 29}, {"ruleId": "1993", "severity": 1, "message": "2643", "line": 827, "column": 6, "nodeType": "1995", "endLine": 827, "endColumn": 38, "suggestions": "2644"}, {"ruleId": "1981", "severity": 1, "message": "2645", "line": 841, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 841, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2646", "line": 841, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 841, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2162", "line": 1, "column": 114, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 126}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2429", "line": 41, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2430", "line": 42, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 42, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2355", "line": 42, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 42, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2431", "line": 43, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 43, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2356", "line": 43, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 43, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2647", "line": 51, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2436", "line": 52, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 52, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2648", "line": 79, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 79, "endColumn": 32}, {"ruleId": "1993", "severity": 1, "message": "2649", "line": 164, "column": 6, "nodeType": "1995", "endLine": 164, "endColumn": 26, "suggestions": "2650"}, {"ruleId": "1981", "severity": 1, "message": "2651", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2652", "line": 2, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 29}, {"ruleId": "1981", "severity": 1, "message": "2653", "line": 3, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2654", "line": 3, "column": 32, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 4, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 4, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 35}, {"ruleId": "1981", "severity": 1, "message": "2151", "line": 5, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 5, "column": 37, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 47}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 5, "column": 49, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 57}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 5, "column": 59, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 64}, {"ruleId": "1981", "severity": 1, "message": "2168", "line": 5, "column": 97, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 103}, {"ruleId": "1981", "severity": 1, "message": "2170", "line": 5, "column": 105, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 116}, {"ruleId": "1981", "severity": 1, "message": "2169", "line": 5, "column": 118, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 131}, {"ruleId": "1981", "severity": 1, "message": "2084", "line": 6, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 8, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2655", "line": 10, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2656", "line": 17, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2211", "line": 21, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2212", "line": 22, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2210", "line": 32, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 32, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2353", "line": 33, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 33, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2350", "line": 34, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 34, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2428", "line": 35, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 35, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2657", "line": 36, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2289", "line": 37, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 37, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2280", "line": 38, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 38, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2658", "line": 39, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 39, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2282", "line": 40, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 40, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2283", "line": 41, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2429", "line": 45, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 45, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2430", "line": 46, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 46, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2355", "line": 46, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 46, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2431", "line": 47, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 47, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2356", "line": 47, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 47, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 48, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 48, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 49, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2436", "line": 52, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 52, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2202", "line": 91, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 91, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2659", "line": 2, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2660", "line": 2, "column": 30, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2661", "line": 2, "column": 48, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 66}, {"ruleId": "1981", "severity": 1, "message": "2662", "line": 10, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2353", "line": 12, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2358", "line": 14, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 14, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2282", "line": 15, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2289", "line": 16, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2657", "line": 17, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 17, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2284", "line": 20, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 9}, {"ruleId": "1981", "severity": 1, "message": "2285", "line": 21, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2280", "line": 22, "column": 5, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2663", "line": 119, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 119, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2664", "line": 1, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2077", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2425", "line": 2, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 48}, {"ruleId": "1981", "severity": 1, "message": "2659", "line": 3, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2665", "line": 5, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2666", "line": 6, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 2, "column": 29, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 2, "column": 39, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2150", "line": 2, "column": 52, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 62}, {"ruleId": "1981", "severity": 1, "message": "2293", "line": 21, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2289", "line": 22, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2280", "line": 25, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 25, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2429", "line": 40, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 40, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2431", "line": 41, "column": 12, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2356", "line": 41, "column": 30, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2436", "line": 157, "column": 11, "nodeType": "1983", "messageId": "1984", "endLine": 157, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2150", "line": 2, "column": 92, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 102}, {"ruleId": "1981", "severity": 1, "message": "2667", "line": 72, "column": 19, "nodeType": "1983", "messageId": "1984", "endLine": 72, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 1, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 1, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 1, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2668", "line": 3, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 1, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 2, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2669", "line": 2, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 40}, {"ruleId": "1981", "severity": 1, "message": "2670", "line": 1, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2118", "line": 2, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 34}, {"ruleId": "1981", "severity": 1, "message": "2397", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2398", "line": 10, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 21}, {"ruleId": "1993", "severity": 1, "message": "2671", "line": 46, "column": 5, "nodeType": "1995", "endLine": 46, "endColumn": 14, "suggestions": "2672"}, {"ruleId": "1981", "severity": 1, "message": "2673", "line": 3, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 4, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2674", "line": 24, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 1, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 1, "column": 43, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 51}, {"ruleId": "1981", "severity": 1, "message": "2475", "line": 3, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2421", "line": 4, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 39}, {"ruleId": "1981", "severity": 1, "message": "2065", "line": 1, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2675", "line": 2, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2676", "line": 2, "column": 28, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 50}, {"ruleId": "1981", "severity": 1, "message": "1999", "line": 3, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 7, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2677", "line": 9, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2402", "line": 9, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2678", "line": 10, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2655", "line": 12, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2533", "line": 13, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2440", "line": 55, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 55, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 69, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 69, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2679", "line": 98, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 98, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2680", "line": 99, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 99, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2681", "line": 99, "column": 20, "nodeType": "1983", "messageId": "1984", "endLine": 99, "endColumn": 32}, {"ruleId": "1981", "severity": 1, "message": "2429", "line": 112, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 112, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2430", "line": 114, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 114, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2355", "line": 114, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 114, "endColumn": 44}, {"ruleId": "1981", "severity": 1, "message": "2431", "line": 115, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 115, "endColumn": 25}, {"ruleId": "1981", "severity": 1, "message": "2356", "line": 115, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 115, "endColumn": 46}, {"ruleId": "1981", "severity": 1, "message": "2682", "line": 117, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 117, "endColumn": 38}, {"ruleId": "1981", "severity": 1, "message": "2683", "line": 126, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 126, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2684", "line": 127, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 127, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2685", "line": 128, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 128, "endColumn": 18}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 173, "column": 22, "nodeType": "2075", "messageId": "2076", "endLine": 173, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2095", "line": 282, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 282, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2686", "line": 344, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 344, "endColumn": 31}, {"ruleId": "1981", "severity": 1, "message": "2687", "line": 345, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 345, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2688", "line": 346, "column": 7, "nodeType": "1983", "messageId": "1984", "endLine": 346, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2436", "line": 373, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 373, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2689", "line": 2, "column": 51, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 6, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 7, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2366", "line": 9, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2690", "line": 10, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 12, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 13, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2402", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 23, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2586", "line": 51, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 23}, {"ruleId": "1981", "severity": 1, "message": "2691", "line": 54, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 54, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 62, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 62, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2692", "line": 64, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 64, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2687", "line": 110, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 110, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2693", "line": 110, "column": 18, "nodeType": "1983", "messageId": "1984", "endLine": 110, "endColumn": 28}, {"ruleId": "1981", "severity": 1, "message": "2694", "line": 161, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 161, "endColumn": 12}, {"ruleId": "1993", "severity": 1, "message": "2695", "line": 176, "column": 5, "nodeType": "1995", "endLine": 176, "endColumn": 20, "suggestions": "2696"}, {"ruleId": "1981", "severity": 1, "message": "2562", "line": 269, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 269, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2679", "line": 286, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 286, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2697", "line": 287, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 287, "endColumn": 15}, {"ruleId": "1981", "severity": 1, "message": "2683", "line": 287, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 287, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2684", "line": 288, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 288, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2077", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2689", "line": 1, "column": 51, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2698", "line": 1, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 72}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 4, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 5, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 6, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2338", "line": 7, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2366", "line": 8, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2690", "line": 9, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 11, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 12, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2222", "line": 13, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 5}, {"ruleId": "1981", "severity": 1, "message": "2699", "line": 15, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 27}, {"ruleId": "1981", "severity": 1, "message": "2400", "line": 15, "column": 48, "nodeType": "1983", "messageId": "1984", "endLine": 15, "endColumn": 63}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 16, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 23, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 23, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2691", "line": 36, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 36, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2657", "line": 37, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 37, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2284", "line": 39, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 39, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2285", "line": 40, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 40, "endColumn": 6}, {"ruleId": "1981", "severity": 1, "message": "2279", "line": 41, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 41, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2280", "line": 42, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 42, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2022", "line": 46, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 46, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2567", "line": 46, "column": 22, "nodeType": "1983", "messageId": "1984", "endLine": 46, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2694", "line": 83, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 83, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2700", "line": 90, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 90, "endColumn": 26}, {"ruleId": "1981", "severity": 1, "message": "2562", "line": 99, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 99, "endColumn": 20}, {"ruleId": "1981", "severity": 1, "message": "2425", "line": 2, "column": 26, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 48}, {"ruleId": "1981", "severity": 1, "message": "2077", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2651", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2689", "line": 1, "column": 51, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2698", "line": 1, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 72}, {"ruleId": "1981", "severity": 1, "message": "2151", "line": 3, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 4, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 5, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 6, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2338", "line": 7, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2366", "line": 8, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2690", "line": 9, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2701", "line": 10, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 11, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 12, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2222", "line": 13, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 5}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 16, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 18, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2154", "line": 19, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2398", "line": 20, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2397", "line": 21, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2656", "line": 31, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2691", "line": 38, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 38, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 49, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 50, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 50, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2077", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2651", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2689", "line": 1, "column": 51, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2698", "line": 1, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 72}, {"ruleId": "1981", "severity": 1, "message": "2151", "line": 3, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 4, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 5, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 6, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2338", "line": 7, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2366", "line": 8, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2690", "line": 9, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2701", "line": 10, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 11, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 12, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2222", "line": 13, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 5}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 16, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 18, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2154", "line": 19, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2398", "line": 20, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2397", "line": 21, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2656", "line": 31, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2691", "line": 38, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 38, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 49, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 50, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 50, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2077", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2651", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2689", "line": 1, "column": 51, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2698", "line": 1, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 72}, {"ruleId": "1981", "severity": 1, "message": "2151", "line": 3, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 4, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 5, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 6, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2338", "line": 7, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2366", "line": 8, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2690", "line": 9, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2701", "line": 10, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 11, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 12, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2222", "line": 13, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 5}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 16, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 18, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2154", "line": 19, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2398", "line": 20, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2397", "line": 21, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2656", "line": 31, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2691", "line": 39, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 39, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 50, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 50, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 51, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 51, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2077", "line": 1, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 36}, {"ruleId": "1981", "severity": 1, "message": "2651", "line": 1, "column": 38, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 49}, {"ruleId": "1981", "severity": 1, "message": "2689", "line": 1, "column": 51, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 61}, {"ruleId": "1981", "severity": 1, "message": "2698", "line": 1, "column": 63, "nodeType": "1983", "messageId": "1984", "endLine": 1, "endColumn": 72}, {"ruleId": "1981", "severity": 1, "message": "2151", "line": 3, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 11}, {"ruleId": "1981", "severity": 1, "message": "2152", "line": 4, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 4, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2078", "line": 5, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 5, "endColumn": 8}, {"ruleId": "1981", "severity": 1, "message": "2079", "line": 6, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 6, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2338", "line": 7, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 7, "endColumn": 13}, {"ruleId": "1981", "severity": 1, "message": "2366", "line": 8, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 8, "endColumn": 12}, {"ruleId": "1981", "severity": 1, "message": "2690", "line": 9, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 16}, {"ruleId": "1981", "severity": 1, "message": "2701", "line": 10, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 10, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2427", "line": 11, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 11, "endColumn": 10}, {"ruleId": "1981", "severity": 1, "message": "2024", "line": 12, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 12, "endColumn": 7}, {"ruleId": "1981", "severity": 1, "message": "2222", "line": 13, "column": 2, "nodeType": "1983", "messageId": "1984", "endLine": 13, "endColumn": 5}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 16, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 18, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 18, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2154", "line": 19, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 19, "endColumn": 22}, {"ruleId": "1981", "severity": 1, "message": "2398", "line": 20, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 20, "endColumn": 21}, {"ruleId": "1981", "severity": 1, "message": "2397", "line": 21, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 21, "endColumn": 18}, {"ruleId": "1981", "severity": 1, "message": "2347", "line": 22, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 22, "endColumn": 19}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 24, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 24, "endColumn": 14}, {"ruleId": "1981", "severity": 1, "message": "2656", "line": 31, "column": 6, "nodeType": "1983", "messageId": "1984", "endLine": 31, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2691", "line": 38, "column": 3, "nodeType": "1983", "messageId": "1984", "endLine": 38, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "1988", "line": 49, "column": 10, "nodeType": "1983", "messageId": "1984", "endLine": 49, "endColumn": 17}, {"ruleId": "1981", "severity": 1, "message": "2085", "line": 50, "column": 25, "nodeType": "1983", "messageId": "1984", "endLine": 50, "endColumn": 42}, {"ruleId": "1981", "severity": 1, "message": "2426", "line": 3, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 3, "endColumn": 35}, {"ruleId": "1981", "severity": 1, "message": "2219", "line": 9, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 9, "endColumn": 14}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 69, "column": 78, "nodeType": "2075", "messageId": "2076", "endLine": 69, "endColumn": 80}, {"ruleId": "1981", "severity": 1, "message": "2702", "line": 71, "column": 17, "nodeType": "1983", "messageId": "1984", "endLine": 71, "endColumn": 32}, {"ruleId": "1993", "severity": 1, "message": "2703", "line": 91, "column": 6, "nodeType": "1995", "endLine": 91, "endColumn": 31, "suggestions": "2704"}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 96, "column": 84, "nodeType": "2075", "messageId": "2076", "endLine": 96, "endColumn": 86}, {"ruleId": "1993", "severity": 1, "message": "2703", "line": 114, "column": 6, "nodeType": "1995", "endLine": 114, "endColumn": 32, "suggestions": "2705"}, {"ruleId": "1993", "severity": 1, "message": "2706", "line": 59, "column": 5, "nodeType": "1995", "endLine": 59, "endColumn": 14, "suggestions": "2707"}, {"ruleId": "1981", "severity": 1, "message": "2708", "line": 122, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 122, "endColumn": 20}, {"ruleId": "2073", "severity": 1, "message": "2215", "line": 124, "column": 28, "nodeType": "2075", "messageId": "2076", "endLine": 124, "endColumn": 30}, {"ruleId": "1981", "severity": 1, "message": "2080", "line": 2, "column": 27, "nodeType": "1983", "messageId": "1984", "endLine": 2, "endColumn": 37}, {"ruleId": "1981", "severity": 1, "message": "2709", "line": 16, "column": 8, "nodeType": "1983", "messageId": "1984", "endLine": 16, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2710", "line": 193, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 193, "endColumn": 24}, {"ruleId": "1981", "severity": 1, "message": "2711", "line": 262, "column": 9, "nodeType": "1983", "messageId": "1984", "endLine": 262, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'lazy' is defined but never used.", "Identifier", "unusedVar", "'Router' is defined but never used.", "'useParams' is defined but never used.", "'Cookies' is defined but never used.", "'signOut' is assigned a value but never used.", "'loggedOut' is assigned a value but never used.", "'isResetLinkValid' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setLoginUserDetail' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'checkResetLinkConsumed' and 'location.pathname'. Either include them or remove the dependency array.", "ArrayExpression", ["2712"], "'UserManager' is defined but never used.", "'Content' is defined but never used.", "'axios' is defined but never used.", "'decodedToken' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.pathname', 'noLayoutRoutes', and 'signOut'. Either include them or remove the dependency array.", ["2713"], "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'LockOpenIcon' is defined but never used.", "'NoAccountsOutlinedIcon' is defined but never used.", "'AccountCircleOutlinedIcon' is defined but never used.", "'KeyOutlinedIcon' is defined but never used.", "'CopyAllOutlinedIcon' is defined but never used.", "'LockOutlinedIcon' is defined but never used.", "'snackbarIcon' is assigned a value but never used.", "'Outlet' is defined but never used.", "'useLocation' is defined but never used.", "'useNavigate' is defined but never used.", "'Home' is defined but never used.", "'ProfileSettings' is defined but never used.", "'Domain' is defined but never used.", "'Translater' is defined but never used.", "'CodeInstall' is defined but never used.", "'Teamsetting' is assigned a value but never used.", "'user' is assigned a value but never used.", "'userDetails' is assigned a value but never used.", "'passwordLogId' is assigned a value but never used.", "'Alert' is defined but never used.", "'GridRenderCellParams' is defined but never used.", "'FormControlLabel' is defined but never used.", "'DeleteIcon' is defined but never used.", "'MailIcon' is defined but never used.", "'CustomGrid' is defined but never used.", "'MarkEmailReadIcon' is defined but never used.", "'organizationsList' is defined but never used.", "'CustomColumnMenu' is defined but never used.", "'FilterPopup' is defined but never used.", "'AddBoxIcon' is defined but never used.", "'Delete' is defined but never used.", "'CustomDataGridProps' is defined but never used.", "'email' is defined but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'emailiddelete' is assigned a value but never used.", "'sidebarOpen' is assigned a value but never used.", "'storedOrganization' is assigned a value but never used.", "'searchText' is assigned a value but never used.", "'setErrors' is assigned a value but never used.", "'AccountDeleteDetails' is assigned a value but never used.", "'setAccountDeleteDetails' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'FetchAccounts'. Either include it or remove the dependency array.", ["2714"], "'email' is assigned a value but never used.", "'accountIdNew' is assigned a value but never used.", "'onPageChange' is assigned a value but never used.", "'onPageSizeChange' is assigned a value but never used.", "'CustomToolbar' is assigned a value but never used.", "'handleDownloadExcelClick' is assigned a value but never used.", "'filteredColumnNames' is assigned a value but never used.", "'handleApplyFilters' is assigned a value but never used.", "'Navigate' is defined but never used.", "'Login' is defined but never used.", "'jwt_decode' is defined but never used.", "'LoginUserInfo' is defined but never used.", "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", ["2715"], "'logout' is defined but never used.", "'adminApiService' is defined but never used.", "'setUserDetail' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userDetails'. Either include it or remove the dependency array.", ["2716"], "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "'useEffect' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Switch' is defined but never used.", "'Radio' is defined but never used.", "'RadioGroup' is defined but never used.", "'CloseIcon' is defined but never used.", "'setOrganizationId' is assigned a value but never used.", "'setskip' is assigned a value but never used.", "'settop' is assigned a value but never used.", "'totalcount' is assigned a value but never used.", "'isRtl' is assigned a value but never used.", "'selectedOrganizationId' is assigned a value but never used.", "'openPopup' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "'alphanumericRegex' is assigned a value but never used.", "'response' is assigned a value but never used.", "'handleSelectChange' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'successMessage' is assigned a value but never used.", "'SAinitialsData' is defined but never used.", "'Avatar' is defined but never used.", "'quickadopt' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "'LogoutIcon' is defined but never used.", "'LocalActivityIcon' is defined but never used.", "'Popup' is defined but never used.", "'Grid' is defined but never used.", "'LanguageIcon' is defined but never used.", "'translateText' is defined but never used.", "'AccountSettings' is defined but never used.", "'getLanguages' is defined but never used.", "'getPlatformLabels' is defined but never used.", "'setSidebarOpen' is defined but never used.", "'MenuOpenIcon' is defined but never used.", "'MenuIcon' is defined but never used.", "'Sidebar' is defined but never used.", "'settings' is defined but never used.", "'LogoutPopup' is defined but never used.", "'userManager' is defined but never used.", "'userUrl' is defined but never used.", "'translatedLabels' is assigned a value but never used.", "'setTranslatedLabels' is assigned a value but never used.", "'setLanguages' is assigned a value but never used.", "'selectedLanguages' is assigned a value but never used.", ["2717"], "React Hook useEffect has missing dependencies: 'ORGANIZATION_ID' and 'userDetails'. Either include them or remove the dependency array.", ["2718"], ["2719"], "'languageKey' is assigned a value but never used.", "'labelsNew' is assigned a value but never used.", "'handleHomeClick' is assigned a value but never used.", "'handleOrgClick' is assigned a value but never used.", "'handleAccountClick' is assigned a value but never used.", "'handleGuideClick' is assigned a value but never used.", "'handleAccSeetingsClick' is assigned a value but never used.", "'handleTeamClick' is assigned a value but never used.", "'handleThemesClick' is assigned a value but never used.", "'handleBillingClick' is assigned a value but never used.", "'handleInstallClick' is assigned a value but never used.", "'handlenotifyClick' is assigned a value but never used.", "'handleQuickAdoptClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setAccountId'. Either include it or remove the dependency array.", ["2720"], "'toggleSidebar' is assigned a value but never used.", "'orgRtl' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'setSidebarOpen' is assigned a value but never used.", "jsx-a11y/role-supports-aria-props", "The attribute aria-pressed is not supported by the role checkbox. This role is implicit on the element input.", "'Typography' is defined but never used.", "'TextField' is defined but never used.", "'Button' is defined but never used.", "'Link' is defined but never used.", "'InputAdornment' is defined but never used.", "'blue' is defined but never used.", "'blueGrey' is defined but never used.", "'TextareaAutosize' is defined but never used.", "'orgId' is assigned a value but never used.", "'isSidebarOpen' is defined but never used.", "'Container' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Card' is defined but never used.", "'isHidden' is assigned a value but never used.", "'setUserType' is assigned a value but never used.", ["2721"], "'Autocomplete' is defined but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContentText' is defined but never used.", "'DialogActions' is defined but never used.", "'GridColumnMenuProps' is defined but never used.", "'deleteUserRole' is defined but never used.", "'GetAllAccountsList' is defined but never used.", "'getOrganizationById' is defined but never used.", "'EditAccount' is defined but never used.", "'ActionDialogProps' is defined but never used.", "'selectedRoles' is assigned a value but never used.", "'setSelectedRoles' is assigned a value but never used.", "'timeZone' is assigned a value but never used.", "'isDialogReadOnly' is assigned a value but never used.", "'dialogMode' is assigned a value but never used.", "'currentRow' is assigned a value but never used.", "'roleNameToIdMap' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'OrganizationId'. Either exclude it or remove the dependency array. Outer scope values like 'OrganizationId' aren't valid dependencies because mutating them doesn't re-render the component.", ["2722"], ["2723"], "React Hook useEffect has missing dependencies: 'paginationModel.page' and 'paginationModel.pageSize'. Either include them or remove the dependency array.", ["2724"], "'roleIdToNameMap' is assigned a value but never used.", "'limitcount' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserRoles'. Either include it or remove the dependency array.", ["2725"], "'handleSearch' is assigned a value but never used.", "'uniqueUserNames' is assigned a value but never used.", "'uniqueEmails' is assigned a value but never used.", "'uniqueRoles' is assigned a value but never used.", "'ActionDialog' is assigned a value but never used.", "React Hook useEffect has unnecessary dependencies: 'selectedAccount' and 'selectedEmail'. Either exclude them or remove the dependency array. Outer scope values like 'selectedAccount' aren't valid dependencies because mutating them doesn't re-render the component.", ["2726"], "'duplicateRoles' is assigned a value but never used.", "'SubmitCreatenewUser' is defined but never used.", "'inputs' is assigned a value but never used.", "'openEditPopup' is assigned a value but never used.", "'handleReset' is assigned a value but never used.", "'SubmitCreateUser' is defined but never used.", "'models' is assigned a value but never used.", "'showPopup' is assigned a value but never used.", "'showEditPopup' is assigned a value but never used.", "'showDeletePopup' is assigned a value but never used.", "'setShowDeletePopup' is assigned a value but never used.", "'containerHeight' is assigned a value but never used.", "'buttonMarginTop' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'GetAppTwoToneIcon' is defined but never used.", "'format' is defined but never used.", "'moment' is defined but never used.", "'styles' is defined but never used.", "'Menu' is defined but never used.", "'FormGroup' is defined but never used.", "'Box' is defined but never used.", "'SaveAltIcon' is defined but never used.", "'getOrganizationsData' is defined but never used.", "'AnyAaaaRecord' is defined but never used.", "'fetchUserDataFromApi' is defined but never used.", "'menuVisible' is assigned a value but never used.", "'setMenuVisible' is assigned a value but never used.", "'gridHeight' is assigned a value but never used.", "'setGridHeight' is assigned a value but never used.", "'activePlan' is assigned a value but never used.", "'setInputs' is assigned a value but never used.", "'activeId' is assigned a value but never used.", "'setActiveId' is assigned a value but never used.", "'filteredUsers' is assigned a value but never used.", "'setFilteredUsers' is assigned a value but never used.", "'users' is assigned a value but never used.", "'setUsers' is assigned a value but never used.", "'setOrderByFields' is assigned a value but never used.", "'logo' is assigned a value but never used.", "'setLogo' is assigned a value but never used.", "'logoUrl' is assigned a value but never used.", "'setTimezoneError' is assigned a value but never used.", "'currentDate' is assigned a value but never used.", "'setPlanTypeError' is assigned a value but never used.", "'createddate' is assigned a value but never used.", "'pendingSwitchState' is assigned a value but never used.", "'setCheckedOne' is assigned a value but never used.", "'IsActive' is assigned a value but never used.", "'setIsActive' is assigned a value but never used.", "'handleDeleteClick' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "'selectedValue' is assigned a value but never used.", "'handleRadioChange' is assigned a value but never used.", "'planEditClick' is assigned a value but never used.", "'organizations' is assigned a value but never used.", "'setOrganizations' is assigned a value but never used.", "'filterModel' is assigned a value but never used.", "'setFilterModel' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters', 'isPaginationResetDone', and 'sortModel'. Either include them or remove the dependency array.", ["2727"], "React Hook useEffect has missing dependencies: 'filters', 'paginationModel.page', and 'paginationModel.pageSize'. Either include them or remove the dependency array.", ["2728"], "'filteredRows' is assigned a value but never used.", "'handleExportMenuClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'dateFormat'. Either include it or remove the dependency array.", ["2729"], "'toBase64' is assigned a value but never used.", "'validateLogo' is assigned a value but never used.", "'setColumnMenuApi' is assigned a value but never used.", "'handleColumnMenuClose' is assigned a value but never used.", "'useTransition' is defined but never used.", "'EditOutlinedIcon' is defined but never used.", "'DeleteOutlineOutlinedIcon' is defined but never used.", "'settingsiconAnnouncements' is defined but never used.", "'subscribe' is defined but never used.", "'Settings' is defined but never used.", "'setName' is assigned a value but never used.", "'OrganizationId' is assigned a value but never used.", "'setTotalcount' is assigned a value but never used.", "'orderByFields' is assigned a value but never used.", "'filters' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'tourName' is assigned a value but never used.", "'setTourName' is assigned a value but never used.", "'organizationId' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTours'. Either include it or remove the dependency array.", ["2730"], "'tourslist' is assigned a value but never used.", "'setTourslist' is assigned a value but never used.", "'bannerName' is assigned a value but never used.", "'setBannerName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBanners'. Either include it or remove the dependency array.", ["2731"], "'handleKeyDown' is assigned a value but never used.", ["2732"], "'userInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2733"], ["2734"], "@typescript-eslint/no-redeclare", "'Tooltip' is already defined.", "redeclared", "'statusss' is assigned a value but never used.", "'TooltipsNew' is assigned a value but never used.", "'setTooltipsNew' is assigned a value but never used.", "'filterss' is assigned a value but never used.", "'setFilterss' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTooltips'. Either include it or remove the dependency array.", ["2735"], "'HotspotsNew' is assigned a value but never used.", "'setHotspotsNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchHotspots'. Either include it or remove the dependency array.", ["2736"], "React Hook useEffect has a missing dependency: 'fetchChecklists'. Either include it or remove the dependency array.", ["2737"], ["2738"], "'CircularProgress' is defined but never used.", "'BorderColorOutlinedIcon' is defined but never used.", "'formatDateTime' is defined but never used.", "'formatDate' is assigned a value but never used.", "'truncatedText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchHistoryData'. Either include it or remove the dependency array.", ["2739"], ["2740"], "React Hook useEffect has a missing dependency: 'loadSystemPrompt'. Either include it or remove the dependency array.", ["2741"], ["2742"], "'Chip' is defined but never used.", "'useAuth' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadHistoricalPrompt'. Either include it or remove the dependency array.", ["2743"], ["2744"], "'useRef' is defined but never used.", "'FormControl' is defined but never used.", "'AssignmentOutlinedIcon' is defined but never used.", "'VisibilityIcon' is defined but never used.", "'GetAllAccounts' is defined but never used.", "'fetchDeleteAccountDetails' is defined but never used.", "'GetAccountsList' is defined but never used.", "'AccountCustomColumnMenu' is defined but never used.", "'SearchIcon' is defined but never used.", "'ClearIcon' is defined but never used.", "'JSEncrypt' is defined but never used.", "'ConstructionOutlined' is defined but never used.", "'openaikey' is defined but never used.", "'accountidedit' is assigned a value but never used.", "'setAccountIdEdit' is assigned a value but never used.", "'showeditPopup' is assigned a value but never used.", "'setShowEditPopup' is assigned a value but never used.", "'setemailiddelete' is assigned a value but never used.", "'setSnackbarMessage' is assigned a value but never used.", "'setSnackbarSeverity' is assigned a value but never used.", "'Organizationid' is assigned a value but never used.", "'sortModel' is assigned a value but never used.", "'handleSortModelChange' is assigned a value but never used.", "'setPaginationModel' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'FetchAccountById' and 'FetchSystemPrompts'. Either include them or remove the dependency array. Outer scope values like 'accountId' aren't valid dependencies because mutating them doesn't re-render the component.", ["2745"], "'emailId' is assigned a value but never used.", "'accountid' is assigned a value but never used.", "'globalhandleSearch' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Divider' is defined but never used.", "'UnPublishIcon' is defined but never used.", "'RadioProps' is defined but never used.", "'SavePageTargets' is defined but never used.", "'ConfirmationDialog' is defined but never used.", "'getAllGuides' is defined but never used.", "'setGuideId' is assigned a value but never used.", "'open' is assigned a value but never used.", "'setOpen' is assigned a value but never used.", "'initialGuide' is assigned a value but never used.", "'setInitialGuide' is assigned a value but never used.", "'hasUnsavedChanges' is assigned a value but never used.", "'setHasUnsavedChanges' is assigned a value but never used.", "'customPublishDate' is assigned a value but never used.", "'setCustomPublishDate' is assigned a value but never used.", "'customUnPublishDate' is assigned a value but never used.", "'setCustomUnPublishDate' is assigned a value but never used.", "'CustomDivider' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.pathname' and 'navigate'. Either include them or remove the dependency array.", ["2746"], "'handleDrawClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'guide?.GuideDetails.TargetUrl'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setTriggers' needs the current value of 'guide.GuideDetails.TargetUrl'.", ["2747"], "React Hook useEffect has a missing dependency: 'guide?.GuideDetails?.Frequency'. Either include it or remove the dependency array.", ["2748"], "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "React Hook useEffect has missing dependencies: 'HandlePublishToggle' and 'currentGuideId'. Either include them or remove the dependency array.", ["2749"], "'linkexpirationimage' is defined but never used.", "'onHandleClick' is assigned a value but never used.", "'Visibility' is defined but never used.", "'VisibilityOff' is defined but never used.", "'superAdminLogin' is defined but never used.", "'encryptPassword' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'getAllUsers' is defined but never used.", "'userDetails' is defined but never used.", "'UserId' is defined but never used.", "'OrganizationId' is defined but never used.", "'setUser' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'userIds' is assigned a value but never used.", "'setuserId' is assigned a value but never used.", "'organizationDetails' is assigned a value but never used.", "'setOrganizationDetails' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'UserId' is assigned a value but never used.", ["2750"], "React Hook useEffect has an unnecessary dependency: 'userDetails'. Either exclude it or remove the dependency array. Outer scope values like 'userDetails' aren't valid dependencies because mutating them doesn't re-render the component.", ["2751"], "Assignments to the 'UserId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "MemberExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'adminUrl' is assigned a value but never used.", "'userUrl' is assigned a value but never used.", "'GridColumnMenuHideItem' is defined but never used.", "'adminUrl' is defined but never used.", "'Snackbar' is defined but never used.", "'GetAllAccounts' is assigned a value but never used.", "'snackbarOpen' is assigned a value but never used.", "'snackbarMessage' is assigned a value but never used.", "'snackbarSeverity' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountidedit' and 'fetchAccountDetails'. Either include them or remove the dependency array.", ["2752"], ["2753"], "'handleOrganizationDropdownOpen' is assigned a value but never used.", "'handleSnackbarClose' is assigned a value but never used.", "'FormLabel' is defined but never used.", "'CameraAlt' is defined but never used.", "'GetUserDetail' is defined but never used.", "'publicKey' is assigned a value but never used.", "'contactEditable' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentProfileData' and 'updatedProfileData'. Either include them or remove the dependency array. You can also do a functional update 'setCurrentProfileData(c => ...)' if you only need 'currentProfileData' in the 'setCurrentProfileData' call.", ["2754"], ["2755"], "'selectedDate' is assigned a value but never used.", "'getYesterdayDate' is assigned a value but never used.", "'Banners' is defined but never used.", "'Auditlog' is defined but never used.", "'Cursor' is defined but never used.", "'useTheme' is defined but never used.", "'MuiTooltip' is defined but never used.", "'setTranslatedTitles' is assigned a value but never used.", ["2756"], "'CardContent' is defined but never used.", "'Opacity' is defined but never used.", "'height' is defined but never used.", "'inherits' is defined but never used.", "'LinkIcon' is defined but never used.", "'UpgradeIcon' is defined but never used.", "'FileUpload' is already defined.", "'useridedit' is assigned a value but never used.", "'fileUploads' is assigned a value but never used.", "'setFileUploads' is assigned a value but never used.", "'columns' is assigned a value but never used.", "'handleEdit' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "'GridPaginationModel' is defined but never used.", "'DatePicker' is defined but never used.", "'AdapterDateFns' is defined but never used.", "'getOrganization' is defined but never used.", "'GetAuditLogsByOrganizationId' is defined but never used.", "'SearchParams' is defined but never used.", "'totalRecords' is assigned a value but never used.", "'setTotalRecords' is assigned a value but never used.", "'referenceTypes' is assigned a value but never used.", "'setReferenceTypes' is assigned a value but never used.", "'orgid' is assigned a value but never used.", "'setOrgId' is assigned a value but never used.", "'types' is assigned a value but never used.", "'setTypes' is assigned a value but never used.", "'organization' is assigned a value but never used.", "'defaultOrganization' is assigned a value but never used.", "'orgname' is assigned a value but never used.", "'setCreatedUser' is assigned a value but never used.", "'eventType' is assigned a value but never used.", "'setEventType' is assigned a value but never used.", "'allAuditLogData' is assigned a value but never used.", "'setAllAuditLogData' is assigned a value but never used.", "'setSelectedOrganizationId' is assigned a value but never used.", "'setSelectedUser' is assigned a value but never used.", "'isClearing' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFilteredData' and 'selectedUser'. Either include them or remove the dependency array.", ["2757"], "React Hook useEffect has missing dependencies: 'fetchFilteredData', 'organizationId', and 'selectedUser'. Either include them or remove the dependency array.", ["2758"], "'selectedOptions' is assigned a value but never used.", "'setSelectedOptions' is assigned a value but never used.", "'setSearchText' is assigned a value but never used.", "'userType' is assigned a value but never used.", "'allAuditLogs' is assigned a value but never used.", "'setAllAuditLogs' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchFilteredData'. Either include it or remove the dependency array.", ["2759"], ["2760"], "React Hook useEffect has a missing dependency: 'fetchTrainingDocuments'. Either include it or remove the dependency array.", ["2761"], "'handleDownload' is assigned a value but never used.", "'isValidDocumentType' is assigned a value but never used.", "'getPriorityLabel' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'GridColumnsManagement' is defined but never used.", "'Popover' is defined but never used.", "'ToggleOffOutlinedIcon' is defined but never used.", "'ToggleOnRoundedIcon' is defined but never used.", "'userData' is defined but never used.", "'PersonOffSharpIcon' is defined but never used.", "'MarkEmailRead' is defined but never used.", "'PersonOutlineOutlinedIcon' is defined but never used.", "'DraftsIcon' is defined but never used.", "'Unblockaccount' is defined but never used.", "'Mail' is defined but never used.", "'Keyvertical' is defined but never used.", "'Lockopen' is defined but never used.", "'Submitdisableuser' is defined but never used.", "'BlockUser' is defined but never used.", "'UnblockUser' is defined but never used.", "'deactivateUser' is defined but never used.", "'activateUser' is defined but never used.", "'NoAccountsIcon' is defined but never used.", "'error' is defined but never used.", "'Search' is defined but never used.", "'glblsrch' is assigned a value but never used.", "'fieldName' is defined but never used.", "'useridreset' is assigned a value but never used.", "'setUserIdReset' is assigned a value but never used.", "'setLastName' is assigned a value but never used.", "'setUserName' is assigned a value but never used.", "'helperText' is assigned a value but never used.", "'usersEmails' is assigned a value but never used.", "'newpassword' is assigned a value but never used.", "'setNewpassword' is assigned a value but never used.", "'columnname' is assigned a value but never used.", "'setColumnname' is assigned a value but never used.", "'skipss' is defined but never used.", "'topss' is defined but never used.", "'handleLastnameFocus' is assigned a value but never used.", "'handleUsernameFocus' is assigned a value but never used.", "'handleEmailFocus' is assigned a value but never used.", "'setEmailConfirmed' is assigned a value but never used.", "'blocked' is assigned a value but never used.", "'setBlocked' is assigned a value but never used.", "'activate' is assigned a value but never used.", "'setActivate' is assigned a value but never used.", "'isactive' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2762"], "'handleNextButtonClick' is assigned a value but never used.", "'handlePreviousButtonClick' is assigned a value but never used.", "'handleChange' is assigned a value but never used.", "'handleSubmituser' is assigned a value but never used.", "'onChange' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'handlePageSizeChange' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'userId' is assigned a value but never used.", "'setUserId' is assigned a value but never used.", "'location' is assigned a value but never used.", "'loggedinUserInfo' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'Navigate' and 'signOut'. Either include them or remove the dependency array. Outer scope values like 'userDetails' aren't valid dependencies because mutating them doesn't re-render the component.", ["2763"], "'AnyMxRecord' is defined but never used.", "'stepImage' is assigned a value but never used.", "'guides' is assigned a value but never used.", "'useSnackbar' is defined but never used.", "'usernames' is assigned a value but never used.", "'GridToolbar' is defined but never used.", "'ChevronLeftIcon' is defined but never used.", "'ChevronRightIcon' is defined but never used.", "'CustomPaginationProps' is defined but never used.", "'lastValue' is assigned a value but never used.", "'totalrowss' is defined but never used.", "'GridColDef' is defined but never used.", "'fetchUserDataFromApi' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userId'. Either include it or remove the dependency array.", ["2764"], "'navigate' is assigned a value but never used.", "'i18n' is defined but never used.", "'AddIcon' is defined but never used.", "'classNames' is defined but never used.", "'NativeSelect' is defined but never used.", "'Theme' is defined but never used.", "'lang' is defined but never used.", "'getLabels' is defined but never used.", "'LanguageMap' is defined but never used.", "'guideTypeOptionsNew' is assigned a value but never used.", "'setGuideTypeOptionsNew' is assigned a value but never used.", "'labels' is assigned a value but never used.", "'setLabels' is assigned a value but never used.", "'setdropdownLanguage' is assigned a value but never used.", "'selectedLanguage' is assigned a value but never used.", "'setSelectedLanguage' is assigned a value but never used.", "'toText' is assigned a value but never used.", "'setToText' is assigned a value but never used.", "'translatedTitle' is assigned a value but never used.", "'setTranslatedTitle' is assigned a value but never used.", "'selectedArtifactIds' is assigned a value but never used.", "'ToLanguageNew' is assigned a value but never used.", "'setGuides' is assigned a value but never used.", "'extractUniqueGuides' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'dialogHeight' is assigned a value but never used.", "'selectedLanguagesNew' is assigned a value but never used.", "'setSelectedLanguagesNew' is assigned a value but never used.", "'orgLanguages' is assigned a value but never used.", "'setOrgLanguages' is assigned a value but never used.", "'combinedLanguages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAddLanguage'. Either include it or remove the dependency array.", ["2765"], "'handleNavigationOrButtonClick' is assigned a value but never used.", "'updatedLabel' is assigned a value but never used.", "'filteredGuides' is assigned a value but never used.", "'setFilteredGuides' is assigned a value but never used.", "'showDropdown' is assigned a value but never used.", "'setShowDropdown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userDetails?.OrganizationId'. Either include it or remove the dependency array.", ["2766"], "'result' is assigned a value but never used.", "'artifactLabels' is assigned a value but never used.", "'setArtifactLabels' is assigned a value but never used.", "'labelsTwo' is assigned a value but never used.", "'setLabelsTwo' is assigned a value but never used.", "'ArtifactlabelsNew' is assigned a value but never used.", "'selectedGuideType' is assigned a value but never used.", "'setSelectedGuideType' is assigned a value but never used.", "'filteredLabelNames' is assigned a value but never used.", "'setFilteredLabelNames' is assigned a value but never used.", "'mappedGuidesNew' is assigned a value but never used.", "'setMappedGuidesNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["2767"], "'isSaving' is assigned a value but never used.", "'setIsSaving' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'handleconfirmclosepopup' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isShareFeedbackPopupTwo'. Either include it or remove the dependency array. Outer scope values like 'ShareFeedbackPopup' aren't valid dependencies because mutating them doesn't re-render the component.", ["2768"], "'ChangeEvent' is defined but never used.", "'getAllOrganizations' is defined but never used.", "'SubmitAccountDetails' is defined but never used.", "'fetchAccountsById' is defined but never used.", "'User' is defined but never used.", "'ErrorFields' is defined but never used.", "'setModels' is assigned a value but never used.", "'orderByField' is assigned a value but never used.", "'fetchOrganizations' is defined but never used.", "'getOrganizations' is defined but never used.", "'updateOrganization' is defined but never used.", "'setModelsData' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'useState' is defined but never used.", "'ModelTrainingSharp' is defined but never used.", "'Organization' is defined but never used.", "'guideStatus' is assigned a value but never used.", "'API_URL' is assigned a value but never used.", "'idsApiService' is defined but never used.", "'ResetPassword' is defined but never used.", "React Hook useEffect has missing dependencies: 'column' and 'searchText'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setFilteredOptions' needs the current value of 'searchText'.", ["2769"], "'Status' is defined but never used.", "'headers' is assigned a value but never used.", "'TrainingDocument' is defined but never used.", "'TrainingDocumentUpload' is defined but never used.", "'fetchUsersList' is defined but never used.", "'AnyCnameRecord' is defined but never used.", "'apierror' is assigned a value but never used.", "'isTouched' is assigned a value but never used.", "'setIsTouched' is assigned a value but never used.", "'handleTogglePasswordVisibility' is assigned a value but never used.", "'setEmails' is assigned a value but never used.", "'setContactNumbers' is assigned a value but never used.", "'formValid' is assigned a value but never used.", "'newErrors' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'errorMessages' is assigned a value but never used.", "'FocusEvent' is defined but never used.", "'FormHelperText' is defined but never used.", "'fetchUsersList' is assigned a value but never used.", "'userDetailss' is assigned a value but never used.", "'setIsValid' is assigned a value but never used.", "'age' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchUserDetails' and 'userId'. Either include them or remove the dependency array.", ["2770"], "'emails' is assigned a value but never used.", "'FormEvent' is defined but never used.", "'SubmitUserDetails' is defined but never used.", "'handleGenderChange' is assigned a value but never used.", "'SelectChangeEvent' is defined but never used.", "'allDisplayNames' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'optionsModel'. Either include them or remove the dependency array.", ["2771"], ["2772"], "React Hook useEffect has missing dependencies: 'column' and 'searchText'. Either include them or remove the dependency array. If 'setFilteredOptions' needs the current value of 'searchText', you can also switch to useReducer instead of useState and read 'searchText' in the reducer.", ["2773"], "'dummyAgents' is assigned a value but never used.", "'ChartPlaceholder' is defined but never used.", "'overviewMetrics' is assigned a value but never used.", "'aiPerformanceMetrics' is assigned a value but never used.", {"desc": "2774", "fix": "2775"}, {"desc": "2776", "fix": "2777"}, {"desc": "2778", "fix": "2779"}, {"desc": "2780", "fix": "2781"}, {"desc": "2782", "fix": "2783"}, {"desc": "2784", "fix": "2785"}, {"desc": "2784", "fix": "2786"}, {"desc": "2782", "fix": "2787"}, {"desc": "2788", "fix": "2789"}, {"desc": "2782", "fix": "2790"}, {"desc": "2791", "fix": "2792"}, {"desc": "2793", "fix": "2794"}, {"desc": "2795", "fix": "2796"}, {"desc": "2797", "fix": "2798"}, {"desc": "2793", "fix": "2799"}, {"desc": "2800", "fix": "2801"}, {"desc": "2802", "fix": "2803"}, {"desc": "2804", "fix": "2805"}, {"desc": "2806", "fix": "2807"}, {"desc": "2808", "fix": "2809"}, {"desc": "2810", "fix": "2811"}, {"desc": "2812", "fix": "2813"}, {"desc": "2814", "fix": "2815"}, {"desc": "2816", "fix": "2817"}, {"desc": "2818", "fix": "2819"}, {"desc": "2820", "fix": "2821"}, {"desc": "2822", "fix": "2823"}, {"desc": "2824", "fix": "2825"}, {"kind": "2826", "justification": "2827"}, {"desc": "2828", "fix": "2829"}, {"kind": "2826", "justification": "2827"}, {"desc": "2830", "fix": "2831"}, {"kind": "2826", "justification": "2827"}, {"desc": "2832", "fix": "2833"}, {"desc": "2834", "fix": "2835"}, {"desc": "2836", "fix": "2837"}, {"desc": "2838", "fix": "2839"}, {"desc": "2840", "fix": "2841"}, {"desc": "2842", "fix": "2843"}, {"desc": "2793", "fix": "2844"}, {"desc": "2845", "fix": "2846"}, {"desc": "2782", "fix": "2847"}, {"desc": "2848", "fix": "2849"}, {"desc": "2850", "fix": "2851"}, {"desc": "2782", "fix": "2852"}, {"desc": "2853", "fix": "2854"}, {"desc": "2855", "fix": "2856"}, {"desc": "2857", "fix": "2858"}, {"desc": "2859", "fix": "2860"}, {"desc": "2861", "fix": "2862"}, {"desc": "2863", "fix": "2864"}, {"desc": "2865", "fix": "2866"}, {"desc": "2867", "fix": "2868"}, {"desc": "2869", "fix": "2870"}, {"desc": "2871", "fix": "2872"}, {"desc": "2873", "fix": "2874"}, {"desc": "2875", "fix": "2876"}, {"desc": "2877", "fix": "2878"}, {"desc": "2879", "fix": "2880"}, {"desc": "2881", "fix": "2882"}, {"desc": "2883", "fix": "2884"}, {"desc": "2877", "fix": "2885"}, "Update the dependencies array to be: [checkResetLinkConsumed, location.pathname, passwordLogId]", {"range": "2886", "text": "2887"}, "Update the dependencies array to be: [location.pathname, loggedOut, noLayoutRoutes, signOut]", {"range": "2888", "text": "2889"}, "Update the dependencies array to be: [FetchAccounts, paginationModel]", {"range": "2890", "text": "2891"}, "Update the dependencies array to be: [navigate]", {"range": "2892", "text": "2893"}, "Update the dependencies array to be: [userDetails]", {"range": "2894", "text": "2895"}, "Update the dependencies array to be: [ORGANIZATION_ID, userDetails]", {"range": "2896", "text": "2897"}, {"range": "2898", "text": "2897"}, {"range": "2899", "text": "2895"}, "Update the dependencies array to be: [accounts, setAccountId]", {"range": "2900", "text": "2901"}, {"range": "2902", "text": "2895"}, "Update the dependencies array to be: [userId]", {"range": "2903", "text": "2904"}, "Update the dependencies array to be: []", {"range": "2905", "text": "2906"}, "Update the dependencies array to be: [paginationModel.page, paginationModel.pageSize]", {"range": "2907", "text": "2908"}, "Update the dependencies array to be: [fetchUserRoles, paginationModel, selectedAccount]", {"range": "2909", "text": "2910"}, {"range": "2911", "text": "2906"}, "Update the dependencies array to be: [filters, isPaginationResetDone, paginationModel, sortModel]", {"range": "2912", "text": "2913"}, "Update the dependencies array to be: [filters, paginationModel.page, paginationModel.pageSize, sortModel]", {"range": "2914", "text": "2915"}, "Update the dependencies array to be: [dateFormat]", {"range": "2916", "text": "2917"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchTours]", {"range": "2918", "text": "2919"}, "Update the dependencies array to be: [fetchBanners, searchQuery]", {"range": "2920", "text": "2921"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchBanners]", {"range": "2922", "text": "2923"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "2924", "text": "2925"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchAnnouncements]", {"range": "2926", "text": "2927"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchTooltips]", {"range": "2928", "text": "2929"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchHotspots]", {"range": "2930", "text": "2931"}, "Update the dependencies array to be: [fetchChecklists, searchQuery]", {"range": "2932", "text": "2933"}, "Update the dependencies array to be: [paginationModel, activeTab, accountId, fetchChecklists]", {"range": "2934", "text": "2935"}, "Update the dependencies array to be: [agentData, fetchHistoryData, paginationModel]", {"range": "2936", "text": "2937"}, "directive", "", "Update the dependencies array to be: [agentData, loadSystemPrompt]", {"range": "2938", "text": "2939"}, "Update the dependencies array to be: [historyItem, loadHistoricalPrompt]", {"range": "2940", "text": "2941"}, "Update the dependencies array to be: [paginationModel, FetchAccountById, FetchSystemPrompts]", {"range": "2942", "text": "2943"}, "Update the dependencies array to be: [currentGuideId, guideStatus, location.pathname, navigate]", {"range": "2944", "text": "2945"}, "Update the dependencies array to be: [guide?.GuideDetails.TargetUrl, triggers]", {"range": "2946", "text": "2947"}, "Update the dependencies array to be: [guide?.GuideDetails?.Frequency, location.state]", {"range": "2948", "text": "2949"}, "Update the dependencies array to be: [HandlePublishToggle, currentGuideId, guide]", {"range": "2950", "text": "2951"}, "Update the dependencies array to be: [navigate, user]", {"range": "2952", "text": "2953"}, {"range": "2954", "text": "2906"}, "Update the dependencies array to be: [accountidedit, fetchAccountDetails, showEditPopup]", {"range": "2955", "text": "2956"}, {"range": "2957", "text": "2895"}, "Update the dependencies array to be: [currentProfileData, updatedProfileData, userDetails.UserId]", {"range": "2958", "text": "2959"}, "Update the dependencies array to be: [userDetails, userDetails.OrganizationId]", {"range": "2960", "text": "2961"}, {"range": "2962", "text": "2895"}, "Update the dependencies array to be: [fetchFilteredData, organizationId, selectedUser]", {"range": "2963", "text": "2964"}, "Update the dependencies array to be: [fetchFilteredData, organizationId, paginationModel, selectedUser]", {"range": "2965", "text": "2966"}, "Update the dependencies array to be: [fetchFilteredData, organizationId]", {"range": "2967", "text": "2968"}, "Update the dependencies array to be: [fetchFilteredData, organizationId, paginationModel]", {"range": "2969", "text": "2970"}, "Update the dependencies array to be: [userDetails?.OrganizationId, paginationModel, accountId, fetchTrainingDocuments]", {"range": "2971", "text": "2972"}, "Update the dependencies array to be: [fetchData, paginationModel.page, paginationModel.pageSize]", {"range": "2973", "text": "2974"}, "Update the dependencies array to be: [Navigate, signOut]", {"range": "2975", "text": "2976"}, "Update the dependencies array to be: [showEditPopup, userId]", {"range": "2977", "text": "2978"}, "Update the dependencies array to be: [triggerAdd, selectedLanguages, handleAddLanguage]", {"range": "2979", "text": "2980"}, "Update the dependencies array to be: [userDetails?.OrganizationId]", {"range": "2981", "text": "2982"}, "Update the dependencies array to be: [toLanguage, type, selectedData, filters]", {"range": "2983", "text": "2984"}, "Update the dependencies array to be: [isShareFeedbackPopupTwo]", {"range": "2985", "text": "2986"}, "Update the dependencies array to be: [column, options, searchText]", {"range": "2987", "text": "2988"}, "Update the dependencies array to be: [fetchUserDetails, showEditPopup, userId]", {"range": "2989", "text": "2990"}, "Update the dependencies array to be: [options, models, column, filters, optionsModel]", {"range": "2991", "text": "2992"}, "Update the dependencies array to be: [column, models, allNames, filters, optionsModel]", {"range": "2993", "text": "2994"}, {"range": "2995", "text": "2988"}, [3381, 3396], "[checkResetLinkConsumed, location.pathname, passwordLogId]", [3393, 3404], "[location.pathname, loggedOut, noLayoutRoutes, signOut]", [5375, 5392], "[FetchAccounts, paginationModel]", [786, 788], "[navigate]", [1691, 1693], "[userDetails]", [7395, 7412], "[ORGANIZATION_ID, userDetails]", [8235, 8237], [9069, 9071], [13725, 13735], "[accounts, setAccountId]", [3508, 3510], [5600, 5624], "[userId]", [6602, 6618], "[]", [7622, 7624], "[paginationModel.page, paginationModel.pageSize]", [11955, 11989], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, paginationModel, selectedAccount]", [25410, 25442], [10680, 10697], "[filters, isPaginationResetDone, paginationModel, sortModel]", [11271, 11282], "[filters, paginationModel.page, paginationModel.pageSize, sortModel]", [17051, 17053], "[dateFormat]", [5924, 5962], "[paginationModel, activeTab, accountId, fetchTours]", [4512, 4525], "[fetchBanners, searchQuery]", [6186, 6224], "[paginationModel, activeTab, accountId, fetchBanners]", [3795, 3808], "[fetchAnnouncements, searchQuery]", [5301, 5339], "[paginationModel, activeTab, accountId, fetchAnnouncements]", [5669, 5708], "[paginationModel, activeTab, accountId, fetchTooltips]", [5656, 5695], "[paginationModel, activeTab, accountId, fetchHotspots]", [3762, 3775], "[fetch<PERSON><PERSON><PERSON><PERSON>, searchQuery]", [5250, 5288], "[paginationModel, activeTab, accountId, fetchChecklists]", [2474, 2502], "[agentData, fetchHistoryData, paginationModel]", [6161, 6172], "[agentData, loadSystemPrompt]", [1765, 1778], "[historyItem, loadHistoricalPrompt]", [7699, 7726], "[pagination<PERSON><PERSON><PERSON>, FetchAccountById, FetchSystemPrompts]", [10452, 10481], "[currentGuideId, guideStatus, location.pathname, navigate]", [14349, 14359], "[guide?.GuideDetails.TargetUrl, triggers]", [16255, 16271], "[guide?.GuideDetails?.Frequency, location.state]", [28715, 28722], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, currentGuideId, guide]", [3342, 3348], "[navigate, user]", [6699, 6712], [2757, 2772], "[accountidedit, fetchAccountDetails, showEditPopup]", [4433, 4435], [3580, 3601], "[currentProfileData, updatedProfileData, userDetails.UserId]", [3874, 3903], "[userDetails, userDetails.OrganizationId]", [4883, 4885], [7828, 7844], "[fetchFiltered<PERSON><PERSON>, organizationId, selectedUser]", [8782, 8799], "[fetchFilteredData, organizationId, paginationModel, selectedUser]", [6243, 6259], "[fetchFilteredData, organizationId]", [8381, 8414], "[fetchFilteredData, organizationId, paginationModel]", [2668, 2725], "[userDetails?.OrganizationId, paginationModel, accountId, fetchTrainingDocuments]", [32844, 32891], "[fetchData, paginationModel.page, paginationModel.pageSize]", [2258, 2271], "[Navigate, signOut]", [1172, 1187], "[showEditPopup, userId]", [12726, 12757], "[triggerAdd, selectedLanguages, handleAddLanguage]", [21506, 21508], "[userDetails?.OrganizationId]", [28665, 28697], "[toLanguage, type, selectedData, filters]", [6018, 6038], "[isShareFeedbackPopupTwo]", [1371, 1380], "[column, options, searchText]", [4870, 4885], "[fetchUserDetails, showEditPopup, userId]", [2946, 2971], "[options, models, column, filters, optionsModel]", [3978, 4004], "[column, models, allNames, filters, optionsModel]", [1602, 1611]]