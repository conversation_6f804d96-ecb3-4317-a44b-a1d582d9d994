{"ast": null, "code": "import _objectSpread from\"E:/Code/Qadpt/quickadapt/QuickAdaptWeb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from\"react\";import{NavLink,useLocation}from\"react-router-dom\";import{subscribe}from\"../adminMenu/sidemenustate\";import{List,Typography,Box}from\"@mui/material\";import{styled}from'@mui/material/styles';import{useAuth}from\"../auth/AuthProvider\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// Styled components for modern design\nconst ModernSidebar=styled(Box)(_ref=>{let{sidebarOpen}=_ref;return{width:sidebarOpen?'280px':'80px',height:'100vh',backgroundColor:'var(--color-white)',borderRight:'1px solid var(--color-gray-200)',transition:'var(--transition-normal)',position:'fixed',left:0,top:'45px',zIndex:10,boxShadow:'var(--shadow-sm)'};});const SidebarHeader=styled(Box)({padding:'var(--spacing-6) var(--spacing-4) var(--spacing-4)',borderBottom:'1px solid var(--color-gray-100)'});const SidebarTitle=styled(Typography)({fontSize:'var(--font-size-lg)',fontWeight:'var(--font-weight-semibold)',color:'var(--color-gray-900)',margin:0});const ModernList=styled(List)({padding:'var(--spacing-2) 0'});const ModernListItemWrapper=styled(NavLink)(_ref2=>{let{active}=_ref2;return _objectSpread(_objectSpread({margin:'0 var(--spacing-2)',borderRadius:'var(--radius-md)',marginBottom:'var(--spacing-1)',padding:'var(--spacing-3) var(--spacing-4)',transition:'var(--transition-fast)',textDecoration:'none',display:'block',cursor:'pointer'},active&&{backgroundColor:'var(--color-primary-50)',borderLeft:'3px solid var(--color-primary-600)'}),{},{'&:hover':{backgroundColor:active?'var(--color-primary-50)':'var(--color-gray-50)'}});});const ModernListItemText=styled(Typography)(_ref3=>{let{active}=_ref3;return{fontSize:'var(--font-size-sm)',fontWeight:active?'var(--font-weight-medium)':'var(--font-weight-normal)',color:active?'var(--color-primary-700)':'var(--color-gray-700)',transition:'var(--transition-fast)'};});const Settings=()=>{var _userDetails$Organiza,_userDetails$UserType;const[sidebarOpen,setSidebarOpen]=useState(true);const location=useLocation();const isHidden=location.pathname===\"/settings/team\";const{signOut,userDetails}=useAuth();const[OrganizationId,setOrganizationId]=useState((_userDetails$Organiza=userDetails===null||userDetails===void 0?void 0:userDetails.OrganizationId)!==null&&_userDetails$Organiza!==void 0?_userDetails$Organiza:\"\");const[userType,setUserType]=useState((_userDetails$UserType=userDetails===null||userDetails===void 0?void 0:userDetails.UserType)!==null&&_userDetails$UserType!==void 0?_userDetails$UserType:\"\");const[user,setUser]=useState(null);useEffect(()=>{const unsubscribe=subscribe(setSidebarOpen);return()=>unsubscribe();},[]);useEffect(()=>{const userInfoString=localStorage.getItem(\"userInfo\");if(userInfoString){try{const userInfo=JSON.parse(userInfoString);if(userInfo['user']){const parsedUser=JSON.parse(userInfo['user']);setUser(parsedUser);if(parsedUser){var _parsedUser$Organizat;const OrgId=(_parsedUser$Organizat=parsedUser.OrganizationId)!==null&&_parsedUser$Organizat!==void 0?_parsedUser$Organizat:'';setOrganizationId(OrgId);}}}catch(error){console.error(\"Error parsing userInfo: \",error);}}else if(userDetails){setUser(userDetails);if(userDetails){var _userDetails$Organiza2;const OrgId=(_userDetails$Organiza2=userDetails.OrganizationId)!==null&&_userDetails$Organiza2!==void 0?_userDetails$Organiza2:'';setOrganizationId(OrgId);}}},[]);const{t:translate}=useTranslation();const settingsItems=[{text:translate('Team'),path:\"/\".concat(OrganizationId,\"/team\"),access:['admin']},{text:translate('Roles'),path:\"/\".concat(OrganizationId,\"/roles\"),access:['admin']},{text:translate('Account'),path:\"/\".concat(OrganizationId,\"/accounts\"),access:['admin']},{text:translate('Multilingual'),path:\"/settings/multilingual\",access:['admin','user']},{text:translate('Domain'),path:\"/settings/domains\",access:['admin']},{text:translate('Rights'),path:\"/settings/rights\",access:['admin','user']},{text:translate('Alerts'),path:\"/settings/alerts\",access:['admin','user']},{text:translate('Billing'),path:\"/settings/billing\",access:['admin']},{text:translate('Installation'),path:\"/settings/install\",access:['admin','user']},{text:translate('Activity Log'),path:\"/settings/activitylog\",access:['admin']},{text:translate('Agents'),path:\"/settings/agents\",access:['admin','user']},{text:translate('Training'),path:\"/settings/training\",access:['admin','user']}];return/*#__PURE__*/_jsxs(ModernSidebar,{sidebarOpen:sidebarOpen,children:[/*#__PURE__*/_jsx(SidebarHeader,{children:/*#__PURE__*/_jsx(SidebarTitle,{children:\"Settings\"})}),/*#__PURE__*/_jsx(ModernList,{children:settingsItems.map((item,index)=>{if(!item.access.includes(userType.toLowerCase())){return null;}const isActive=location.pathname===item.path||item.path===\"/settings/agents\"&&(location.pathname===\"/settings/scripts\"||location.pathname===\"/settings/scripthistory\"||location.pathname===\"/settings/scripthistoryviewer\");return/*#__PURE__*/_jsx(ModernListItemWrapper,{active:isActive,to:item.path,children:/*#__PURE__*/_jsx(ModernListItemText,{active:isActive,children:item.text})},index);})})]});};export default Settings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "NavLink", "useLocation", "subscribe", "List", "Typography", "Box", "styled", "useAuth", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "ModernSidebar", "_ref", "sidebarOpen", "width", "height", "backgroundColor", "borderRight", "transition", "position", "left", "top", "zIndex", "boxShadow", "SidebarHeader", "padding", "borderBottom", "SidebarTitle", "fontSize", "fontWeight", "color", "margin", "ModernList", "ModernListItemWrapper", "_ref2", "active", "_objectSpread", "borderRadius", "marginBottom", "textDecoration", "display", "cursor", "borderLeft", "ModernListItemText", "_ref3", "Settings", "_userDetails$Organiza", "_userDetails$UserType", "setSidebarOpen", "location", "isHidden", "pathname", "signOut", "userDetails", "OrganizationId", "setOrganizationId", "userType", "setUserType", "UserType", "user", "setUser", "unsubscribe", "userInfoString", "localStorage", "getItem", "userInfo", "JSON", "parse", "parsedUser", "_parsedUser$Organizat", "OrgId", "error", "console", "_userDetails$Organiza2", "t", "translate", "settingsItems", "text", "path", "concat", "access", "children", "map", "item", "index", "includes", "toLowerCase", "isActive", "to"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptWeb/src/components/settings/Settings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { NavLink, useLocation } from \"react-router-dom\";\r\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport { Container, List, ListItem, ListItemText, Typography, Box } from \"@mui/material\";\r\nimport { styled } from '@mui/material/styles';\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport { User } from \"../../models/User\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport Card from \"../common/Card\";\r\n\r\n\r\n// Styled components for modern design\r\nconst ModernSidebar = styled(Box)<{ sidebarOpen: boolean }>(({ sidebarOpen }) => ({\r\n  width: sidebarOpen ? '280px' : '80px',\r\n  height: '100vh',\r\n  backgroundColor: 'var(--color-white)',\r\n  borderRight: '1px solid var(--color-gray-200)',\r\n  transition: 'var(--transition-normal)',\r\n  position: 'fixed',\r\n  left: 0,\r\n  top: '45px',\r\n  zIndex: 10,\r\n  boxShadow: 'var(--shadow-sm)',\r\n}));\r\n\r\nconst SidebarHeader = styled(Box)({\r\n  padding: 'var(--spacing-6) var(--spacing-4) var(--spacing-4)',\r\n  borderBottom: '1px solid var(--color-gray-100)',\r\n});\r\n\r\nconst SidebarTitle = styled(Typography)({\r\n  fontSize: 'var(--font-size-lg)',\r\n  fontWeight: 'var(--font-weight-semibold)',\r\n  color: 'var(--color-gray-900)',\r\n  margin: 0,\r\n});\r\n\r\nconst ModernList = styled(List)({\r\n  padding: 'var(--spacing-2) 0',\r\n});\r\n\r\nconst ModernListItemWrapper = styled(NavLink)<{ active: boolean }>(({ active }) => ({\r\n  margin: '0 var(--spacing-2)',\r\n  borderRadius: 'var(--radius-md)',\r\n  marginBottom: 'var(--spacing-1)',\r\n  padding: 'var(--spacing-3) var(--spacing-4)',\r\n  transition: 'var(--transition-fast)',\r\n  textDecoration: 'none',\r\n  display: 'block',\r\n  cursor: 'pointer',\r\n\r\n  ...(active && {\r\n    backgroundColor: 'var(--color-primary-50)',\r\n    borderLeft: '3px solid var(--color-primary-600)',\r\n  }),\r\n\r\n  '&:hover': {\r\n    backgroundColor: active ? 'var(--color-primary-50)' : 'var(--color-gray-50)',\r\n  },\r\n}));\r\n\r\nconst ModernListItemText = styled(Typography)<{ active: boolean }>(({ active }) => ({\r\n  fontSize: 'var(--font-size-sm)',\r\n  fontWeight: active ? 'var(--font-weight-medium)' : 'var(--font-weight-normal)',\r\n  color: active ? 'var(--color-primary-700)' : 'var(--color-gray-700)',\r\n  transition: 'var(--transition-fast)',\r\n}));\r\n\r\nconst Settings = () => {\r\n  const [sidebarOpen, setSidebarOpen] = useState(true);\r\n  const location = useLocation();\r\n  const isHidden = location.pathname === \"/settings/team\";\r\n  const { signOut, userDetails } = useAuth();\r\n\tconst [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? \"\");\r\n\tconst [userType, setUserType] = useState(userDetails?.UserType ?? \"\");\r\n\r\n  const [user, setUser] = useState<User | null>(null);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst unsubscribe = subscribe(setSidebarOpen);\r\n\t\treturn () => unsubscribe();\r\n\t}, []);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst userInfoString = localStorage.getItem(\"userInfo\");\r\n\t\tif (userInfoString) {\r\n\t\t\ttry {\r\n\t\t\t\tconst userInfo = JSON.parse(userInfoString);\r\n\t\t\t\tif (userInfo['user']) {\r\n\t\t\t\t\tconst parsedUser = JSON.parse(userInfo['user']);\r\n\t\t\t\t\tsetUser(parsedUser);\r\n\t\t\t\t\tif (parsedUser) {\r\n\t\t\t\t\t\tconst OrgId = parsedUser.OrganizationId ?? '';\r\n\t\t\t\t\t\tsetOrganizationId(OrgId);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error parsing userInfo: \", error);\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if (userDetails) {\r\n\t\t\tsetUser(userDetails);\r\n\t\t\tif (userDetails) {\r\n\t\t\t\tconst OrgId = userDetails.OrganizationId ?? '';\r\n\t\t\t\tsetOrganizationId(OrgId);\r\n\t\t\t}\r\n\t\t}\r\n\t}, []);\r\n\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\tconst settingsItems = [\r\n\t\t{ text: translate('Team'), path: `/${OrganizationId}/team`, access: ['admin'] },\r\n\t\t{ text: translate('Roles'), path: `/${OrganizationId}/roles`, access: ['admin'] },\r\n\t\t{ text: translate('Account'), path: `/${OrganizationId}/accounts`, access: ['admin'] },\r\n\t\t{ text: translate('Multilingual'), path: \"/settings/multilingual\", access: ['admin','user'] },\r\n\t\t{ text: translate('Domain'), path: \"/settings/domains\", access: ['admin'] },\r\n\t\t{ text: translate('Rights'), path: \"/settings/rights\", access: ['admin','user'] },\r\n\t\t{ text: translate('Alerts'), path: \"/settings/alerts\", access: ['admin','user'] },\r\n\t\t{ text: translate('Billing'), path: \"/settings/billing\", access: ['admin'] },\r\n\t\t{ text: translate('Installation'), path: \"/settings/install\", access: ['admin','user'] },\r\n\t\t{ text: translate('Activity Log'), path: \"/settings/activitylog\", access: ['admin'] },\r\n\t\t{ text: translate('Agents'), path: \"/settings/agents\", access: ['admin','user'] },\r\n\t\t{ text: translate('Training'), path: \"/settings/training\", access: ['admin','user'] },\r\n\t];\r\n\r\n\treturn (\r\n\t\t<ModernSidebar sidebarOpen={sidebarOpen}>\r\n\t\t\t<SidebarHeader>\r\n\t\t\t\t<SidebarTitle>Settings</SidebarTitle>\r\n\t\t\t</SidebarHeader>\r\n\r\n\t\t\t<ModernList>\r\n\t\t\t\t{settingsItems.map((item, index) => {\r\n\t\t\t\t\tif (!item.access.includes(userType.toLowerCase())) {\r\n\t\t\t\t\t\treturn null;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst isActive = location.pathname === item.path ||\r\n\t\t\t\t\t\t(item.path === \"/settings/agents\" &&\r\n\t\t\t\t\t\t (location.pathname === \"/settings/scripts\" ||\r\n\t\t\t\t\t\t  location.pathname === \"/settings/scripthistory\" ||\r\n\t\t\t\t\t\t  location.pathname === \"/settings/scripthistoryviewer\"));\r\n\r\n\t\t\t\t\treturn (\r\n\t\t\t\t\t\t<ModernListItemWrapper\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tactive={isActive}\r\n\t\t\t\t\t\t\tto={item.path}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ModernListItemText active={isActive}>\r\n\t\t\t\t\t\t\t\t{item.text}\r\n\t\t\t\t\t\t\t</ModernListItemText>\r\n\t\t\t\t\t\t</ModernListItemWrapper>\r\n\t\t\t\t\t);\r\n\t\t\t\t})}\r\n\t\t\t</ModernList>\r\n\t\t</ModernSidebar>\r\n\t);\r\n};\r\n\r\nexport default Settings;\r\n"], "mappings": "2HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,OAAO,CAAEC,WAAW,KAAQ,kBAAkB,CACvD,OAAwBC,SAAS,KAAQ,4BAA4B,CACrE,OAAoBC,IAAI,CAA0BC,UAAU,CAAEC,GAAG,KAAQ,eAAe,CACxF,OAASC,MAAM,KAAQ,sBAAsB,CAC7C,OAASC,OAAO,KAAQ,sBAAsB,CAE9C,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAI/C;AACA,KAAM,CAAAC,aAAa,CAAGP,MAAM,CAACD,GAAG,CAAC,CAA2BS,IAAA,MAAC,CAAEC,WAAY,CAAC,CAAAD,IAAA,OAAM,CAChFE,KAAK,CAAED,WAAW,CAAG,OAAO,CAAG,MAAM,CACrCE,MAAM,CAAE,OAAO,CACfC,eAAe,CAAE,oBAAoB,CACrCC,WAAW,CAAE,iCAAiC,CAC9CC,UAAU,CAAE,0BAA0B,CACtCC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,CAAC,CACPC,GAAG,CAAE,MAAM,CACXC,MAAM,CAAE,EAAE,CACVC,SAAS,CAAE,kBACb,CAAC,EAAC,CAAC,CAEH,KAAM,CAAAC,aAAa,CAAGpB,MAAM,CAACD,GAAG,CAAC,CAAC,CAChCsB,OAAO,CAAE,oDAAoD,CAC7DC,YAAY,CAAE,iCAChB,CAAC,CAAC,CAEF,KAAM,CAAAC,YAAY,CAAGvB,MAAM,CAACF,UAAU,CAAC,CAAC,CACtC0B,QAAQ,CAAE,qBAAqB,CAC/BC,UAAU,CAAE,6BAA6B,CACzCC,KAAK,CAAE,uBAAuB,CAC9BC,MAAM,CAAE,CACV,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAU,CAAG5B,MAAM,CAACH,IAAI,CAAC,CAAC,CAC9BwB,OAAO,CAAE,oBACX,CAAC,CAAC,CAEF,KAAM,CAAAQ,qBAAqB,CAAG7B,MAAM,CAACN,OAAO,CAAC,CAAsBoC,KAAA,MAAC,CAAEC,MAAO,CAAC,CAAAD,KAAA,QAAAE,aAAA,CAAAA,aAAA,EAC5EL,MAAM,CAAE,oBAAoB,CAC5BM,YAAY,CAAE,kBAAkB,CAChCC,YAAY,CAAE,kBAAkB,CAChCb,OAAO,CAAE,mCAAmC,CAC5CP,UAAU,CAAE,wBAAwB,CACpCqB,cAAc,CAAE,MAAM,CACtBC,OAAO,CAAE,OAAO,CAChBC,MAAM,CAAE,SAAS,EAEbN,MAAM,EAAI,CACZnB,eAAe,CAAE,yBAAyB,CAC1C0B,UAAU,CAAE,oCACd,CAAC,MAED,SAAS,CAAE,CACT1B,eAAe,CAAEmB,MAAM,CAAG,yBAAyB,CAAG,sBACxD,CAAC,IACD,CAAC,CAEH,KAAM,CAAAQ,kBAAkB,CAAGvC,MAAM,CAACF,UAAU,CAAC,CAAsB0C,KAAA,MAAC,CAAET,MAAO,CAAC,CAAAS,KAAA,OAAM,CAClFhB,QAAQ,CAAE,qBAAqB,CAC/BC,UAAU,CAAEM,MAAM,CAAG,2BAA2B,CAAG,2BAA2B,CAC9EL,KAAK,CAAEK,MAAM,CAAG,0BAA0B,CAAG,uBAAuB,CACpEjB,UAAU,CAAE,wBACd,CAAC,EAAC,CAAC,CAEH,KAAM,CAAA2B,QAAQ,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,qBAAA,CACrB,KAAM,CAAClC,WAAW,CAAEmC,cAAc,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAAqD,QAAQ,CAAGlD,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAmD,QAAQ,CAAGD,QAAQ,CAACE,QAAQ,GAAK,gBAAgB,CACvD,KAAM,CAAEC,OAAO,CAAEC,WAAY,CAAC,CAAGhD,OAAO,CAAC,CAAC,CAC3C,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAG3D,QAAQ,EAAAkD,qBAAA,CAACO,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEC,cAAc,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACvF,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAG7D,QAAQ,EAAAmD,qBAAA,CAACM,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEK,QAAQ,UAAAX,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAEpE,KAAM,CAACY,IAAI,CAAEC,OAAO,CAAC,CAAGhE,QAAQ,CAAc,IAAI,CAAC,CAEpDC,SAAS,CAAC,IAAM,CACf,KAAM,CAAAgE,WAAW,CAAG7D,SAAS,CAACgD,cAAc,CAAC,CAC7C,MAAO,IAAMa,WAAW,CAAC,CAAC,CAC3B,CAAC,CAAE,EAAE,CAAC,CAENhE,SAAS,CAAC,IAAM,CACf,KAAM,CAAAiE,cAAc,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CACvD,GAAIF,cAAc,CAAE,CACnB,GAAI,CACH,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC,CAC3C,GAAIG,QAAQ,CAAC,MAAM,CAAC,CAAE,CACrB,KAAM,CAAAG,UAAU,CAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC,CAC/CL,OAAO,CAACQ,UAAU,CAAC,CACnB,GAAIA,UAAU,CAAE,KAAAC,qBAAA,CACf,KAAM,CAAAC,KAAK,EAAAD,qBAAA,CAAGD,UAAU,CAACd,cAAc,UAAAe,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC7Cd,iBAAiB,CAACe,KAAK,CAAC,CACzB,CACD,CACD,CAAE,MAAOC,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CACjD,CACD,CAAC,IACI,IAAIlB,WAAW,CAAE,CACrBO,OAAO,CAACP,WAAW,CAAC,CACpB,GAAIA,WAAW,CAAE,KAAAoB,sBAAA,CAChB,KAAM,CAAAH,KAAK,EAAAG,sBAAA,CAAGpB,WAAW,CAACC,cAAc,UAAAmB,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC9ClB,iBAAiB,CAACe,KAAK,CAAC,CACzB,CACD,CACD,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAEI,CAAC,CAAEC,SAAU,CAAC,CAAGrE,cAAc,CAAC,CAAC,CAEzC,KAAM,CAAAsE,aAAa,CAAG,CACrB,CAAEC,IAAI,CAAEF,SAAS,CAAC,MAAM,CAAC,CAAEG,IAAI,KAAAC,MAAA,CAAMzB,cAAc,SAAO,CAAE0B,MAAM,CAAE,CAAC,OAAO,CAAE,CAAC,CAC/E,CAAEH,IAAI,CAAEF,SAAS,CAAC,OAAO,CAAC,CAAEG,IAAI,KAAAC,MAAA,CAAMzB,cAAc,UAAQ,CAAE0B,MAAM,CAAE,CAAC,OAAO,CAAE,CAAC,CACjF,CAAEH,IAAI,CAAEF,SAAS,CAAC,SAAS,CAAC,CAAEG,IAAI,KAAAC,MAAA,CAAMzB,cAAc,aAAW,CAAE0B,MAAM,CAAE,CAAC,OAAO,CAAE,CAAC,CACtF,CAAEH,IAAI,CAAEF,SAAS,CAAC,cAAc,CAAC,CAAEG,IAAI,CAAE,wBAAwB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAC,MAAM,CAAE,CAAC,CAC7F,CAAEH,IAAI,CAAEF,SAAS,CAAC,QAAQ,CAAC,CAAEG,IAAI,CAAE,mBAAmB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAE,CAAC,CAC3E,CAAEH,IAAI,CAAEF,SAAS,CAAC,QAAQ,CAAC,CAAEG,IAAI,CAAE,kBAAkB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAC,MAAM,CAAE,CAAC,CACjF,CAAEH,IAAI,CAAEF,SAAS,CAAC,QAAQ,CAAC,CAAEG,IAAI,CAAE,kBAAkB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAC,MAAM,CAAE,CAAC,CACjF,CAAEH,IAAI,CAAEF,SAAS,CAAC,SAAS,CAAC,CAAEG,IAAI,CAAE,mBAAmB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAE,CAAC,CAC5E,CAAEH,IAAI,CAAEF,SAAS,CAAC,cAAc,CAAC,CAAEG,IAAI,CAAE,mBAAmB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAC,MAAM,CAAE,CAAC,CACxF,CAAEH,IAAI,CAAEF,SAAS,CAAC,cAAc,CAAC,CAAEG,IAAI,CAAE,uBAAuB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAE,CAAC,CACrF,CAAEH,IAAI,CAAEF,SAAS,CAAC,QAAQ,CAAC,CAAEG,IAAI,CAAE,kBAAkB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAC,MAAM,CAAE,CAAC,CACjF,CAAEH,IAAI,CAAEF,SAAS,CAAC,UAAU,CAAC,CAAEG,IAAI,CAAE,oBAAoB,CAAEE,MAAM,CAAE,CAAC,OAAO,CAAC,MAAM,CAAE,CAAC,CACrF,CAED,mBACCtE,KAAA,CAACC,aAAa,EAACE,WAAW,CAAEA,WAAY,CAAAoE,QAAA,eACvCzE,IAAA,CAACgB,aAAa,EAAAyD,QAAA,cACbzE,IAAA,CAACmB,YAAY,EAAAsD,QAAA,CAAC,UAAQ,CAAc,CAAC,CACvB,CAAC,cAEhBzE,IAAA,CAACwB,UAAU,EAAAiD,QAAA,CACTL,aAAa,CAACM,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACnC,GAAI,CAACD,IAAI,CAACH,MAAM,CAACK,QAAQ,CAAC7B,QAAQ,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAAE,CAClD,MAAO,KAAI,CACZ,CAEA,KAAM,CAAAC,QAAQ,CAAGtC,QAAQ,CAACE,QAAQ,GAAKgC,IAAI,CAACL,IAAI,EAC9CK,IAAI,CAACL,IAAI,GAAK,kBAAkB,GAC/B7B,QAAQ,CAACE,QAAQ,GAAK,mBAAmB,EACzCF,QAAQ,CAACE,QAAQ,GAAK,yBAAyB,EAC/CF,QAAQ,CAACE,QAAQ,GAAK,+BAA+B,CAAE,CAE1D,mBACC3C,IAAA,CAACyB,qBAAqB,EAErBE,MAAM,CAAEoD,QAAS,CACjBC,EAAE,CAAEL,IAAI,CAACL,IAAK,CAAAG,QAAA,cAEdzE,IAAA,CAACmC,kBAAkB,EAACR,MAAM,CAAEoD,QAAS,CAAAN,QAAA,CACnCE,IAAI,CAACN,IAAI,CACS,CAAC,EANhBO,KAOiB,CAAC,CAE1B,CAAC,CAAC,CACS,CAAC,EACC,CAAC,CAElB,CAAC,CAED,cAAe,CAAAvC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}