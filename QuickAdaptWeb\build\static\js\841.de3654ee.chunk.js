"use strict";(self.webpackChunkquickadapt=self.webpackChunkquickadapt||[]).push([[841],{3841:(a,e,n)=>{n.r(e),n.d(e,{default:()=>r});n(5043);const i=n.p+"static/media/Expiredlink.b6db55b8eccba1f8472790338c0aa0fb.svg";var s=n(3216),t=n(2518),p=n(579);const r=()=>{(0,s.Zp)();return(0,p.jsxs)("div",{style:{textAlign:"center",backgroundColor:"#f0f4f8",padding:"60px",marginTop:"-40px"},children:[(0,p.jsx)("h1",{children:"Oops!"}),(0,p.jsx)("img",{src:i,alt:"Reset Password Illustration"}),(0,p.jsx)("p",{children:"Reset Password Link Expired"}),(0,p.jsx)(t.A,{href:"https://app.quickadopt.in/Login",variant:"contained",style:{marginTop:"20px",padding:"10px 20px",fontSize:"16px",textTransform:"none"},children:"Back To Login Page"})]})}}}]);
//# sourceMappingURL=841.de3654ee.chunk.js.map