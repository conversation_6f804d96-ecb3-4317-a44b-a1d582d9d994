{"version": 3, "file": "static/js/130.df5d0f15.chunk.js", "mappings": "qXAiDA,MAAMA,EAAW,CAChB,YAAe,CACd,QACA,UAED,YAAe,CACd,cACA,cACA,cACA,gBACA,sBACA,sBACA,sBACA,iBACA,iBACA,iBACA,gBACA,gBACA,gBACA,mBACA,mBACA,mBACA,gBACA,gBACA,gBACA,iBACA,iBACA,iBACA,oBAED,KAAQ,CACP,iBACA,cACA,kBACA,mBAED,SAAY,CACX,mBACA,kBACA,mBAED,QAAW,CACV,iBACA,iBACA,mBAiqBF,EA7pByCC,KACxC,MAAOC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAqB,KAChDC,EAAcC,IAAmBF,EAAAA,EAAAA,aACjCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAgBC,IAAqBN,EAAAA,EAAAA,UAAmB,KACxDO,EAAgBC,IAAqBR,EAAAA,EAAAA,aACrCS,EAAOC,IAAYV,EAAAA,EAAAA,UAAwBW,EAAAA,KAC3CC,EAAOC,IAAYb,EAAAA,EAAAA,UAAmB,KACtCc,EAAaC,IAAkBf,EAAAA,EAAAA,UAAiB,KAChDgB,EAAMC,IAAWjB,EAAAA,EAAAA,UAAwB,OACzCkB,EAAeC,IAAoBnB,EAAAA,EAAAA,UAAwB,OAC3DoB,EAAaC,IAAkBrB,EAAAA,EAAAA,UAAmB,KAClDsB,EAAMC,IAAWvB,EAAAA,EAAAA,UAAiB,KAClCwB,EAAMC,IAAWzB,EAAAA,EAAAA,UAAwB,OACzC0B,EAAeC,IAAoB3B,EAAAA,EAAAA,UAAgB,KACnD4B,EAAcC,IAAmB7B,EAAAA,EAAAA,UAAiB,KAClD8B,EAAqBC,IAA0B/B,EAAAA,EAAAA,UAAS,KACrDgC,EAASC,IAAcjC,EAAAA,EAAAA,UAAS,KACnCkC,EAAQC,IAAanC,EAAAA,EAAAA,UAAS,KAC9BoC,GAAaC,KAAkBrC,EAAAA,EAAAA,UAAiB,KAChDsC,GAAYC,KAAiBvC,EAAAA,EAAAA,UAAiB,KAC9CwC,GAAWC,KAAgBzC,EAAAA,EAAAA,UAAiB,KAC5C0C,GAAiBC,KAAsB3C,EAAAA,EAAAA,UAAqB,KAC5D4C,GAAwBC,KAA6B7C,EAAAA,EAAAA,UAAwB,OAC7E8C,GAAOC,KAAY/C,EAAAA,EAAAA,UAAgB,KACnCgD,GAAcC,KAAmBjD,EAAAA,EAAAA,UAAS,OAC1CkD,GAAYC,KAAiBnD,EAAAA,EAAAA,WAAS,IACtCoD,GAAeC,KAAoBrD,EAAAA,EAAAA,UAAS,KAC5CsD,GAASC,KAAcvD,EAAAA,EAAAA,UAAS,KAIhCwD,GAAaC,KAAkBzD,EAAAA,EAAAA,WAAS0D,EAAAA,EAAAA,QACxCC,GAAiBC,KAAsB5D,EAAAA,EAAAA,UAAS,CACtD6D,KAAM,EACNC,SAAU,MAGJC,GAAMC,KAAWhE,EAAAA,EAAAA,UAAS,IACvBiE,GAAKC,KAAUlE,EAAAA,EAAAA,UAAS,KACxBmE,GAAYC,KAAiBpE,EAAAA,EAAAA,UAAS,GAY1CqE,GAAwBC,IACtBC,EAAAA,EAAAA,GAAO,IAAIC,KAAKF,GAAa,wBAErCG,EAAAA,EAAAA,YAAU,KAEPpD,EADEH,GAAiBtB,EAAWsB,GACftB,EAAWsB,GAEX,IAEjBD,EAAQ,GAAG,GACT,CAACC,KAEJuD,EAAAA,EAAAA,YAAU,KACwBC,WAC/B,MAAMC,QAAgBC,EAAAA,EAAAA,IAAmBjD,EAAkBvB,GAC3D,GAAIuE,GAAWA,EAAQE,OAAS,EAAG,CACpClD,EAAiBgD,GAEjB,MAAM7C,EAAsB6C,EAAQ,GAC9B7C,IACLC,EAAuBD,GACvBtB,EAAkBsB,EAAoBnB,gBACtCsB,EAAWH,EAAoBR,MAE/B,GAGFwD,EAA0B,GACtB,KAELL,EAAAA,EAAAA,YAAU,KACwBC,WAC/B,MAAMC,QAAgBC,EAAAA,EAAAA,IAAmBjD,EAAkBvB,GAC3D,GAAIuE,EAAS,CACdhD,EAAiBgD,GAEjB,MAAM7C,EAAsB6C,EAAQI,MAAMC,GAAyB,UAAbA,EAAIC,OACtDnD,GACFD,EAAgBC,EAAoBnB,eAErC,GAGFmE,EAA0B,GACtB,KAaLL,EAAAA,EAAAA,YAAU,KACT,GAAIlE,EAAgB,CACOmE,iBACpBQ,EAAAA,EAAAA,IACJ/C,EACA/B,EACAG,EACA,EACA,GACA6D,GACA,GACA,GACD,EAGDe,GACAC,GAAkBpC,GAAazC,GAC/BC,EAAkBD,EACnB,IACI,CAACA,KACJkE,EAAAA,EAAAA,YAAU,KACX,GAAIY,MAAMC,QAAQpD,GAAS,CAEzB,MAAMqD,EAAYrD,EAAOsD,KAAIhE,GAAQA,EAAKiE,UAC1C1C,GAASwC,EACX,MACEG,QAAQC,MAAM,0BAA2BzD,EAC3C,GACI,CAACA,IACJ,MAAM0D,GAAeC,MACfC,GAAyBF,GAAMG,SAAS,EAAG,QAC1CC,GAAUC,KAAejG,EAAAA,EAAAA,UAAuB6F,IAAMC,MACtDI,GAAQC,KAAanG,EAAAA,EAAAA,UAAuB6F,IAAMD,IAAOQ,MAAM,SAYvE3B,EAAAA,EAAAA,YAAU,KACTW,GAAkBpC,GAAczC,EAAe,GAC7C,CAACoD,KASL,MAAMyB,GAAoBV,MAAO1B,EAAiBzC,KACjD,GAAKA,EAKL,IAEEH,GAAW,GAGX,MAAMiG,EAAY1C,GAAgBG,UAAY,GAExCwC,EADa3C,GAAgBE,KAAOwC,EAEpCE,EAAO5C,GAAgBG,SAG7BE,GAAQsC,GACRpC,GAAOqC,GAGP,MAAMC,QAAiBC,EAAAA,EAAAA,IACxBH,EACAC,EACAhG,EACA6C,GACAE,GACQ,OAAR0C,SAAQ,IAARA,QAAQ,EAARA,GAAUU,cACJ,OAANR,SAAM,IAANA,QAAM,EAANA,GAAQQ,cACRtC,GACErE,EACAiB,EACAE,EACAoB,GACFU,GAGC,GAAIwD,GAAYnB,MAAMC,QAAQkB,GAAW,CAE1C,MAAMG,EAA2BH,EAAShB,KAAKoB,IAAQ,CACrDC,WAAYD,EAAIC,WAChBC,YAAaF,EAAIE,YACjB7B,KAAM2B,EAAI3B,KACVtE,eAAgBiG,EAAIjG,eACpBoG,cAAeH,EAAIG,cACnBC,KAAMJ,EAAII,KACVC,UAAWL,EAAIK,UACfC,QAASN,EAAIM,QACbC,UAAWP,EAAIO,UACfC,YAAa/C,GAAqBuC,EAAIQ,iBAGxCrH,EAAa4G,EACZ,MACDjB,QAAQC,MAAM,2BAA4Ba,EAE3C,CAAE,MAAOb,GACPD,QAAQC,MAAM,6BAA8BA,EAC9C,CAAC,QACCvF,GAAW,EACb,MA1DEsF,QAAQC,MAAM,4CA0DhB,EAqKD,OAPAlB,EAAAA,EAAAA,YAAU,MACT4C,EAAAA,EAAAA,KAAU,KACT5D,IAAeC,EAAAA,EAAAA,MAAgB,GAC9B,GACA,KAIF4D,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EAEFD,EAAAA,EAAAA,MAAA,OAAKE,UAAU,aAAYD,SAAA,EACrBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,kBAAiBD,UAC/BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,cAAaD,SAAC,kBAE9BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mBAAkBD,UAEnCE,EAAAA,EAAAA,KAACC,EAAAA,EAAY,CACZC,QAASjG,EACTkG,eAAiBC,GAAWA,EAAO5C,KACnC6C,MAAOvH,EAAiBmB,EAAcqD,MAAKC,GAAOA,EAAIrE,iBAAmBJ,IAAkB,KAC3FwH,SAAUA,CAACC,EAAOC,KACjBzH,GAA0B,OAARyH,QAAQ,IAARA,OAAQ,EAARA,EAAUtH,iBAAkB,KAAK,EAGpDuH,YAAcC,IACbV,EAAAA,EAAAA,KAACW,EAAAA,GAASC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACLF,GAAM,IACVG,MAAM,sBACNC,YAAUF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACNF,EAAOI,YAAU,IACpBC,GAAI,CACH,oCAAqC,CACpCvE,IAAK,QAEN,oCAAqC,CACpCwE,WAAYvH,EAAgB,UAAY,SACxC+C,IAAK,sBA2CZwD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uBAAsBD,UACpCD,EAAAA,EAAAA,MAAA,OAAKE,UAAU,eAAcD,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKE,UAAU,aAAYD,SAAA,EACxBE,EAAAA,EAAAA,KAACC,EAAAA,EAAY,CAACF,UAAU,aACrBG,QAASe,OAAOC,KAAK/I,GACrBkI,MAAO5G,EACP6G,SAAUA,CAACC,EAAOC,IACf9G,EAAiB8G,GACpBC,YAAcC,IACXV,EAAAA,EAAAA,KAACW,EAAAA,GAASC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CAACb,UAAS,0BAAAoB,OAA4B1H,EAAgB,UAAY,WACrEiH,GAAM,IACVG,MAAO,WACPC,YAAUF,EAAAA,EAAAA,GAAA,GACJF,EAAOI,kBAItBd,EAAAA,EAAAA,KAACC,EAAAA,EAAY,CAACF,UAAU,aACrBG,QAASvG,EACT0G,MAAO9G,EACP+G,SAAUA,CAACC,EAAOC,IACfhH,EAAQgH,GACXC,YAAcC,IACXV,EAAAA,EAAAA,KAACW,EAAAA,GAASC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CAACb,UAAS,0BAAAoB,OAA4B1H,EAAgB,UAAY,WACrEiH,GAAM,IACVG,MAAM,aACNC,YAAUF,EAAAA,EAAAA,GAAA,GACJF,EAAOI,qBAKzBd,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UAChCD,EAAAA,EAAAA,MAACuB,EAAAA,EAAoB,CAACC,YAAaC,EAAAA,EAAaxB,SAAA,EAChDE,EAAAA,EAAAA,KAACuB,EAAAA,EAAc,CAACxB,UAAS,kBAAAoB,OAAoB5C,GAAW,GAAK,cACxDsC,MAAM,YACNW,MAAO,CAAC,OAAQ,QAAS,OAC7BnB,MAAO9B,GACP+B,SAAWE,GACVhC,GAAYgC,EAAWpC,IAAMoC,GAAY,MAC1CiB,YAAmB,OAANhD,SAAM,IAANA,GAAAA,QAAUiD,EAEvBC,UAAW,CACVC,UAAW,CACV7B,UAAW,oBAEZ8B,MAAO,CAAEC,WAAW,OAGV9B,EAAAA,EAAAA,KAACuB,EAAAA,EAAc,CAACxB,UAAS,0BAAAoB,OAA4B5C,GAAW,GAAK,cAC5EsC,MAAM,UACNW,MAAO,CAAC,OAAQ,QAAS,OACfnB,MAAO5B,GACP6B,SAAWE,GACR9B,GAAU8B,EAAWpC,IAAMoC,GAAU7B,MAAM,OAAS,MACvDoD,YAAqB,OAARxD,SAAQ,IAARA,GAAAA,QAAYmD,EACvCC,UAAW,CACVC,UAAW,CACT7B,UAAW,oBAEb8B,MAAO,CAAEC,WAAW,YAKhB9B,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAACZ,UAAU,WAClBiC,QAAQ,WACRnB,MAAM,OACNR,MAAOxG,EACPyG,SAAW2B,IACRnI,EAAQmI,EAAEC,OAAO7B,OACjBvF,GAAcmH,EAAEC,OAAO7B,MAAM,KAGvCR,EAAAA,EAAAA,MAAA,OAAKE,UAAU,gBAAeD,SAAA,EAC1BE,EAAAA,EAAAA,KAACmC,EAAAA,EAAM,CACJH,QAAQ,WACRI,MAAM,UACNC,QAASA,KAIN3G,IAAc,GAEdpC,EAAe,IACfE,EAAQ,IACRE,EAAiB,IACjBI,EAAQ,IACRgB,GAAc,IACd0D,GAAY,MACZE,GAAU,MACV1E,EAAQ,IAERmC,GAAmB,CAAEC,KAAM,EAAGC,SAAU,IAAK,EAC9CyD,SAAC,WAGNE,EAAAA,EAAAA,KAACmC,EAAAA,EAAM,CACJH,QAAQ,YACRI,MAAM,UACNC,QAASA,IA/UCpF,WACpB,GAAKnE,EAKL,IAEEH,GAAW,GAGX,MAAMiG,EAAY1C,GAAgBG,UAAY,GAExCwC,EADa3C,GAAgBE,KAAOwC,EAEpCE,EAAO5C,GAAgBG,SAG7BE,GAAQsC,GACRpC,GAAOqC,GAGP,MAAMC,QAAiBC,EAAAA,EAAAA,IACxBH,EACAC,EACAhG,EACA6C,GACAE,GACQ,OAAR0C,SAAQ,IAARA,QAAQ,EAARA,GAAUU,cACJ,OAANR,SAAM,IAANA,QAAM,EAANA,GAAQQ,cACRtC,GACErE,EACAiB,EACAE,EACAI,EACFc,IAGC,GAAIoE,GAAYnB,MAAMC,QAAQkB,GAAW,CAE1C,MAAMG,EAA2BH,EAAShB,KAAKoB,IAAQ,CACrDC,WAAYD,EAAIC,WAChBC,YAAaF,EAAIE,YACjB7B,KAAM2B,EAAI3B,KACVtE,eAAgBiG,EAAIjG,eACpBoG,cAAeH,EAAIG,cACnBC,KAAMJ,EAAII,KACVC,UAAWL,EAAIK,UACfC,QAASN,EAAIM,QACbC,UAAWP,EAAIO,UACfC,YAAa/C,GAAqBuC,EAAIQ,iBAGxCrH,EAAa4G,EACZ,MACDjB,QAAQC,MAAM,2BAA4Ba,EAE3C,CAAE,MAAOb,GACPD,QAAQC,MAAM,6BAA8BA,EAC9C,CAAC,QACCvF,GAAW,EACb,MA1DEsF,QAAQC,MAAM,gDA0DhB,EAmRyBoE,CAAaxJ,GAC5ByJ,SAA0B,KAAhBlJ,IACG,KAATE,GAAwB,OAATA,KACG,KAAlBE,GAA0C,OAAlBA,IACZ,OAAb8E,IACW,OAAXE,IACS,KAAT1E,GAAwB,KAATF,EAAYiG,SAAC,mBAQvCpH,GACAsH,EAAAA,EAAAA,KAAA,OAAKD,UAAU,eAAcD,UAC5BE,EAAAA,EAAAA,KAAA,OACCwC,IAAKC,EACLC,IAAI,UACJ3C,UAAU,2BAIVC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAQ,CACP5C,UAAU,oBA0DU6C,KAAMvK,EAC3BwK,QA3lBwB,CAC7B,CAAEhB,MAAO,gBAAiBiB,WAAY,WAAYC,MAAO,IAAKC,KAAM,EAAEC,mBAAmB,GACzF,CAACpB,MAAM,OAAOiB,WAAW,aAAaC,MAAM,IAAIC,KAAK,EAAEC,mBAAmB,GAC1E,CAAEpB,MAAO,OAAQiB,WAAY,OAAQC,MAAO,IAAKC,KAAM,EAAEC,mBAAmB,GAC5E,CAACpB,MAAM,YAAaiB,WAAW,aAAcC,MAAM,IAAIC,KAAK,EAAEC,mBAAmB,GACjF,CAAEpB,MAAO,UAAWiB,WAAY,UAAWC,MAAO,IAAKC,KAAM,EAAEC,mBAAmB,GAClF,CAAEpB,MAAO,YAAaiB,WAAY,aAAcC,MAAO,IAAKC,KAAM,EAAEC,mBAAmB,GACvF,CAAEpB,MAAO,cAAeiB,WAAY,eAAgBC,MAAO,IAAKC,KAAM,EAAEC,mBAAmB,IAqlBtFC,SAAWC,GAAQA,EAAI/D,WAEvBlD,gBAAiBA,GACjBkH,wBAAyBjH,GACjBkH,YAAU,EACVC,eAAe,SACvBC,SAAU7G,GACV8G,gBAAiB,CAAC,GAAI,GAAI,GAAI,KAC9BC,WAAY,CACVC,mBAAoB,CACrBC,iBAAkB,qBAGnBC,4BAA4B,EAC5BlL,QAASA,MAIR,C", "sources": ["components/auditLog/SuperAdminAuditLogList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n\tDataGrid,\r\n\tGridColDef,\r\n\tGridToolbarContainer,\r\n\tGridToolbarColumnsButton,\r\n\tGridToolbarFilterButton,\r\n\tGridToolbarDensitySelector,\r\n\tGridPaginationModel\r\n} from \"@mui/x-data-grid\";\r\nimport {\r\n\tButton,\r\n\tMenu,\r\n\tMenuItem,\r\n\tTextField,\r\n\tAutocomplete,\r\n\tIconButton\r\n} from \"@mui/material\";\r\nimport SaveAltIcon from \"@mui/icons-material/SaveAlt\";\r\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\r\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\r\nimport { AdapterDateFns } from \"@mui/x-date-pickers/AdapterDateFns\";\r\nimport loader from \"../../assets/loader.gif\";\r\nimport { getOrganization } from \"../../services/OrganizationService\";\r\nimport { isSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\nimport { GetAuditLogsBySearch, GetAuditLogsByOrganizationId } from \"../../services/AuditLogServices\";\r\nimport { SearchParams } from \"../../models/SearchParams\";\r\nimport { format } from 'date-fns';\r\nimport ClearIcon from '@mui/icons-material/Clear';\r\nimport { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';\r\nimport { fetchOrganizations } from \"../../services/AuditLogServices\";\r\nimport { fetchUsersList } from \"../../services/UserService\";\r\nimport { Box } from '@mui/material';\r\nimport { OrganizationId } from \"../common/Home\";\r\nimport dayjs, { Dayjs } from 'dayjs';\r\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\r\n\r\ninterface AuditLog {\r\n\tAuditLogId: string;\r\n\tReferenceId: string;\r\n\tName: string;\r\n\tReferenceType: string;\r\n\tOrganizationId: string;\r\n\tType: string;\r\n\tCreatedBy: string;\r\n\tBrowser: string,\r\n\tIPAddress: string,\r\n\tCreatedDate: string;\r\n}\r\nconst categories={\r\n\t\"UserSession\": [\r\n\t\t\"Login\",\r\n\t\t\"Logout\"\r\n\t],\r\n\t\"Experiences\": [\r\n\t\t\"TourCreated\",\r\n\t\t\"TourUpdated\",\r\n\t\t\"TourDeleted\",\r\n\t\t\"TourPublished\",\r\n\t\t\"AnnouncementCreated\",\r\n\t\t\"AnnouncementUpdated\",\r\n\t\t\"AnnouncementDeleted\",\r\n\t\t\"TooltipCreated\",\r\n\t\t\"TooltipUpdated\",\r\n\t\t\"TooltipDeleted\",\r\n\t\t\"BannerCreated\",\r\n\t\t\"BannerUpdated\",\r\n\t\t\"BannerDeleted\",\r\n\t\t\"ChecklistCreated\",\r\n\t\t\"ChecklistUpdated\",\r\n\t\t\"ChecklistDeleted\",\r\n\t\t\"SurveyCreated\",\r\n\t\t\"SurveyUpdated\",\r\n\t\t\"SurveyDeleted\",\r\n\t\t\"HotspotCreated\",\r\n\t\t\"HotspotUpdated\",\r\n\t\t\"HotspotDeleted\",\r\n\t\t\"HotspotPublished\"\r\n\t],\r\n\t\"User\": [\r\n\t\t\"NewUserCreated\",\r\n\t\t\"UserUpdated\",\r\n\t\t\"UserDeactivated\",\r\n\t\t\"UserReactivated\",\r\n\t],\r\n\t\"UserRole\": [\r\n\t\t\"UserRoleAssigned\",\r\n\t\t\"UserRoleUpdated\",\r\n\t\t\"UserRoleDeleted\"\r\n\t],\r\n\t\"Account\": [\r\n\t\t\"AccountCreated\",\r\n\t\t\"AccountUpdated\",\r\n\t\t\"AccountDeleted\",\r\n\t]\r\n}\r\n\r\nconst SuperAdminAuditLogList: React.FC = () => {\r\n\tconst [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);\r\n\tconst [totalRecords, setTotalRecords] = useState();\r\n\tconst [loading, setLoading] = useState(false);\r\n\tconst [referenceTypes, setReferenceTypes] = useState<string[]>([]);\r\n\tconst [organizationId, setOrganizationId] = useState<string | null>();\r\n\tconst [orgid, setOrgId] = useState<string | null>(OrganizationId);\r\n\tconst [types, setTypes] = useState<string[]>([]);\r\n\tconst [environment, setEnvironment] = useState<string>(\"\");\r\n\tconst [type, setType] = useState<string | null>(null);\r\n\tconst [referenceType, setReferenceType] = useState<string | null>(null);\r\n\tconst [typeOptions, setTypeOptions] = useState<string[]>([]);\r\n\tconst [name, setName] = useState<string>(\"\");\r\n\tconst [user, setUser] = useState<string | null>(null);\r\n\tconst [organizations, setOrganizations] = useState<any[]>([]);\r\n\tconst [organization, setOrganization] = useState<string>('');\r\n\tconst [defaultOrganization, setDefaultOrganization] = useState(\"\");  // Store the default organization\r\n    const [orgname, setOrgName] = useState(\"\");  // Name associated with the organization\r\n\tconst [models, setModels] = useState(\"\");\r\n\tconst [createdUser, setCreatedUser] = useState<string>('');\r\n\tconst [nameFilter, setNameFilter] = useState<string>('');\r\n\tconst [eventType, setEventType] = useState<string>('');\r\n\tconst [allAuditLogData, setAllAuditLogData] = useState<AuditLog[]>([]);\r\n\tconst [selectedOrganizationId, setSelectedOrganizationId] = useState<number | null>(null);\r\n\tconst [users, setUsers] = useState<any[]>([]);\r\n\tconst [selectedUser, setSelectedUser] = useState(null);\r\n\tconst [isClearing, setIsClearing] = useState(false);\r\n\tconst [orderByFields, setOrderByFields] = useState(\"\");\r\n\tconst [filters, setFilters] = useState([]);\r\n  \r\n\r\n\r\n\tconst [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());\r\n\tconst [paginationModel, setPaginationModel] = useState({\r\n\t\tpage: 0,\r\n\t\tpageSize: 15,\r\n\t});\r\n\t\r\n\tconst [skip, setskip] = useState(0);\r\n    const [top, settop] = useState(10);\r\n    const [totalcount, setTotalcount] = useState(0);\r\n\tconst columns: GridColDef[] = [\r\n\t\t{ field: \"ReferenceType\", headerName: \"Category\", width: 350, flex: 1,disableColumnMenu: true },\r\n\t\t{field:\"Type\",headerName:\"Event Type\",width:350,flex:1,disableColumnMenu: true},\r\n\t\t{ field: \"Name\", headerName: \"Name\", width: 350, flex: 1,disableColumnMenu: true },\r\n\t\t{field:\"IPAddress\", headerName:\"IP Address\", width:350,flex:1,disableColumnMenu: true},\r\n\t\t{ field: \"Browser\", headerName: \"Browser\", width: 300, flex: 1,disableColumnMenu: true },\r\n\t\t{ field: \"CreatedBy\", headerName: \"Created By\", width: 300, flex: 1,disableColumnMenu: true },\r\n\t\t{ field: \"CreatedDate\", headerName: \"Created Date\", width: 400 ,flex: 1,disableColumnMenu: true },\r\n\t];\r\n\t\r\n\t\r\n\tconst formatIndianDateTime = (dateString: string) => {\r\n\t\treturn format(new Date(dateString), 'dd-MM-yyyy HH:mm:ss');\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (referenceType && categories[referenceType as keyof typeof categories]) {\r\n\t\t  setTypeOptions(categories[referenceType as keyof typeof categories]);\r\n\t\t} else {\r\n\t\t  setTypeOptions([]);\r\n\t\t}\r\n\t\tsetType(\"\");\r\n\t}, [referenceType]);\r\n\t// here setting organizationid \r\n\tuseEffect(() => {\r\n\t\tconst fetchAndSetOrganizations = async () => {\r\n\t\t  const orgData = await fetchOrganizations(setOrganizations, setLoading);\r\n\t\t  if (orgData && orgData.length > 0) {\r\n\t\t\tsetOrganizations(orgData);\r\n\t\t\t// Set the first organization as the default selected organization\r\n\t\t\tconst defaultOrganization = orgData[0];\r\n\t\t\t  if (defaultOrganization) {\r\n\t\t\t\tsetDefaultOrganization(defaultOrganization);\r\n\t\t\t\tsetOrganizationId(defaultOrganization.OrganizationId);\r\n\t\t\t\tsetOrgName(defaultOrganization.name); // Set the default name\r\n\t\t\t}\r\n\t\t  }\r\n\t\t};\r\n\t  \r\n\t\tfetchAndSetOrganizations();\r\n\t  }, []);\r\n\t  \r\n\tuseEffect(() => {\r\n\t\tconst fetchAndSetOrganizations = async () => {\r\n\t\t  const orgData = await fetchOrganizations(setOrganizations, setLoading);\r\n\t\t  if (orgData) {\r\n\t\t\tsetOrganizations(orgData);\r\n\t\t\t  // Find \"Quixy\" and set it as the default selected organization\r\n\t\t\tconst defaultOrganization = orgData.find((org:any) => org.Name === \"Quixy\");\r\n\t\t\tif (defaultOrganization) {\r\n\t\t\t  setOrganization(defaultOrganization.OrganizationId);\r\n\t\t\t}\r\n\t\t  }\r\n\t\t};\r\n\t  \r\n\t\tfetchAndSetOrganizations();\r\n\t  }, []);\r\n\t  \r\n\t// useEffect(() => {\r\n\t// \tconst fetchAndSetOrganizations = async () => {\r\n\t// \t  const orgData = await fetchOrganizations(setOrganizations, setLoading);\r\n\t// \t\tif (orgData) {\r\n\t// \t\tsetOrganizations(orgData);\r\n\t// \t  }\r\n\t// \t};\r\n\t\r\n\t// \tfetchAndSetOrganizations();\r\n\t// }, []);\r\n// here from selected or default orgid setting users \r\n\tuseEffect(() => {\r\n\t\tif (organizationId) {\r\n\t\t  const fetchAndSetUsers = async () => {\r\n\t\t\tawait fetchUsersList(\r\n\t\t\t  setModels, // Function to set models\r\n\t\t\t  setLoading, // Function to set loading state\r\n\t\t\t  organizationId, // Organization ID\r\n\t\t\t  0, // Initial skip\r\n\t\t\t  15, // Limit for top\r\n\t\t\t  setTotalcount, // Function to set total count\r\n\t\t\t  '', // Default ordering field\r\n\t\t\t  [] // Default filters\r\n\t\t\t);\r\n\t\t  };\r\n\t  \r\n\t\t\tfetchAndSetUsers();\r\n\t\t\tfetchFilteredData(selectedUser,organizationId);\r\n\t\t\tsetOrganizationId(organizationId);\r\n\t\t}\r\n\t  }, [organizationId]); \r\n\t  useEffect(() => {\r\n\t\tif (Array.isArray(models)) {\r\n\t  \r\n\t\t  const usernames = models.map(user => user.EmailId); \r\n\t\t  setUsers(usernames);\r\n\t\t} else {\r\n\t\t  console.error('Models is not an array:', models);\r\n\t\t}\r\n\t  }, [models]); \r\n\t  const today: Dayjs = dayjs(); // Always use Dayjs\r\n\t  const defaultFromDate: Dayjs = today.subtract(2, 'day');\r\n\t  const [fromDate, setFromDate] = useState<Dayjs | null>(dayjs(defaultFromDate));\r\n\t  const [toDate, setToDate] = useState<Dayjs | null>(dayjs(today).endOf('day'));\r\n\t  \r\n// useEffect(() => {\r\n// \t// Set default dates to last 3 days\r\n// \tconst today = new Date();\r\n// \tconst defaultFromDate = new Date(today);\r\n// \tdefaultFromDate.setDate(today.getDate() - 3);\r\n  \r\n// \tsetFromDate(defaultFromDate);\r\n// \tsetToDate(today);\r\n//   }, []); // Empty dependency array to run only once on component mount\r\n  \r\n  useEffect(() => {\r\n\t  fetchFilteredData(selectedUser, organizationId);\r\n  }, [paginationModel]); \r\n\r\n  \r\n//   useEffect(() => {\r\n// \tif (fromDate && toDate) {\r\n// \t  fetchFilteredData(selectedUser);\r\n// \t}\r\n\t//   },  [selectedUser, fromDate, toDate, paginationModel.page, paginationModel.pageSize, type]);\r\n\t\r\n\tconst fetchFilteredData = async (selectedUser:any,organizationId: any) => {\r\n\t\tif (!organizationId) {\r\n\t\t  console.error(\"Organization ID is required to fetch data\");\r\n\t\t  return;\r\n\t\t}\r\n\t  \r\n\t\ttry {\r\n\t\t  // Set loading to true before the request\r\n\t\t  setLoading(true);\r\n\t  \r\n\t\t  // Calculate skips and tops based on pagination model\r\n\t\t  const skipcount = paginationModel.pageSize || 15;\r\n\t\t  const limitcount = paginationModel.page * skipcount;\r\n\t\t  const skips = limitcount;\r\n\t\t  const tops = paginationModel.pageSize;\r\n\t  \r\n\t\t  // Set skip and top state\r\n\t\t  setskip(skips);\r\n\t\t  settop(tops);\r\n\t  \r\n\t\t  // Fetch audit logs with the adjusted function call\r\n\t\t  const response = await GetAuditLogsBySearch(\r\n\t\t\tskips,\r\n\t\t\ttops,\r\n\t\t\torganizationId,\r\n\t\t\torderByFields,\r\n\t\t\tfilters,\r\n\t\t\tfromDate?.toISOString(),\r\n\t\t\ttoDate?.toISOString(),\r\n\t\t\tsetTotalcount,\r\n\t\t\t  setAuditLogs,\r\n\t\t\t  type,\r\n\t\t\t  referenceType,\r\n\t\t\t  nameFilter,\r\n\t\t\tselectedUser\r\n\t\t  );\r\n\t  \r\n\t\t  if (response && Array.isArray(response)) {\r\n\t\t\t// Process the response data\r\n\t\t\tconst auditLogData: AuditLog[] = response.map((log: any) => ({\r\n\t\t\t  AuditLogId: log.AuditLogId,\r\n\t\t\t  ReferenceId: log.ReferenceId,\r\n\t\t\t  Name: log.Name,\r\n\t\t\t  OrganizationId: log.OrganizationId,\r\n\t\t\t  ReferenceType: log.ReferenceType,\r\n\t\t\t  Type: log.Type,\r\n\t\t\t  CreatedBy: log.CreatedBy,\r\n\t\t\t  Browser: log.Browser,\r\n\t\t\t  IPAddress: log.IPAddress,\r\n\t\t\t  CreatedDate: formatIndianDateTime(log.CreatedDate),\r\n\t\t\t}));\r\n\t  \r\n\t\t\tsetAuditLogs(auditLogData);\r\n\t\t  } else {\r\n\t\t\tconsole.error(\"Invalid response format:\", response);\r\n\t\t  }\r\n\t\t} catch (error) {\r\n\t\t  console.error('Error fetching audit logs:', error);\r\n\t\t} finally {\r\n\t\t  setLoading(false);\r\n\t\t}\r\n\t};\r\n\tconst handleSearch = async (organizationId: any) => {\r\n\t\tif (!organizationId) {\r\n\t\t  console.error(\"Organization ID is required to perform search\");\r\n\t\t  return;\r\n\t\t}\r\n\t  \r\n\t\ttry {\r\n\t\t  // Set loading state to true before fetching data\r\n\t\t  setLoading(true);\r\n\t  \r\n\t\t  // Calculate skips and tops based on pagination model\r\n\t\t  const skipcount = paginationModel.pageSize || 15;\r\n\t\t  const limitcount = paginationModel.page * skipcount;\r\n\t\t  const skips = limitcount;\r\n\t\t  const tops = paginationModel.pageSize;\r\n\t  \r\n\t\t  // Set skip and top state\r\n\t\t  setskip(skips);\r\n\t\t  settop(tops);\r\n\t  \r\n\t\t  // Fetch audit logs using the GetAuditLogsBySearch function\r\n\t\t  const response = await GetAuditLogsBySearch(\r\n\t\t\tskips,\r\n\t\t\ttops,\r\n\t\t\torganizationId,\r\n\t\t\torderByFields,\r\n\t\t\tfilters,\r\n\t\t\tfromDate?.toISOString(),\r\n\t\t\ttoDate?.toISOString(),\r\n\t\t\tsetTotalcount,\r\n\t\t\t  setAuditLogs,\r\n\t\t\t  type,\r\n\t\t\t  referenceType,\r\n\t\t\t  name,\r\n\t\t\tcreatedUser\r\n\t\t  );\r\n\t  \r\n\t\t  if (response && Array.isArray(response)) {\r\n\t\t\t// Process the response data\r\n\t\t\tconst auditLogData: AuditLog[] = response.map((log: any) => ({\r\n\t\t\t  AuditLogId: log.AuditLogId,\r\n\t\t\t  ReferenceId: log.ReferenceId,\r\n\t\t\t  Name: log.Name,\r\n\t\t\t  OrganizationId: log.OrganizationId,\r\n\t\t\t  ReferenceType: log.ReferenceType,\r\n\t\t\t  Type: log.Type,\r\n\t\t\t  CreatedBy: log.CreatedBy,\r\n\t\t\t  Browser: log.Browser,\r\n\t\t\t  IPAddress: log.IPAddress,\r\n\t\t\t  CreatedDate: formatIndianDateTime(log.CreatedDate),\r\n\t\t\t}));\r\n\t  \r\n\t\t\tsetAuditLogs(auditLogData);\r\n\t\t  } else {\r\n\t\t\tconsole.error(\"Invalid response format:\", response);\r\n\t\t  }\r\n\t\t} catch (error) {\r\n\t\t  console.error(\"Error fetching audit logs:\", error);\r\n\t\t} finally {\r\n\t\t  setLoading(false);\r\n\t\t}\r\n\t  };\r\n  \r\n//   const handleSearch = async (organizationId:any) => {\r\n//     // try {\r\n// \t// \tsetLoading(true);\r\n// \t// \tconst params: SearchParams = {\r\n// \t// \t  skip: paginationModel.pageSize * paginationModel.page,\r\n// \t// \t  top: paginationModel.pageSize,\r\n// \t// \t  fromDate: fromDate ? fromDate.toISOString() : undefined,\r\n// \t// \t  toDate: toDate ? toDate.toISOString() : undefined,\r\n// \t// \t\tcreatedUser: selectedUser || undefined,\r\n// \t// \t  name: nameFilter || undefined,\r\n// \t// \t\ttype: type || undefined,\r\n// \t// \t\treferenceType:referenceType|| undefined,\r\n// \t// \t  organizationId:organizationId||undefined\r\n// \t// \t};\r\n\t\r\n// \t// \t  const response = await GetAuditLogsBySearch(params, setTotalcount);\r\n// \t// \t//let allResults: AuditLog[] = [...allAuditLogData];\r\n// \t// \tconst auditLogData: AuditLog[] = response.results.map((log: any) => ({\r\n// \t// \t  AuditLogId: log.AuditLogId,\r\n// \t// \t  ReferenceId: log.ReferenceId,\r\n// \t// \t\tName: log.Name,\r\n// \t// \t  OrganizationId: log.OrganizationId,\r\n// \t// \t  ReferenceType: log.ReferenceType,\r\n// \t// \t\tType: log.Type,\r\n// \t// \t\tCreatedBy: log.CreatedBy,\r\n// \t// \t\tBrowser: log.Browser,\r\n// \t// \t\tIPAddress: log.IPAddress,\r\n// \t// \t\tCreatedDate: formatIndianDateTime(log.CreatedDate),\r\n  \r\n// \t// \t}));\r\n\t\r\n// \t// \t// allResults = [...allResults, ...auditLogData];\r\n// \t// \t// setAllAuditLogData(allResults);\r\n// \t// \tsetAuditLogs(auditLogData);\r\n//     // } catch (error) {\r\n//     //     console.error(\"Error fetching audit logs:\", error);\r\n//     // } finally {\r\n//     //     setLoading(false);\r\n//     // }\r\n// };\r\n\r\n\r\n\r\n\r\n\tconst handleDownloadExcel = async () => {\r\n\t\t// Implement the download Excel functionality here\r\n\t};\r\n\r\n\tconst CustomToolbar: React.FC<any> = () => {\r\n\t\tconst [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n\r\n\t\tconst handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {\r\n\t\t\tsetAnchorEl(event.currentTarget);\r\n\t\t};\r\n\r\n\t\tconst handleExportMenuClose = () => {\r\n\t\t\tsetAnchorEl(null);\r\n\t\t};\r\n\r\n\t\treturn (\r\n\t\t\t<GridToolbarContainer>\r\n\t\t\t\t<GridToolbarColumnsButton />\r\n\t\t\t\t<GridToolbarFilterButton />\r\n\t\t\t\t<GridToolbarDensitySelector />\r\n\t\t\t\t<Button\r\n\t\t\t\t\taria-controls=\"export-menu\"\r\n\t\t\t\t\taria-haspopup=\"true\"\r\n\t\t\t\t\tonClick={handleExportMenuClick}\r\n\t\t\t\t\tstyle={{ marginLeft: \"10px\" }}\r\n\t\t\t\t\tstartIcon={<SaveAltIcon />}\r\n\t\t\t\t>\r\n\t\t\t\t\tExport\r\n\t\t\t\t</Button>\r\n\t\t\t\t<Menu\r\n\t\t\t\t\tid=\"export-menu\"\r\n\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\tkeepMounted\r\n\t\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\t\tonClose={handleExportMenuClose}\r\n\t\t\t\t>\r\n\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\thandleDownloadExcel();\r\n\t\t\t\t\t\t\thandleExportMenuClose();\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tDownload Excel\r\n\t\t\t\t\t</MenuItem>\r\n\t\t\t\t</Menu>\r\n\t\t\t</GridToolbarContainer>\r\n\t\t);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tsubscribe(() => {\r\n\t\t\tsetSidebarOpen(isSidebarOpen());\r\n\t\t});\r\n\t}, []);\r\n\r\n\r\n\treturn (\r\n\t\t<div>\r\n\r\n<div className=\"qadpt-head\">\r\n\t\t\t\t\t\t<div className=\"qadpt-title-sec\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-title\">Audit Logs</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div className=\"qadpt-right-part\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t<Autocomplete\r\n\t\t\t\t\toptions={organizations}\r\n\t\t\t\t\tgetOptionLabel={(option) => option.Name}\r\n\t\t\t\t\tvalue={organizationId ? organizations.find(org => org.OrganizationId === organizationId) : null}\r\n\t\t\t\t\tonChange={(event, newValue) => {\r\n\t\t\t\t\t\tsetOrganizationId(newValue?.OrganizationId || null);\r\n\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\trenderInput={(params) => (\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t{...params}\r\n\t\t\t\t\t\t\tlabel=\"Select Organization\"\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t...params.InputProps,\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t'& .MuiAutocomplete-popupIndicator': {\r\n\t\t\t\t\t\t\t\t\t\ttop: '-1px'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t'& .MuiAutocomplete-clearIndicator': {\r\n\t\t\t\t\t\t\t\t\t\tvisibility: referenceType ? 'visible' : 'hidden',\r\n\t\t\t\t\t\t\t\t\t\ttop: '-1px'\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// sx={{\r\n\t\t\t\t\t// \twidth: 195,\r\n\t\t\t\t\t// \tmarginRight: 2,\r\n\t\t\t\t\t// \tmarginLeft: \"-30px\",\r\n\t\t\t\t\t// \tposition: \"relative\", // Ensure the element can be positioned\r\n\t\t\t\t\t// \ttop: \"-20px\", \r\n\t\t\t\t\t// \t'& .MuiOutlinedInput-input': {\r\n\t\t\t\t\t// \t\tpadding: '0px',\r\n\t\t\t\t\t// \t\theight: '18px',\r\n\t\t\t\t\t// \t\ttop: \"-11px\"\r\n\t\t\t\t\t// \t},\r\n\t\t\t\t\t// \t'& .MuiOutlinedInput-notchedOutline': {\r\n\t\t\t\t\t// \t\tborderRadius: \"5px\",\r\n\t\t\t\t\t// \t\theight: \"35px\",\r\n\r\n\t\t\t\t\t// \t},\r\n\t\t\t\t\t// \t'& .MuiInputLabel-outlined': {\r\n\t\t\t\t\t// \t\ttop: \"-9px\"\r\n\t\t\t\t\t// \t},\r\n\t\t\t\t\t// \t'& .MuiAutocomplete-inputRoot': {\r\n\t\t\t\t\t// \t\t\t\t\theight: \"35px\"\r\n\t\t\t\t\t// \t\t\t\t}\r\n\r\n\t\t\t\t\t// }}\r\n\t\t\t\t/>\r\n\t\t\t\r\n\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\r\n\t\t\r\n  {/* <p className=\"org-text\">Organization</p> */}\r\n\t\t\t\t{/* <p className=\"org-text\">Organization</p> */}\r\n\t\t\t\r\n\t\t\r\n\r\n\t\t\t\r\n   <div className=\"grid-toolbar-options\">\r\n\t\t\t\t<div className=\"left-options\">\r\n         <div className=\"drp-fields\">\r\n            <Autocomplete className=\"auto-filed\"\r\n               options={Object.keys(categories)}\r\n               value={referenceType}\r\n               onChange={(event, newValue) =>\r\n                  setReferenceType(newValue)}\r\n               renderInput={(params) => (\r\n                  <TextField className={`qadpt-activty-category ${referenceType ? 'visible' : 'hidden'}`}\r\n                     {...params}\r\n                     label= \"Category\"\r\n                     InputProps={{\r\n                        ...params.InputProps,\r\n                     }}\r\n                  />\r\n               )} />\r\n            <Autocomplete className=\"auto-filed\"\r\n               options={typeOptions}\r\n               value={type}\r\n               onChange={(event, newValue) =>\r\n                  setType(newValue)}\r\n               renderInput={(params) => (\r\n                  <TextField className={`qadpt-activty-category ${referenceType ? 'visible' : 'hidden'}`}\r\n                     {...params}\r\n                     label=\"Event Type\"\r\n                     InputProps={{\r\n                        ...params.InputProps,\r\n                     }}\r\n                  />\r\n               )} />\r\n         </div>\r\n         <div className=\"dt-fields\">\r\n\t\t\t<LocalizationProvider dateAdapter={AdapterDayjs}>\r\n\t\t\t<DateTimePicker className={`qadpt-DateTime ${fromDate ? '' : 'hide-close'}`}\r\n\t\t\t\t\t\t\t\tlabel=\"From Date\"\r\n\t\t\t\t\t\t\t\tviews={['year', 'month', 'day']}\r\n\t\t\t\tvalue={fromDate}\r\n\t\t\t\tonChange={(newValue) =>\r\n\t\t\t\t\tsetFromDate(newValue ? dayjs(newValue) : null)}\r\n\t\t\t\tmaxDateTime={toDate ?? undefined}\r\n\t\t\t\t\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\ttextField: {\r\n\t\t\t\t\t\tclassName: \"qadpt-datepicker\", \r\n\t\t\t\t\t},\r\n\t\t\t\t\tfield: { clearable: true },\r\n\t\t\t\t}}\r\n               />\r\n               <DateTimePicker className={`qadpt-DateTime dt-fld2 ${fromDate ? '' : 'hide-close'}`}\r\n\t\t\t\t\t\t\t\tlabel=\"To Date\"\r\n\t\t\t\t\t\t\t\tviews={['year', 'month', 'day']}\r\n                  value={toDate}\r\n                  onChange={(newValue) =>\r\n                     setToDate(newValue ? dayjs(newValue).endOf('day') : null)}\r\n                  minDateTime={fromDate ?? undefined}\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\ttextField: {\r\n\t\t\t\t\t  className: \"qadpt-datepicker\", \r\n\t\t\t\t\t},\r\n\t\t\t\t\tfield: { clearable: true },\r\n\t\t\t\t}}\r\n               />\r\n            </LocalizationProvider>\r\n         </div>\r\n         <TextField className=\"name-fld\"\r\n            variant=\"outlined\"\r\n            label=\"Name\"\r\n            value={name}\r\n            onChange={(e) => {\r\n               setName(e.target.value);\r\n               setNameFilter(e.target.value); // Update nameFilter when name changes\r\n            }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t <div className=\"right-options\">\r\n         <Button\r\n            variant=\"outlined\"\r\n            color=\"primary\"\r\n            onClick={() => {\r\n               // Immediately clear the audit logs data\r\n               // setAuditLogs([]); \r\n               // Set clearing mode to true\r\n               setIsClearing(true);\r\n               // Reset state and data\r\n               setEnvironment(\"\");\r\n               setType(\"\");\r\n               setReferenceType(\"\");\r\n               setName(\"\");\r\n               setNameFilter(\"\");\r\n               setFromDate(null);\r\n               setToDate(null);\r\n               setUser(\"\");\r\n               // Reset pagination without triggering data fetch\r\n               setPaginationModel({ page: 0, pageSize: 15 });\r\n            }}>\r\n            Clear\r\n         </Button>\r\n         <Button\r\n            variant=\"contained\"\r\n            color=\"primary\"\r\n            onClick={() => handleSearch(organizationId)}\r\n            disabled={environment === \"\" &&\r\n               (type === \"\" || type === null) &&\r\n               (referenceType === \"\" || referenceType === null) &&\r\n               fromDate === null &&\r\n               toDate === null &&\r\n               user === \"\" && name === \"\"}>\r\n           Search\r\n         </Button>\r\n      </div>\t\r\n      </div>\r\n     \r\n   </div>\r\n\r\n\t\t\t{loading ? (\r\n\t\t\t\t<div className=\"Loaderstyles\">\r\n\t\t\t\t\t<img\r\n\t\t\t\t\t\tsrc={loader}\r\n\t\t\t\t\t\talt=\"Spinner\"\r\n\t\t\t\t\t\tclassName=\"LoaderSpinnerStyles\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</div>\r\n\t\t\t) : (\r\n\t\t\t\t\t\t<DataGrid\r\n\t\t\t\t\t\t\t className=\"qadpt-setting-grd\"\r\n\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t// \tborderColor: \"#F6EEEE\",\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeaders\": {\r\n\t\t\t\t\t\t\t// \t\tbackgroundImage: \"linear-gradient(to right, #F6EEEE, #F6EEEE)\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeaderTitle\": {\r\n\t\t\t\t\t\t\t// \t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\t// \t\tcolor: \"#767475\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeader\": {\r\n\t\t\t\t\t\t\t// \t\tbackgroundImage: \"linear-gradient(to right, #F6EEEE, #F6EEEE)\",\r\n\t\t\t\t\t\t\t// \t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t// \t\tborderRight: \"1px solid #F6EEEE\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeader, .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t// \t\tborderRight: \"1px solid #F6EEEE\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-row\": {\r\n\t\t\t\t\t\t\t// \t\t\"& .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t// \t\t\tborderBottom: \"none\",\r\n\t\t\t\t\t\t\t// \t\t},\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \tposition: \"relative\",\r\n\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t//   sx={{\r\n\t\t\t\t\t\t\t// \tborderColor: \"black\",\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeaders\": {\r\n\t\t\t\t\t\t\t// \t  backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeaderTitle\": {\r\n\t\t\t\t\t\t\t// \t  fontWeight: \"bold\",\r\n\t\t\t\t\t\t\t// \t  color: \"white\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeader\": {\r\n\t\t\t\t\t\t\t// \t  backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\r\n\t\t\t\t\t\t\t// \t  borderBottom: \"2px solid #ddd\", // Increased border weight for column headers\r\n\t\t\t\t\t\t\t// \t  color: \"white\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeader--alignLeft\": {\r\n\t\t\t\t\t\t\t// \t  backgroundImage: \"linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))\",\r\n\t\t\t\t\t\t\t// \t  color: \"white\",\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t// \t  borderBottom: \"2px solid #ddd\", // Increased border weight for cells\r\n\t\t\t\t\t\t\t// \t  borderRight: \"2px solid #ddd\", // Increased border weight for cells\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-columnHeader, .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t// \t  borderRight: \"2px solid #ddd\", // Increased border weight for both headers and cells\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t// \t\"& .MuiDataGrid-row\": {\r\n\t\t\t\t\t\t\t// \t  \"&:last-child .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t// \t\tborderBottom: \"none\", // Remove border from the last row\r\n\t\t\t\t\t\t\t// \t  },\r\n\t\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t\t//   }}\r\n                            rows={auditLogs}\r\n\t\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\t\tgetRowId={(row) => row.AuditLogId}\r\n\r\n\t\t\t\t\t\t\tpaginationModel={paginationModel}\r\n\t\t\t\t\t\t\tonPaginationModelChange={setPaginationModel}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  pagination\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  paginationMode=\"server\"\r\n\t\t\t\t\t\t\trowCount={totalcount}\r\n\t\t\t\t\t\t\tpageSizeOptions={[15, 25, 50, 100]}\r\n\t\t\t\t\t\t\tlocaleText={{\r\n\t\t\t\t\t\t\t  MuiTablePagination: {\r\n\t\t\t\t\t\t\t\tlabelRowsPerPage: \"Records Per Page\",\r\n\t\t\t\t\t\t\t  },\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableRowSelectionOnClick={true}\r\n\t\t\t\t\t\t\tloading={loading}\r\n/>\r\n\r\n\t\t\t)}\r\n\t\t</div>\r\n\t\t\r\n\t);\r\n\t\r\n};\r\n\r\nexport default SuperAdminAuditLogList;\r\n"], "names": ["categories", "SuperAdminAuditLogList", "auditLogs", "setAuditLogs", "useState", "totalRecords", "setTotalRecords", "loading", "setLoading", "referenceTypes", "setReferenceTypes", "organizationId", "setOrganizationId", "orgid", "setOrgId", "OrganizationId", "types", "setTypes", "environment", "setEnvironment", "type", "setType", "referenceType", "setReferenceType", "typeOptions", "setTypeOptions", "name", "setName", "user", "setUser", "organizations", "setOrganizations", "organization", "setOrganization", "defaultOrganization", "setDefaultOrganization", "orgname", "setOrgName", "models", "setModels", "created<PERSON>ser", "setCreated<PERSON>ser", "nameFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eventType", "setEventType", "allAuditLogData", "setAllAuditLogData", "selectedOrganizationId", "setSelectedOrganizationId", "users", "setUsers", "selected<PERSON>ser", "setSelectedUser", "isClearing", "setIsClearing", "order<PERSON><PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filters", "setFilters", "sidebarOpen", "setSidebarOpen", "isSidebarOpen", "paginationModel", "setPaginationModel", "page", "pageSize", "skip", "setskip", "top", "settop", "totalcount", "setTotalcount", "formatIndianDateTime", "dateString", "format", "Date", "useEffect", "async", "orgData", "fetchOrganizations", "length", "fetchAndSetOrganizations", "find", "org", "Name", "fetchUsersList", "fetchAndSetUsers", "fetchFilteredData", "Array", "isArray", "usernames", "map", "EmailId", "console", "error", "today", "dayjs", "defaultFromDate", "subtract", "fromDate", "setFromDate", "toDate", "setToDate", "endOf", "skipcount", "skips", "tops", "response", "GetAuditLogsBySearch", "toISOString", "auditLogData", "log", "AuditLogId", "ReferenceId", "ReferenceType", "Type", "CreatedBy", "Browser", "<PERSON><PERSON><PERSON>", "CreatedDate", "subscribe", "_jsxs", "children", "className", "_jsx", "Autocomplete", "options", "getOptionLabel", "option", "value", "onChange", "event", "newValue", "renderInput", "params", "TextField", "_objectSpread", "label", "InputProps", "sx", "visibility", "Object", "keys", "concat", "LocalizationProvider", "dateAdapter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DateTimePicker", "views", "maxDateTime", "undefined", "slotProps", "textField", "field", "clearable", "minDateTime", "variant", "e", "target", "<PERSON><PERSON>", "color", "onClick", "handleSearch", "disabled", "src", "loader", "alt", "DataGrid", "rows", "columns", "headerName", "width", "flex", "disableColumnMenu", "getRowId", "row", "onPaginationModelChange", "pagination", "paginationMode", "rowCount", "pageSizeOptions", "localeText", "MuiTablePagination", "labelRowsPerPage", "disableRowSelectionOnClick"], "sourceRoot": ""}